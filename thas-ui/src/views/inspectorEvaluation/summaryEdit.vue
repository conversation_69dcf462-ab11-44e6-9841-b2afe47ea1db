<!--  -->
<template>
  <div class="summary-edit">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <h4> 提交审查结果 </h4>
      </div>
      <el-form ref="summaryForm" :model="summaryForm" :rules="rules" label-width="110px">
        <el-form-item label="审查结果: " prop="autResult">
          {{autResultText}}
          <!-- <el-input v-model.trim="summaryForm.autDesc" placeholder="请输入自评总结" type="textarea" :rows="4" :maxlength="255"></el-input> -->
        </el-form-item>
         <el-form-item label=" ">
          <el-button type="primary" @click="submitSummary" v-loading="summaryLoading" :disabled="summaryLoading">提交自评终稿</el-button>
        </el-form-item>
        <!-- <el-form-item label="审查总结: " prop="autDesc">
          <el-input v-model.trim="summaryForm.autDesc" placeholder="请输入审查总结" type="textarea" :rows="4" :maxlength="255"></el-input>
        </el-form-item>
        <el-form-item label=" ">
          <el-button type="primary" @click="submitSummary" v-loading="summaryLoading" :disabled="summaryLoading">提交</el-button>
        </el-form-item> -->
      </el-form>
    </el-card>
  </div>
</template>

<script>
import request from "@/utils/request";

export default {
  name: "SummaryEdit",
  components: {},
  props: {
    relation: {
      type: Object,
      default: () => { },
    },
    detail: {
      type: Object,
      default: () => { },
    },
    autResult: {
      type: Number,
      default: () => 1
    }
  },
  watch: {
    detail: {
      deep: true,
      immediate: true,
      handler() {
        setTimeout(() => {
          this.$refs.summaryForm && this.$refs.summaryForm.clearValidate();
        }, 0);
      }
    },
    autResult: {
      immediate: true,
      handler() {
        this.summaryForm.autResult = this.autResult;
      },
    },
  },
  data() {
    return {
      files: [],
      reviewSummaryOptions: [],
      checkSummaryOptions: [],
      reviewResultOptions: [],
      summaryLoading: false,
      summaryForm: {
        autAdvantage: '',
        autEvaluate: '',
        autProposal: '',
        autDesc: '',
        autResult: '1',
        fileIds: ''
      },
      url: {
        report: '/pdf/generate/ftlToPdf',
      },
      rules: {
        autDesc: [{
          required: true,
          message: "请输入审查总结",
          trigger: ["change", "blur"],
        }],
      }
    };
  },

  computed: {
    autResultText() {
      let option = this.checkSummaryOptions.find(option => option.dictValue == this.autResult);
      let text = ''
      if (option) text = option.dictLabel;
      return text
    },
  },

  created() { },

  mounted() {
    this.initDicts();
  },

  methods: {
    initDicts() {
      this.getDicts("review_summary").then((res) => {
        this.reviewSummaryOptions = res.data;
      });
      this.getDicts("check_summary").then((res) => {
        this.checkSummaryOptions = res.data;
      });
      this.getDicts("review_result").then((res) => {
        this.reviewResultOptions = res.data;
      });
    },

    async submitAutSaAud(data) {
      return request({
        url: "/aut/sa/aud/submitAutSaAud",
        method: "post",
        data,
      });
    },

    submitSummary() {
      this.$refs.summaryForm.validate(async (valid) => {
        if (valid) {
          this.summaryLoading = true;
          let {
            autDesc,
            autResult,
          } = this.summaryForm;

          let data = {
            autCode: this.relation.autCode,
            accountId: this.$store.getters.userId + "",
            submitType: this.AutSaAudCurrentStatusEnum.FR_SUMMARY,
            autSaAudLists: [{
              autDesc,
              autResult,
              clauseId: ''
            }],
          }

          try {
            await this.submitAutSaAud(data);
            setTimeout(() => {
              this.summaryLoading = false;
            }, 2000);
            this.$emit("summarySubmit");
          } catch (error) {
            this.$message({
              type: 'error',
              message: error.message
            })
          }
        }
      })
    },

    filesValidator(rule, value, callback) {
      if (!value) {
        callback("请上传审核证明");
      }
      callback();
    },

    onSuccess(res, file) {
      file.fileId = res.data.fileId;
      this.files.push(file);
      this.summaryForm.fileIds = (
        this.files.map((file) => file.fileId) || []
      ).join(",");
      this.$refs.summaryForm.validateField("fileIds");
    },

    onRemove(error, file, length, index) {
      this.files.splice(index, 1);
      this.summaryForm.fileIds = (
        this.files.map((file) => file.fileId) || []
      ).join(",");
    },
  },
};
</script>
<style lang="scss" scoped>
.summary-edit {
  .box-card {
    margin-bottom: 10px;
  }
  // padding: 24px;
  ::v-deep .el-card {
    border: 0;
    .el-card__header {
      padding-bottom: 15px;
      .clearfix h4 {
        float: left;
        font-weight: bold;
        margin: 0;
      }
    }

    .grade .el-select .el-input.is-disabled .el-input__inner {
      background-color: #ffffff;
      border: 1px solid #dcdfe6;
      color: #606266;
    }
  }
}
</style>
