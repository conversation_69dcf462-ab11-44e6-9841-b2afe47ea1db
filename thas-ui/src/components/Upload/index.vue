<template>
  <el-upload :class="{ eventNone: loading }" class="evaluation-upload" :accept="accept" :multiple="multiple"
    :action="action" :data="data" :file-list="fileList" :before-upload="handleBeforeUpload" :on-success="handleSuccess"
    :on-error="handleUploadError" :show-file-list="false" :with-credentials="true" :headers="headers"
    :disabled="disabled">
    <div class="upload-content" slot="trigger" :style="{}">
      <el-button type="primary" icon="el-icon-document-add" size="small" v-loading="loading"
        :disabled="loading || disabled">{{ text || "文件上传" }}</el-button>
    </div>
    <div class="upload-content file-list"
      :style="{ display: multiple ? 'block' : 'inline-block', width: multiple ? '100%' : 'calc(100% - 220px)' }">
      <ul class="file-ul" v-if="!loading">
        <li class="file-li" v-for="(file, index) in fileList" :key="index">
          <span @click="download(file)">{{ file.name || file.fileName }}</span>
          <i class="el-icon-close close" @click="handleRemove(file, index)"></i>
        </li>
      </ul>
      <el-button v-else type="text" style="cursor: initial" size="small" :disabled="loading">文件上传中。。。</el-button>
    </div>
    <span slot="tip" class="el-upload__tip">
      <span v-if="tip">{{ tips || "请上传纸质扫描文件或电子档文件；" }}</span>
      <span>
        <i v-if="headSupple" style="font-style: normal">
          {{ `${headSupple}，` }}
        </i>
        支持{{ accept }}格式文件
        <i v-if="fileSize" style="font-style: normal">，大小不超过{{ fileSize }}M</i>
        <i v-if="!single && fileLimit" style="font-style: normal">，数量不超过{{ fileLimit }}个</i>
        <i v-if="acceptSupple" style="font-style: normal">
          {{ `，${acceptSupple}` }}
        </i>
      </span>
    </span>
  </el-upload>
</template>
<script>
import { getToken } from "@/utils/auth";
import { download } from "@/utils/request";

export default {
  name: "uploader",
  props: {
    accept: {
      type: String,
      default: () => ".JPG,.JPEG,.PNG,.PDF",
    },
    tip: {
      type: Boolean,
      default: () => true,
    },
    tips: {
      type: String,
      default: () => '',
    },
    acceptSupple: {
      type: String,
      default: () => "",
    },
    fileSize: {
      type: Number,
      default: () => 100,
    },
    fileLimit: {
      type: Number,
      default: () => 10,
    },
    headSupple: {
      type: String,
      default: () => "",
    },
    multiple: {
      type: Boolean,
      default: () => false,
    },
    disabled: {
      type: Boolean,
      default: () => false,
    },
    action: {
      type: String,
      default: () =>
        `${localStorage.baseUrl || process.env.VUE_APP_BASE_API
        }/common/uploadFile`,
    },
    files: {
      type: Array,
      default: () => [],
    },
    single: {
      type: Boolean,
      default: () => false,
    },
    data: {
      type: Object,
      default: () => { },
    },
    text: {
      type: String,
      default: () => "文件上传",
    },
  },
  watch: {
    files: {
      immediate: true,
      deep: true,
      handler() {
        this.fileList = [...this.files];
      },
    },
  },
  data() {
    return {
      fileList: [],
      loading: false,
      waitFiles: 0,
      showLimitOverflow: false,

    };
  },
  computed: {
    headers() {
      return {
        Authorization: this.action.includes("binTang")
          ? ""
          : "Bearer " + getToken(),
      };
    },
  },
  methods: {
    handleBeforeUpload(file) {
      if (this.multiple) {
        let len = this.fileList.length;
        if (len >= this.fileLimit) {
          if (this.showLimitOverflow) return false;
          this.showLimitOverflow = true;
          this.$message({
            type: "warning",
            message: "上传的数量已超过限制！",
            onClose: () => {
              this.showLimitOverflow = false;
            }
          });
          return false;
        } else {
          this.waitFiles++;
          if (len + this.waitFiles > this.fileLimit) {
            if (this.showLimitOverflow) return false;
            this.showLimitOverflow = true;
            this.$message({
              type: "warning",
              message: "上传的数量已超过限制，超出部分将不在上传",
              onClose: () => {
                this.showLimitOverflow = false;
              }
            });
            return false
          }
        }
      } else if (!this.single && this.fileList.length + this.waitFiles >= this.fileLimit) {
        this.$message({
          type: "warning",
          message: "上传的数量已超过限制！",
        });
        return false;
      }
      const list = this.accept
        .split(",")
        .map((accept) => accept.toLocaleUpperCase());
      const suffix = "." + file.name.split(".").pop().toLocaleUpperCase();
      if (!list.includes(suffix)) {
        this.$message({
          type: "warning",
          message: "请上传指定格式的文件！",
        });
        return false;
      }
       if(file.size <= 0) {
        this.$message({
          type: "warning",
          message: `上传的文件大小不得为0M`,
        });
        return false;
       }
      // 当fileSize大于0时才检查文件大小是否符合，为0或者小于0则不校验文件大小
      if (this.fileSize > 0 && file.size / 1024 / 1024 > this.fileSize) {
        this.$message({
          type: "warning",
          message: `上传的文件大小不得超过${this.fileSize}M`,
        });
        return false;
      }
      this.loading = true;
      const hasAction = this.action !== "";
      if (!hasAction) {
        if (this.single) {
          this.$set(this.fileList, 0, file);
        } else {
          this.fileList.push(file);
        }
      }
      this.$emit("fileAdd", file);
      return hasAction;
    },

    handleRemove(file, index) {
      this.fileList.splice(index, 1);
      this.$emit("fileRemove", file, this.fileList.length, index);
    },

    handleSuccess(response, file, fileList) {
      if (response.code === 200) {
        if (this.multiple) {
          let all = fileList.every(file => file.response || file.fileId);
          this.loading = !all;
          if (all) {
            this.waitFiles = 0
          }
        } else {
          this.loading = false;
        }
        this.$emit("uploadSuccess", response, file, fileList);
      } else {
        this.loading = false;
        this.$emit("uploadError");
      }
    },
    // 上传失败
    handleUploadError(err) {
      this.loading = false;
      this.$message.error("上传失败, 请重试");
      this.$emit("fileRemove", err, -1, -1);
    },
    // 下载
    download(file) {
      if (this.loading || !file.fileId) return;
      let fileId =
        file.response && file.response.data
          ? file.response.data.fileId
          : file.fileId;
      download(
        "/common/downloadFile",
        { fileId: Number(fileId) },
        file.name || file.fileName
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.evaluation-upload {
  overflow: hidden;

  ::v-deep .el-upload {
    display: inline-block;
    text-align: left;
  }

  &.eventNone {
    pointer-events: none;
  }

  .el-upload__tip {
    display: block;
    margin-top: 0;

    span {
      font-size: 12px;
      display: block;
      line-height: 18px;
    }
  }
}

.upload-content {
  display: inline-block;

  ::v-deep .el-button {
    margin-bottom: 5px;
  }
}

.file-list {
  margin-left: 15px;

  .file-ul {
    margin: 0;
    padding-left: 0;
    list-style: none;

    display: flex;
    width: 100%;
    flex-wrap: wrap;
    align-content: stretch;
    justify-content: flex-start;
    align-items: baseline;

    .file-li {
      width: 49%;
      min-width: 300px;
      line-height: 24px;

      &:hover .close {
        visibility: visible;
      }

      display: inline-block;
      // margin-right: 30px;
      padding-right: 6px;
      color: #649af6;

      span {
        cursor: pointer;
      }

      .close {
        color: #b2b2b2;
        display: inline-block;
        margin-left: 2px;
        cursor: pointer;
        font-weight: bold;
        font-size: 12px;
        vertical-align: middle;
        visibility: hidden;
      }
    }
  }
}
</style>
