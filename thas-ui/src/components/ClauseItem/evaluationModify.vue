<!--  -->
<template>
  <!-- 审查评审报告  准确性确认  验证评审员验证 -->
  <div class="evaluation-check">
    <el-divider content-position="left">{{ reportTitle }}</el-divider>
    <el-form ref="form" :model="formData" :rules="rules" label-width="110px">
      <el-form-item :label="`${reviewResult}：`" :class="{ readonly: readOnly }" prop="autResult">
        <el-select v-if="!readOnly" v-model.trim="formData.autResult" clearable filterable :placeholder="`请选择${this.reviewResult}`">
          <el-option v-for="(item, index) in reviewResultOptions" :key="index" :value="item.dictValue" :label="item.dictLabel"></el-option>
        </el-select>
        <span v-else> {{ getResultTitle(formData.autResult) }}</span>
      </el-form-item>
      <el-form-item :label="`${reviewDesc}：`" :class="{ readonly: readOnly }" prop="autDesc">
        <el-input v-model.trim="formData.autDesc" v-if="!readOnly" type="textarea" :rows="4" :maxlength="50"></el-input>
        <span v-else> {{ formData.autDesc }}</span>
      </el-form-item>
      <el-form-item v-if="!readOnly">
        <el-button type="primary" @click="submit" v-loading="submitLoading" :disabled="submitLoading">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "EvaluationCheck",
  components: {},
  props: {
    reportTitle: {
      type: String,
      default: () => "",
      required: true,
    },
    label: {
      type: String,
      default: () => "",
    },
    status: {
      type: [String, Number],
      default: () => false,
    },
    saAud: {
      type: Object,
      default: () => { },
    },
    clause: {
      type: Object,
      default: () => { },
    },
    type: {
      type: [Number, String],
      default: () => -1,
    },
  },
  watch: {
    saAud: {
      immediate: true,
      deep: true,
      handler() {
        const { autResult = "", autDesc = "" } = this.saAud || {};
        this.formData = {
          autResult,
          autDesc,
        };
        this.$refs['form'] && this.$refs['form'].clearValidate('autResult')
        this.$refs['form'] && this.$refs['form'].clearValidate('autDesc')
        this.submitLoading = false;
      },
    },
  },
  data() {
    return {
      formData: {
        autResult: "",
        autDesc: "",
      },

      reviewResultOptions: [],
      submitLoading: false,
    };
  },

  computed: {
    desc() {
      if (
        this.type ==
        this.AutSaAudSubmitTypeEnum.REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW
      ) {
        return "回应";
      }
      return "描述";
    },
    rules() {
      return {
        autResult: [
          {
            required: true,
            message: `请选择${this.label}结果`,
            trigger: "change",
          },
        ],
        autDesc: [
          {
            required: true,
            message: `请选择${this.label}${this.desc}`,
            trigger: ["change", "blur"],
          },
        ],
      };
    },
    isHospital() {
      // 医疗机构
      return (
        this.$store.getters.roles.includes("hospital") || this.showOnlyHospital
      );
    },
    isAssessor() {
      // 评审员
      return (
        this.$store.getters.roles.includes("assessor") ||
        this.$store.getters.roles.includes("assessor-leader")
      );
    },
    isInspector() {
      // 审查员
      return (
        this.$store.getters.roles.includes("inspector") ||
        this.$store.getters.roles.includes("inspector-leader")
      );
    },
    isSeniorAssessor() {
      return this.$store.getters.roles.includes("senior-assessor");
    },
    hasSaAud() {
      return this.saAud && Object.keys(this.saAud).length != 0;
    },
    saAudTypeMatch() {
      if (this.hasSaAud) {
        return this.type == this.saAud.type;
      }
      return false;
    },
    readOnly() {
      let statues = [
        this.AutSaAudStatusEnum.REVIEWER_MODIFICATION_REVIEW,
        this.AutSaAudStatusEnum.REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW,
      ];
      if (statues.includes(Number(this.status)))
        return false || (this.hasSaAud && this.saAudTypeMatch);
      else return true;
    },
    examiner() {
      return `${this.label}员`;
    },
    reviewResult() {
      return `${this.label}结果`;
    },
    reviewDesc() {
      return `${this.label}${this.desc}`;
    },
    getResultTitle() {
      return (value) => {
        return (
          this.reviewResultOptions.find(
            (option) => option.dictValue == value
          ) || {}
        ).dictLabel;
      };
    },
  },

  created() { },

  mounted() {
    this.initDicts();
  },

  methods: {
    initDicts() {
      this.getDicts("review_result").then((res) => {
        this.reviewResultOptions = res.data;
      });
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.submitLoading = true;
        let type = 100,
          status = Number(this.status);

        if (status == this.AutSaAudStatusEnum.REVIEWER_MODIFICATION_REVIEW) {
          type = this.AutSaAudSubmitTypeEnum.REVIEWER_MODIFICATION_REVIEW;
        } else if (
          status ==
          this.AutSaAudStatusEnum.REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW
        ) {
          type =
            this.AutSaAudSubmitTypeEnum
              .REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW;
        }

        let clauseId;
        if (this.saAud) {
          clauseId = this.saAud.clauseId;
        } else {
          clauseId = this.clause.clauseId;
        }

        this.$emit("onComplete", null, this.formData, clauseId, type);

        setTimeout(() => {
          this.submitLoading = false;
        }, 5000);
      });
    },
  },
};
</script>
<style lang='scss' scoped>
.evaluation-check {
  ::v-deep .el-divider {
    margin-bottom: 20px;
    .el-divider__text {
      background-color: #fbfbfb;
    }
  }
  ::v-deep .el-form-item {
    margin-bottom: 24px;
    .el-form-item__content {
      overflow: visible;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
