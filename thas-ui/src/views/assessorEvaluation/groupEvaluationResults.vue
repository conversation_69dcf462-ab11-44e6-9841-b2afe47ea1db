<template>
  <div v-loading="loading" class="group-evaluation-results">
    <!-- 分组评价结果 -->
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane v-for="(item, index) in evaluateResultStatsVoList" :key="index" :label="item.domainName"
        :name="item.domainId">
      </el-tab-pane>
    </el-tabs>
    <el-table :data="tableData" border>
      <el-table-column label="评价结果" prop="name" align="center"></el-table-column>
      <el-table-column label="优秀" prop="1" align="center"></el-table-column>
      <el-table-column label="良好" prop="2" align="center"></el-table-column>
      <el-table-column label="达标" prop="3" align="center"></el-table-column>
      <el-table-column label="部分达标" prop="4" align="center"></el-table-column>
      <el-table-column label="不达标" prop="5" align="center"></el-table-column>
      <el-table-column label="达标及以上" prop="6" align="center"></el-table-column>
      <el-table-column label="不适用" prop="7" align="center"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import request from '@/utils/request'
export default {
  name: 'GroupEvaluationResults',
  data() {
    return {
      loading: false,
      activeName: '',
      tableData: [
        {
          name: '基本款',
          '1': '0',
          '2': '0',
          '3': '0',
          '4': '0',
          '5': '0',
          '6': '0',
          '7': '0'
        },
        {
          name: '非基本款',
          '1': '0',
          '2': '0',
          '3': '0',
          '4': '0',
          '5': '0',
          '6': '0',
          '7': '0'
        },
        {
          name: '总数',
          '1': '0',
          '2': '0',
          '3': '0',
          '4': '0',
          '5': '0',
          '6': '0',
          '7': '0'
        },
        {
          name: '本组基本款各项结果占比',
          '1': '0%',
          '2': '0%',
          '3': '0%',
          '4': '0%',
          '5': '0%',
          '6': '0%',
          '7': '0%'
        },
        {
          name: '本组非基本款各项结果占比',
          '1': '0%',
          '2': '0%',
          '3': '0%',
          '4': '0%',
          '5': '0%',
          '6': '0%',
          '7': '0%'
        },
        {
          name: '合计占比',
          '1': '0%',
          '2': '0%',
          '3': '0%',
          '4': '0%',
          '5': '0%',
          '6': '0%',
          '7': '0%'
        }
      ],
      evaluateResultStatsVoList: []
    }
  },
  created() {
    if (this.$route.query.autCode) {
      this.queryDetail()
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event)
      for (let index = 0; index < this.evaluateResultStatsVoList[tab.index].evaluateInfoList.length; index++) {
        let key = index + 1
        if (index === 5) {
          key = 7
        } else if (index === 6) {
          key = 6
        }
        this.tableData[0][key] = this.evaluateResultStatsVoList[tab.index].evaluateInfoList[index].basicClauseCount || 0
        this.tableData[1][key] = this.evaluateResultStatsVoList[tab.index].evaluateInfoList[index].notBasicClauseCount || 0
        this.tableData[2][key] = this.evaluateResultStatsVoList[tab.index].evaluateInfoList[index].count || 0
        this.tableData[3][key] = (this.evaluateResultStatsVoList[tab.index].evaluateInfoList[index].basicRate || 0) + '%'
        this.tableData[4][key] = (this.evaluateResultStatsVoList[tab.index].evaluateInfoList[index].notBasicRate || 0) + '%'
        this.tableData[5][key] = (this.evaluateResultStatsVoList[tab.index].evaluateInfoList[index].rateCount || 0) + '%'
      }
    },
    // 获取评审评价数据详情
    queryDetail() {
      this.loading = true
      request({
        url: '/aut/sa/aud/queryDetail',
        method: 'post',
        data: {
          autCode: this.$route.query.autCode,
          accountId: this.$store.getters.userId
        }
      }).then((res) => {
        console.log(res)
        if (res && res.code === 200 && res.data) {
          this.evaluateResultStatsVoList = res.data.evaluateResultStatsVoList || []
          this.activeName = this.evaluateResultStatsVoList && this.evaluateResultStatsVoList.length ? this.evaluateResultStatsVoList[0].domainId : ''
          if (this.evaluateResultStatsVoList[0] && this.evaluateResultStatsVoList[0].evaluateInfoList && this.evaluateResultStatsVoList[0].evaluateInfoList.length) {
            for (let index = 0; index < this.evaluateResultStatsVoList[0].evaluateInfoList.length; index++) {
              let key = index + 1
              if (index === 5) {
                key = 7
              } else if (index === 6) {
                key = 6
              }
              this.tableData[0][key] = this.evaluateResultStatsVoList[0].evaluateInfoList[index].basicClauseCount || 0
              this.tableData[1][key] = this.evaluateResultStatsVoList[0].evaluateInfoList[index].notBasicClauseCount || 0
              this.tableData[2][key] = this.evaluateResultStatsVoList[0].evaluateInfoList[index].count || 0
              this.tableData[3][key] = (this.evaluateResultStatsVoList[0].evaluateInfoList[index].basicRate || 0) + '%'
              this.tableData[4][key] = (this.evaluateResultStatsVoList[0].evaluateInfoList[index].notBasicRate || 0) + '%'
              this.tableData[5][key] = (this.evaluateResultStatsVoList[0].evaluateInfoList[index].rateCount || 0) + '%'
            }
          }
        }
      }).catch((error) => {
        console.log(error)
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.group-evaluation-results {}
</style>
