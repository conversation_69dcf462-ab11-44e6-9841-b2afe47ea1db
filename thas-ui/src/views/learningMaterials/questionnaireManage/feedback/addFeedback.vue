<template>
  <div class='add-feedback'>
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>添加反馈表</span>
      </div>

      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="反馈表名称" prop="title">
          <el-input v-model.trim="form.title" placeholder="请输入反馈表名称" :maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="反馈表类型" prop="type">
          <el-select v-model.trim="form.type" placeholder="请选择反馈表类型" clearable filterable>
            <el-option v-for="classifyItem in feedbackClassifyOptions" :key="classifyItem.dictValue" :value="classifyItem.dictValue" :label="classifyItem.dictLabel"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="反馈表描述" prop="description">
          <el-input type="textarea" v-model.trim="form.description" placeholder="请输入反馈表描述" show-word-list :maxlength="200"></el-input>
        </el-form-item>
      </el-form>

      <el-button @click="add">创建题目</el-button>
      <br />
      <br />

      <el-form ref="feedForm" :model="{list}" inline>
        <el-tabs v-model="activeName" type="card" closable @tab-remove="removeTab">
          <el-tab-pane :key="item.activeName" v-for="(item, idx) in list" :label="item.activeTitle" :name="item.activeName">
            <el-form-item label="题目：" :prop="`list.${idx}.question`" :rules="[{required: true, message: '请输入题目', trigger: ['change', 'blur']}]">
              <el-input v-model="item.question" style="width:300px;" :maxlength="100"></el-input>
              &nbsp;
              &nbsp;
              <el-button @click="addQuestion(item, idx)">创建问题</el-button>
            </el-form-item>
            <br />
            <el-form-item label="备注：">
              <el-input type="textarea" :rows="2" v-model="item.remark" style="width:500px;" show-word-list :maxlength="200"></el-input>
            </el-form-item>
            <br />
            <br />

            <div v-for="(child, index) in item.questionList" :key="index">
              <el-form-item v-if="item.questionList.length > 1" label="子标题：" :prop="`list.${idx}.questionList.${index}.subtitle`" :rules="[{required: item.questionList.length > 1, message: '请输入子标题', trigger: ['change', 'blur']}]">
                <el-input v-model="child.subtitle" style="width:300px;" :maxlength="100"></el-input>
              </el-form-item>
              <el-form-item label="类型：" :prop="`list.${idx}.questionList.${index}.type`" :rules="[{required: true, message: '请选择类型', trigger: ['change', 'blur']}]">
                <el-select v-model="child.type" style="width:200px;" clearable>
                  <el-option :value="1" label="表格"></el-option>
                  <el-option :value="2" label="多行文本框"></el-option>
                </el-select>
              </el-form-item>
              <el-button v-if="item.questionList.length > 1" type="primary" @click="delChildQ(item.questionList, index)">删除</el-button>

              <br v-if="child.type == 1" />
              <br v-if="child.type == 1" />

              <el-form-item v-if="child.type == 1" label="是否循环表格：">
                <el-radio-group v-model="child.circulation">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
              &nbsp;
              <el-form-item v-if="child.type == 1 && child.circulation" label="循环类型：" :prop="`list.${idx}.questionList.${index}.cycleType`" :rules="[{required: true, message: '请选择循环类型', trigger: ['change', 'blur']}]">
                <el-select v-model="child.cycleType" style="width:200px;" clearable filterable>
                  <el-option v-for="(types, index) in cycleType" :key="index" :value="types.dictValue" :label="types.dictLabel" v-if="form.type !== '5' || (form.type === '5' && types.dictValue !== 'student')"></el-option>
                </el-select>
              </el-form-item>
              <br v-if="child.type == 1" />
              <br v-if="child.type == 1" />
              <!-- score remark -->
              <el-table :data="child.questionList" v-if="child.type == 1" border>
                <el-table-column label="序号" type="index"></el-table-column>
                <el-table-column>
                  <template #header>
                    {{child.questionTitle.theme}}
                    <el-button type="text" circle size="mini" icon="el-icon-edit" @click="modify('theme', child.questionTitle.theme, child)"></el-button>
                  </template>
                  <template slot-scope="{row, $index}">
                    <el-form-item :prop="`list.${idx}.questionList.${index}.questionList.${$index}.label`" :rules="[{required: true, message: '请输入主题', trigger: ['change', 'blur']}]">
                      <el-input v-model="row.label" :rows="1" type="textarea" placeholder="请输入主题" show-word-list :maxlength="200"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="">
                  <template #header>
                    {{child.questionTitle.score}}
                    <el-button type="text" circle size="mini" icon="el-icon-edit" @click="modify('score', child.questionTitle.score, child)"></el-button>
                  </template>
                  <template slot-scope="{row, $index}">
                    <el-form-item :prop="`list.${idx}.questionList.${index}.questionList.${$index}.type`" :rules="[{required: true, message: '请选择类型', trigger: ['change', 'blur']}]">
                      <el-select v-model="row.type" clearable>
                        <el-option value="1" label="单选框（1~5分）"></el-option>
                        <el-option value="3" label="单选框（0~5分）"></el-option>
                        <el-option value="2" label="多行文本框"></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column>
                  <template #header>
                    {{child.questionTitle.remark}}
                    <el-button type="text" circle size="mini" icon="el-icon-edit" @click="modify('remark', child.questionTitle.remark, child)"></el-button>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                  <template slot-scope="{$index}">
                    <el-button v-if="$index > 0" type="text" @click="delQue(child.questionList, $index)">删除</el-button>
                    <el-button type="text" @click="add2(child.questionList, $index)">添加主题</el-button>
                  </template>
                </el-table-column>
              </el-table>

              <br v-if="child.type == 1" />
              <br v-if="child.type == 1" />
              <el-form-item v-if="child.type == 1" label="备注：" :prop="`list.${idx}.questionList.${index}.remark`">
                <el-input type="textarea" v-model="child.remark" :rows="2" style="width:500px;" show-word-list :maxlength="200"></el-input>
              </el-form-item>
              <el-divider></el-divider>
            </div>

          </el-tab-pane>
        </el-tabs>
      </el-form>

      <el-button @click="save">保存</el-button>
    </el-card>
  </div>
</template>

<script>
import request from "@/utils/request";

export default {
  name: 'addFeedback',

  data() {
    return {
      activeName: '',
      list: [],
      id: '',
      feedbackClassifyOptions: [],
      form: {
        title: '',
        description: '',
        type: ''
      },
      default: {
        label: '',
        questionList: [],
        type: '0',
        question: '',
        remark: '',
      },
      question: {
        question: '',
        type: '',
        value: '',
        comment: '',
      },

      childQuestion: {
        subtitle: '',
        remark: '',
        type: '',
        questionTitle: {
          theme: '主题',
          score: '认可情况，请在对应的框内勾选✔。如果答案是1-2分的，请说明具体原因。（5:非常满意1:非常不满意）',
          remark: '具体原因（评分为1-2分时为必填）',
        },
        questionList: [{
          question: '',
          type: '',
          value: '',
          comment: '',
        }],
        circulation: false,
        cycleType: '',
      },

      cycleType: [{
        dictLabel: '评审员',
        dictValue: 'reviewer'
      },
      {
        dictLabel: '评审学员',
        dictValue: 'student'
      }],

      rules: {
        title: [{
          required: true,
          message: '请输入反馈问卷名称',
          trigger: ['change', 'blur']
        }],
        type: [{
          required: true,
          message: '请选择反馈问卷类型',
          trigger: ['change', 'blur']
        }],
        description: [{
          required: true,
          message: '请输入反馈问卷描述',
          trigger: ['change', 'blur']
        }],
      },

      theme: {},
      titleIdx: 1,
    };
  },

  components: {},

  computed: {
    getTitle() {
      return (title, listId, childId) => {
        return this.theme[listId][childId]
      }
    }
  },

  mounted() {
    this.initDicts();
    if (this.$route.path.includes('/toAddFeedback/')) {
      this.id = this.$route.path.split('/').pop() - 0;
      this.getPaperDetail();
    }
    // if (this.$route.query && this.$route.query.id) {
    //   this.id = this.$route.query.id
    //   this.getPaperDetail();
    // }
  },

  methods: {

    initDicts() {
      this.getDicts("feedback_classify").then((response) => {
        this.feedbackClassifyOptions = response.data;
      });
    },

    getPaperDetail() {
      request({
        url: "/system/questionnaire/" + this.id,
        method: "get",
      }).then((res) => {
        let { data = [], description, title, type, id } = res.data
        this.list = data.map((que, index) => {
          if (index === 0) {
            this.activeName = `title${index + 1}`
          }
          return {
            ...que,
            questionList: JSON.parse(que.questionOptions || '[]'),
            activeName: `title${index + 1}`,
            activeTitle: `题目${index + 1}`,
          }
        });
        this.form = {
          description,
          title,
          type: '' + type,
          id
        }
        this.titleIdx = this.list.length + 1;
      });
    },

    add() {
      let data = {
        activeName: `title${this.titleIdx}`,
        activeTitle: `题目${this.titleIdx}`,
        ...JSON.parse(JSON.stringify(this.default))
      }
      this.list.push(data)

      this.titleIdx++;

      this.$nextTick(() => {
        this.activeName = data.activeName
      })
    },

    addQuestion(item, idx) {
      item.questionList.push(JSON.parse(JSON.stringify(this.childQuestion)))
    },

    removeTab(targetName) {
      let tab = this.list.find(({ activeName }) => activeName == targetName);
      if (tab) {
        let index = this.list.indexOf(tab);
        if (index != -1) {
          this.list.splice(index, 1)
        }
        if (this.list.length === 0) { return; }
        if (this.list[index]) {
          this.activeName = this.list[index].activeName
        } else {
          this.activeName = this.list[index - 1].activeName
        }
      }
    },

    add2(list, index) {
      list.splice(index + 1, 0, { ...this.question })
      // list.push({ ...this.question })
    },

    delQue(list, index) {
      if (list[index]) {
        list.splice(index, 1);
      }
    },

    delChildQ(list, index) {
      if (list[index]) {
        list.splice(index, 1);
      }
    },

    modify(label, origin, data) {
      let title = ''
      if (label == 'theme') {
        title = '主题'
      } else if (label == 'score') {
        title = '打分'
      } else if (label == 'remark') {
        title = '描述'
      }
      this.$prompt(`请输入${title}`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /[\S]+/,
        inputErrorMessage: `请输入${title}`,
        inputValue: origin,
        inputType: 'textarea',
        closeOnPressEscape: false,
        closeOnClickModal: false,
      }).then(({ value }) => {
        this.$set(data.questionTitle, label, value)
      }).catch(() => {
        this.$set(data.questionTitle, label, origin)
      })
    },

    async save() {
      try {
        await this.$refs.form.validate();
        this.$refs.feedForm.validate((flag, error) => {
          if (!flag) {
            // console.log(error);
            if (error) {
              let keys = Object.keys(error);
              if (keys.length) {
                let key = keys[0];
                let values = key.split('.');
                this.activeName = `title${(values[1] - 0) + 1}`
              }
            }
            return;
          } else {
            let notQue = null;
            this.list.forEach(data => {
              if (data.type == 1 && (!data.questionList || data.questionList.length == 0)) {
                notQue = data;
              }
            })

            if (notQue) {
              return this.$message({
                type: 'warning',
                message: `${notQue.activeTitle}尚未添加具体主题`
              })
            }

            let data = {
              ...this.form,
              data: this.list.map(data => {
                return {
                  ...data,
                  questionOptions: JSON.stringify(data.questionList)
                }
              }),
            }

            request({
              url: "/system/questionnaire",
              method: "post",
              data,
            }).then(() => {
              this.back();
            });
          }
        })
      } catch (error) {
        // console.log(error);
      }
    },

    back() {
      this.$store.dispatch("tagsView/delView", this.$route) &&
        this.$router.push("/questionnaireManage/questionnaireList");
    },
  }
}

</script>

<style lang='scss' scoped>
.add-feedback {
  ::v-deep .el-table {
    .el-form-item {
      margin-bottom: 0;
      width: 100%;
      .el-form-item__content {
        width: 100% !important;
      }
      .el-form-item__error {
        position: relative;
      }
    }
  }
}
</style>
