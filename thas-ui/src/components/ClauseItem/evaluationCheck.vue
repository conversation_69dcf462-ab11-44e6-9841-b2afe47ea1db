<!--  -->
<template>
  <!-- 审查评审报告  准确性确认  验证评审员验证 -->
  <div class="evaluation-check">
    <el-divider content-position="left">{{ reportTitle }}</el-divider>
    <el-form ref="form" :model="formData" :rules="rules" label-width="110px">
      <el-form-item :label="`${reviewResult}：`" :class="{ readonly: readOnly }" prop="autResult">
        <el-select v-if="!readOnly" clearable filterable v-model.trim="formData.autResult" :placeholder="`请选择${this.reviewResult}`">
          <el-option v-for="(item, index) in agreeOrNotOptions" :key="index" :value="item.dictValue" :label="item.dictLabel"></el-option>
        </el-select>
        <span v-else> {{ getResultTitle(formData.autResult) }}</span>
      </el-form-item>
      <el-form-item :label="`${reviewDesc}：`" :class="{ readonly: readOnly }" prop="autDesc">
        <el-input v-model.trim="formData.autDesc" v-if="!readOnly" type="textarea" :rows="4" :maxlength="50"></el-input>
        <span v-else> {{ formData.autDesc }}</span>
      </el-form-item>
      <el-form-item v-if="!readOnly">
        <el-button type="primary" @click="submit" v-loading="submitLoading" :disabled='submitLoading'>提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "EvaluationCheck",
  components: {},
  props: {
    reportTitle: {
      type: String,
      default: () => "",
      required: true,
    },
    label: {
      type: String,
      default: () => "",
    },
    status: {
      type: [String, Number],
      default: () => false,
    },
    saAud: {
      type: Object,
      default: () => { },
    },
    clause: {
      type: Object,
      default: () => { },
    },
    type: {
      type: [Number, String],
      default: () => -1,
    },
  },
  watch: {
    saAud: {
      immediate: true,
      deep: true,
      handler() {
        const { autResult = null, autDesc = null } = this.saAud || {};
        this.formData = {
          autResult,
          autDesc,
        };
        this.submitLoading = false;
      },
    },
  },
  data() {
    return {
      formData: {
        autResult: "",
        autDesc: "",
      },
      agreeOrNotOptions: [],
      submitLoading: false,
    };
  },

  computed: {
    desc() {
      return this.type == this.AutSaAudSubmitTypeEnum.SENIOR_REVIEWER_REVIEW ||
        this.status == this.AutSaAudStatusEnum.WAIT_SENIOR_REVIEWER_REVIEW ||
        this.status == this.AutSaAudStatusEnum.SENIOR_REVIEWER_REVIEW_PROC
        ? this.formData.autResult != 1 ? "疑问" : '描述'
        : "描述";
    },
    rules() {

      return {
        autResult: [
          {
            required: true,
            message: `请选择${this.label}结果`,
            trigger: "change",
          },
        ],
        autDesc: [
          {
            required: this.formData.autResult != 1,
            message: `请输入${this.desc}`,
            trigger: ["change", "blur"],
          },
        ],
      };
    },
    isHospital() {
      // 医疗机构
      return (
        this.$store.getters.roles.includes("hospital") || this.showOnlyHospital
      );
    },
    isAssessor() {
      // 评审员
      return (
        this.$store.getters.roles.includes("assessor") ||
        this.$store.getters.roles.includes("assessor-leader")
      );
    },
    isInspector() {
      // 审查员
      return (
        this.$store.getters.roles.includes("inspector") ||
        this.$store.getters.roles.includes("inspector-leader")
      );
    },
    isSeniorAssessor() {
      return this.$store.getters.roles.includes("senior-assessor");
    },
    saAudTypeMatch() {
      if (this.hasSaAud) {
        return this.type == this.saAud.type;
      }
      return false;
    },
    hasSaAud() {
      return this.saAud && Object.keys(this.saAud).length != 0;
    },
    readOnly() {
      let statues1 = [
        this.AutSaAudStatusEnum.WAIT_REVIEW_REVIEW_REPORT,
        this.AutSaAudStatusEnum.REVIEW_REVIEW_REPORT_PROC,
      ],
        statues2 = [
          this.AutSaAudStatusEnum.WAIT_FACTUAL_ACCURACY_REVIEW,
          this.AutSaAudStatusEnum.FACTUAL_ACCURACY_REVIEW_PROC,
        ],
        statues3 = [
          this.AutSaAudStatusEnum.WAIT_SENIOR_REVIEWER_REVIEW,
          this.AutSaAudStatusEnum.SENIOR_REVIEWER_REVIEW_PROC,
        ];
      if (this.isHospital && statues2.includes(Number(this.status)))
        return false || (this.hasSaAud && this.saAudTypeMatch);
      else if (this.isInspector && statues1.includes(Number(this.status)))
        return false || (this.hasSaAud && this.saAudTypeMatch);
      else if (this.isSeniorAssessor && statues3.includes(Number(this.status)))
        return false || (this.hasSaAud && this.saAudTypeMatch);
      else return true;
    },
    examiner() {
      return `${this.label}员`;
    },
    reviewResult() {
      return `${this.label}结果`;
    },
    reviewDesc() {
      return `${this.label}${this.desc}`;
    },
    getResultTitle() {
      return (value) => {
        return (
          this.agreeOrNotOptions.find((option) => option.dictValue == value) ||
          {}
        ).dictLabel;
      };
    },
  },

  created() {
  },

  mounted() {
    this.initDicts();
  },

  methods: {
    initDicts() {
      this.getDicts("agree_or_not").then((res) => {
        this.agreeOrNotOptions = res.data;
      });
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.submitLoading = true;
        let type = 100,
          status = Number(this.status);

        if (
          status == this.AutSaAudStatusEnum.WAIT_REVIEW_REVIEW_REPORT ||
          status == this.AutSaAudStatusEnum.REVIEW_REVIEW_REPORT_PROC
        ) {
          type = this.AutSaAudSubmitTypeEnum.REVIEW_REVIEW_REPORT;
        } else if (
          status == this.AutSaAudStatusEnum.WAIT_FACTUAL_ACCURACY_REVIEW ||
          status == this.AutSaAudStatusEnum.FACTUAL_ACCURACY_REVIEW_PROC
        ) {
          type = this.AutSaAudSubmitTypeEnum.FACTUAL_ACCURACY_REVIEW;
        } else if (
          status == this.AutSaAudStatusEnum.WAIT_SENIOR_REVIEWER_REVIEW ||
          status == this.AutSaAudStatusEnum.SENIOR_REVIEWER_REVIEW_PROC
        ) {
          type = this.AutSaAudSubmitTypeEnum.SENIOR_REVIEWER_REVIEW;
        }

        let clauseId;
        if (this.saAud) {
          clauseId = this.saAud.clauseId;
        } else {
          clauseId = this.clause.clauseId;
        }

        this.$emit("onComplete", null, this.formData, clauseId, type);

        setTimeout(() => {
          this.submitLoading = false;
        }, 5000);
      });
    },
  },
};
</script>
<style lang='scss' scoped>
.evaluation-check {
  ::v-deep .el-divider {
    margin-bottom: 20px;
    .el-divider__text {
      background-color: #fbfbfb;
    }
  }
  ::v-deep .el-form-item {
    margin-bottom: 24px;
    .el-form-item__content {
      overflow: visible;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
