<template>
  <el-card v-loading="loading" class="system-notification">
    <el-row :gutter="24" style="margin-top: 24px;">
      <el-col :span="24">
        <el-table :data="dataSource" border>
          <el-table-column align="center" label="序号" type="index" :index="indexMethod"></el-table-column>
          <el-table-column align="center" label="消息内容" prop="content">
            <template slot-scope="scope">
              <div @click="toDetail(scope.row)" style="cursor: pointer;" :style="{ 'font-weight': scope.row.isRead === '0' ? 'bold' : 'normal' }">{{ scope.row.content|| "暂无数据" }}</div>
            </template>
          </el-table-column>
          <!-- <el-table-column align="center" label="来源" prop="source"></el-table-column> -->
          <el-table-column align="center" label="发送时间" prop="createTime"></el-table-column>
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="del(scope.row)">删除</el-button>
              <el-button v-if="scope.row.isRead === '0'" type="text" @click="read(scope.row)">标为已读</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <el-dialog title="消息详情" :visible.sync="dialogVisible" width="40%" :close-on-click-modal="false">
      <span>{{ noticeContent || "暂无数据" }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">返回</el-button>
      </span>
    </el-dialog>
  </el-card>
</template>

<script>
import request from '@/utils/request'
export default {
  name: "SystemNotification",
  data() {
    return {
      dataSource: [],
      total: 0,
      dialogVisible: false,
      noticeContent: '',
      loading: false
    }
  },
  created() {
    this.query()
  },
  methods: {
    query() {
      this.loading = true
      request({
        url: '/system/messageReceiveRecord/list',
        method: "get",
        params: { userId: this.$store.state.user.userId }
      }).then((res) => {
        this.loading = false
        if (res.code != 200) return
        this.dataSource = res.rows
        this.total = res.total
      }).catch((error) => {
        // console.log(error)
        this.loading = false
      })
    },
    del(data) {
      // console.log('删除', data)
      this.$confirm('此操作将永久删除该消息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        request({
          url: '/system/messageReceiveRecord/' + data.id,
          method: "delete"
        }).then((res) => {
          if (res.code != 200) return
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.query()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    read(data) {
      request({
        url: '/system/messageReceiveRecord',
        method: "put",
        data: { id: data.id, isRead: 1 }
      }).then((res) => {
        if (res.code != 200) return
        this.query()
      })
    },
    toDetail(data) {
      this.noticeContent = data.content
      this.dialogVisible = true
      this.read(data)
    },
    indexMethod(index) {
      return index + 1;
    }
  }
}
</script>

<style lang='scss' scoped>
.system-notification {
  padding: 24px;
  margin-bottom: 10px;
  ::v-deep .el-dialog {
    .el-dialog__header {
      background-color: #3c81f5;
      padding-top: 10px;
      .el-dialog__title {
        color: white;
      }
      .el-dialog__headerbtn {
        top: 15px;
        .el-dialog__close {
          color: white;
        }
      }
    }
    .el-dialog__body {
      padding-bottom: 0;
      & > span {
        color: black;
      }
    }
  }
}
</style>