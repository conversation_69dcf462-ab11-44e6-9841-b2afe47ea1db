<template>
  <div class="work-experience">
    <el-form ref="elform" :model="formData">
      <el-table border :data="formData.list">
        <el-table-column label="时间起（年月）">
          <template slot-scope="scope">
            <el-form-item :prop="`list.${scope.$index}.startTime`" :rules="rules.startTime" style="display:inline-block;">
              <el-date-picker :key="scope.$index.endTime" format="yyyy-MM" style="width: 98%;" type="month"
                value-format="yyyy-MM" v-model.trim="scope.row.startTime" placeholder="开始日期"
                :picker-options="pickerOptions(scope.row, 1)"></el-date-picker>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="时间止（年月）">
          <template slot-scope="scope">
            <el-form-item :prop="`list.${scope.$index}.endTime`" :rules="rules.endTime" style="display:inline-block;">
              <el-date-picker :key="scope.$index.startTime" :class="{ hitherto: scope.row.hitherto }" type="month"
                format="yyyy-MM" style="width: 98%;" value-format="yyyy-MM" v-model.trim="scope.row.endTime"
                :placeholder="scope.row.hitherto ? '至今' : '结束日期'" :picker-options="pickerOptions(scope.row, 2)"
                @change="(date) => dateChange(scope.row, date, 2)"></el-date-picker>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="工作单位">
          <template slot-scope="scope">
            <el-form-item :prop="`list.${scope.$index}.company`" :rules="rules.company">
              <el-input v-model.trim="scope.row.company" style="width: 98%" placeholder="请输入工作单位"
                :maxlength="50"></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="职务">
          <template slot-scope="scope">
            <el-form-item :prop="`list.${scope.$index}.companyPost`" :rules="rules.companyPost">
              <el-input v-model.trim="scope.row.companyPost" placeholder="请输入职务" style="width: 98%"
                :maxlength="50"></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <div style="text-align:center;">
              <el-button type="text" v-if="formData.list.length > 1" @click="delWorkExp(scope.$index)">删除</el-button>
              <el-button type="text" v-if="lastOne(scope.$index)" @click="addWorkExp">新增</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>
</template>

<script>
const defaultExp = {
  startTime: "",
  endTime: "",
  companyPost: "",
  company: "",
  hitherto: false,
  hitTime: ''
};

export default {
  name: "WorkExperience",
  props: {
    info: {
      type: Object,
      default: () => { },
    },
    experiences: {
      type: Array,
      default: () => [],
    }
  },
  watch: {
    info: {
      immediate: true,
      deep: true,
      handler() {
        if (this.info && Object.keys(this.info).length) {
          for (const key in this.info) {
            if (Object.hasOwnProperty.call(this.info, key)) {
              this.$set(this.formData, key, this.info[key]);
            }
          }
        }
      },
    },
    experiences: {
      immediate: true,
      deep: true,
      handler() {
        let list = this.experiences.length ? this.experiences : [{...defaultExp}];
        list.forEach(work => {
          if (work.endTime == '9999-12') {
            this.$set(work, 'hitherto', true)
            this.$set(work, 'hitTime', '9999-12')
            this.$set(work, 'endTime', '')
          }
        })
        this.$set(this.formData, 'list', list)
      }
    }
  },
  data() {
    return {
      formData: {
        list: [
          {
            ...defaultExp,
          },
        ],
      },
      rules: {
        startTime: [
          {
            required: true,
            message: '请选择开始日期',
            trigger: ["change", "blur"],
          },
        ],
        endTime: [
          {
            required: true,
            validator: (rule, value, callback) => {
              this.$nextTick(() => {
                let index = rule.field.split('.')[1]
                let row = this.formData.list[index]
                if (row.hitherto) {
                  callback();
                } else {
                  if (value) {
                    callback()
                  } else {
                    callback("请选择结束日期")
                  }
                }
              })
            },
            trigger: ["change", "blur"],
          },
        ],

        companyPost: [
          {
            required: true,
            message: "请输入职务",
            trigger: ["change", "blur"],
          },
        ],
        company: [
          {
            required: true,
            message: "请输入工作单位",
            trigger: ["change", "blur"],
          },
        ],
      },
    };
  },

  components: {},

  computed: {
    lastOne() {
      return (index) => this.formData.list.length === index + 1;
    },

    pickerOptions() {
      return (row, type) => {
        let that = this
        return {
          shortcuts: type == 2 ? [
            {
              text: '至今',
              onClick(picker) {
                that.$set(row, 'hitherto', true)
                that.$set(row, 'hitTime', '9999-12')
                picker.$emit('pick', '');
              }
            },
          ] : null,

          disabledDate(time) {
            // 禁用条件： 日历上的时间要大于档期时间
            let basicDisabled = time.getTime() > Date.now()
            if (type == 1) {
              if (row.endTime) {
                // 禁用条件: 日历时间要晚于【结束日期】
                return time.getTime() > new Date(row.endTime).getTime()
              }
              return basicDisabled
            } else {
              if (row.startTime) {
                return basicDisabled || time.getTime() < (new Date(row.startTime).getTime() - (24 * 60 * 60 * 1000))
              } else {
                return basicDisabled
              }
            }
          }
        }
      }
    }
  },

  mounted() {
    // // console.log(this.pickerOptions({}, 1));
    // // console.log(this.pickerOptions({}, 2));
  },

  methods: {
    async getValue() {
      try {
        const re = await this.$refs["elform"].validate();
        if (re) {
          return this.formData.list.map((li) => {
            const { hitTime, hitherto, startTime, endTime, ...data } = li;
            return {
              startTime,
              endTime: hitherto ? hitTime : endTime,
              ...data,
            };
          });
        }
        return false;
      } catch (error) {
        // console.log(error);
        return false;
      }
    },

    getDraft() {
      return this.formData.list.map((li) => {
        const { time, startTime, endTime, ...data } = li;
        return {
          startTime: time[0],
          endTime: time[1],
          ...data,
        };
      });
    },

    addWorkExp() {
      this.formData.list.push({
        ...defaultExp,
      });
    },

    delWorkExp(index) {
      this.formData.list.length > 1 && this.formData.list.splice(index, 1);
    },

    dateChange(row, date, type) {
      if (type == 2) {
        if (date) {
          row.hitherto = false;
          this.hitTime = ''
        }
      }
    }
  },
};
</script>

<style lang='scss' scoped>
.work-experience {
  padding: 24px;
}

::v-deep .el-form {
  .el-form-item {
    margin-bottom: 0;
  }

  .el-form-item__error {
    position: unset;
  }
}

::v-deep .hitherto.el-date-editor input.el-input__inner {
  &::-webkit-input-placeholder {
    /* Edge */
    color: #606266;
  }

  &:-ms-input-placeholder {
    /* Internet Explorer 10-11 */
    color: #606266;
  }

  &::placeholder {
    color: #606266;
  }
}
</style>