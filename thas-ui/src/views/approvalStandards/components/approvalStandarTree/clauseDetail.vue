<template>
  <div class="clause-detail">
    <div class="clause-detail-item" v-for="(clause, index) in clauseVoList" :key="index" :name="index">
      <span class="placeholder"></span>
      <span class="clauseId" style="visibility: hidden;">款{{ clause.clauseId }}：</span>
      <span class="clause-node-value">
        {{ clause.isStar == 1 ? "★" : "" }}{{ clause.clauseNo }}
      </span>
      <div>
        <span class="clause-detail-value"> {{ clause.clause }} </span>
        <div class="clause-detail-detail-action">
          <el-popover placement="top" width="500" trigger="click" popper-class="clausePopperClass">
            <div class="scroll-div" :class="{showScrollbar:showScroll}" @mouseover="mouseover" @mouseout="mouseout">
              <div>
                <div>
                  <h4>{{clause.detailRulesTitle}}</h4>
                  <pre>{{clause.detailRulesDesc || '暂无细则'}}</pre>
                </div>

                <div>
                  <span v-if="clause.evidenceMaterial">达标佐证材料</span>
                  <pre style="margin-top:5px;">{{clause.evidenceMaterial }}</pre>
                </div>

                <div>
                  <div v-for="(standard, index) in clause.cstEvaluationCriterionList" :key="index" v-show="standard.evaluate || standard.standard">
                    <h4>{{standard.evaluate}}</h4>
                    <pre>{{ standard.standard }}</pre>
                  </div>
                </div>
              </div>
            </div>
            <el-button type="text" slot="reference">细则</el-button>
          </el-popover>

          <el-popover placement="top" width="500" popper-class="clausePopperClass" trigger="click" :content="clause.regulationFile">
            <div class="scroll-div" :class="{showScrollbar:showScroll}" @mouseover="mouseover" @mouseout="mouseout">
              <pre>{{ clause.regulationFile || "暂无相关政策文件" }}</pre>
            </div>
            <el-button type="text" slot="reference">相关政策文件</el-button>
          </el-popover>

          <el-popover placement="top" trigger="click" popper-class="clausePopperClass" width="600px">
            <div class="scroll-div" :class="{showScrollbar:showScroll}" @mouseover="mouseover" @mouseout="mouseout">
              <pre>{{ clause.internationalReference || "暂无国际参考文献" }}</pre>
            </div>
            <el-button type="text" slot="reference">国际参考文献</el-button>
          </el-popover>

          <el-button type="text" style="float: right; margin-right: 15px" @click="toClauseDetail(clause)">款项详情<i class="el-icon-arrow-right"></i>
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ClauseDetail",
  props: {
    clauseVoList: {
      type: Array,
      default: () => [],
    },
    render: {
      type: Boolean,
      default: () => false,
    },
  },
  watch: {
    render: {
      deep: true,
      immediate: true,
      handler() {
        this.render &&
          this.clauseVoList &&
          this.clauseVoList.length &&
          this.loadDetail();
      },
    },
  },
  data() {
    return {
      showScroll: false,
    }
  },
  methods: {
    toClauseDetail({ clauseId, versionId }) {
      this.$router.push({
        name: "ClauseParticulars",
        params: {
          clauseId,
          versionId,
          canModify: true
        },
      });
    },

    async loadDetail() {
      let clauseList = this.clauseVoList;
      let articleId = clauseList[0].articleId,
        versionId = clauseList[0].versionId;
      const data = await this.getClausesDetail(versionId, articleId);
    },
    mouseover(event) {
      this.$nextTick(() => {
        if (event.currentTarget && event.currentTarget.children && event.currentTarget.children[0]) {
          let child = event.currentTarget.children[0];
          if (child.clientHeight > 300) {
            this.showScroll = true;
          }
        }
      })
    },
    mouseout() {
      this.showScroll = false;
    }
  },
};
</script>

<style lang="scss" scoped>
.clause-detail:last-child {
  border-bottom: 0;
}
.clause-detail-item {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  padding-top: 15px;
  & > div {
    flex: 1;
    flex-grow: 1;
  }
  .placeholder {
    width: 52px;
  }
  .clauseId {
    width: 70px;
    text-align: left;
  }
}
.clause-detail-item:last-child {
  border-bottom: 0;
}
.clause-detail {
  border-bottom: 1px solid #e0e0e0;
  font-size: 16px;
  .clause-detail-item {
    & > span:first-child {
      color: #888888;
      font-weight: lighter;
    }
    & > span {
      display: block;
    }
  }
  .clause-node-value {
    width: 120px;
  }

  .clause-detail-detail-action {
    ::v-deep .el-button {
      font-size: 14px;
      margin-right: 15px;
    }
  }
}
.scroll-div {
  max-height: 300px;
  width: 100%;
  & > div {
    width: 98%;
  }
  overflow-y: scroll;
  h4 {
    color: black;
  }
  &::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
  }
}
.scroll-div.showScrollbar {
  &::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #535353;
  }
  &::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    background: #ededed;
  }
}
</style>
<style lang="scss">
.clausePopperClass {
  width: 80% !important;

  pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    line-height: 16px;
  }
}
</style>
