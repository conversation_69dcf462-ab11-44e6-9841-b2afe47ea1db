<template>
  <div class="application-authorizer-info">
    <el-divider content-position="left">法人信息</el-divider>
    <el-form ref="legalForm" :model="legalData" :rules="rules" size="small" label-width="123px">
      <el-row :gutter="15">
        <el-col :span="12">
          <el-form-item label="法人姓名" prop="legalPersonName">
            <el-input v-model.trim="legalData.legalPersonName" placeholder="请输入法人姓名" clearable :style="{ width: '100%' }" :maxlength="50"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="法人性别" prop="gender">
            <el-select v-model.trim="legalData.gender" placeholder="请输入法人性别" clearable filterable :style="{ width: '100%' }">
              <el-option v-for="(item, index) in genderOptions" :key="index" :label="item.dictLabel" :value="item.dictValue" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :span="12">
          <el-form-item label="证件类型" prop="certificateType">
            <el-select v-model.trim="legalData.certificateType" placeholder="请选择证件类型" clearable filterable :style="{ width: '100%' }" @change="
                (value) => clearCertificatesNumberByType('certificateNumber')
              ">
              <el-option v-for="(item, index) in certificateTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="证件号" prop="certificateNumber">
            <el-input v-model.trim="legalData.certificateNumber" placeholder="请输入证件号" clearable :style="{ width: '100%' }"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :span="12">
          <el-form-item label="法人职务" prop="legalPersonPost">
            <el-input v-model.trim="legalData.legalPersonPost" placeholder="请输入法人职务" clearable :style="{ width: '100%' }" :maxlength="50"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="法人职称" prop="legalPersonTitle">
            <el-input v-model.trim="legalData.legalPersonTitle" placeholder="请输入法人职称" clearable :style="{ width: '100%' }" :maxlength="50"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :span="12">
          <el-form-item label="法人座机" style="margin-bottom: 0" prop="legalPersonPhone">
            <el-form-item prop="legalPersonAreaCode" :rules="rules.legalPersonAreaCode" style="display: inline-block; width: 90px">
              <el-input v-model.trim="legalData.legalPersonAreaCode" placeholder="座机区号" :style="{ width: '100%' }">
              </el-input>
            </el-form-item>
            -
            <el-form-item prop="legalPersonPhoneNum" :rules="rules.legalPersonPhoneNum" style="display: inline-block; width: calc(100% - 104px)">
              <el-input v-model.trim="legalData.legalPersonPhoneNum" placeholder="请输入法人座机号码" clearable :style="{ width: '100%' }"></el-input>
            </el-form-item>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="法人传真" prop="legalPersonFax">
            <el-input v-model.trim="legalData.legalPersonFax" placeholder="请输入法人传真" clearable :style="{ width: '100%' }"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :span="12">
          <el-form-item label="法人手机" prop="legalPersonMobile">
            <el-input v-model.trim="legalData.legalPersonMobile" placeholder="请输入法人手机" clearable :style="{ width: '100%' }"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="法人邮箱" prop="legalPersonEmail">
            <el-input v-model.trim="legalData.legalPersonEmail" placeholder="请输入法人邮箱" clearable :maxlength="128" :style="{ width: '100%' }"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :span="24">
          <el-form-item label="证件照附件" prop="legalPersonPhoto" required>
            <upload :files="files1" :accept="`.JPG,.JPEG,.PNG`" :data="{ type: '1' }" :fileLimit="2" :action="`${uploadUrl}/binTang/common/uploadFile`" @uploadSuccess="fileAdd" @fileRemove="fileRemove"></upload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider content-position="left">被授权人信息</el-divider>
      <el-row :gutter="15">
        <el-col :span="12">
          <el-form-item label="被授权人姓名" prop="authContactName">
            <el-input v-model.trim="legalData.authContactName" placeholder="请输入被授权人姓名" clearable :style="{ width: '100%' }" :maxlength="50">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="authContactMobile">
            <el-input v-model.trim="legalData.authContactMobile" placeholder="请输入手机号" clearable :style="{ width: '100%' }">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :span="12">
          <el-form-item label="证件类型" prop="certificateType1">
            <el-select v-model.trim="legalData.certificateType1" placeholder="请选择证件类型" clearable filterable :style="{ width: '100%' }" @change="
                (value) => clearCertificatesNumberByType('certificateNumber1')
              ">
              <el-option v-for="(item, index) in certificateTypeOptions" :key="index" :label="item.dictLabel" :value="item.dictValue" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="证件号" prop="certificateNumber1">
            <el-input v-model.trim="legalData.certificateNumber1" placeholder="请输入证件号" clearable :style="{ width: '100%' }">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :span="12">
          <el-form-item label="被授权人邮箱" prop="authContactEmail">
            <el-input v-model.trim="legalData.authContactEmail" placeholder="请输入被授权人邮箱" clearable :maxlength="128" :style="{ width: '100%' }">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系地址" prop="contactAddress">
            <el-input v-model.trim="legalData.contactAddress" placeholder="请输入联系地址" clearable :style="{ width: '100%' }" :maxlength="50">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :span="24">
          <el-form-item label="证件照附件" prop="authContactPhoto" required>
            <upload :accept="`.JPG,.JPEG,.PNG`" :files="files2" :action="`${uploadUrl}/binTang/common/uploadFile`" :data="{ type: '1' }" :fileLimit="2" @fileRemove="fileRemove1" @uploadSuccess="fileAdd1"></upload>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="医院授权书附件" prop="hospitalCertificateAuth" required>
            <upload :action="`${uploadUrl}/binTang/common/uploadFile`" :files="files3" :data="{ type: '1' }" :single="true" @uploadSuccess="fileAdd2"></upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
import certificatesVue from "./certificates.vue";
export default {
  components: {},
  props: {
    initData: {
      type: [Object, null],
      default: () => null
    }
  },
  data() {
    return {
      legalData: {
        authContactName: "",
        authContactMobile: "",
        certificateType: "",
        certificateNumber: "",
        authContactEmail: "",
        contactAddress: "",
        hospitalCertificateAuth: "",
        authContactPhoto: "",
        //
        legalPersonName: "",
        gender: "",
        certificateType1: "",
        certificateNumber1: "",
        legalPersonPost: "",
        legalPersonTitle: "",
        legalPersonPhone: "",
        legalPersonPhoneNum: "",
        legalPersonAreaCode: "",
        legalPersonFax: "",
        legalPersonMobile: "",
        legalPersonEmail: "",
        legalPersonPhoto: "",
      },
      authFields: [
        "authContactName",
        "authContactMobile",
        "certificateType1",
        "certificateNumber1",
        "authContactEmail",
        "contactAddress",
        "hospitalCertificateAuth",
        "authContactPhoto",
      ],
      legalFields: [
        "gender",
        "certificateType",
        "certificateNumber",
        "legalPersonName",
        "legalPersonPost",
        "legalPersonTitle",
        "legalPersonPhone",
        "legalPersonPhoneNum",
        "legalPersonAreaCode",
        "legalPersonFax",
        "legalPersonMobile",
        "legalPersonEmail",
        "legalPersonPhoto",
      ],
      rules: {
        authContactName: [
          {
            required: true,
            message: "请输入被授权人姓名",
            trigger: "blur",
          },
        ],
        authContactMobile: [
          {
            required: true,
            // message: "请输入手机号码",
            validator: (rule, value, callback) => this.mobileValid(rule, value, callback),
            trigger: ["blur", "change"],
          },
        ],
        certificateType: [
          {
            required: true,
            message: "请选择证件类型",
            trigger: "change",
          },
        ],
        certificateType1: [
          {
            required: true,
            message: "请选择证件类型",
            trigger: "change",
          },
        ],
        certificateNumber: [
          {
            validator: (rule, value, callback) =>
              this.certificatesValid(1, value, callback),
          },
        ],
        authContactEmail: [
          {
            required: true,
            // message: "请输入联系邮箱",
            validator: (rule, value, callback) => this.emailValid(rule, value, callback),
            trigger: ["blur", "change"],
          },
        ],
        contactAddress: [
          {
            required: true,
            message: "请输入联系地址",
            trigger: "blur",
          },
        ],
        legalPersonName: [
          {
            required: true,
            message: "请输入法人姓名",
            trigger: "blur",
          },
        ],
        gender: [
          {
            required: true,
            message: "请选择法人性别",
            trigger: "change",
          },
        ],
        certificateType: [
          {
            required: true,
            message: "请选择证件类型",
            trigger: "change",
          },
        ],
        certificateNumber1: [
          {
            validator: (rule, value, callback) =>
              this.certificatesValid(2, value, callback),
          },
        ],
        legalPersonPost: [
          {
            required: true,
            message: "请输入法人职务",
            trigger: "blur",
          },
        ],
        legalPersonTitle: [
          {
            required: true,
            message: "请输入法人职称",
            trigger: "blur",
          },
        ],
        legalPersonPhoneNum: [
          {
            required: true,
            // message: "请输入手机号码",
            validator: (rule, value, callback) => this.phoneValid(rule, value, callback),
            trigger: ["blur", "change"],
          },
        ],
        legalPersonAreaCode: [
          {
            required: true,
            // message: "请输入手机号码",
            validator: (rule, value, callback) => this.areaCodeValid(rule, value, callback),
            trigger: ["blur", "change"],
          },
        ],
        legalPersonPhone: [
          {
            required: true,
          },
        ],
        legalPersonFax: [
          {
            required: true,
            // message: "请输入手机号码",
            validator: (rule, value, callback) => this.faxValid(rule, value, callback),
            trigger: ["blur", "change"],
          },
        ],
        legalPersonMobile: [
          {
            required: true,
            // message: "请输入手机号码",
            validator: (rule, value, callback) => this.mobileValid(rule, value, callback),
            trigger: ["blur", "change"],
          },
        ],
        legalPersonEmail: [
          {
            required: true,
            // message: "请输入联系邮箱",
            validator: (rule, value, callback) => this.emailValid(rule, value, callback),
            trigger: ["blur", "change"],
          },
        ],
        legalPersonPhoto: [
          {
            required: true,
            message: "请上传证件照附件",
            trigger: "blur",
          },
        ],
        hospitalCertificateAuth: [
          {
            required: true,
            message: "请上传医院授权书附件",
            trigger: "blur",
          },
        ],
        authContactPhoto: [
          {
            required: true,
            message: "请上传证件照附件",
            trigger: "blur",
          },
        ],
      },
      hospitalCertificateAuthAction:
        "https://jsonplaceholder.typicode.com/posts/",
      legalPersonPhotoAction: "https://jsonplaceholder.typicode.com/posts/",
      authContactPhotoAction: "https://jsonplaceholder.typicode.com/posts/",
      certificateTypeOptions: [],
      genderOptions: [],
      files1: [],
      files2: [],
      files3: [],
      regMap: {},
    };
  },
  computed: {
    uploadUrl() {
      return localStorage.baseUrl || process.env.VUE_APP_BASE_API;
    },
  },
  watch: {},
  created() { },
  mounted() {
    this.initDicts();
    if (!this.initData) return;
    let { hospitalAuthContact = {}, hospitalLegalPerson = {}, fileDetails = {} } = this.initData || {}
    this.legalFields.forEach(field => {
      if (field == 'legalPersonAreaCode' || field == 'legalPersonPhoneNum') {
        let phones = hospitalLegalPerson['legalPersonPhone'].split('-')
        this.legalData['legalPersonAreaCode'] = phones[0]
        this.legalData['legalPersonPhoneNum'] = phones[1]
      } else if (field == 'legalPersonPhoto') {
        this.legalData[field] = `${hospitalLegalPerson[field]}`
        this.files1 = fileDetails[hospitalLegalPerson['legalPersonPhoto']]
      } else if (!isNaN(hospitalLegalPerson[field]) && (hospitalLegalPerson[field] || hospitalLegalPerson[field] === 0)) {
        this.legalData[field] = `${hospitalLegalPerson[field]}`
      } else {
        this.legalData[field] = hospitalLegalPerson[field]
      }
    })

    this.authFields.forEach(field => {
      if (field == 'certificateType1') {
        this.legalData[field] = `${hospitalAuthContact['certificateType']}`
      } else if (field == 'certificateNumber1') {
        this.legalData[field] = `${hospitalAuthContact['certificateNumber']}`
      } else if (field == 'authContactPhoto') {
        this.legalData[field] = `${hospitalAuthContact[field]}`
        this.files2 = fileDetails[hospitalAuthContact['authContactPhoto']]
      } else if (field == 'hospitalCertificateAuth') {
        this.legalData[field] = `${hospitalAuthContact[field]}`
        this.files3 = fileDetails[hospitalAuthContact['hospitalCertificateAuth']]
      } else if (!isNaN(hospitalAuthContact[field]) && hospitalAuthContact[field]) {
        this.legalData[field] = `${hospitalAuthContact[field]}`
      } else {
        this.legalData[field] = hospitalAuthContact[field]
      }
    })
  },
  methods: {
    initDicts() {
      this.getDicts("sys_user_sex").then((response) => {
        this.genderOptions = response.data;
      });
      this.getDicts("certificate_type").then((response) => {
        this.certificateTypeOptions = response.data;
      });

      this.getBingConfigKey("reg.email").then(response => {
        this.regMap['regEmail'] = response.msg.trim();
      });

      this.getBingConfigKey("reg.mobile").then(response => {
        this.regMap['regMobile'] = response.msg.trim();
      });

      this.getBingConfigKey("reg.areaCode").then(response => {
        this.regMap['regAreaCode'] = response.msg.trim();
      });

      this.getBingConfigKey("reg.phone").then(response => {
        this.regMap['regPhone'] = response.msg.trim();
      });
      this.getBingConfigKey("reg.fax").then(response => {
        this.regMap['regFax'] = response.msg.trim();
      });

      this.getBingConfigKey("reg.idCard").then(response => {
        this.regMap['1'] = response.msg.trim();
      });
      this.getBingConfigKey("reg.passport").then(response => {
        this.regMap['2'] = response.msg.trim();
      });
      this.getBingConfigKey("reg.reentry").then(response => {
        this.regMap['3'] = response.msg.trim();
      });
      this.getBingConfigKey("reg.hkCard").then(response => {
        this.regMap['4'] = response.msg;
      });
      this.getBingConfigKey("reg.moCard").then(response => {
        this.regMap['5'] = response.msg.trim();
      });
      this.getBingConfigKey("reg.twCard").then(response => {
        this.regMap['6'] = response.msg.trim();
      });
    },

    areaCodeValid(rule, value, callback) {
      if (!value) callback('请输入座机区号')
      let regString = this.regMap['regAreaCode'];
      if (!regString) callback();
      let reg = new RegExp(regString);
      if (!reg.test(value)) callback('请输入座机区号')
      callback()
    },

    phoneValid(rule, value, callback) {
      if (!value) callback('请输入座机号码')
      let regString = this.regMap['regPhone'];
      if (!regString) callback();
      let reg = new RegExp(regString);
      if (!reg.test(value)) callback('请输入正确的座机号码')
      callback()
    },

    faxValid(rule, value, callback) {
      if (!value) callback('请输入传真号码')
      let regString = this.regMap['regFax'];
      if (!regString) callback();
      let reg = new RegExp(regString);
      if (!reg.test(value)) callback('请输入正确的传真号码')
      callback()
    },

    emailValid(rule, value, callback) {
      if (!value) callback('请输入邮箱')
      let regString = this.regMap['regEmail'];
      if (!regString) callback();
      let reg = new RegExp(regString);
      if (!reg.test(value)) callback('请输入正确的邮箱')
      callback()
    },

    mobileValid(rule, value, callback) {
      if (!value) callback('请输入手机号')
      let regString = this.regMap['regMobile'];
      if (!regString) callback();
      let reg = new RegExp(regString);
      if (!reg.test(value)) callback('请输入正确的手机号')
      callback()
    },

    async getValue() {
      try {
        this.legalData.legalPersonPhone = this.legalData.legalPersonAreaCode + '-' + this.legalData.legalPersonPhoneNum
        const le = await this.$refs["legalForm"].validate();
        if (le) {
          const legal = {};
          const auth = {};
          for (const key in this.legalData) {
            // 法人
            if (
              Object.hasOwnProperty.call(this.legalData, key) &&
              this.legalFields.includes(key)
            ) {
              legal[key] = this.legalData[key];
            }

            legal.legalPersonPhone = `${legal.legalPersonAreaCode}-${legal.legalPersonPhoneNum}`;

            // 被授权
            if (
              Object.hasOwnProperty.call(this.legalData, key) &&
              this.authFields.includes(key)
            ) {
              if (key.includes("1")) {
                auth[key.slice(0, key.length - 1)] = this.legalData[key];
              } else {
                auth[key] = this.legalData[key];
              }
            }
          }
          return {
            auth,
            legal,
          };
        }
      } catch (error) {
        // console.log(error);
        return null;
      }
    },

    clearCertificatesNumberByType(prop) {
      this.$set(this.legalData, prop, "");
      this.$refs.legalForm.validateField(prop);
    },

    certificatesValid(type, value, callback) {
      let label = type == 1 ? "certificateType" : "certificateType1";
      if (!this.legalData[label]) {
        if (value) return callback();
        return callback('请输入证件号码')
      }
      let regString = this.regMap[this.legalData[label]]
      if (regString) {
        let cer = this.certificateTypeOptions.find(({ dictValue }) => dictValue == this.legalData[label]);
        if (!value) callback('请输入证件号码')
        let reg = new RegExp(regString);
        if (!reg.test(value)) callback('请输入正确的证件号码')
      }
      callback();
    },


    fileAdd(response, file) {
      if (response.code != 200)
        return this.$message.error(response.msg || response.message);
      // this.files1.length = 0;
      file.fileId = response.data.fileId;
      this.files1.push(file);
      this.$set(
        this.legalData,
        "legalPersonPhoto",
        this.files1.map((file) => file.fileId).join(",")
      );
      this.$refs.legalForm.validateField("legalPersonPhoto");
      this.bus.$emit('fileIdUpload', 'legalPersonPhoto',  this.files1.map((file) => file.fileId))

    },
    fileRemove(file, length, index) {
      this.files1.splice(index, 1);
      this.$set(
        this.legalData,
        "legalPersonPhoto",
        this.files1.map((file) => file.fileId).join(",")
      );
      this.$refs.legalForm.validateField("legalPersonPhoto");
    },

    fileAdd1(response, file) {
      if (response.code != 200)
        return this.$message.error(response.msg || response.message);
      file.fileId = response.data.fileId;
      this.files2.push(file);
      this.$set(
        this.legalData,
        "authContactPhoto",
        this.files2.map((file) => file.fileId).join(",")
      );
      this.$refs.legalForm.validateField("authContactPhoto");
      this.bus.$emit('fileIdUpload', 'authContactPhoto',  this.files2.map((file) => file.fileId))
    },
    fileRemove1(file, length, index) {
      this.files2.splice(index, 1);
      this.$set(
        this.legalData,
        "authContactPhoto",
        this.files2.map((file) => file.fileId).join(",")
      );
      this.$refs.legalForm.validateField("authContactPhoto");
    },
    fileAdd2(response, file) {
      if (response.code != 200)
        return this.$message.error(response.msg || response.message);
      this.$set(
        this.legalData,
        "hospitalCertificateAuth",
        response.data.fileId
      );
      file.fileId = response.data.fileId;
      this.files3.length = 0;
      this.files3.push(file);
      this.$refs.legalForm.validateField("hospitalCertificateAuth");
      this.bus.$emit('fileIdUpload', 'hospitalCertificateAuth',  this.files3.map((file) => file.fileId))
    },
    fileRemove2(file, length, index) {
      this.files3.length = 0;
    },
  },
};
</script>
<style>
.el-upload__tip {
  line-height: 1.2;
}
</style>
