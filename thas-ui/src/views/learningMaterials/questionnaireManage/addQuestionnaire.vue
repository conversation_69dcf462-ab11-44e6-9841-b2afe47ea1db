<template>
  <div class="add-questionnaire">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>{{$route.query.id ? "编辑问卷" : "添加问卷"}}</span>
        <!-- <div style="float: right; padding: 3px 0">
          <span>考试总分：</span>
          <span>{{score}}</span>
          <span style="padding-right: 10px; border-right: 2px solid #ccc;">分</span>
          <span style="padding-left: 10px;">试卷题目数：</span>
          <span>{{questionList.length}}</span>
        </div> -->
      </div>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="问卷名称" prop="title">
          <el-input v-model.trim="form.title" placeholder="请输入问卷名称" maxlength="128"></el-input>
        </el-form-item>
        <el-form-item label="问卷类别" prop="type">
          <el-select v-model.trim="form.type" placeholder="请选择问卷类型" clearable filterable>
            <el-option v-for="classifyItem in questionnaireClassifyOptions" :key="classifyItem.dictValue" :value="classifyItem.dictValue" :label="classifyItem.dictLabel"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="问卷描述" prop="description">
          <el-input type="textarea" v-model.trim="form.description" placeholder="请输入问卷描述" maxlength="128"></el-input>
        </el-form-item>
      </el-form>
      <div>
        <div v-for="(item, index) in questionList" :key="index" style="display: flex; justify-content: space-between; border-top: 2px solid #e9eaeb; margin-left: 100px;" @mouseover="hoverIndex = index" @mouseout="hoverIndex = -1">
          <div v-if="item.isEdit">
            <edit-question :question-info="item" :index="index" :isQuestionnaire="true" @add="editSubject" @cancel="cancel"></edit-question>
          </div>
          <div v-else>
            <el-form size="medium" label-width="0px">
              <el-form-item label="">
                <div style="font-size: 16px; font-weight: bold;">
                  <div style="word-break: break-all;">{{item.question}}</div>
                  <!-- <span style="margin-left: 30px; color: #00ae22;">（分值：{{item.score}}分）</span> -->
                </div>
                <el-radio-group v-if="item.questionType == 1">
                  <el-radio v-for="(question, i) in item.singleAnswerOptions" :key="i" :label="question.label"></el-radio>
                </el-radio-group>
                <el-checkbox-group v-else-if="item.questionType == 2" value="null">
                  <el-checkbox v-for="(question, i) in item.multipleAnswerOptions" :key="i" :label="question.label"></el-checkbox>
                </el-checkbox-group>
                <el-input :maxlength="50" v-else-if="item.questionType == 3" readonly placeholder="请输入" style="width: 240px;"></el-input>
                <el-input :maxlength="50" v-else-if="item.questionType == 4" type="textarea" :rows="3" readonly placeholder="请输入" style="width: 500px;"></el-input>
              </el-form-item>
            </el-form>
          </div>
          <div class="option-btns" :class="{'show-btn': index == hoverIndex}">
            <el-button v-show="!item.isEdit" type="text" icon="el-icon-edit" @click="edit(index)">编辑</el-button>
            <el-button type="text" style="color: #f00;" icon="el-icon-delete" @click="del(index)">删除</el-button>
          </div>
        </div>
      </div>
      <edit-question v-show="showAddForm" ref="editQuestionForm" :isQuestionnaire="true" @add="addSubject" @cancel="cancel"></edit-question>
      <el-button v-show="!showAddForm" icon="el-icon-plus" @click="showAddForm = true">添加题目</el-button>
      <div style="margin-top: 50px;">
        <el-button type="primary" @click="save">保存</el-button>
        <el-button @click="back">返回</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import EditQuestion from "@/components/TextQuestion/EditQuestion.vue";
import request from "@/utils/request";
export default {
  name: "AddQuestionnaire",
  components: {
    EditQuestion,
  },
  data() {
    return {
      form: {
        title: "",
        type: null,
        description: "",
      },
      questionnaireClassifyOptions: [],
      rules: {
        title: [{ required: true, message: "请输入问卷名称", trigger: "blur" }],
        type: [
          { required: true, message: "请选择问卷类别", trigger: "change" },
        ],
        description: [
          { required: true, message: "请输入问卷描述", trigger: "blur" },
        ],
      },
      questionList: [],
      showAddForm: false,
      timeout: null,
      hoverIndex: -1,
    };
  },
  computed: {
    score() {
      return this.questionList.reduce((prev, cur) => {
        return prev + parseInt(cur.score);
      }, 0);
    },
  },
  created() {
    this.initDicts();
    if (this.$route.query.id) {
      this.getPaperDetail();
    }
  },
  methods: {
    initDicts() {
      this.getDicts("questionnaire_classify").then((response) => {
        this.questionnaireClassifyOptions = response.data;
      });
    },
    getPaperDetail() {
      request({
        url: "/system/questionnaire/" + this.$route.query.id,
        method: "get",
      }).then((res) => {
        if (res.code != 200) {
          this.$message({
            type: "error",
            message: res.message || "系统错误，请稍后再试。",
          });
        } else {
          this.form.title = res.data.title;
          this.form.type = String(res.data.type);
          this.form.description = res.data.description;
          for (let index = 0; index < res.data.data.length; index++) {
            let item = {
              questionType: res.data.data[index].type,
              question: res.data.data[index].question,
              id: res.data.data[index].id,
              hasRequired: res.data.data[index].hasRequired,
            };
            if (res.data.data[index].type == 1) {
              item.singleAnswerOptions = JSON.parse(
                res.data.data[index].questionOptions
              );
            } else if (res.data.data[index].type == 2) {
              item.multipleAnswerOptions = JSON.parse(
                res.data.data[index].questionOptions
              );
            }
            this.questionList.push(item);
          }
        }
      });
    },
    addSubject(data) {
      if (data) {
        for (let i = 0; i < this.questionList.length; i++) {
          if (this.questionList[i].question === data.question) {
            return this.$message({
              type: 'warning',
              message: '请勿添加重复的题目'
            })
          }
        }
        if (data.questionType == 1) {
          for (
            let index = 0;
            index < data.singleAnswerOptions.length;
            index++
          ) {
            data.singleAnswerOptions[index].isEdit = false;
          }
        } else if (data.questionType == 2) {
          for (
            let index = 0;
            index < data.multipleAnswerOptions.length;
            index++
          ) {
            data.multipleAnswerOptions[index].isEdit = false;
          }
        }
        this.questionList.push(JSON.parse(JSON.stringify(data)));
        this.$refs.editQuestionForm.resetForm()
        this.showAddForm = false;
      }
    },
    editSubject(data) {
      for (let i = 0; i < this.questionList.length; i++) {
          if (!this.questionList[i].isEdit && (this.questionList[i].question === data.question)) {
            return this.$message({
              type: 'warning',
              message: '请勿添加重复的题目'
            })
          }
        }
      this.questionList.splice(data.index, 1, data);
    },
    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (!this.questionList.length) {
            this.$message({
              type: "error",
              message: "请添加题目！",
            });
            if (this.showAddForm) {
              this.$refs.editQuestionForm.submit();
            }
            return;
          }
          let params = {};
          let data = [];
          for (let index = 0; index < this.questionList.length; index++) {
            let item = {};
            item.type = this.questionList[index].questionType;
            item.question = this.questionList[index].question;
            item.hasRequired = this.questionList[index].hasRequired;
            if (
              this.questionList[index].id ||
              this.questionList[index].id === 0
            ) {
              item.id = this.questionList[index].id;
            }
            if (this.questionList[index].questionType == 1) {
              item.questionOptions = JSON.stringify(
                this.questionList[index].singleAnswerOptions
              );
            } else if (this.questionList[index].questionType == 2) {
              item.questionOptions = JSON.stringify(
                this.questionList[index].multipleAnswerOptions
              );
            }
            data.push(item);
          }
          params = {
            ...this.form,
            data,
          };
          if (this.$route.query.id || this.$route.query === 0) {
            params.id = this.$route.query.id;
          }
          request({
            url: "/system/questionnaire",
            method: "post",
            data: params,
          }).then((res) => {
            if (res.code != 200) {
              this.$message({
                type: "error",
                message: res.message || "系统错误，请稍后再试。",
              });
            } else {
              this.$message({
                type: "success",
                message:
                  this.$route.query || this.$route.query === 0
                    ? "编辑问卷成功！"
                    : "添加问卷成功！",
              });
              this.back();
            }
          });
        } else {
          return false;
        }
      });
    },
    cancel(index) {
      if (index || index === 0) {
        this.questionList[index].isEdit = false;
        const obj = JSON.parse(JSON.stringify(this.questionList[index]));
        this.questionList.splice(index, 1, obj);
      } else {
        this.showAddForm = false;
      }
    },
    edit(index) {
      this.questionList[index].isEdit = true;
      const obj = JSON.parse(JSON.stringify(this.questionList[index]));
      this.questionList.splice(index, 1, obj);
    },
    del(index) {
      this.questionList.splice(index, 1);
    },
    back() {
      this.$store.dispatch("tagsView/delView", this.$route) &&
        this.$router.push("/questionnaireManage/questionnaireList");
    },
  },
};
</script>

<style lang="scss" scoped>
.add-questionnaire {
  .el-radio {
    display: block;
    padding: 10px 0 0 0;
    line-height: 36px;
    &:nth-child(1) {
      padding: 0;
    }
    ::v-deep .el-radio__label {
      display: inline-block;
      word-break: break-all;
      white-space: normal;
      vertical-align: top;
    }
  }
  .el-checkbox {
    padding: 10px 0 0 0;
    display: block;
    &:nth-child(1) {
      padding: 0;
    }
    ::v-deep .el-checkbox__label {
      display: inline-block;
      word-break: break-all;
      white-space: normal;
      vertical-align: top;
      line-height: 36px;
    }
  }
  .option-btns {
    display: none;
  }
  .show-btn {
    display: block;
  }
}
</style>
