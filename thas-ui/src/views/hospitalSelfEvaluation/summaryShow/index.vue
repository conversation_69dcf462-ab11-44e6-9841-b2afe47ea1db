<!--  -->
<template>
  <div class="summary-show">
    <!-- 等待中心审核 -->
    <el-card class="box-card" v-if="
        autSaAudStatus == AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL &&
        isHospital &&
        !showOnlyHospital
      " shadow="never">
      <h4 style="font-weight: bold;margin-left:15px;">自评已提交, 等待中心审核</h4>
      <p style="font-size: 12px;margin-left:15px;">
        提交时间: {{ detail.autSaAudSum.submitDate }}
      </p>
    </el-card>

    <!-- 评审报告验证结果 -->
    <el-card class="box-card" shadow="never" v-if="detail.seniorReviewerReviewSum && !isConclusion">
      <div slot="header" class="clearfix">
        <h4>评审报告验证结果</h4>
        <el-button size="mini" class="resultToggle" @click="resultToggleClick(6)">
          {{ resultToggle(6) ? "收起" : "展开" }}
        </el-button>
      </div>

      <el-descriptions size="small">
        <el-descriptions-item label="一般性评价">{{ (detail.seniorReviewerReviewSum || {}).autEvaluate }}</el-descriptions-item>
        <el-descriptions-item label="评审报告优点">{{ (detail.seniorReviewerReviewSum || {}).autAdvantage }}</el-descriptions-item>
        <el-descriptions-item label="修订评审报告的建议"> {{ (detail.seniorReviewerReviewSum || {}).autProposal }}</el-descriptions-item>
        <el-descriptions-item label="请对报告进行整体评分">{{
            suggestionOptions.find(
              (op) =>
                op.dictValue == (detail.seniorReviewerReviewSum || {}).autResult
            ).dictLabel
          }}</el-descriptions-item>
        <el-descriptions-item label="认证授予建议">
          <dict-span dictType="review_summary" :value="(detail.seniorReviewerReviewSum || {}).autSuggestion"></dict-span>
        </el-descriptions-item>
        <el-descriptions-item label="认证授予建议的理由">{{ (detail.seniorReviewerReviewSum || {}).autDesc }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 事实性认证结果 -->
    <el-card class="box-card" shadow="never" v-if="detail.factualAccuracyReviewSum && !isConclusion">
      <div slot="header" class="clearfix">
        <h4>事实准确性确认结果</h4>
        <el-button size="mini" class="resultToggle" @click="resultToggleClick(5)">
          {{ resultToggle(5) ? "收起" : "展开" }}
        </el-button>
      </div>
      <el-form label-width="90PX" v-show="resultToggle(5)">
        <el-form-item label="需修改项：">
          <modify-table v-if="needModifyClauseList4.length" :versionId="detail.versionId" :key='needModifyClauseList4.length' :data="needModifyClauseList4"></modify-table>
          <span v-else>无需修改</span>
        </el-form-item>
        <el-form-item label="认证总结：" style="margin-bottom: 0">
          {{ (detail.factualAccuracyReviewSum || {}).autDesc }}
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 审查组长审查结果 -->
    <el-card class="box-card" shadow="never" v-if="detail.reviewReviewReportSum && !isConclusion">
      <div slot="header" class="clearfix">
        <h4>评审报告审查结果</h4>
        <el-button size="mini" class="resultToggle" @click="resultToggleClick(4)">
          {{ resultToggle(4) ? "收起" : "展开" }}
        </el-button>
      </div>
      <el-form label-width="90PX" v-show="resultToggle(4)">
        <el-form-item label="需修改项：">
          <modify-table v-if="needModifyClauseList3.length" :versionId="detail.versionId" :key='needModifyClauseList3.length' :data="needModifyClauseList3"></modify-table>
          <span v-else>无需修改</span>
        </el-form-item>
        <el-form-item label="审查总结：" style="margin-bottom: 0">
          {{ (detail.reviewReviewReportSum || {}).autDesc }}
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 评审结果 展示 -->
    <el-card class="box-card" shadow="never" v-if="
        autSaAudStatus > AutSaAudStatusEnum.WAIT_AUD_SECOND_TRIAL_SUM &&
        !isConclusion
      ">
      <div slot="header" class="clearfix">
        <h4>现场评审结果</h4>
        <el-button size="mini" class="resultToggle" @click="resultToggleClick(3)">
          {{ resultToggle(3) ? "收起" : "展开" }}
        </el-button>
      </div>
      <el-form label-width="90px" v-show="resultToggle(3)">
        <el-form-item :label="`评审结果：`" prop="autResult">
          <el-table :data="auditResult2" border>
            <el-table-column label="" prop="message" width="180"></el-table-column>
            <el-table-column label="优秀" prop="1">
              <template slot-scope="scope">
                {{ scope.row[1] || 0 }}
              </template>
            </el-table-column>
            <el-table-column label="良好" prop="2">
              <template slot-scope="scope">
                {{ scope.row[2] || 0 }}
              </template>
            </el-table-column>
            <el-table-column label="达标" prop="3">
              <template slot-scope="scope">
                {{ scope.row[3] || 0 }}
              </template>
            </el-table-column>
            <el-table-column label="部分达标" prop="4">
              <template slot-scope="scope">
                {{ scope.row[4] || 0 }}
              </template>
            </el-table-column>
            <el-table-column label="不达标" prop="5">
              <template slot-scope="scope">
                {{ scope.row[5] || 0 }}
              </template>
            </el-table-column>
            <el-table-column label="达标占比" prop="yRangeU"></el-table-column>
            <el-table-column label="部分达标占比" prop="nRangeU"></el-table-column>
          </el-table>
          <span style="font-size: 12px">认证通过标准：基本款100%达标，非基本款达标≥40%，部分达标≥20%，共≥60%。</span>
        </el-form-item>
        <el-form-item label="评审结果：">
          <dict-span dictType="review_summary" :value="(detail.audSecondTrialSum || {}).autResult"></dict-span>
        </el-form-item>
        <el-form-item label="评审总结：" style="margin-bottom: 0">
          {{ (detail.audSecondTrialSum || {}).autDesc }}
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 审查结果 展示 -->
    <el-card class="box-card" shadow="never" v-if="
        autSaAudStatus >= AutSaAudStatusEnum.AUD_FIRST_TRIAL_SUM_PASS &&
        !isConclusion && (isHospital || isInspector)
      ">
      <div slot="header" class="clearfix">
        <h4>形式审查结果</h4>
        <el-button size="mini" class="resultToggle" @click="resultToggleClick(2)">
          {{ resultToggle(2) ? "收起" : "展开" }}
        </el-button>
      </div>
      <el-form label-width="90px" v-show="resultToggle(2)">
        <el-form-item label="审查结果：">
          <dict-span dictType="check_summary" :value="(detail.audFirstTrialSum || {}).autResult"></dict-span>
        </el-form-item>
        <el-form-item label="审查总结：" style="margin-bottom: 0">
          {{ (detail.audFirstTrialSum || {}).autDesc }}
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 医院自评总结 展示 -->
    <el-card class="box-card" shadow="never" v-if="
        autSaAudStatus >= AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL &&
        autSaAudStatus != AutSaAudStatusEnum.AUD_FIRST_TRIAL_SUM_REJECT &&
        !isConclusion
      ">
      <div slot="header" class="clearfix">
        <h4>医院自评报告</h4>
        <el-button size="mini" class="resultToggle" @click="resultToggleClick(1)">
          {{ resultToggle(1) ? "展开" : "收起" }}
        </el-button>
      </div>
      <el-descriptions size="small">
        <el-descriptions-item label="医院名称">{{ detail.hospitalName }}</el-descriptions-item>
        <el-descriptions-item label="自评总结">
          <span style="white-space: pre-wrap;">
            {{ (detail.autSaAudSum || {}).autAdvantage }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="自评报告">
          <span v-for="(file, index) in autSaAudSumFiles" :key="index" style="margin-right: 10px; color: #1890ff; cursor: pointer">
            <span @click="download(file.fileId, file.fileName)">{{
              file.fileName
            }}</span>
          </span>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script>
import DictSpan from "@/components/DetailMessage/dictSpan.vue";
import ModifyTable from '../components/modifyTable.vue'

import { download } from "@/utils/request";
export default {
  name: "SummaryShow",
  components: {
    DictSpan,
    ModifyTable
  },
  props: {
    autSaAudStatus: {
      type: Number,
      default: () => -1,
    },
    showOnlyHospital: {
      type: Boolean,
      default: () => false,
    },
    detail: {
      type: Object,
      default: () => { },
    },
    isConclusion: {
      type: Boolean,
      default: () => false,
    },
    summaryForm: {
      type: Object,
      default: () => { },
    },
    conclusionText: {
      type: String,
      default: () => "",
    },
  },
  watch: {},
  data() {
    return {
      suggestionOptions: [
        {
          dictValue: "1",
          dictLabel: "优秀",
        },
        {
          dictValue: "2",
          dictLabel: "良好",
        },
        {
          dictValue: "3",
          dictLabel: "中等",
        },
        {
          dictValue: "4",
          dictLabel: "尚可",
        },
        {
          dictValue: "5",
          dictLabel: "较差",
        },
      ],
      reviewSummaryOptions: [],
      checkSummaryOptions: [],
      reviewResultOptions: [],
      resultToggleMap: {
        1: true,
        2: false,
        3: false,
        4: false,
        5: false,
        6: false,
      },
    };
  },

  computed: {
    isHospital() {
      // 医疗机构
      return (
        this.$store.getters.roles.includes("hospital") || this.showOnlyHospital
      );
    },
    isAssessor() {
      // 评审员
      return (
        this.$store.getters.roles.includes("assessor") ||
        this.$store.getters.roles.includes("assessor-leader")
      );
    },
    isSeniorAssessor() {
      // 评审员
      return this.$store.getters.roles.includes("senior-assessor");
    },
    isInspector() {
      // 审查员
      return (
        this.$store.getters.roles.includes("inspector") ||
        this.$store.getters.roles.includes("inspector-leader")
      );
    },

    isSelf() {
      // 自评
      return (
        this.autSaAudStatus >= this.AutSaAudStatusEnum.WAIT_TERM_SA &&
        this.autSaAudStatus <= this.AutSaAudStatusEnum.TERM_SA_SUM
      );
    },

    isTruth() {
      return (
        this.autSaAudStatus >=
        this.AutSaAudStatusEnum.WAIT_FACTUAL_ACCURACY_REVIEW &&
        this.autSaAudStatus <= this.AutSaAudStatusEnum.CONFIRM_REVIEW_FACTUAL
      );
    },

    // 被驳回后的自评
    isSelfByRejected() {
      return (
        this.autSaAudStatus ==
        this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_SUM_REJECT
      );
    },

    // 形式审查
    isCheck() {
      return (
        this.autSaAudStatus >= this.AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL &&
        this.autSaAudStatus <=
        this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_INQUIRE_PROC
      );
    },

    // 审查驳回
    isCheckReject() {
      return (
        this.autSaAudStatus ==
        this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_REVIEW_REJECT
      );
    },

    isAudit() {
      // 评审
      return (
        this.autSaAudStatus >=
        this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_INQUIRE_PROC
      );
    },

    isAuditCon() {
      return (
        this.autSaAudStatus == this.AutSaAudStatusEnum.WAIT_AUD_SECOND_TRIAL_SUM
      );
    },

    isAuditComplete() {
      return (
        this.autSaAudStatus >=
        this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_SUM_REJECT
      );
    },

    isAuditPass() {
      return this.autSaAudStatus >= this.AutSaAudStatusEnum.AUD_PASS;
    },

    resultToggle() {
      return (idx) => this.resultToggleMap[idx];
    },

    auditResult2() {
      let { autSaAudResult, basicReport, nonBasicReport } =
        this.detail.autSaAudReport;
      this.summaryForm.autResult = `${autSaAudResult}`;
      let psBaseRange = (basicReport.passCount * 100) / basicReport.sumCount;
      let ptBaseRange =
        (basicReport.partPassCount * 100) / basicReport.sumCount;
      let baseMap = {
        message: `基本款（${basicReport.sumCount}款）`,
        yRange: psBaseRange,
        yRangeU:
          (`${psBaseRange}`.includes(".")
            ? psBaseRange.toFixed(2)
            : psBaseRange) + "%",
        nRange: ptBaseRange,
        nRangeU:
          (`${ptBaseRange}`.includes(".")
            ? ptBaseRange.toFixed(2)
            : ptBaseRange) + "%",
        ...basicReport.termCountMap,
      };

      let psNBaseRange =
        (nonBasicReport.passCount * 100) / nonBasicReport.sumCount;
      let ptNBaseRange =
        (nonBasicReport.partPassCount * 100) / nonBasicReport.sumCount;

      let otherMap = {
        message: `非基本款（${nonBasicReport.sumCount}款）`,
        yRange: psNBaseRange,
        yRangeU:
          (`${psNBaseRange}`.includes(".")
            ? psNBaseRange.toFixed(2)
            : psNBaseRange) + "%",
        nRange: ptNBaseRange,
        nRangeU:
          (`${ptNBaseRange}`.includes(".")
            ? ptNBaseRange.toFixed(2)
            : ptNBaseRange) + "%",
        ...nonBasicReport.termCountMap,
      };
      return [baseMap, otherMap];
    },

    autSaAudSumFiles() {
      const ids = (this.detail.autSaAudSum || { fileIds: "" }).fileIds;
      const map = this.detail.fileDetailMap || {};
      const idList = ids.split(",");
      const list = [];
      idList.forEach((id) => {
        if (map[id] && map[id][0]) {
          list.push(map[id][0]);
        }
      });
      return list;
    },

    needModifyClauseList2() {
      return dType => {
        let status = this.detail.autSaAudStatus;
        let type = this.AutSaAudSubmitTypeEnum.DEFAULT_TYPE_99;
        if (status == this.AutSaAudStatusEnum.REVIEW_REVIEW_REPORT_SUM) {
          type = this.AutSaAudSubmitTypeEnum.REVIEW_REVIEW_REPORT;
        } else if (
          status == this.AutSaAudStatusEnum.FACTUAL_ACCURACY_REVIEW_SUM
        ) {
          type = this.AutSaAudSubmitTypeEnum.FACTUAL_ACCURACY_REVIEW;
        }
        if (dType) {
          type = dType;
        }
        const list = this.detail.autSaAudListMap[type] || [];
        const ids = list.filter(li => li.autResult == 2).map(li => Number(li.clauseId));
        return ids;
      }
    },

    needModifyClauseList3() {
      return this.needModifyClauseList2(this.AutSaAudSubmitTypeEnum.REVIEW_REVIEW_REPORT);
    },

    needModifyClauseList4() {
      return this.needModifyClauseList2(this.AutSaAudSubmitTypeEnum.FACTUAL_ACCURACY_REVIEW);
    },
  },

  created() { },
  mounted() {
    this.initDicts();
  },

  methods: {
    initDicts() {
      this.getDicts("review_summary").then((res) => {
        this.reviewSummaryOptions = res.data;
      });
      this.getDicts("check_summary").then((res) => {
        this.checkSummaryOptions = res.data;
      });
      this.getDicts("review_result").then((res) => {
        this.reviewResultOptions = res.data;
      });
    },
    resultToggleClick(idx) {
      this.$set(this.resultToggleMap, idx, !this.resultToggleMap[idx]);
    },

    download(fileId, name) {
      download(
        "/common/downloadFile",
        {
          fileId,
        },
        name
      );
    },
  },
};
</script>
<style lang='scss' scoped>
.summary-show {
  .resultToggle {
    color: rgb(64, 158, 255);
    float: right;
    font-size: 12px;
    cursor: pointer;
  }
  .box-card {
    ::v-deep .el-card__body {
      padding: 0;
      .el-form {
        padding: 15px 20px 20px 20px;
      }
    }
  }
  .box-card + .box-card {
    margin-top: 15px;
  }
  // padding: 24px;
  ::v-deep .el-card {
    border: 0;
    .el-card__header {
      padding-bottom: 15px;
      .clearfix h4 {
        float: left;
        font-weight: bold;
        margin: 0;
      }
    }

    .grade .el-select .el-input.is-disabled .el-input__inner {
      background-color: #ffffff;
      border: 1px solid #dcdfe6;
      color: #606266;
    }
  }
}
</style>
