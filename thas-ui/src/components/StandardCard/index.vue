<!--  -->
<template>
  <div class='standard-card'>
    <el-tabs size="small" v-model.trim="activeName" @tab-click="handleClick">
      <el-tab-pane v-for="(chapter, index) in chapterList" :label="`${chapter.chapterNo} ${chapter.chapter}`"
        :name="`${chapter.chapterId}`" :key="index">
        <div>
          <span style="vertical-align: top;margin-left:10px"> 节： </span>
          <el-radio-group size="small" v-model.trim="radio" @change="handleChange">
            <el-radio-button size="small" v-for="(section, index) in sectionList" :key="index" :label="section.sectionId">
              {{ `${section.sectionNo} ${section.section}` }}
            </el-radio-button>
          </el-radio-group>
        </div>
        <div style="margin-top:5px">
          <span style="vertical-align: top;margin-left:10px"> 条： </span>
          <el-select :class="radio && !select ? 'tip' : ''" size="small" v-model.trim="select" clearable filterable placeholder="请选择条" popper-class="standardCardSelectOption"
            @change='handleSelect'>
            <el-option v-for="(article, index) in articleList" :key="index"
              :label="`${article.articleNo} ${article.article}`" :value="article.articleId">
            </el-option>
          </el-select>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { getStandard } from "@/api/system/standard";

export default {
  name: 'StandardCard',
  components: {},
  props: {
    // 需要展示的款
    clauseIds: {
      type: Array,
      default: () => [],
    },
    versionId: {
      type: [String, Number],
      default: () => '',
    },
    fitClauseIds: {
      type: Array,
      default: () => [],
    },
  },
  watch: {},
  data() {
    return {
      activeName: '1',
      radio: '',
      select: '',
      chapterList: [],
      sectionList: [],
      articleList: [],
      clauseList: [],

      chapterIds: [],
      sectionIds: [],
      articleIds: [],
    };
  },

  computed: {

  },

  async created() {
    try {
      await getStandard(this.versionId);
      this.$nextTick(() => this.getAllIds())
    } catch (error) {
      // console.log(error);
    }
  },

  mounted() { },

  methods: {
    handleClick(val) {
      this.sectionList = (this.chapterList.find(chapter => chapter.chapterId == this.activeName).sectionVoList || [])
        .filter(section => this.sectionIds.includes(section.sectionId) || this.sectionIds.includes(`${section.sectionId}`));
      this.radio = ''
      this.select = ''
      this.articleList = [];
      this.clauseList = []
      this.$emit("selectChange", '', this.clauseList, {
        chapter: this.activeName,
        section: this.radio,
        article: this.select,
      });
    },
    handleChange() {
      this.select = ''
      this.clauseList = []
      this.articleList = (this.sectionList.find(section => section.sectionId == this.radio).articleVoList || [])
        .filter(article => this.articleIds.includes(article.articleId) || this.articleIds.includes(`${article.articleId}`));;
      this.$emit("selectChange", '', this.clauseList, {
        chapter: this.activeName,
        section: this.radio,
        article: this.select,
      });
    },

    handleSelect() {
      let clauseVoList = this.articleList.find(article => article.articleId == this.select).clauseVoList;
      let clauseIds = this.clauseIds.map(id => Number(id))
      let fitClauseIds = this.fitClauseIds.map(id => Number(id))
      this.clauseList = clauseVoList.filter(clause => {
        if (clauseIds.length) {
          return clauseIds.includes(clause.clauseId);
        } else {
          return !fitClauseIds.includes(clause.clauseId);
        }
      })
      this.$emit("selectChange", '', this.clauseList, {
        chapter: this.activeName,
        section: this.radio,
        article: this.select,
      });

      this.getClausesDetail(this.versionId || this.$store.getters.standardBaseVersionId, this.select);
    },

    getAllIds() {
      let versionId = this.versionId || this.$store.getters.standardBaseVersionId;
      const clauses = this.$store.getters.standardTypeItemByVersionId(
        versionId,
        "clause",
        "list"
      );
      const clauseList = this.clauseIds.length ? clauses.filter(
        (clause) =>
          this.clauseIds.includes(`${clause.clauseId}`) ||
          this.clauseIds.includes(clause.clauseId)
      ) : clauses.filter((clause) => !this.fitClauseIds.includes(`${clause.clauseId}`) && !this.fitClauseIds.includes(clause.clauseId));

      const chapterList = this.$store.getters.standardTypeItemByVersionId(
        versionId,
        "chapter",
        "list"
      );

      const chapters = this.$store.getters.standardTypeItemByVersionId(
        versionId,
        "chapter",
        "map"
      );

      const sections = this.$store.getters.standardTypeItemByVersionId(
        versionId,
        "section",
        "map"
      );
      const articles = this.$store.getters.standardTypeItemByVersionId(
        versionId,
        "article",
        "map"
      );

      this.chapterIds = [...new Set(clauseList.map(
        (clause) => chapters[clause.chapterId].chapterId
      ))];
      this.sectionIds = [...new Set(clauseList.map(
        (clause) => sections[clause.sectionId].sectionId
      ))];
      this.articleIds = [...new Set(clauseList.map(
        (clause) => articles[clause.articleId].articleId
      ))];

      this.chapterList = chapterList.filter((chapter) => {
        if (this.chapterIds.length)
          return this.chapterIds.includes(chapter.chapterId) || this.chapterIds.includes(`${chapter.chapterId}`);
        return true;
      });
      if (this.chapterList.length) {
        this.activeName = `${this.chapterList[0].chapterId}`
        this.handleClick();
      }


      this.$emit('loaded', clauses)
      this.selfInspection();
    },

    selfInspection() {
      if (this.articleIds.length && this.articleIds.length === 1 || this.clauseIds.length && this.clauseIds.length === 1) {
        this.radio = this.sectionIds[0];
        this.handleChange();
        this.select = this.articleIds[0];
        this.handleSelect();
      } else if (this.sectionIds.length && this.sectionIds.length === 1) {
        this.radio = this.sectionIds[0];
        this.handleChange();
      }
    }
  }
}

</script>

<style lang='scss' scoped>
.standard-card {
  ::v-deep .el-tabs {
    .el-tabs__item {
      height: 30px;
      line-height: 20px;
    }
  }

  ::v-deep .el-radio-group {
    width: calc(100% - 60px);

    .el-radio-button {
      display: block;
      width: 100%;
      margin-left: 10px;

      .el-radio-button__inner {
        width: 100%;
        text-align: left;
        color: black;
        border: 1px solid #e5e5e4;
        border-radius: 0;
      }

      &+.el-radio-button .el-radio-button__inner {
        border-top: 0;
      }
    }

    .el-radio-button__orig-radio:checked+.el-radio-button__inner {
      background-color: #eaf6fe;
      border-color: #e5e5e4;
      color: black;
      box-shadow: none;

      &::after {
        content: "√";
        float: right;
        color: #3e82f3;
        font-weight: bolder;
      }
    }
  }

  ::v-deep .el-select {
    margin-left: 10px;
    width: calc(100% - 60px);
  }
  ::v-deep .el-select.tip .el-input__inner {
    border: 1px solid #F56C6C;
  }
}
</style>

<style lang='scss'>
.standardCardSelectOption {
  max-width: 500px;
}
</style>
