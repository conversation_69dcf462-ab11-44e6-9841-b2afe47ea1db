<template>
  <el-dialog class="standards-normal-dialog" title="修改信息" destroy-on-close :close-on-click-modal="false" :visible.sync="dialogVisible" width="80%">
    <el-form ref="form" :model="form" label-width="80px" :rules="rules">
      <!-- <el-form-item label="类型：">
        {{ form.type }}
      </el-form-item> -->

      <div v-for="(li, index) in form.list" :key="index">
        <el-form-item label="评价：" :prop="`list.${index}.evaluate`" :rules="rules.evaluate">
          {{li.evaluate}}
          <!-- <span></span> -->
          <!-- <el-input :maxlength="10" v-model.trim="li.evaluate" style="width: 60%"></el-input> -->
          <!-- <el-button type="text" @click="removeStan(index)" style="margin-left: 5px" v-if="form.list.length > 1">删除评价</el-button>
          <el-button v-if="form.list.length - 1 == index" style="margin-left: 15px" type="text" @click="addStan">添加评价</el-button> -->
        </el-form-item>
        <el-form-item label="描述：" :prop="`list.${index}.standard`" :rules="rules.standard">
          <el-input type="textarea" :maxlength="250" :rows="6" v-model.trim="li.standard"></el-input>
        </el-form-item>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialogVisible = false">返 回</el-button>
      <el-button type="primary" size="small" @click="save">保 存</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "standardsDialog",
  data() {
    return {
      dialogVisible: false,
      form: {
        type: "",
        list: [],
      },
      rules: {
        evaluate: {
          required: true,
          message: "请输入评价",
        },
        standard: {
          required: true,
          message: "请输入描述",
        },
      },
      versionId: "",
    };
  },
  methods: {
    show(list, versionId, type) {
      this.$set(this.form, "list", JSON.parse(JSON.stringify(list)));
      this.versionId = versionId;
      this.dialogVisible = true;
      this.form.type = type;
    },
    addStan() {
      this.form.list.push({
        evaluate: "",
        standard: "",
        versionId: Number(this.versionId),
      });
    },
    removeStan(index) {
      this.form.list.splice(index, 1);
    },

    save() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.dialogVisible = false;
          this.$emit("modify", this.form);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.standards-normal-dialog {
  ::v-deep .el-dialog {
    &:not(.is-fullscreen) {
      margin-top: 5vh !important;
    }
    .el-dialog__header {
      background-color: #3d81f5;
      .el-dialog__title {
        color: white;
        font-weight: bold;
      }
      .el-dialog__headerbtn i {
        color: white;
      }
    }
    .el-dialog__body {
      padding-bottom: 0;
      overflow: hidden;
      .el-form {
        // height: 500px;
        overflow: auto;
      }
    }
  }
  /* for Chrome */
  ::-webkit-scrollbar {
    display: none;
  }
}
</style>
