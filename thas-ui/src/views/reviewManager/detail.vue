<!--  -->
<template>
  <div class='review-manager-detail' :v-loading="loading">
    <div v-if="showPage">
      <el-card class="box-card" v-if="isSecond">
        <div slot="header" class="clearfix">
          <span>评审报告验证结果</span>
        </div>
        <el-form>
          <el-form-item label="需修改项：">
            <modify-table v-if='needModifyClauseList5.length' :versionId="detail.versionId" :key='needModifyClauseList5.length' :data="needModifyClauseList5" :showAll="false"></modify-table>
            <span v-else>无需修改</span>
          </el-form-item>
          <el-form-item label="一般性评价：" style="margin-bottom: 0">
            {{ (seniorReviewerReviewSum || {}).autEvaluate || '--' }}
          </el-form-item>
          <el-form-item label="评审报告优点：" style="margin-bottom: 0">
            {{ (seniorReviewerReviewSum || {}).autAdvantage || '--' }}
          </el-form-item>
          <el-form-item label="修订评审报告的建议：" style="margin-bottom: 0">
            {{ (seniorReviewerReviewSum || {}).autProposal || '--' }}
          </el-form-item>
          <el-form-item label="请对报告进行整体评分：" style="margin-bottom: 0">
            {{
                (suggestionOptions.find(
                  (op) =>
                    op.dictValue == (seniorReviewerReviewSum || {}).autResult
                ) || {}).dictLabel || '--'
              }}
          </el-form-item>
          <el-form-item label="认证授予建议：" style="margin-bottom: 0">
            <dict-span v-if="(seniorReviewerReviewSum || {}).autSuggestion" dictType="review_summary" :value="(seniorReviewerReviewSum || {}).autSuggestion"></dict-span>
            <span v-else>--</span>
          </el-form-item>
          <el-form-item label="认证授予建议的理由：" style="margin-bottom: 0">
            {{ (seniorReviewerReviewSum || {}).autDesc || '--' }}
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card" v-if="!isSecond">
        <div slot="header" class="clearfix">
          <span>事实准确性确认结果</span>
        </div>
        <el-form>
          <el-form-item label="需修改项：">
            <modify-table v-if="needModifyClauseList4.length" :versionId="detail.versionId" :key='needModifyClauseList4.length' :data="needModifyClauseList4" :showAll="false"></modify-table>
            <span v-else>无需修改</span>
          </el-form-item>
          <el-form-item label="认证总结：">
            {{detail.factualAccuracyReviewSum.autDesc}}
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card" v-if="detail.autSaAudStatus == AutSaAudCurrentStatusEnum.CONFIRM_REVIEW_FACTUAL">
        <el-button type="primary" @click="turnToCheckLeader" v-loading='turning' :disabled="turning">转给评审组长</el-button>
      </el-card>

      <el-card class="box-card" v-if="showModify">
        <el-button type="primary" @click="modifyConfirm" v-loading='turning' :disabled="turning">拒绝所有修改</el-button>
        <el-button type="primary" @click="modifyConfirm" v-loading='turning' :disabled="turning">需修改转评审员</el-button>
      </el-card>
    </div>
  </div>
</template>

<script>
import request from "@/utils/request";
import ModifyTable from '../hospitalSelfEvaluation/components/modifyTable.vue'



export default {
  name: 'ReviewManagerDetail',
  components: {
    ModifyTable
  },
  props: {},
  watch: {},
  beforeRouteEnter(to, from, next) {
    if (to.query.type == 2) {
      // this.$set(to.meta, 'title', '修改确认')
      to.meta.title = '修改确认'
    } else {
      to.meta.title = '审查确认'
      // this.$set(to.meta, 'title', '审查确认')?
    }
    next();
  },
  data() {
    return {
      detail: {},
      showPage: false,
      loading: true,
      turning: false,
      type: 0,
      seniorReviewerReviewSum: {},
      suggestionOptions: [
        {
          dictValue: "1",
          dictLabel: "优秀",
        },
        {
          dictValue: "2",
          dictLabel: "良好",
        },
        {
          dictValue: "3",
          dictLabel: "中等",
        },
        {
          dictValue: "4",
          dictLabel: "尚可",
        },
        {
          dictValue: "5",
          dictLabel: "较差",
        },
      ],
    };
  },

  computed: {

    isSecond() {
      return this.detail.autSaAudStatus === this.AutSaAudCurrentStatusEnum.CHECK_MODIFIED_TERMS_SECOND
    },

    showModify() {
      let status = [
        this.AutSaAudCurrentStatusEnum.CHECK_MODIFIED_TERMS_FIRST,
        this.AutSaAudCurrentStatusEnum.CHECK_MODIFIED_TERMS_SECOND
      ]
      return status.includes(this.detail.autSaAudStatus)
    },

    needModifyClauseList2() {
      return dType => {
        const list = this.detail.autSaAudListMap[dType] || [];
        const ids = list.filter(li => li.autResult == 2).map(li => Number(li.clauseId));
        return ids;
      }
    },

    needModifyClauseList5() {
      return this.needModifyClauseList2(this.AutSaAudCurrentStatusEnum.TR_CLAUSE);
    },

    needModifyClauseList4() {
      return this.needModifyClauseList2(this.AutSaAudCurrentStatusEnum.FAR_CLAUSE);
    },
  },

  created() { },

  mounted() {
    if (!this.$route.query.autCode) {
      this.$store.dispatch("tagsView/delView", this.$route) &&
        this.$router.push({ path: "/ReviewManagerList" });
      return this.$message({
        type: 'warning',
        message: '缺少必要参数'
      })
    }
    this.getDetail();
  },

  methods: {
    getDetail() {
      request({
        url: "/aut/sa/aud/queryDetail",
        method: "post",
        data: {
          autCode: this.$route.query.autCode,
          accountId: this.$store.getters.userId,
        },
      }).then((res) => {
        this.detail = res.data;
        this.showPage = true;
        this.loading = false;
        try {
          this.seniorReviewerReviewSum = JSON.parse(this.detail.seniorReviewerReviewSum.autDesc)
        } catch (error) {

        }
      });
    },

    modifyConfirm() {
      request({
        url: "/aut/sa/aud/submitAutSaAud",
        method: "post",
        data: {
          autCode: this.$route.query.autCode,
          accountId: this.$store.getters.userId,
          submitType: this.isSecond ? this.AutSaAudCurrentStatusEnum.TR_CLAUSE_W_M : this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_W_M,
          autSaAudLists: [],
        },
      }).then((res) => {
        this.$store.dispatch("tagsView/delView", this.$route);
        !this.isSecond && this.$router.push({ path: "/fAssessor/m/assessorEvaluationList" });
        this.isSecond && this.$router.push({ path: "/fAssessor/m/assessorEvaluationList" });
      });
    },

    turnToCheckLeader() {
      this.turning = true;
      request({
        url: "/aut/sa/aud/submitAutSaAud",
        method: "post",
        data: {
          autCode: this.$route.query.autCode,
          accountId: this.$store.getters.userId + "",
          submitType: this.AutSaAudCurrentStatusEnum.FAR_SUMMARY_CONFIRM,
          autSaAudLists: [],
        },
      }).then((res) => {
        // this.getDetail();
        this.$store.dispatch("tagsView/delView", this.$route);
        !this.isSecond && this.$router.push({ path: "/fInspector/CheckReviewList" });
      }).finally(() => {
        this.turning = false;
      });
    },
  }
}

</script>
<style lang='scss' scoped>
.review-manager-detail {
  .box-card + .box-card {
    margin-top: 15px;
  }
}
</style>
