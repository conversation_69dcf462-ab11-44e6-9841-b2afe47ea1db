package com.thas.web.mapper;

import com.thas.web.domain.TraLearnResource;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 学习资源Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-20
 */
@Mapper
public interface TraLearnResourceMapper {
    /**
     * 查询学习资源
     *
     * @param id 学习资源主键
     * @return 学习资源
     */
    TraLearnResource selectTraLearnResourceById(Long id);

    /**
     * 查询学习资源列表
     *
     * @param traLearnResource 学习资源
     * @return 学习资源集合
     */
    List<TraLearnResource> selectTraLearnResourceList(TraLearnResource traLearnResource);

    /**
     * 新增学习资源
     *
     * @param traLearnResource 学习资源
     * @return 结果
     */
    int insertTraLearnResource(TraLearnResource traLearnResource);

    /**
     * 修改学习资源
     *
     * @param traLearnResource 学习资源
     * @return 结果
     */
    int updateTraLearnResource(TraLearnResource traLearnResource);

    /**
     * 删除学习资源
     *
     * @param id 学习资源主键
     * @return 结果
     */
    int deleteTraLearnResourceById(Long id);

    /**
     * 批量删除学习资源
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTraLearnResourceByIds(Long[] ids);

    int learnNumPlus(Long id);
}
