<template>
  <div class="review-plan-audit-list">
    <el-table :data="dataSource" border stripe>
      <el-table-column align="center" label="操作">
        <template slot-scope="scope">
          <el-button type="text">查看详情</el-button>
          <el-button type="text">审核</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="评审人员" prop=""></el-table-column>
      <el-table-column align="center" label="评审安排与流程" prop=""></el-table-column>
      <el-table-column align="center" label="状态" prop=""></el-table-column>
      <el-table-column align="center" label="分配时间" prop=""></el-table-column>
    </el-table>
  </div>
</template>

<script>
import request from "@/utils/request";

export default {
  name: "",
  data() {
    return {
      dataSource: [],
      url: {
        list: '/system/distribution/selectPlannedDistributionInfoByApplyNo'
      }
    };
  },

  components: {},

  computed: {},

  mounted() {
    request({
      url: this.url.list,
      method: 'get',
    })
  },

  methods: {},
};
</script>
<style lang='scss' scoped>
.review-plan-audit-list {
  padding: 20px;
  background-color: #fff;
}
</style>