<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.AsaStatusConfigMapper">
    
    <resultMap type="AsaStatusConfig" id="AsaStatusConfigResult">
        <result property="id"    column="id"    />
        <result property="currentStatus"    column="current_status"    />
        <result property="statusDesc"    column="status_desc"    />
        <result property="pageDesc"    column="page_desc"    />
        <result property="nextStatusConfig"    column="next_status_config"    />
        <result property="serviceName"    column="service_name"    />
        <result property="processMethod"    column="process_method"    />
        <result property="checkMethod"    column="check_method"    />
        <result property="submitType"    column="submit_type"    />
        <result property="cycleStage"    column="cycle_stage"    />
        <result property="createId"    column="create_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateId"    column="update_id"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAsaStatusConfigVo">
        select id, current_status, status_desc, page_desc, next_status_config, service_name, process_method, check_method, submit_type, cycle_stage, create_id, create_time, update_id, update_time from asa_status_config
    </sql>

    <select id="selectAsaStatusConfigList" parameterType="AsaStatusConfig" resultMap="AsaStatusConfigResult">
        <include refid="selectAsaStatusConfigVo"/>
        <where>  
            <if test="currentStatus != null  and currentStatus != ''"> and current_status = #{currentStatus}</if>
            <if test="statusDesc != null  and statusDesc != ''"> and status_desc = #{statusDesc}</if>
            <if test="pageDesc != null  and pageDesc != ''"> and page_desc = #{pageDesc}</if>
            <if test="nextStatusConfig != null  and nextStatusConfig != ''"> and next_status_config = #{nextStatusConfig}</if>
            <if test="serviceName != null  and serviceName != ''"> and service_name like concat('%', #{serviceName}, '%')</if>
            <if test="processMethod != null  and processMethod != ''"> and process_method = #{processMethod}</if>
            <if test="checkMethod != null  and checkMethod != ''"> and check_method = #{checkMethod}</if>
            <if test="submitType != null  and submitType != ''"> and submit_type = #{submitType}</if>
            <if test="cycleStage != null  and cycleStage != ''"> and cycle_stage = #{cycleStage}</if>
            <if test="createId != null "> and create_id = #{createId}</if>
            <if test="updateId != null "> and update_id = #{updateId}</if>
        </where>
    </select>
    
    <select id="selectAsaStatusConfigById" parameterType="Long" resultMap="AsaStatusConfigResult">
        <include refid="selectAsaStatusConfigVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertAsaStatusConfig" parameterType="AsaStatusConfig" useGeneratedKeys="true" keyProperty="id">
        insert into asa_status_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="currentStatus != null and currentStatus != ''">current_status,</if>
            <if test="statusDesc != null and statusDesc != ''">status_desc,</if>
            <if test="nextStatusConfig != null and nextStatusConfig != ''">next_status_config,</if>
            <if test="serviceName != null and serviceName != ''">service_name,</if>
            <if test="processMethod != null and processMethod != ''">process_method,</if>
            <if test="checkMethod != null and checkMethod != ''">check_method,</if>
            <if test="submitType != null and submitType != ''">submit_type,</if>
            <if test="cycleStage != null and cycleStage != ''">cycle_stage,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="currentStatus != null and currentStatus != ''">#{currentStatus},</if>
            <if test="statusDesc != null and statusDesc != ''">#{statusDesc},</if>
            <if test="nextStatusConfig != null and nextStatusConfig != ''">#{nextStatusConfig},</if>
            <if test="serviceName != null and serviceName != ''">#{serviceName},</if>
            <if test="processMethod != null and processMethod != ''">#{processMethod},</if>
            <if test="checkMethod != null and checkMethod != ''">#{checkMethod},</if>
            <if test="submitType != null and submitType != ''">#{submitType},</if>
            <if test="cycleStage != null and cycleStage != ''">#{cycleStage},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateAsaStatusConfig" parameterType="AsaStatusConfig">
        update asa_status_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="currentStatus != null and currentStatus != ''">current_status = #{currentStatus},</if>
            <if test="statusDesc != null and statusDesc != ''">status_desc = #{statusDesc},</if>
            <if test="nextStatusConfig != null and nextStatusConfig != ''">next_status_config = #{nextStatusConfig},</if>
            <if test="serviceName != null and serviceName != ''">service_name = #{serviceName},</if>
            <if test="processMethod != null and processMethod != ''">process_method = #{processMethod},</if>
            <if test="checkMethod != null and checkMethod != ''">check_method = #{checkMethod},</if>
            <if test="submitType != null and submitType != ''">submit_type = #{submitType},</if>
            <if test="cycleStage != null and cycleStage != ''">cycle_stage = #{cycleStage},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAsaStatusConfigById" parameterType="Long">
        delete from asa_status_config where id = #{id}
    </delete>

    <delete id="deleteAsaStatusConfigByIds" parameterType="String">
        delete from asa_status_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>