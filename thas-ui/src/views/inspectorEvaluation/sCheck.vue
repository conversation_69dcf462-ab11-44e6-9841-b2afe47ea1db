<!--  -->
<template>
  <div class="audit-evaluation" ref="auditEvaluation" v-loading="loading">
    <div v-if="distributed">
      <!-- 医院名称 -->
      <el-card class="box-card" shadow="always" style="margin-bottom: 10px; margin-left: -5px">
        <h1 style="margin: 0">{{ detail.hospitalName }}</h1>
      </el-card>

      <!-- 总结详情 -->
      <el-card v-if="showAutReject.length" class="box-card autScrollCard" shadow="never">
        <div slot="header" class="clearfix">
          <h4>审查驳回总览表</h4>
          <el-button v-if="toggle" type="text" size="mini" style="float:right;" @click="toggle = false" icon="el-icon-arrow-up">收起</el-button>
          <el-button v-if="!toggle" type="text" size="mini" style="float:right;" @click="toggle = true" icon="el-icon-arrow-down">展开</el-button>
        </div>
        <modify-table v-show="toggle" :data="showAutReject" :versionId='relation.autCsId' :showAll='false' :split="false">
        </modify-table>
      </el-card>

      <!-- 评价  -->
      <el-card class="box-card autScrollCard" shadow="never" :style="{height: autCardHeight}">
        <div slot="header" class="clearfix">
          <h4>形式审查审核</h4>
          <div class="header-right" v-if="standardLoadedKey" :key="headerSlotKeys">
            <span v-if="!isSummary && (showAutPass.length || showAutReject.length)">
              <span v-if="showAutPass.length && !hasReject" class="showAutNum" :class="{lightHigh: lightHigh == 10}" @click="handleClauseFilter(10, showAutPass)">
                审查通过 {{showAutPass.length}} 款
              </span>

              <span v-if="showAutRejectTotal.length" class="showAutNum" :class="{lightHigh: lightHigh == 20}" @click="handleClauseFilter(20, showAutRejectTotal)">
                审查驳回 {{showAutRejectTotal.length}} 款
              </span>
              <span v-if="showAutReject.length" class="showAutNum" :class="{lightHigh: lightHigh == 30}" @click="handleClauseFilter(30, showAutReject)">
                驳回待修改 {{showAutReject.length}} 款
              </span>
            </span>

            <el-button type="primary" size="mini" v-if="waitConfirmFirst" @click="checkConfirm" style="margin-left:10px">审核完成</el-button>
            <!-- <el-button type="primary" size="mini" v-if="isSummary" @click="submitFinal" style="margin-left:10px">提交医院自评终稿</el-button> -->
          </div>
        </div>
        <!-- 款项选择器 -->
        <!-- <evaluation-base :versionId="relation.autCsId" :clauseIds="clauseIds" @selectChange="selectChange" @loaded="standardLoaded"></evaluation-base> -->
        <standard-card ref="standardCard" :key="`base${loadingKeys}`" :clauseIds="clauseIds" :versionId="relation.autCsId" @selectChange="selectChange" @loaded="standardLoaded">
        </standard-card>

        <!-- 款项 -->
        <div v-show="clauseList.length" class="evaluation-content" :key="`ach${selectedKeys}`" :style="{height: clauseItemHeight}">
          <el-form :key="loadingKeys" label-width="100px">
            <clause-item v-for="(clause, index) in clauseList" v-show="isThisPage(index + 1)" :clause="clause" :key="clause.clauseId">
              <evaluation-self :readonly="true" :type="AutSaAudCurrentStatusEnum.SA_CLAUSE" :clause="clause" :aut="saMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-self>
              <evaluation-examine isLeader :showEdit="!isSummary" :type="AutSaAudCurrentStatusEnum.FR_CLAUSE" :clause="clause" :aut="frMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-examine>
            </clause-item>
            <pagination v-show="total > limit" :total="total" :page.sync="pageNum" :limit.sync="limit" :pageSizes="[limit]" />
          </el-form>
        </div>
      </el-card>
      <!-- 总结提交 -->
      <!-- <summary-edit v-if="isSummary" :detail="detail" :relation="relation" :autResult="autResult" @summarySubmit="summarySubmit"></summary-edit> -->
    </div>

  </div>
</template>

<script>
import evaluationMixin from "@/mixins/evaluation.vue";
import SummaryEdit from './summaryEdit.vue'

export default {
  name: "SInspectorIndex",
  mixins: [evaluationMixin],
  components: {
    SummaryEdit,
  },
  props: {},
  watch: {
    detail: {
      deep: true,
      immediate: true,
      handler() {
        let statues = [
          this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_REVIEW_REJECT,
          this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_SUM_REJECT
        ]
        if (this.detail && this.detail.autSaAudStatus && statues.includes(this.detail.autSaAudStatus)) {
          this.handleError();
        }
      },
    },
  },
  data() {
    return {
      show: false,
      loading: true,
      relation: {},
      detail: {},
      clauseIds: [],
      specialInit: true,
      edit: false,
      toggle: false,
      lightHigh:0
    };
  },

  computed: {
    waitConfirmFirst() {
      return this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.WAIT_CONFIRM_FIRST_TRIAL
    },

    showExamine() {
      return true;
    },

    isSummary() {
      return this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.WAIT_AUD_FIRST_TRIAL_SUM
    },

    wholeClauseIds() {
      return this.wholeClauseList.map(({ clauseId }) => Number(clauseId));
    },

    autResult() {
      const list = Object.values(this.frMap);
      let value = list.every(li => li.autResult == 1) ? 1 : 2
      return value;
    },

    showAutPass() {
      const list = Object.values(this.frMap);
      let list2 = list.filter(({ autResult }) => autResult == 1)
      let list3 = list2.map(({ clauseId }) => Number(clauseId));
      return list3
    },

    showAutRejectTotal() {
      if (this.detail.rejectAutSaAudListMap && this.detail.rejectAutSaAudListMap[this.AutSaAudCurrentStatusEnum.FR_CLAUSE]) {
        let frMap = this.detail.rejectAutSaAudListMap[this.AutSaAudCurrentStatusEnum.FR_CLAUSE] || {}
        let list3 = Object.values(frMap).map(({ clauseId }) => Number(clauseId));
        if (list3.length < 5) this.toggle = true;
        return list3 || []
      }

      const list = Object.values(this.frMap);
      let list2 = list.filter(({ autResult }) => autResult != 1)
      let list3 = list2.map(({ clauseId }) => Number(clauseId));
      return list3 || []
    },

    showAutReject() {
      const list = Object.values(this.frMap);
      let list3 = list.filter(({ rejected }) => rejected).map(({ clauseId }) => Number(clauseId));
      if (list3.length < 5) this.toggle = true;
      return list3
    },

    hasReject() {
      let listMap = this.detail.rejectAutSaAudListMap || {}
      return Array.isArray(listMap[this.AutSaAudCurrentStatusEnum.SA_CLAUSE]);
    },


    showAutFilter() {
      if (this.wholeClauseList.length === 0) return [];
      const list = Object.values(this.frvMap);
      let wholeClauseIds = this.wholeClauseIds;
      let frClauseIds = list.map(({ clauseId }) => Number(clauseId));
      let list2 = wholeClauseIds.reduce((list, next) => {
        if (!frClauseIds.includes(Number(next))) list.push(Number(next));
        return list;
      }, [])
      return list2;
    },

    showAutRjFilter() {
      const list = Object.values(this.frMap);
      const rjList = list.filter(({ rejected }) => rejected).map(({ clauseId }) => clauseId);
      return rjList;
    },
  },

  destroyed() {
    this.bus.$off("autSubmit");
  },

  mounted() {
    this.init2(true, this.$route.query.autCode, this.$route.query.type);
  },

  methods: {
    initFinalBack() {
      if (this.showAutReject.length) {
        this.$set(this, 'clauseIds', this.showAutRejectTotal)
        this.$set(this, 'distributeClauseIds', this.showAutRejectTotal)
        this.loadingKeys++;
      } else {
        this.$set(this, 'clauseIds', [])
        this.$set(this, 'distributeClauseIds', [])
        this.loadingKeys++;
      }

      console.log(this.clauseIds);

      if (this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.WAIT_AUD_FIRST_TRIAL_SUM) {
        this.handleError();
      }
    },

    handleError() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push({ path: "/fInspector/s/inspectorEvaluationList?preliminary=0" });
    },

    // 初审复查
    submitAllSaS() {
      let ids = Object.values(this.frvMap).map(({ clauseId }) => clauseId)
      this.makeData(this.AutSaAudCurrentStatusEnum.FR_CLAUSE_CONFIRM, undefined, undefined, ids)
    },

    summarySubmit() {
      this.handleError();
    },

    async checkConfirm() {
      let data = {
        autCode: this.relation.autCode,
        accountId: this.$store.getters.userId + "",
        submitType: this.AutSaAudCurrentStatusEnum.FR_CLAUSE_CONFIRM,
        autSaAudLists: []
      }
      try {
        let res = await this.submit(data);
        // 报错就不提示
        if (!res) return;
        this.$message({
          type:'success',
          message: "审核完成"
        });
        this.init2(false, this.$route.query.autCode, this.$route.query.type);
      } catch (error) {
        if (error.code == 1000003) {
          this.init2(false, this.$route.query.autCode, this.$route.query.type);
        }
      } finally {
        // this.termSubmitLoading = false;
      }
    },

  },
};
</script>
<style lang="scss" scoped>
.box-card + .box-card {
  margin-top: 15px;
}
h4 {
  margin: 10px 0;
}
.clause-item {
  position: relative;
}

span > .showAutNum::after {
  content: "|";
}
span > .showAutNum:last-child::after {
  content: "";
}
::v-deep .el-table__cell {
  padding: 2px 0;
}
</style>
