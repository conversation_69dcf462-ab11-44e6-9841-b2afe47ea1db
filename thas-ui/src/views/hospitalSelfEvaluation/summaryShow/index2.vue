<!--  -->
<template>
  <div class='summary-show'>
    <el-collapse v-model="activeNames">
      <el-collapse-item title="医院自评报告" name="1">
        <el-descriptions :column="1">
          <el-descriptions-item label="自评总结">
            <span style="white-space: pre-wrap;">
              {{ (formData || {}).autAdvantage || '--' }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="自评报告" :labelStyle="{ lineHeight: '28px' }">
            <span v-if="!formData.files || formData.files.length == 0"> -- </span>
            <div v-else v-for="file in formData.files" :key="file.fileId">
              {{ file.fileName || '--' }}
              <span v-if="file.fileName" class="fileOpera">
                <el-button size="mini" type="text" @click="downLoad(file.fileId, file.downLoadFileName)">下载自评报告</el-button>
                <el-button size="mini" type="text" @click="view(file.url)">预览自评报告</el-button>
              </span>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </el-collapse-item>
    </el-collapse>
    <!-- <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <h4>医院自评报告</h4>
      </div>
      <el-descriptions :column="1">
        <el-descriptions-item label="亮点">{{ (formData || {}).autAdvantage || '--' }}</el-descriptions-item>
        <el-descriptions-item label="不足"> {{ (formData || {}).autEvaluate || '--' }}</el-descriptions-item>
        <el-descriptions-item label="改进计划">{{ (formData || {}).autProposal || '--' }}</el-descriptions-item>
        <el-descriptions-item label="审核证明" :labelStyle="{ lineHeight: '28px' }">
          <span v-if="!formData.files || formData.files.length == 0"> -- </span>
          <div v-else v-for="file in formData.files" :key="file.fileId">
            {{ file.fileName || '--' }}
            <span v-if="file.fileName" class="fileOpera">
              <el-button size="mini" type="text" @click="downLoad(file.fileId, file.fileName)">下载自评报告</el-button>
              <el-button size="mini" type="text" @click="view(file.url)">预览自评报告</el-button>
            </span>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </el-card> -->
  </div>
</template>

<script>
import { download } from "@/utils/request";
export default {
  name: 'SummaryShow',
  components: {},
  props: {
    formData: {
      type: Object,
      default: () => { }
    },
  },
  watch: {},
  data() {
    return {
      activeNames: '0',
    };
  },

  computed: {},

  created() { },

  mounted() { },

  methods: {
    downLoad(id, name) {
      download("/common/downloadFile", { fileId: id }, name);
    },
    view(url) {
      window.open(url, '_blank')
    },
  }
}

</script>

<style lang='scss' scoped>
.summary-show {
  .fileOpera {
    display: inline-block;
    margin-left: 5px;
    // visibility: hidden;
  }

  .box-card {
    ::v-deep .el-card__body {
      padding-bottom: 0;
      padding-top: 7.5px;
    }
  }

  ::v-deep .el-collapse {
    border: 1px solid #e6ebf5;
    border-radius: 4px;

    .el-collapse-item__header {
      font-family: inherit;
      font-weight: 500;
      line-height: 1.1;
      color: inherit;
      padding: 14px 15px 15px;
      min-height: 40px;
      font-size: 16px;
      border-bottom: 1px solid #e6ebf5;
    }

    .el-descriptions__body {
      padding: 15px 20px 20px 20px;
      padding-bottom: 0;
      padding-top: 7.5px;

    }

    .el-collapse-item__content {
      padding-bottom: 0;
    }
  }
}
</style>
