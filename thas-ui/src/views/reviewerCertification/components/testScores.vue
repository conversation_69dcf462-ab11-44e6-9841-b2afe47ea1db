<template>
  <div class="test-scores">
    <el-table :data="dataSource" border>
      <el-table-column label="学习资源" align="center" prop="title">
        <template slot-scope="scope">
          <span @click="scope.row.status != 1 && toLearn(scope.row.learnResourceId)" :style="{
              color: scope.row.status != 1 ? '#409EFF' : '',
              cursor: scope.row.status != 1 ? 'pointer' : '',
            }">{{ scope.row.title }}</span>
        </template>
      </el-table-column>
      <el-table-column label="考卷名称" align="center" prop="paperName" />
      <el-table-column label="通过状态" align="center" prop="status">
        <template slot-scope="scope">
          <span v-if="scope.row.status == 1" style="color: green">通过</span>
          <span v-else-if="scope.row.status == 0" style="color: red">未通过</span>
          <span v-else>还未考试</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import request from "@/utils/request";

export default {
  name: "TestScores",

  data() {
    return {
      url: {
        list: "/system/exam/list",
      },
      dataSource: [],
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.query();
  },

  methods: {
    query() {
      request({
        url: this.url.list,
        method: "get",
        data: {},
      }).then((res) => {
        this.dataSource = (res.rows || []).filter((li) => !!li);
      });
    },
    async pass() {
      return request({
        url: "/system/exam/pass",
        method: "get",
      });
    },

    async getValue() {
      try {
        if(!this.dataSource.length) {
          this.$message({
            type:'error',
            message: '请联系管理员发布考卷，并全部考试通过！'
          })
          return false;
        }
        const res = await this.pass();
        // console.log(res);
        return true;
      } catch (error) {
        return false;
      }
    },

    getDraft() {
      return true;
    },

    toLearn(learnResourceId) {
      this.$router.push({
        path: '/learningMaterials/courseDetail',
        query: {
          id: learnResourceId
        }
      })
    },
  },
};
</script>
<style lang='scss' scoped>
.test-scores {
  padding: 24px;
}
</style>