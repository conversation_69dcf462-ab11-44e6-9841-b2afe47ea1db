<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import request from "@/utils/request";
import echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "300px",
    },
    chartData: {
      type: Object,
      default: {
        name: '',
        xAxisData: [],
        yAxisData: [],
        yAxisName: "",
        dataZoomValue: 3,
        colorList: [],
        interestDescMap: {},
        rjAllotReviewerResMap: {}
      },
    },
  },
  data() {
    return {
      chart: null
    };
  },
  computed: {
    isHalfChart() {
      let name = this.chartData.name;
      return name === 'reviewersWork' || name === 'trainTeacher'
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val);
      },
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, null, {renderer: 'canvas'});
      this.setOptions(this.chartData);
    },
    setOptions({
      name,
      xAxisData,
      yAxisData,
      yAxisName,
      dataZoomValue,
      colorList,
      interestDescMap,
      rjAllotReviewerResMap
    } = {}) {
      this.chart.setOption({
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
            axis: 'auto'
          },
          formatter: this.isHalfChart ? false : (params) => {
            let list = name === 'interest' ? interestDescMap[params[0].name] : name === 'rjAllotReviewer' ? rjAllotReviewerResMap[params[0].name] : [];
            var table =
              '<table class="bordered"><thead><tr>' +
              '<th>评审员名称</th>' + (name === 'interest' ? '<th>利益冲突原因</th>' : '') + '</tr></thead><tbody>';
            for (var i = 0; i < list.length; i++) {
              table +=
                "<tr>" +
                "<td>" +
                  list[i].reviewerName +
                "</td>" +
                (name === 'interest' ? "<td>" +
                  list[i].interestDesc +
                "</td>" : "");
            }
            table += "</tr></tbody>";
            return table;
          }
        },
        grid: {
          top: 30,
          left: 16,
          right: "2%",
          bottom: "5%",
          containLabel: true,
        },
        dataZoom: [
          {
            type: "slider",
            endValue: dataZoomValue,
            show: true,
            zoomLock: true,
            xAxisIndex: [0],
            height: 8,
            bottom: 0,
            fillerColor: "rgba(0, 0, 0, .1)",
            backgroundColor: "#FFF",
            showDataShadow: false,
            showDetail: false,
            minValueSpan: dataZoomValue,
            maxValueSpan: dataZoomValue,
            brushSelect: false,
          },
          {
            type: "inside",
            show: true,
            xAxisIndex: [0],
          },
        ],
        xAxis: {
          type: "category",
          data: xAxisData,
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            interval: 0,
            rotate: this.isHalfChart ? 30 : 0,
            fontWeight: 'bold'
          }
        },
        yAxis: {
          name: yAxisName,
          nameTextStyle: {
            fontWeight: 'bold'
          },
          type: "value",
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: "dashed",
              color: ["#ccc"],
            },
          },
          minInterval: 1,
          axisLabel: {
            fontWeight: 'bold'
          }
        },
        series: [
          {
            type: "bar",
            showBackground: false,
            barWidth: this.isHalfChart ? 5 : 20, //柱子宽度
            barGap: 1, //柱子之间间距
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: colorList[0] },
                { offset: 0.5, color: colorList[1] },
                { offset: 1, color: colorList[0] },
              ]),
            },
            emphasis: {
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: colorList[0] },
                  { offset: 0.7, color: colorList[2] },
                  { offset: 1, color: colorList[0] },
                ]),
              },
            },
            data: yAxisData,
          },
        ],
      });
    },
  },
};
</script>

<style lang="scss">
.bordered {
  width: 100%;
  font-size: 12px;
  border-collapse: collapse;
  th,
  td {
    border: 1px solid #999;
    padding: 0px 5px;
  }
}

.bordered caption {
  font-weight: bold;
}

.bordered thead tr {
  background-color: #333;
  color: #fff;
}

.bordered tbody tr td:first-child {
  color: #fff;
}

.bordered tbody tr td {
  padding-right: 10px;
  white-space: pre-wrap;
  &:first-child {
    width: 100px;
  }
  &:last-child {
    max-width: 600px;
  }
}
</style>
