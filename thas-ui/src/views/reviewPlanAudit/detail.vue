<template>
  <div class="review-plan-audit-detail">
    <el-card class="box-card" shadow="never">
      <!-- <div slot="header" class="clearfix">
        <span>评审计划审核</span>
      </div> -->
      <el-form v-loading="loading" size="small" element-loading-text="拼命加载中" :model="formData" :rules="rules" ref="elForm" label-width="170px">
        <el-form-item v-if="persons && persons.length" :label="`${!detail.hosStatus ? '评审员审核：' : '评审员：'}`" :show-message="false">
          <div class="card-Item" v-for="(text, index) in persons" :key="index">
            <el-image :key="text.reviewerBaseInfoVo.headPortrait" :src="fileUrl(text.reviewerBaseInfoVo.headPortrait)">
              <div slot="error" class="image-slot">
                <i class="el-icon-user-solid"></i>
              </div>
            </el-image>
            <div>
              <span>
                <p>
                  {{
                    text.reviewerBaseInfoVo &&
                    text.reviewerBaseInfoVo.reviewerName
                  }}
                  {{ text.leaderIs == 1 ? "(组长)" : "" }}
                  <span v-if="text.fieldIdList == 'SENIOR_REVIEW'">( <i style="color: blue">验证评审员</i>)</span>
                  <i class="el-icon-document" style="font-size:12px;color:#1890ff;vertical-align: top;cursor: pointer;" @click="toVisit(text)"></i>
                </p>
                <span v-if="text.fieldIdList != 'SENIOR_REVIEW'">分配的分组数：{{ text.fieldCount }}</span>
                <p style="margin-top: 10px; margin-bottom: 0">
                  <span class="company" :title="
                      text.reviewerBaseInfoVo &&
                      text.reviewerBaseInfoVo.companyPost
                    ">
                    {{
                      text.reviewerBaseInfoVo &&
                      text.reviewerBaseInfoVo.companyPost
                    }}
                  </span>
                  <span class="company" style="width: 100px" :title="
                      text.reviewerBaseInfoVo && text.reviewerBaseInfoVo.company
                    ">
                    |
                    {{
                      text.reviewerBaseInfoVo && text.reviewerBaseInfoVo.company
                    }}
                  </span>
                </p>
              </span>
              <div>
                <span style="font-size: 12px">{{ statusText(text) }}</span>
                <div v-if="!detail.hosStatus" style="display: inline-block; margin-left: 5px">
                  <el-button type="primary" v-show="showAgree(text.reviewerStatus)" size="mini" @click="auditAssessor(text, 1)" style="margin-right: 5px">同意</el-button>
                  <el-button type="danger" v-show="showReject(text.reviewerStatus)" size="mini" @click="auditAssessor(text, 2)">拒绝</el-button>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item v-if="hasReject" label="拒绝评审员的理由：" prop="description" key="description">
          <div class="tip">请注意，拒绝的原因应是基于任何已知的困难或利益冲突，并详细说明。</div>
          <el-input v-if="!detail.hosStatus" type="textarea" :rows="4" :maxlength="200" v-model.trim="formData.description" @input="() => hasChange = true" style="width: 60%"></el-input>
          <span v-else>{{ detail.description || '--'}}</span>
        </el-form-item>
        <el-form-item v-if="trainees && trainees.length" :label="`${!detail.hosStatus ? '评审学员审核：' : '评审学员：'}`" prop="trainees">
          <div class="card-Item" v-for="(text, index) in trainees" :key="index">
            <el-image :key="text.reviewerBaseInfoVo.headPortrait" :src="fileUrl(text.reviewerBaseInfoVo.headPortrait)">
              <div slot="error" class="image-slot">
                <i class="el-icon-user-solid"></i>
              </div>
            </el-image>
            <div>
              <span>
                <p>
                  {{
                    text.reviewerBaseInfoVo &&
                    text.reviewerBaseInfoVo.reviewerName
                  }}
                  <i class="el-icon-document" style="font-size:12px;color:#1890ff;vertical-align: top;cursor: pointer;" @click="toVisit(text)"></i>
                </p>
                <span style="visibility: hidden;">分配的分组数：--</span>

                <p style="margin-top: 10px; margin-bottom: 0">
                  <span class="company" :title="
                      text.reviewerBaseInfoVo &&
                      text.reviewerBaseInfoVo.companyPost
                    ">
                    {{
                      text.reviewerBaseInfoVo &&
                      text.reviewerBaseInfoVo.companyPost
                    }}
                  </span>
                  <span class="company" style="width: 100px" :title="
                      text.reviewerBaseInfoVo && text.reviewerBaseInfoVo.company
                    ">
                    |
                    {{
                      text.reviewerBaseInfoVo && text.reviewerBaseInfoVo.company
                    }}
                  </span>
                </p>
              </span>
              <div>
                <span style="font-size: 12px">{{ statusText(text) }}</span>
                <div v-if="!detail.hosStatus" style="display: inline-block; margin-left: 5px">
                  <el-button type="primary" v-show="showAgree(text.reviewerStatus)" size="mini" @click="auditAssessor(text, 1, '2')" style="margin-right: 5px">同意</el-button>
                  <el-button type="danger" v-show="showReject(text.reviewerStatus)" size="mini" @click="auditAssessor(text, 2, '2')">拒绝</el-button>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item v-if="hasTraineesReject" label="拒绝评审学员的理由：" prop="refuseStudentReason" key="refuseStudentReason">
          <div class="tip">请注意，拒绝的原因应是基于任何已知的困难或利益冲突，并详细说明。</div>
          <el-input v-if="!detail.hosStatus" type="textarea" :rows="4" :maxlength="200" v-model.trim="formData.refuseStudentReason" @input="() => hasChange = true" style="width: 60%"></el-input>
          <span v-else>{{ detail.refuseStudentReason || '--'}}</span>
        </el-form-item>
        <el-form-item v-if="dataSource && dataSource.length" :label="`${!detail.hosStatus ? '评审安排与流程审核：' : '评审安排与流程：'}`">
          <div class="cycle-box">
            <div>
              <div v-for="(item, index) in dataSource" :key="index" class="cycle-item">
                <div class="cycle-name">{{ item.sysDictDataVo.dictLabel }}</div>
                <div class="cycle-time"> {{ splitTime(item.cycle) }}</div>
              </div>
            </div>
            <!-- <div v-if="!detail.hosStatus && detail.cycleStatus == 0"> -->
            <div v-if="detail.cycleStatus == 0">
              <el-button v-if="detail.cycleStatus != 1" type="primary" style="margin-right: 5px;" @click="auditTime(1)" size="mini">同意</el-button>
              <el-button v-if="detail.cycleStatus != 2" type="danger" size="mini" @click="auditTime(2)">拒绝</el-button>
            </div>
            <div v-else>
              <span v-if="detail.cycleStatus == 1" style="color: #67C23A;">已通过</span>
              <span v-if="detail.cycleStatus == 2" style="color: #F56C6C;">已拒绝</span>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="" v-if="!detail.hosStatus && persons && persons.length">上述评审员将于上述评审日期对{{$store.getters.nickName}}开展评审工作，他们的简介已发送到贵单位“医院评审评价管理平台”账号中。 请于{{detail.beforeCycleDateStr}}之前完成下表，并上传至“医院评审评价管理平台”。</el-form-item>
        <el-form-item v-if="!detail.hosStatus && persons && persons.length" label="下载评审员名单确认表：" label-width="210px">
          <el-button type="primary" icon="el-icon-download" @click="downloadApplyForm" :disabled="!allAudit">下载文件</el-button>
        </el-form-item>
        <el-form-item v-if="!detail.hosStatus && persons && persons.length" label="上传盖章后评审员名单确认表：" label-width="210px">
          <upload :disabled="!allAudit || hasChange" :accept='`.PDF`' :action='`${uploadUrl}/common/uploadFile`' :files="files" :single="true" :data="{type: '1', downLoadFileName: `${$store.getters.nickName}-评审员名单确认表.pdf`}" @uploadSuccess="uploadSuccess" @fileRemove="fileRemove"></upload>
        </el-form-item>
        <el-form-item v-if="persons && persons.length" label=" " label-width="210px">
          <el-button type="primary" @click="submit" v-if="!detail.hosStatus" :disabled="submitLoading || !allAudit || hasChange || !revFileId" v-loading="submitLoading">提交审核结果</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <dict-span dictType="review_cycle" value="0" style="display: none"></dict-span>

    <el-dialog :visible.sync="dialogVisible" width="740px" :show-close="false" custom-class="dialogReviewer" :before-close="handleClose">
      <div class="selfFlex">
        <el-image class="image" :src="fileUrl(dialogReviewer.headPortrait)"></el-image>
        <p class="selfProfile">{{dialogReviewer.reviewerName}}</p>
      </div>
      <div class="divider">
        <el-divider></el-divider>
        <el-divider></el-divider>
      </div>
      <div class="selfContent">
        <p class="reviewerName">个人简介</p>
        <p class="profile">{{dialogReviewer.profile}}</p>
      </div>
      <div class="divider">
        <el-divider></el-divider>
        <el-divider></el-divider>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="closeDetail">关 闭</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import DictSpan from "@/components/DetailMessage/dictSpan.vue";
import request, { downloadPDF } from "@/utils/request";

export default {
  name: "",
  data() {
    return {
      url: {
        modifyReview: "/system/reviewer",
        modifyCycle: "/system/cycle/hos/review",
        detail: "/system/distribution/selectPlannedDistributionInfoByType",
        submit: "/system/distribution/submitAuditResult",
      },
      formData: {
        description: "",
        refuseStudentReason: "",
      },
      persons: [],
      dataSource: [],
      detail: {},
      key: 0,
      loading: true,
      submitLoading: false,
      revFileId: "",
      files: [],
      trainees: [],
      allReady: false,
      hasChange: true,

      dialogVisible: false,
      dialogReviewer: {},
    };
  },

  components: {
    DictSpan,
  },

  computed: {
    isReject() {
      return (status) => {
        return status != 2;
      };
    },

    statusText() {
      return (person) => {
        let text = "待审核";
        if (person.reviewerStatus == 1) text = "已同意";
        if (person.reviewerStatus == 2) text = "已拒绝";
        if (this.detail.hosStatus && person.reviewerStatus == 0) {
          text = "已同意";
        }
        return text;
      };
    },

    splitTime() {
      return (times) => {
        if (!(times || "").includes(",")) return "";
        return times.split(",").join("~");
      };
    },

    cycleStatusText() {
      return (status) => {
        let text = "待审核";
        if (status == 1) text = "已同意";
        if (status == 2) text = "已拒绝";
        return text;
      };
    },
    showAgree() {
      return (status) => {
        return status == null || status == 0 || status == 2;
      };
    },
    showReject() {
      return (status) => {
        return status == null || status == 0 || status == 1;
      };
    },
    hasReject() {
      if (!this.detail || !Object.keys(this.detail).length) return true;
      const flag = this.persons.some((person) => person.reviewerStatus == 2);
      return flag && !this.detail.hosStatus;
    },

    hasTraineesReject() {
      if (!this.detail || !Object.keys(this.detail).length) return true;
      const flag = this.trainees.some((train) => train.reviewerStatus == 2);
      return flag && !this.detail.hosStatus;
    },

    rules() {
      return {
        description: [
          {
            required: this.hasReject,
            message: "请输入拒绝评审员的理由",
            trigger: ["blur"],
          },
        ],
        refuseStudentReason: [
          {
            required: this.hasTraineesReject,
            message: "请输入拒绝评审学员的理由",
            trigger: ["blur"],
          },
        ],
      };
    },
    uploadUrl() {
      return localStorage.baseUrl || process.env.VUE_APP_BASE_API;
    },

    allAudit() {
      // // console.log(this.detail.hospitalReviewerVoList);
      return this.detail?.hospitalReviewerVoList?.filter(reviewer => {
        return reviewer.fieldIdList != "SENIOR_REVIEW"
      }).every(
        (reviewer) => {
          return reviewer.reviewerStatus != 0
        }
      )
    },

    fileUrl() {
      return (fileId) => {
        if (!fileId) return;
        let map = this.detail?.fileDetailMap
        let files = map[fileId];
        if (!files || files.length == 0) return ''
        return files[0]?.url || ''
      }
    }
  },
  mounted() {
    this.applyNo = this.$route.params.applyNo || "";
    if (this.$route.path === "/reviewPlanAudit/reviewPlanAuditDetail") {
      this.getDetail();
    } else if (this.$route.path === "/reviewPlanAudit/auditReviewer") {
      this.getDetail2();
    }
  },

  methods: {
    getDetail() {
      request({
        url: this.url.detail + `?applyNo=${this.applyNo}&type=hospital`,
        method: "get",
      })
        .then((res) => {
          if (res.code == 200) {
            this.$set(this, "detail", res.data);
            if (res.data.hospitalReviewCycleVoList) {
              this.$set(
                this,
                "dataSource",
                res.data.hospitalReviewCycleVoList.sort(
                  (a, b) => Number(a.stageValue) - Number(b.stageValue)
                )
              );
            }

            if (res.data.hospitalReviewerVoList) {
              this.$set(
                this,
                "persons",
                res.data.hospitalReviewerVoList.filter(
                  (per) =>
                    per.reviewerBaseInfoVo &&
                    per.fieldIdList != "TRAINEES_REVIEW" &&
                    per.fieldIdList != "SENIOR_REVIEW"
                )
              );
              this.$set(
                this,
                "trainees",
                res.data.hospitalReviewerVoList.filter(
                  (per) =>
                    per.reviewerBaseInfoVo &&
                    per.fieldIdList == "TRAINEES_REVIEW"
                )
              );
            }
            this.$nextTick(() => {
              this.$refs.elForm &&
                this.$refs.elForm.clearValidate("description");
                this.$refs.elForm.clearValidate("refuseStudentReason");
            });
            this.loading = false;
            this.key++;
          }
        })
        .catch((error) => {
          // console.log(error);
          this.$store.dispatch("tagsView/delView", this.$route) &&
            this.$router.push({ path: "/" });
        });
    },
    getDetail2() {
      request({
        url: this.url.detail + `?applyNo=${this.applyNo}&type=reviewer`,
        method: "get",
      })
        .then((res) => {
          // // console.log(res);
          if (res.code == 200) {
            this.$set(this, "detail", res.data);

            if (res.data.hospitalReviewCycleVoList) {
              this.$set(
                this,
                "dataSource",
                res.data.hospitalReviewCycleVoList.sort(
                  (a, b) => Number(a.stageValue) - Number(b.stageValue)
                )
              );
            }
            if (res.data.hospitalReviewerVoList) {
              this.$set(
                this,
                "persons",
                res.data.hospitalReviewerVoList.filter(
                  (per) =>
                    per.reviewerBaseInfoVo &&
                    per.fieldIdList != "TRAINEES_REVIEW" &&
                    per.fieldIdList != "SENIOR_REVIEW"
                )
              );
              this.$set(
                this,
                "trainees",
                res.data.hospitalReviewerVoList.filter(
                  (per) => per.fieldIdList == "TRAINEES_REVIEW"
                )
              );
            }
            this.$nextTick(() => {
              this.$refs.elForm &&
                this.$refs.elForm.clearValidate("description");
                this.$refs.elForm.clearValidate("refuseStudentReason");
            });
            this.loading = false;
            this.key++;
          }
        })
        .catch((error) => {
          // console.log(error);
          this.$store.dispatch("tagsView/delView", this.$route) &&
            this.$router.push({ path: "/" });
        });
    },
    getDetail3() { },

    toVisit({ reviewerBaseInfoVo }) {
      this.dialogReviewer = {
        reviewerName: reviewerBaseInfoVo.reviewerName,
        headPortrait: reviewerBaseInfoVo.headPortrait,
        profile: reviewerBaseInfoVo.profile,
        companyPost: reviewerBaseInfoVo.companyPost,
        company: reviewerBaseInfoVo.company,
      }

      this.dialogVisible = true;
      // let { accountId, userName } = reviewerBaseInfoVo;
      // this.$router.push({
      //   path: "/reviewPlanAudit/assessorDetail",
      //   query: { accountId, userName, genre: 1 },
      // });
    },

    auditAssessor(assessor, status, roleType = 1) {
      this.loading = true;
      request({
        url: this.url.modifyReview,
        method: "put",
        data: {
          id: assessor.id,
          reviewerStatus: status,
          traineesFlag: roleType == 2 ? status : ''
        },
      })
        .then((res) => {
          this.getDetail2();
        })
        .finally(() => {
          this.loading = false;
          this.hasChange = true;
        });
    },

    auditTime(status) {
      this.loading = true;
      request({
        url: this.url.modifyCycle,
        method: "post",
        data: {
          applyNo: this.detail.applyNo,
          status,
        },
      })
        .then(() => {
          if (status == 1) {
            this.getDetail();
          } else {
            this.$store.dispatch("tagsView/delView", this.$route) &&
              this.$router.push({ path: "/" });
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    submit() {
      let reviewerAudit = this.detail.hospitalReviewerVoList.some(
        (reviewer) =>
          reviewer.reviewerStatus == 0 &&
          reviewer.fieldIdList != "TRAINEES_REVIEW" &&
          reviewer.fieldIdList != "SENIOR_REVIEW"
      );
      if (reviewerAudit) {
        return this.$message({
          type: "warning",
          message: "尚有未审核的评审员，请审核！",
        });
      }
      let traineesAudit = this.detail.hospitalReviewerVoList.some(
        (reviewer) =>
          reviewer.reviewerStatus == 0 &&
          reviewer.fieldIdList == "TRAINEES_REVIEW"
      );
      if (traineesAudit) {
        return this.$message({
          type: "warning",
          message: "尚有未审核的学员，请审核！",
        });
      }

      this.$refs.elForm.validate((valid) => {
        if (!valid) return;

        if (!this.revFileId) {
          return this.$message({
            type: "warning",
            message: "上传盖章后评审员名单确认表！",
          });
        }

        // const review = this.persons
        //   .filter((person) => person.fieldIdList != "SENIOR_REVIEW")
        //   .some((person) => person.reviewerStatus == 2);

        const reviewer = this.persons.filter((person) => person.fieldIdList != "SENIOR_REVIEW" && person.fieldIdList != 'TRAINEES_REVIEW')
          .some((person) => person.reviewerStatus == 2);

        const trainees = this.trainees.filter((person) => person.fieldIdList == 'TRAINEES_REVIEW')
          .some((person) => person.reviewerStatus == 2);
        // if (
        //   this.detail.hospitalReviewerVoList.some(
        //     (review) => !review.reviewerBaseInfoVo
        //   ).length
        // ) {
        //   this.formData.description =
        //     this.formData.description + "，存在认证信息为空的评审员";
        // }

        this.detail.hospitalReviewerVoList.forEach(review => {
          if (!review.reviewerBaseInfoVo) {
            if (review.fieldIdList === 'TRAINEES_REVIEW') {
              this.formData.refuseStudentReason = this.formData.refuseStudentReason + "，存在认证信息为空的评审员"
            } else {
              this.formData.description = this.formData.description + "，存在认证信息为空的评审员"
            }
          }
        })

        this.submitLoading = true;

        request({
          url: this.url.submit,
          method: "put",
          data: {
            id: this.detail.id,
            applyNo: this.detail.applyNo,
            description: this.formData.description,
            refuseStudentReason: this.formData.refuseStudentReason,
            reviewFlag: reviewer ? 2 : 1,
            traineesFlag: trainees ? 2 : 1,
            revFileId: this.revFileId,
          },
        })
          .then((res) => {
            if (res.code == 200) {
              this.$message({
                type: "success",
                message: "提交成功",
              });
              this.updateAutStatus((reviewer || trainees) ? 2 : 1, false, trainees);
            }
            if (this.revFileId) {
              this.shareCreate([this.revFileId], this.$store.getters.userId, 'hospital_before_review', '', '', this.$store.getters.roles, true, false)
            }
          }).catch(() => {
            this.submitLoading = false;
          }).finally(() => {
            let hasReject = this.detail.hospitalReviewerVoList.some(reviewer => reviewer.reviewerStatus == 2)
            if (hasReject) {
              this.$store.dispatch("tagsView/delView", this.$route) &&
                this.$router.push({ path: "/" });
            } else {
              this.getDetail2()
            }
          }
          )
      });
    },

    updateAutStatus(autResult, notThrow = true, onlyTr = false) {
      this.submitLoading = true;
      request({
        url: "/aut/sa/aud/submitAutSaAud",
        method: "post",
        data: {
          autCode: this.detail.autCode,
          accountId: `${this.$store.getters.userId}`,
          submitType: this.AutSaAudCurrentStatusEnum.NODE_FILP,
          statusProcess: autResult == 1 ? "PS" : onlyTr ? 'XYRJ' : "RJ",
          autSaAudLists: [
            {
              autResult,
              autDesc: "--",
            },
          ],
        },
        throw: notThrow
      }).finally(() => {
        this.submitLoading = false;
      });
    },

    spanMethod({ columnIndex }) {
      if (columnIndex === 3) {
        return [9, 3];
      }
    },

    downloadApplyForm() {
      this.$refs.elForm.validate((valid) => {
        if (!valid) return;
        downloadPDF(
          {
            filename:  `${this.$store.getters.nickName}-评审员名单确认表.pdf`,
            ftlTemplateCode: "hos_rew_plan",
            checkStatus: "N",
            description: this.formData.description || " ",
            refuseStudentReason: this.formData.refuseStudentReason || " ",
            applyNo: this.detail.applyNo,
          },
          () => {
            this.loading = false;
            this.hasChange = false;
          }
        );
      })
    },

    fileRemove(file, length) {
      this.revFileId = "";
    },

    uploadSuccess(response, file) {
      if (response.code != 200) {
        return this.$message.error(response.msg || response.message);
      }
      this.revFileId = response.data.fileId;
      this.files.length = 0;
      this.files.push(file);
    },

    handleClose() {
      this.dialogVisible = false
      this.dialogReviewer = {}
    },

    closeDetail() {
      this.handleClose()
    },
  },
};
</script>

<style lang='scss' scoped>
.review-plan-audit-detail {
  min-height: inherit;
  padding-bottom: 20px;
  background-color: #fff;
  .box-card {
    border: 0;
  }
  ::v-deep .el-form-item__content {
    flex-wrap: wrap;
    display: flex;
    justify-content: flex-start;
  }
  .card-Item {
    display: flex;
    height: 120px;
    margin-right: 15px;
    margin-bottom: 20px;
    width: 350px;
    ::v-deep .el-image {
      width: 120px;
      .image-slot {
        height: 100%;
        background: #f5f7fa;
        color: #c0c4cc;
        vertical-align: middle;
        i {
          font-size: 50px;
          display: inline-block;
          margin: 35px 20px;
        }
      }
    }
    & > div {
      margin-left: 15px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      & > span {
        line-height: initial;
        // margin-bottom: 10px;
        p {
          margin: 0;
          margin-bottom: 5px;
          font-size: 13px;
        }
        p:first-child {
          font-weight: bold;
          font-size: 16px;
          span {
            font-size: 12px;
            vertical-align: bottom;
            font-weight: lighter;
          }
        }
      }
    }
  }
  .company {
    display: inline-block;
    max-width: 80px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    padding-right: 3px;
    cursor: pointer;
  }
  .el-button + .el-button {
    margin-left: 0px;
  }
  .cycle-box {
    display: flex;
    align-items: center;
    width: 100%;
    padding-top: 10px;
  }
  .cycle-item {
    width: 500px;
    margin-bottom: 28px;
    line-height: 1;
    .cycle-name {
      margin-bottom: 8px;
      font-size: 16px;
      color: #4075aa;
    }
    .cycle-time {
      font-size: 14px;
      color: #7b7b7b;
    }
  }
  .tip {
    width: 100%;
    font-size: 12px;
    color: #ff4949;
  }
}
</style>
<style lang="scss">
.el-dialog.dialogReviewer {
  margin-top: 14vh !important;
  .el-dialog__header {
    padding-top: 0;
  }
  .el-dialog__body {
    padding: 0 20px;
    min-height: 430px;
  }

  .el-dialog__footer {
    padding-top: 0px;
    padding-bottom: 10px;
    text-align: center;
  }

  .image {
    width: 180px;
    height: 120px;
    border-radius: 50%;
    border: 1px solid black;
    // margin-right: 36px;
  }

  p {
    text-align: center;
  }

  .selfFlex {
    height: 150px;
    display: flex;
    justify-content: space-around;
    & > * {
      align-self: center;
    }
  }

  .selfProfile {
    font-size: 33px;
    // font-weight: bold;
    letter-spacing: 10px;
    color: #000;
    // margin-left: 25px;
  }
  .selfContent {
     min-height: 186px;
    .reviewerName {
      margin-top: 0;
      padding-top: 10px;
      font-size: 18px;
      font-weight: 600;
      color: black;
    }
    .profile {
      margin-bottom: 0;
      text-align: left;
      text-indent: 40px;
      font-size: 18px;
      line-height: 30px;
      letter-spacing: 2.5px;
    }
  }

  .divider {
    .el-divider {
      background-color: black;
      &:first-child {
        margin-bottom: 0px;
        margin-top: 10px;
      }
      &:last-child {
        margin-top: 2px;
        margin-bottom: 10px;
      }
    }
  }
}
</style>
