<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.CstOfflineTrainingManagementMapper">

    <resultMap type="CstOfflineTrainingManagement" id="CstOfflineTrainingManagementResult">
        <result property="id"    column="id"    />
        <result property="trainingName"    column="training_name"    />
        <result property="trainingType"    column="training_type"    />
        <result property="trainingStatus"    column="training_status"    />
        <result property="trainingTime"    column="training_time"    />
        <result property="trainingLocation"    column="training_location"    />
        <result property="organizers"    column="organizers"    />
        <result property="status"    column="status"    />
        <result property="createId"    column="create_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateId"    column="update_id"    />
        <result property="updateTime"    column="update_time"    />
        <result property="participatesTraining"    column="participates_training"    />

    </resultMap>

    <sql id="selectCstOfflineTrainingManagementVo">
        select id, training_name, training_type, training_status, training_time, training_location, organizers, status, create_id, create_time, update_id, update_time,participates_training
        from cst_offline_training_management

    </sql>


    <select id="selectCstOfflineTrainingManagementList" parameterType="CstOfflineTrainingManagement" resultMap="CstOfflineTrainingManagementResult">
        <include refid="selectCstOfflineTrainingManagementVo"/>
        <where>  
            <if test="trainingName != null  and trainingName != ''"> and training_name like concat('%', #{trainingName}, '%')</if>
            <if test="trainingType != null  and trainingType != ''"> and training_type = #{trainingType}</if>
            <if test="trainingStatus != null  and trainingStatus != ''"> and training_status = #{trainingStatus}</if>
            <if test="filterIs != null and filterIs == 5 ">
                <if test="trainingTime != null ">
                    <![CDATA[AND (DATE_FORMAT(training_time, '%Y-%m-%d') <= DATE_FORMAT( #{trainingTime}, '%Y-%m-%d'))]]>
                </if>
            </if>
            <if test="filterIs != null and filterIs == 1 ">
                <if test="trainingTime != null ">
                    <![CDATA[AND (DATE_FORMAT(training_time, '%Y-%m-%d %H:%i:%s') >= DATE_FORMAT( #{trainingTime}, '%Y-%m-%d %H:%i:%s'))]]>
                </if>
            </if>
            <if test="trainingLocation != null  and trainingLocation != ''"> and training_location = #{trainingLocation}</if>
            <if test="organizers != null  and organizers != ''"> and organizers = #{organizers}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="createId != null "> and create_id = #{createId}</if>
            <if test="updateId != null "> and update_id = #{updateId}</if>
            <if test="participatesTraining != null and participatesTraining == 0">
                and id IN (
                    SELECT
                    training_id
                    FROM
                    cst_reviewer_offline_training
                    WHERE
                    reviewer_id = #{accountId}
                    AND part_status = 1
                    AND status = 1)
            </if>
            <if test="participatesTraining != null and participatesTraining == 1">
                and id NOT IN (
                    SELECT
                    training_id
                    FROM
                    cst_reviewer_offline_training
                    WHERE
                    reviewer_id = #{accountId}
                    AND part_status = 1
                    AND status = 1)
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectCstOfflineTrainingManagementById" parameterType="Long" resultMap="CstOfflineTrainingManagementResult">
        <include refid="selectCstOfflineTrainingManagementVo"/>
        where id = #{id}
    </select>

    <select id="selectCstOfflineTrainingManagementByAccountId" parameterType="String"
            resultMap="CstOfflineTrainingManagementResult">
        SELECT
            id,
            training_name,
            training_type,
            training_status,
            training_time,
            training_location,
            organizers,
            STATUS,
            create_id,
            create_time,
            update_id,
            update_time,
            0 participatesTraining
        FROM
         cst_offline_training_management
        WHERE
            id IN (
            SELECT
            training_id
            FROM
            cst_reviewer_offline_training
            WHERE
            reviewer_id = #{accountId} AND status = 1)
            AND status = 1

    </select>

    <select id="selectIdsByTime" resultType="Long">
        SELECT id FROM cst_offline_training_management
            WHERE NOW() > training_time AND training_status != '0'
    </select>

    <select id="getByManagementId" resultType="com.thas.web.domain.CstOfflineTrainingManagement">
        SELECT * FROM cst_offline_training_management m WHERE m.id= #{id}
    </select>

    <select id="selectId" resultType="java.lang.Long">
        select id
        from cst_offline_training_management
        <where>
            <if test="trainingName != null  and trainingName != ''"> and training_name like concat('%', #{trainingName}, '%')</if>
            <if test="trainingType != null  and trainingType != ''"> and training_type = #{trainingType}</if>
            <if test="trainingStatus != null  and trainingStatus != ''"> and training_status = #{trainingStatus}</if>
        </where>
    </select>
    <select id="selectCstOfflineTrainingManagementByAccountIdAndStatus" resultType="java.lang.Long">
        SELECT
	       COUNT(*)
        FROM
	       cst_offline_training_management cotm
	    LEFT JOIN cst_reviewer_offline_training crot ON cotm.id = crot.training_id
        WHERE
        crot.reviewer_id = #{accountId}
        AND cotm.training_status = 1
        AND cotm.participates_training = 1
		AND cotm.`STATUS` = 1
		AND crot.`STATUS` = 1
		AND crot.apply_status = 1
		AND crot.part_status = 1
    </select>


    <insert id="insertCstOfflineTrainingManagement" parameterType="CstOfflineTrainingManagement" useGeneratedKeys="true" keyProperty="id">
        insert into cst_offline_training_management
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="trainingName != null">training_name,</if>
            <if test="trainingType != null">training_type,</if>
            <if test="trainingTime != null">training_time,</if>
            <if test="trainingLocation != null">training_location,</if>
            <if test="organizers != null and organizers != ''">organizers,</if>
            <if test="trainingStatus != null and trainingStatus != ''">training_status,</if>

            <if test="status != null">status,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="participatesTraining != null">participates_training,</if>

         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="trainingName != null">#{trainingName},</if>
            <if test="trainingType != null">#{trainingType},</if>
            <if test="trainingTime != null">#{trainingTime},</if>
            <if test="trainingLocation != null">#{trainingLocation},</if>
            <if test="organizers != null and organizers != ''">#{organizers},</if>
            <if test="trainingStatus != null and trainingStatus != ''">#{trainingStatus},</if>

            <if test="status != null">#{status},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="participatesTraining != null">#{participatesTraining},</if>
         </trim>
    </insert>

    <update id="updateCstOfflineTrainingManagement" parameterType="CstOfflineTrainingManagement">
        update cst_offline_training_management
        <trim prefix="SET" suffixOverrides=",">
            <if test="trainingName != null">training_name = #{trainingName},</if>
            <if test="trainingType != null">training_type = #{trainingType},</if>
            <if test="trainingStatus != null and trainingStatus != ''">training_status = #{trainingStatus},</if>
            <if test="trainingTime != null">training_time = #{trainingTime},</if>
            <if test="trainingLocation != null">training_location = #{trainingLocation},</if>
            <if test="organizers != null and organizers != ''">organizers = #{organizers},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="participatesTraining != null ">participates_training = #{participatesTraining}</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateStatus" parameterType="CstOfflineTrainingManagement">
        UPDATE cst_offline_training_management
        <trim prefix="SET" suffixOverrides=",">
            <if test="trainingStatus != null and trainingStatus != ''">training_status = #{trainingStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateTrainingStatusByNowTime" parameterType="list">
        UPDATE cst_offline_training_management
        SET training_status = '0' WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="deleteCstOfflineTrainingManagementById" parameterType="Long">
        delete from cst_offline_training_management where id = #{id}
    </delete>

    <delete id="deleteCstOfflineTrainingManagementByIds" parameterType="String">
        delete from cst_offline_training_management where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>