<template>
  <div class="approved">
    <i v-if="value == 0" class="el-icon-question"></i>
    <i v-if="value == 1" class="el-icon-warning"></i>
    <i v-if="value == 2" class="el-icon-success"></i>
    <i v-if="value == 3" class="el-icon-error"></i>
    <h2 v-if="value == 0">
      未查询到申请记录！
      <h5>如已申请，请联系中心客服</h5>
    </h2>
    <h2 v-if="value == 1">平台审核中！</h2>
    <h2 v-if="value == 2">恭喜您！通过审核！</h2>
    <h2 v-if="value == 3">审核被拒绝，请重新申请！</h2>
  </div>
</template>

<script>
export default {
  data() {
    return {
      value: 0,
    }
  },
  methods: {
    setValue(value) {
      this.value = value
    },
  }
};
</script>

<style lang="scss" scoped>
.approved {
  text-align: center;
  ::v-deep .el-icon-success {
    color: #67c23a;
    font-size: 85px;
  }
  .el-icon-warning {
    color: #e6a23c;
    font-size: 85px;
  }
  .el-icon-error {
    color: red;
    font-size: 85px;
  }
  .el-icon-question {
    color: gainsboro;
    font-size: 85px;
  }
  h2 {
    font-weight: bold;
    margin-top: 30px;
  }
}
</style>