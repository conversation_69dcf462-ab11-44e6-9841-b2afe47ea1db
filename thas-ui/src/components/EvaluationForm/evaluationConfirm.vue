<!--  -->
<template>
  <div class='evaluation-audit evaluation' v-loading="submitLoading"  >
    <el-button v-if="showForm && !readonly" class="objection" size="small" type="text"
      @click="showForm = false">取消提问</el-button>
    <el-form ref="evaluationForm" :model="formData" :label-width="labelWidth" :rules="rules">
      <el-form-item v-if="!showForm && !readonly" class="form-item" label=" " style="margin:0;">
        <el-button type="danger" size="mini" @click="showFormChange" style="margin:auto;">有疑问</el-button>
      </el-form-item>
      <el-form-item v-if="showForm || readonly" class="form-item" :class="{ readonly: !modifying && readonly }"
        label="医院是否提出疑问：" prop="autResult">
        <dict-span :noTag='true' :key="checkSummaryOptions.length + Number(formData.autResult)"
          :value="formData.autResult" :options="yNOptions" type="default"></dict-span>
      </el-form-item>
      <el-form-item v-if="showForm || readonly" class="form-item" :class="{ readonly: !modifying && readonly }"
        label="错误/需要注意/澄清的地方：" prop="autDesc" :rules="autDescRule">
        <el-input v-if="modifying && !readonly" type="textarea" v-model="formData.autDesc" placeholder="请填写错误/需要注意/澄清的地方"
          :rows="4" :maxlength="descMaxLength"></el-input>
        <span v-else>{{ formData.autDesc || '--' }}</span>
      </el-form-item>
      <el-form-item class="form-item" label=" " v-if="(showForm || readonly) && modifying">
        <el-button v-if="modifying" type="primary" @click="submit">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import mixin from "./mixin";

const defaultForm = {
  autResult: '',
  autDesc: ''
}
export default {
  name: 'EvaluationConfirm',
  components: {},
  mixins: [mixin],
  props: {},
  watch: {
    showForm() {
      if (!this.showForm) {
        this.reply();
      }
    }
  },
  data() {
    return {
      showForm: false
    };
  },

  computed: {
    autDescRule() {
      return [{
        required: this.formData.autResult == 2,
        message: '请填写错误/需要注意/澄清的地方',
        trigger: 'blur'
      }]
    },
  },

  created() {
    this.autResultMessage = '请选择医院是否提出疑问'
    this.autDescMessage = '请填写错误/需要注意/澄清的地方'
  },

  mounted() {
    this.formData.autResult = 2

    if (!this.aut || this.aut.rejected) {
      this.modifying = true;
    }
    if (this.aut && this.aut.rejected) {
      this.formData.autDesc = '';
      this.$refs.evaluationForm.clearValidate();
    }
  },

  methods: {
    submit() {
      const evaluationForm = this.$refs.evaluationForm;
      if (evaluationForm && evaluationForm.validate) {
        evaluationForm.validate(valid => {
          if (!valid) return;
          this.submitEmit()
        })
      }
    },

    // submitReject() {
    //   this.submitEmit_data({
    //     data: {},
    //     clauseId: this.clause.clauseId,
    //     submitType: !this.isSecond ? this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_M_SKIP : this.AutSaAudCurrentStatusEnum.TR_CLAUSE_M_SKIP,
    //     status: this.status
    //   })
    // },

    showFormChange() {
      this.showForm = true;
      this.bus.$emit('heightChange')
    },
    clearDesc() {
      this.formData.autDesc = '';
    }
  }
}

</script>
<style lang='scss'></style>
