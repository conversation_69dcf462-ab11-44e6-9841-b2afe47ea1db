package com.thas.web.domain;

import com.thas.common.annotation.Excel;
import com.thas.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 对应证书关联对象 certificate_ability
 * 
 * <AUTHOR>
 * @date 2022-01-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CertificateAbility extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增id */
    private Long id;

    /** 医院认证使用applyNo，评审员使用账户号 */
    @ApiModelProperty(value = "医院认证使用applyNo，评审员使用账户号 ")
    private String processCode;

    /** 字典类型 */
    @ApiModelProperty(value = "字典类型 ", required = true)
    private String abilityDictType;

    /** 字典对应键值 */
    @ApiModelProperty(value = "字典对应键值 ", required = true)
    private String abilityDictValue;

    /** 对应学习名称 */
    @ApiModelProperty(value = "对应学习名称 ", required = true)
    private String abilityName;

    /** 对应附件id */
    @ApiModelProperty(value = "对应附件id ", required = true)
    private Long fileId;

    /** 对应到期日 */
    @ApiModelProperty(value = "对应到期日 ", required = true)
    private String expireDate;

    /** 状态:1可用 2不可用 */
    @ApiModelProperty(value = "状态:1可用 2不可用", required = true)
    private Integer status;

    /** 创建者 */
    @ApiModelProperty(value = "创建者")
    private String creator;

    /** 更新者 */
    @ApiModelProperty(value = "更新者")
    private String updater;

}
