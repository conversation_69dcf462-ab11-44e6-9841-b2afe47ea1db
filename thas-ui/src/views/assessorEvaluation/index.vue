<template>
  <div class="assessor-evaluation">
    <template v-if="isTrainees">
      <TraineesIndex></TraineesIndex>
    </template>
    <template v-else>
      <el-form ref="form" :model="queryData" inline label-width="130px">
        <el-form-item label="医院名称：" prop="distributeHospitalName">
          <el-input v-model.trim="queryData.distributeHospitalName" :maxlength="20" placeholder="请输入医院名称"></el-input>
        </el-form-item>
        <el-form-item label="状态：" prop="distributeStatus">
          <el-select v-model.trim="queryData.distributeStatus" placeholder="请选择状态" clearable filterable>
            <el-option v-for="item in progressOptions" :key="item.dictValue" :label="item.dictLabel"
              :value="item.dictValue">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-button type="primary" size="small" @click="query">
            查 询
          </el-button>
          <el-button type="primary" size="small" @click="reset">
            重 置
          </el-button>
        </el-form-item>
      </el-form>
      <el-table :data="dataSource" border stripe v-loading="loading">
        <el-table-column label="操作" align="center" width="150">
          <template slot-scope="scope">
            <div class="table-btn">
              <!-- <el-button type="text" @click="toShowHospital(scope.row.autCode)" v-if="showDetailBtn(scope.row)">自评详情</el-button> -->
              <div v-if="preliminary">
                <el-button type="text" @click="
                  toReview(
                    scope.row.autCode,
                    scope.row.distributeClauseIdList,
                    scope.row.autCsId
                  )
                " v-if="showFDetailBtn(scope.row)">我的评价</el-button>
                <el-button v-if="scope.row.isLeader == 1" type="text" @click="showGroup(scope.row)">小组进度</el-button>
              </div>
              <div v-else>
                <el-button type="text" @click="
                  toReview2(
                    scope.row.autCode,
                    scope.row.status
                  )
                " v-if="showSDetailBtn(scope.row)">我的审核</el-button>
                <!-- <el-button v-if="showGReport(scope.row)" type="text" @click="generateReport(scope.row)">提交报告</el-button> -->
                <el-button v-if="showSReport(scope.row)" type="text" @click="showReport(scope.row)">查看报告结果</el-button>
                <el-button v-if="scope.row.isLeader == 1" type="text" @click="showGroup(scope.row)">小组管理</el-button>
                <!-- <el-button type="text" v-if="revision(scope.row)" @click="toRevision(scope.row.autCode)">组员修订情况</el-button> -->
              </div>
              <el-button v-if="scope.row.reviewReportPdfFileId" type="text"
                @click="downloadReport(scope.row)">评审报告下载</el-button>
              <el-button v-if="scope.row.isLeader == 1 && preliminary == 0 && showTemporaryReport(scope.row)" type="text"
                @click="downloadTemReport(scope.row)">预审核报告下载</el-button>
              <el-button v-if="scope.row.isLeader == 1 && preliminary == 0 && showTemporaryReport(scope.row)" type="text"
                @click="previewReport(scope.row)">预审核报告预览</el-button>
              <!-- <el-button v-if="scope.row.reviewReportPdfFileId" type="text" @click="createReport(scope.row)">生成新报告 测试用</el-button> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column label="医院名称" align="center" prop="hospitalName">
        </el-table-column>
        <!--  -->
        <el-table-column label="自评完成时间" align="center" prop="saAudSubmitTime">
          <template slot-scope="scope">
            {{ scope.row.saAudSubmitTime || "--" }}
          </template>
        </el-table-column>
        <!--  -->
        <el-table-column v-if="preliminary" label="待评价" align="center" prop="waitAudNum">
        </el-table-column>
        <el-table-column v-if="preliminary" label="待组长审核" align="center" prop="waitLeaderAudNum">
        </el-table-column>
        <el-table-column v-if="preliminary" label="组长" align="center" prop="leaderName">
          <template slot-scope="scope">
            {{ scope.row.leaderName || "--" }}
          </template>
        </el-table-column>
        <!--  -->
        <el-table-column v-if="!preliminary" label="评审员" align="center" prop="memberName">
          <template slot-scope="scope">
            <div v-for="(name, index) in memberName(scope.row.memberName)" :key="index">{{ name }}</div>
          </template>
        </el-table-column>
        <el-table-column v-if="!preliminary" label="待复查" align="center" prop="waitLeaderAudNum">
        </el-table-column>
        <!--  -->
        <el-table-column label="评审完成时间" align="center" prop="audSecondSubmitTime">
          <template slot-scope="scope">
            {{ scope.row.audSecondSubmitTime || "--" }}
          </template>
        </el-table-column>
        <el-table-column label="评审安排与流程" align="center" prop="audSecondCycle">
          <template slot-scope="scope">
            {{ scope.row.audSecondCycle ? scope.row.audSecondCycle.replace(',', ' ~ ') : "--" }}
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="hospitalName">
          <template slot-scope="scope">
            {{ AutSaAudCurrentStatusEnum[scope.row.status] }}
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize"
        @pagination="query" />
      <dict-span :dictType="processDictType" value="0" v-show="false"></dict-span>
      <el-dialog :title="groupTitle" :visible.sync="groupDialogVisible" width="80%">
        <el-form :inline="true" :model="groupForm" class="demo-form-inline" label-width="100px">
          <el-form-item label="评审医院：">
            {{ groupForm.hospitalName || "--" }}
          </el-form-item>
          <el-form-item label="评审时间：" style="margin-left: 100px">
            {{ groupForm.audSecondSubmitTime || "--" }}
          </el-form-item>
        </el-form>
        <el-table :data="groupDataSource" border stripe>
          <el-table-column label="评审员" align="center" prop="name">
            <template slot-scope="scope">
              <span>{{ scope.row.name
              }}{{ scope.row.leaderIs == 1 ? "(组长)" : "" }}</span>
            </template>
          </el-table-column>

          <el-table-column :label="`待评价${preliminary ? '' : '(复查)'}`" align="center">
            <template slot-scope="scope">
              <span> {{ scope.row.pendingReviewCount }}款 </span>
              <span v-if="scope.row.leaderIs == 1">
                ，待复查{{ scope.row.leaderPendingReviewCount }}款
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="`已评价${preliminary ? '' : '(复查)'}`" align="center">
            <template slot-scope="scope">
              <span> {{ scope.row.censoredCount }}款 </span>
              <span v-if="scope.row.leaderIs == 1">
                ，已复查{{ scope.row.leaderCensoredCount }}款
              </span>
            </template>
          </el-table-column>
          <el-table-column label="共计评价" align="center">
            <template slot-scope="scope">
              <span> {{ scope.row.leaderPendingReviewCount + scope.row.leaderCensoredCount }}款 </span>
            </template>
          </el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
          <el-button @click="groupDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="groupDialogVisible = false">确 定</el-button>
        </span>
      </el-dialog>
    </template>
  </div>
</template>

<script>
import DictSpan from "@/components/DetailMessage/dictSpan.vue";
import store from "@/store";
import request, { download, downloadPDF } from "@/utils/request";
import { Loading } from "element-ui";
import TraineesIndex from "./traineesIndex.vue";
export default {
  name: "AssessorEvaluationList",
  data() {
    return {
      queryData: {
        distributeHospitalName: "",
        distributeStatus: "",
        pageSize: 10,
        pageNum: 1,
      },
      url: {
        list: "/aut/sa/aud/queryList",
      },
      total: 0,
      dataSource: [],
      groupForm: {},
      preliminary: true,
      groupDialogVisible: false,
      groupDataSource: [],
      groupTitle: "小组进度",
      loading: false,
      // pageType: ''
    };
  },

  components: {
    DictSpan,
    TraineesIndex
  },

  computed: {
    memberName() {
      return str => {
        return str.split(',')
      }
    },
    processDictType() {
      return this.preliminary
        ? "inspector_first_progress_status"
        : "inspector_second_progress_status";
    },
    showDetailBtn() {
      return (row) => {
        return (
          row.autCode &&
          row.status >= this.AutSaAudCurrentStatusEnum.WAIT_AUD_FIRST_TRIAL
        );
      };
    },
    isTrainees() {
      return this.$store.getters.roles.includes("trainees_assessor")
    },
    showFDetailBtn() {
      return (row) => {
        let statuses = [
          this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_SUM_PASS,
          this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_INQUIRE_PROC,
          // this.AutSaAudCurrentStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW,
          this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_REJECT,
        ];
        let flag = true;
        if (row.rejectIds && row.rejectIds.length) {
          flag = row.rejectIds.map(id => {
            return row.distributeClauseIdList.includes(id);
          }).some(flag => flag == true)
        }
        return statuses.includes(row.status) && flag
      };
    },

    showSDetailBtn() {
      return (row) => {
        let statuses = [
          this.AutSaAudCurrentStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW,
          this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC,
          this.AutSaAudCurrentStatusEnum.WAIT_AUD_SECOND_TRIAL_SUM,
        ];
        return statuses.includes(row.status);
      };
    },

    showGReport() {
      return ({ status }) => {
        return this.AutSaAudCurrentStatusEnum.AUD_PASS === status ||
          this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_SUM_REJECT === status ||
          this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_SUM_CONDITIONAL_PASS === status;
      };
    },

    showSReport() {
      return ({ reviewReportPdfFileId, autSaAudReport, status }) => {
        let statues = [
          this.AutSaAudCurrentStatusEnum.AUD_REPORT_CONFIRM,
          this.AutSaAudCurrentStatusEnum.REVIEW_PROCESS_FINISH,
          this.AutSaAudCurrentStatusEnum.WAIT_REPORT,
          this.AutSaAudCurrentStatusEnum.REPORT_FINISH,
        ]
        return autSaAudReport && reviewReportPdfFileId && !statues.includes(status);
      };
    },

    showTemporaryReport() {
      return ({ isLeader, status }) => {
        let statuses = [
          // 第一次复查
          this.AutSaAudCurrentStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW,
          this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC,
          this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_REJECT,
        ]
        return isLeader == 1 && statuses.includes(status);
      }
    },

    showRejectBtn() {
      return (row) => {
        let status = Number(row.status);
        //  return (
        //   row.autCode && status == this.AutSaAudCurrentStatusEnum.
        // )
        return false;
      };
    },

    progressOptions() {
      return this.AutSaAudCurrentStatusEnum.getStatus()
    },


    revision() {
      return ({ isLeader, status }) => {
        let st = [
          this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_REJECT,
          this.AutSaAudCurrentStatusEnum.MODIFIED_TERMS_FIRST_PROC,
          this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW,
        ]
        return isLeader == 1 && st.includes(status)
      }
    }
  },

  mounted() {
    if (!this.isTrainees) {
      this.preliminary = this.$route.query.preliminary != 0;
      this.groupTitle = this.preliminary ? "小组进度" : "小组管理";
      this.initDicts();
      this.query();
    }
  },

  methods: {
    initDicts() { },

    reset() {
      this.$refs["form"].resetFields();
      this.query();
    },

    query() {
      this.loading = true;
      let data = {
        accountId: store.getters.userId + "",
        ...this.queryData,
        // type: this.preliminary ? 5 : 6,
        pageType: this.preliminary ? "sr_clause" : "sr_v_clause",
      };
      request({
        url: this.url.list,
        method: "post",
        data,
      }).then((res) => {
        if (res.code == 200) {
          this.dataSource = res.rows;
        }
      }).catch(() => {
        this.dataSource = []
      }).finally(() => {
        this.loading = false;
      });
    },
    toShowHospital(autCode) {
      this.$router.push({
        path: "/fAssessor/hospitalSelfEvaluationIndex",
        query: {
          readOnly: true,
          isLeader: 0,
          showOnlyHospital: true,
          autCode,
          type: this.AutSaAudSubmitTypeEnum.AUD_SECOND_TRIAL_INQUIRE_PROC,
        },
      });
    },
    toReview(autCode) {
      this.$router.push({
        path: "/fAssessor/fAssessorIndex",
        query: {
          autCode,
          type: "sr_clause",
        },
      });
    },

    toReview2(autCode, status) {
      let path = status === this.AutSaAudCurrentStatusEnum.WAIT_AUD_SECOND_TRIAL_SUM ? "/fAssessor/sAssessorIndex" : "/fAssessor/evaluationAudit"
      this.$router.push({
        path,
        query: {
          autCode,
          type: "sr_v_clause",
        },
      });
    },

    generateReport(row) {
      let loading = Loading.service({
        lock: true,
        text: "生成评审报告中",
        spinner: "el-icon-loading",
      });
      request({
        url: "/aut/sa/aud/submitAutSaAud",
        method: "post",
        data: {
          submitType: this.AutSaAudCurrentStatusEnum.SR_REPORT_SUBMIT,
          autCode: row.autCode,
          accountId: store.getters.userId + "",
          autSaAudLists: [
            {
              autResult: "0",
              autDesc: "--",
            },
          ],
        },
      })
        .then((res) => {
          this.$message({
            type: "success",
            message: "报告已生成",
          });
          this.query();
        })
        .finally(() => {
          loading && loading.close();
        });
    },

    showReport({ autCode, autSaAudReport }) {
      let name =
        autSaAudReport.autSaAudResult == 1
          ? "BaseClausePass"
          : "BaseClauseReject";

      this.$router.push({
        name,
        query: {
          accountId: this.$store.getters.userId,
          pageType: 'sr_v_clause',
          autCode
        }
        // params: {
        //   autSaAudReport,
        // },
      });
    },

    downloadReport({ reviewReportPdfFileId, reviewReportPdfFileName }) {
      download("/common/downloadFile", { fileId: reviewReportPdfFileId }, reviewReportPdfFileName);
    },

    createReport({ autCode }) {
      downloadPDF({
        filename: `测试评审报告.pdf`,
        autCode,
        ftlTemplateCode: 'review_report',
        checkStatus: "N"
      })
    },

    showGroup(row) {
      if (row) {
        this.$set(this, "groupDataSource", row.groupProgressList);
        this.$set(this, "groupForm", {
          hospitalName: row.hospitalName,
          audSecondSubmitTime: row.audSecondSubmitTime,
        });
      }
      this.groupDialogVisible = true;
    },

    downloadTemReport({ autCode }) {
      request({
        url: '/aut/sa/aud/getTemReviewReportFileInfo/' + autCode,
        method: 'get'
      }).then(res => {
        let { fileId, fileName } = res.data
        console.log(res);
        download("/common/downloadFile", { fileId }, fileName);
      })
    },
    previewReport({ autCode }) {
      request({
        url: '/aut/sa/aud/getTemReviewReportFileInfo/' + autCode,
        method: 'get'
      }).then(res => {
        if (res && res.data && res.data.url) {
          window.open(res.data.url, '_blank')
        }
      })
    },

    toRevision(autCode) {
      this.$router.push({
        path: "/fAssessor/revision",
        query: { autCode },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.assessor-evaluation {
  padding: 12px;
  background-color: #fff;


  ::v-deep .table-btn {
    .el-button+.el-button {
      display: block;
      margin: 0 auto;
    }
  }
}
</style>
