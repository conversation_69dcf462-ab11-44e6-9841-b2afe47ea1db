<template>
  <el-card v-loading="loading" class="questionnaire-manage">
    <el-row :gutter="24">
      <el-col :span="24" :xs="24">
        <el-form ref="queryForm" :model="queryData" size="small" label-width="80px" inline>
          <el-form-item label="问卷名称" prop="title">
            <el-input v-model.trim="queryData.title" placeholder="请输入问卷名称" clearable :style="{ width: '100%' }" maxlength="128"></el-input>
          </el-form-item>
          <el-form-item label="问卷类型" prop="type">
            <el-select v-model.trim="queryData.type" placeholder="请选择问卷类型" clearable filterable>
              <el-option v-for="classifyItem in classifyOption" :key="classifyItem.dictValue" :value="classifyItem.dictValue" :label="classifyItem.dictLabel"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model.trim="queryData.status" placeholder="请选择状态" clearable filterable>
              <el-option v-for="statusItem in questionnaireStatusOptions" :key="statusItem.dictValue" :value="statusItem.dictValue" :label="statusItem.dictLabel"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="search">
              查 询
            </el-button>
            <el-button type="primary" size="small" @click="resetForm">
              重 置
            </el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="toAddQuestionnaire">添加问卷</el-button>
        <el-button type="primary" icon="el-icon-plus" size="small" @click="toAddFeedback">添加反馈表</el-button>
      </el-col>
    </el-row>
    <el-row :gutter="24" style="margin-top: 24px">
      <el-col :span="24">
        <el-table :data="dataSource" border>
          <el-table-column align="center" label="操作" width="160px">
            <template slot-scope="scope">
              <el-button type="text" v-if="scope.row.answerNum === 0" @click="edit(scope.row)">编辑</el-button>
              <el-button v-if="scope.row.status === 0" type="text" @click="publish(scope.row)">发布</el-button>
              <el-button v-else-if="scope.row.status === 1" type="text" @click="publish(scope.row)">停止</el-button>
              <el-button type="text" v-if="scope.row.status === 1 && scope.row.type === 5" @click="issue(scope.row)">下发</el-button>
              <el-button type="text" v-if="scope.row.type !== 1 && scope.row.type !== 2 && scope.row.type !== 3" @click="issueDetail(scope.row)">下发详情</el-button>
              <el-button type="text" v-if="scope.row.answerNum === 0" @click="del(scope.row)">删除</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" label="问卷名称" prop="title"></el-table-column>
          <el-table-column align="center" label="问卷类型" prop="type">
            <template slot-scope="scope">
              {{ type(`${scope.row.type}`) }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="答卷" prop="answerNum">
            <template slot-scope="scope">
              <span>{{ scope.row.answerNum + "份" }}</span>
              <!-- <el-button v-else type="text" @click="toQuestionnaireAnalysis(scope.row)">{{ scope.row.answerNum }}份&nbsp;&nbsp;&nbsp;查看答卷</el-button> -->
            </template>
          </el-table-column>
          <el-table-column align="center" label="状态" prop="status">
            <template slot-scope="scope">
              {{ status(`${scope.row.status}`) }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="创建时间" prop="createTime"></el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query" />
      </el-col>
    </el-row>

    <el-dialog class="issue-dialog" title="下发反馈表" :visible.sync="showDialog" destroy-on-close :close-on-click-modal="false" width="800px">
      <div class="issue-tip">请选择需要下发反馈表的评审学员和对应的培训教员 (如评审学员有已下发未填写的反馈表，下拉选项内将不展示该评审学员)。</div>
      <el-form class="issue-form" ref="form" :model="formData" label-width="100px" :rules="rules">
        <div class="form-line" v-for="(issueItem, index) in formData.issueList" :key="index">
          <el-form-item label="评审学员：" :prop="'issueList.' + index + '.traineesAssessorId'" :rules="{required: true, message: '评审学员不能为空', trigger: 'blur'}">
            <el-select v-model.trim="issueItem.traineesAssessorId" filterable clearable @change="assessorChange(issueItem.traineesAssessorId, index)" placeholder="请选择评审学员" style="width: 230px">
              <el-option v-for="(item, index) in assessorList" :disabled="item.disabled" :key="index" :label="item.traineesAssessorName" :value="item.traineesAssessorId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="培训教员：" :prop="'issueList.' + index + '.trainerSeleList'" :rules="{required: true, message: '培训教员不能为空', trigger: 'blur'}">
            <el-select v-model.trim="issueItem.trainerSeleList" filterable clearable multiple placeholder="请选择培训教员" style="width: 230px">
              <el-option v-for="(item, index) in trainerInfoOptions" :key="index" :label="item.nickName" :value="item.userId"></el-option>
            </el-select>
          </el-form-item>
          <div class="icon-btn">
            <i class="el-icon-circle-plus plus" @click="insertIssue"></i>
            <i class="el-icon-delete delete" v-if="index !== 0" @click="deleteIssue(index)"></i>
          </div>
        </div>
      </el-form>
      <div class="msg">{{ sendSubmitMsg }}</div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="showDialog = false">返 回</el-button>
        <el-button type="primary" size="small" @click="submit">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog class="issue-detail-dialog" title="下发详情" :visible.sync="showDetailDialog" destroy-on-close :close-on-click-modal="false" width="800px">
      <div class="table-container">
        <el-table :data="sendDetailList" border :span-method="handleSpanMethod" key="sendDetail">
          <el-table-column label="序号" align="center" prop="order" width="100"></el-table-column>
          <el-table-column label="反馈表名称" align="center" prop="title"></el-table-column>
          <el-table-column label="下发时间" align="center" prop="createTime"></el-table-column>
          <el-table-column label="下发人员" align="center" prop="needName"></el-table-column>
          <el-table-column label="是否填写" align="center" width="100">
            <template slot-scope="scope">
              <span :class="scope.row.fillStatus == 1 ? '' : 'msg'">{{ scope.row.fillStatus == 1 ? '是' : '否' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="showDetailDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
  </el-card>
</template>

<script>
import request from "@/utils/request"
export default {
  name: "QuestionnaireManage",
  data() {
    return {
      dataSource: [],
      queryData: {
        title: "",
        type: null,
        status: null,
        pageSize: 10,
        pageNum: 1
      },
      total: 0,
      questionnaireStatusOptions: [],
      questionnaireClassifyOptions: [],
      feedbackClassifyOptions: [],
      addForm: {
        name: '',
        content: '',
        type: null,
        sendEmail: null,
        status: null,
        role: null
      },
      props: { multiple: true },
      loading: false,
      // 下发
      curRow: {},
      assessorList: [],
      trainerInfoOptions: [],
      showDialog: false,
      formData: {
        issueList: [{
          resultId: null,
          traineesAssessorId: '',
          traineesAssessorName: '',
          trainerSeleList: [],
          trainerInfoList: []
        }]
      },
      rules: {},
      sendSubmitMsg: '',
      // 下发详情
      showDetailDialog: false,
      sendDetailList: [],
      spanArr: [],
      pos: 0
    }
  },
  computed: {
    status() {
      return (value) => {
        const status = this.questionnaireStatusOptions.find(
          (status) => status.dictValue == value
        )
        return status ? status.dictLabel : value
      }
    },
    type() {
      return (value) => {
        const type = this.classifyOption.find(
          (type) => type.dictValue == value
        )
        return type ? type.dictLabel : value
      }
    },

    isNormal() {
      return type => {
        if (!type) return null;
        return this.questionnaireClassifyOptions.some(({ dictValue }) => dictValue == type);
      }
    },

    classifyOption() {
      return [...this.questionnaireClassifyOptions, ...this.feedbackClassifyOptions]
    }
  },
  created() {
    this.initDicts()
    this.query()
    // 获取培训教员列表
    this.getTeachers()
  },
  methods: {
    initDicts() {
      this.getDicts("questionnaire_status").then((response) => {
        this.questionnaireStatusOptions = response.data
      })
      this.getDicts("questionnaire_classify").then((response) => {
        this.questionnaireClassifyOptions = response.data
      })
      this.getDicts("feedback_classify").then((response) => {
        this.feedbackClassifyOptions = response.data;
      });
    },
    search() {
      this.queryData.pageNum = 1
      this.query()
    },
    query() {
      this.loading = true
      request({
        url: '/system/questionnaire/list',
        method: "get",
        params: this.queryData,
      }).then((res) => {
        this.loading = false
        if (res.code != 200) return
        this.dataSource = res.rows
        this.total = res.total
      }).catch((error) => {
        // console.log(error)
        this.loading = false
      })
    },
    resetForm() {
      this.$refs["queryForm"].resetFields()
      this.queryData.pageNum = 1
      this.query()
    },
    edit(data) {
      let flag = this.isNormal(data.type)
      if (flag == null) {
        return;
      }

      if (flag) {
        this.$router.push({
          path: '/learningMaterials/editQuestionnaire',
          query: {
            id: data.id
          }
        })
      } else {
        this.$router.push('/learningMaterials/toAddFeedback/' + data.id)
      }
    },
    publish(data) {
      const content = data.status === 0 ? '确定发布此问卷？' : data.status === 1 ? '此操作将停止发布该问卷, 是否继续？' : ''
      this.$confirm(content, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let message = '发布成功！'
        if (data.status === 1) {
          message = '停止成功！'
        }
        request({
          url: '/system/questionnaire/status',
          method: 'get',
          params: {
            status: data.status == 1 ? '0' : '1',
            id: data.id
          },
        }).then((res) => {
          if (res.code != 200) {
            this.$message({
              type: 'error',
              message: res.message || '系统错误，请稍后再试。'
            })
          } else {
            this.$message({
              type: 'success',
              message
            })
            this.query()
          }
        })
      }).catch(() => { })
    },
    del(data) {
      this.$confirm('此操作将永久删除该问卷, 是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        request({
          url: '/system/questionnaire/' + data.id,
          method: 'delete'
        }).then((res) => {
          if (res.code !== 200) {
            this.$message({
              type: 'success',
              message: res.message || '系统错误，请稍后再试。'
            })
            return
          }
          this.$message({
            type: 'success',
            message: '删除成功！'
          })
          this.query()
        })
      }).catch(() => { })
    },
    toAddQuestionnaire() {
      this.$router.push({ path: '/learningMaterials/addQuestionnaire' })
    },
    toAddFeedback() {
      this.$router.push({ path: '/learningMaterials/toAddFeedback' })
    },
    toQuestionnaireAnalysis(data) {
      this.$router.push({
        path: '/learningMaterials/questionnaireAnalysis',
        query: { id: data.id }
      })
    },
    getSendInfo(row) {
      return request({
        url: '/system/questionnaire/send/info',
        method: 'get',
        params: {
          id: row.id
        }
      })
    },
    getTeachers() {
      request({
        url: '/training-evaluate-result/trainer-valid-data-query',
        method: 'get',
        params: {
          roleName: '评审员'
        }
      }).then(res => {
        this.trainerInfoOptions = res.data.find(({ roleName }) => roleName == '评审员')?.sysUserList || []
      })
    },
    async issue(row) {
      // 获取下发弹窗下拉框评审人员信息
      let sendInfo = await this.getSendInfo(row);
      if (sendInfo.code == 200) {
        this.assessorList = sendInfo.rows || [];
      }
      this.curRow = row;
      this.sendSubmitMsg = '';
      this.showDialog = true;
      this.formData.issueList = [{
        resultId: null,
        traineesAssessorId: '',
        traineesAssessorName: '',
        trainerSeleList: [],
        trainerInfoList: []
      }]
    },
    assessorChange(val, index) {
      // 已选择的评审学员在下拉列表中禁用
      this.assessorList.forEach(item => {
        let disabled = this.formData.issueList.findIndex(single => single.traineesAssessorId === item.traineesAssessorId) !== -1
        this.$set(item, 'disabled', disabled);
      });
      let currentAssessor = this.assessorList.filter(item => item.traineesAssessorId === val)[0];
      this.formData.issueList[index].resultId = currentAssessor?.resultId || '';
      this.formData.issueList[index].traineesAssessorName = currentAssessor?.traineesAssessorName || '';
      this.formData.issueList[index].trainerSeleList = currentAssessor?.trainerInfoList.map(item => +item.trainerId) || [];
    },
    issueDetail(row) {
      this.sendDetailList = [];
      request({
        url: '/system/questionnaire/send/detail',
        method: 'get',
        params: {
          feedBackType: row.type,
          questionnaireId: row.id
        }
      }).then(res => {
        if (res.code == 200) {
          this.showDetailDialog = true;
          this.sendDetailList = res.rows.map(item => {
            return {
              title: row.title,
              ...item
            }
          }) || [];
          this.getSpanArr(this.sendDetailList)
        }
      })
    },
     // 新增医技服务
    insertIssue() {
      this.formData.issueList.push({
        resultId: null,
        traineesAssessorId: '',
        traineesAssessorName: '',
        trainerSeleList: [],
        trainerInfoList: []
      });
    },
    // 删除医技服务
    deleteIssue(index) {
      this.formData.issueList.splice(index, 1);
    },
    submit() {
      this.$refs.form.validate(vaild => {
        if (vaild) {
          let params = this.formData.issueList.map(item => {
            // 处理培训教员字段的数据
            let trainerInfoList = item.trainerSeleList.map(id => {
              let filterObj = this.trainerInfoOptions.filter(info => info.userId === id)[0];
              return {
                trainerId: filterObj.userId,
                trainerName: filterObj.nickName
              }
            });
            return {
              resultId: item.resultId,
              questionnaireId: this.curRow.id,
              traineesAssessorId: item.traineesAssessorId,
              traineesAssessorName: item.traineesAssessorName,
              trainerInfoList
            }
          })
          request({
            url: '/system/questionnaire/send/submit',
            method: 'post',
            data: params
          }).then(res => {
            if (res.code == 200) {
              if (res.msg) {
                this.sendSubmitMsg = res.msg;
              } else {
                this.showDialog = false;
                this.$message({
                  type: 'success',
                  message:'下发成功！'
                })
              }
            }
          })
        }

      })
    },
    handleSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex < 3) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    getSpanArr(data) {
      this.spanArr = [];　
      let order = 0;
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0;
          order = 1;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].createTime === data[i - 1].createTime) {
            this.spanArr[this.pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.pos = i;
            order++;
          }
        }
        this.$set(data[i], 'order', order);
      }
    },
  }
}
</script>

<style lang='scss' scoped>
.questionnaire-manage {
  .issue-tip {
    margin-bottom: 20px;
    color: #333;
  }
  .issue-form {
    max-height: 380px;
    overflow-y: auto;
    .form-line {
      display: flex;
    }
    .icon-btn {
      display: flex;
      align-items: center;
      width: 80px;
      margin-bottom: 22px;
      .plus {
        font-size: 18px;
        color: #3c81f5;
        margin-left: 10px;
        cursor: pointer;
      }
      .delete {
        font-size: 18px;
        color: #ff4949;
        margin-left: 5px;
        cursor: pointer;
      }
    }
  }
  .msg {
    color: #ff4949;
  }

  .table-container {
    max-height: 400px; /* 设置固定高度 */
    overflow-y: auto; /* 添加垂直滚动条 */
  }
}
</style>
