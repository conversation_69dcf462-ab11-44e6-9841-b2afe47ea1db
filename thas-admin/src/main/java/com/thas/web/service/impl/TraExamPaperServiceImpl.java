package com.thas.web.service.impl;

import com.thas.common.constant.Constants;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.SecurityUtils;
import com.thas.web.domain.TraExamDetails;
import com.thas.web.domain.TraExamPaper;
import com.thas.web.domain.vo.TraExamDetailsVO;
import com.thas.web.dto.TraExamPaperDTO;
import com.thas.web.mapper.TraExamDetailsMapper;
import com.thas.web.mapper.TraExamPaperMapper;
import com.thas.web.service.ITraExamPaperService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import oshi.driver.mac.net.NetStat;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 考卷Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
@Service
public class TraExamPaperServiceImpl implements ITraExamPaperService {
    @Autowired
    private TraExamPaperMapper traExamPaperMapper;

    @Autowired
    private TraExamDetailsMapper traExamDetailsMapper;

    /**
     * 查询考卷
     *
     * @param id 考卷主键
     * @return 考卷
     */
    @Override
    public TraExamDetailsVO selectTraExamPaperById(Long id) {
        return traExamPaperMapper.selectTraExamPaperById(id, 1);
    }

    @Override
    public TraExamDetailsVO editTraExamPaperById(Long id) {
        // 如果当前考卷为发布状态，则不允许修改考卷。
        TraExamDetailsVO traExamDetailsVO = traExamPaperMapper.editTraExamPaperById(id);
        Integer status = traExamDetailsVO.getStatus();
        if (Constants.HospitalConstants.NUM_1.equals(status)) {
            throw new ServiceException("当前考卷为发布状态无法编辑！", 500);
        }
        return traExamDetailsVO;
    }

    /**
     * 查询考卷列表
     *
     * @param traExamPaper 考卷
     * @return 考卷
     */
    @Override
    public List<TraExamPaper> selectTraExamPaperList(TraExamPaper traExamPaper) {
        return traExamPaperMapper.selectTraExamPaperList(traExamPaper);
    }

    /**
     * 新增考卷
     *
     * @param traExamPaperDTO 考卷
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertTraExamPaper(TraExamPaperDTO traExamPaperDTO) {
        // 一个学习资源只能有一个考卷有效
        // 保存考卷
        TraExamPaper traExamPaper = dto2TraExamPaper(traExamPaperDTO);
        this.insertTraExamPaper(traExamPaper);

        // 保存考卷详情(题目)
        List<TraExamDetails> traExamDetailsList = parseExamDetails(traExamPaperDTO, traExamPaper.getId());
        if (traExamDetailsMapper.insertBatchExamDetails(traExamDetailsList) <= 0) {
            throw new ServiceException("insert 添加考卷详情失败");
        }
        return 1;
    }

    /**
     * 修改考卷
     *
     * @param traExamPaperDTO 考卷
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateTraExamPaper(TraExamPaperDTO traExamPaperDTO) {
        // 失效原来的记录
        TraExamPaper del = new TraExamPaper();
        del.setId(traExamPaperDTO.getId());
        del.setValidFlag(Constants.HospitalConstants.NUM_2);
        traExamPaperMapper.updateTraExamPaper(del);
        return insertTraExamPaper(traExamPaperDTO);
    }


    /**
     * 通过资源id查询唯一有效的记录
     *
     * @param learnResourceId 资源版本id
     * @return 返回查询结果
     */
    private TraExamPaper selectByLearnResourceIdAndValid(Long learnResourceId) {
        return traExamPaperMapper.selectByLearnResourceIdAndValid(learnResourceId);
    }

    /**
     * 失效数据 将数据中的validFlag改为0
     *
     * @param id tra_exam_paper表中的id
     */
    private void invalidDate(Long id) {
        TraExamPaper traExamPaper = new TraExamPaper();
        traExamPaper.setId(id);
        traExamPaper.setValidFlag(2);
        if (traExamPaperMapper.updateTraExamPaper(traExamPaper) <= 0) {
            throw new ServiceException("更新失败，请刷新页面重试！");
        }
    }

    /**
     * 请求DTO转换数据库对象
     *
     * @param traExamPaperDTO 请求体
     * @return 数据库对象
     */
    private TraExamPaper dto2TraExamPaper(TraExamPaperDTO traExamPaperDTO) {
        TraExamPaper traExamPaper = new TraExamPaper();
        traExamPaper.setLearnResourceId(traExamPaperDTO.getLearnResourceId());
        traExamPaper.setPaperName(traExamPaperDTO.getPaperName());
        traExamPaper.setPaperDiscription(traExamPaperDTO.getPaperDiscription());
        traExamPaper.setTotalScore(traExamPaperDTO.getTotalScore());
        traExamPaper.setPassScore(traExamPaperDTO.getPassScore());
        traExamPaper.setStatus(traExamPaperDTO.getStatus());
        traExamPaper.setCreateId(SecurityUtils.getUsername());
        traExamPaper.setUpdateId(SecurityUtils.getUsername());
        return traExamPaper;
    }

    private void insertTraExamPaper(TraExamPaper traExamPaper) {
        if (traExamPaperMapper.insertTraExamPaper(traExamPaper) <= 0) {
            throw new ServiceException("traExamPaper插入数据失败");
        }
    }

    private List<TraExamDetails> parseExamDetails(TraExamPaperDTO traExamPaperDTO, Long examPaperId) {
        List<TraExamDetails> traExamDetailsList = new ArrayList<>();
        for (TraExamDetails detail : traExamPaperDTO.getDetails()) {
            detail.setExamPaperId(examPaperId);
            traExamDetailsList.add(detail);
        }
        return traExamDetailsList;
    }

    /**
     * 批量删除考卷
     *
     * @param ids 需要删除的考卷主键
     * @return 结果
     */
    @Override
    public int deleteTraExamPaperByIds(Long[] ids) {
         return traExamPaperMapper.deleteTraExamPaperByIds(ids);
    }

    @Override
    public void updateAnswerSheetNum(Long id, String action) {
        traExamPaperMapper.updateAnswerSheetNum(id, action);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateStatus(Long id, Long learnResourceId, Integer status) {
        // 查询是否有历史版本，如果有则失效
        int validFlag = 0;
        if (Constants.HospitalConstants.NUM_1.equals(status)) {
            TraExamPaper traExamPaper = selectByLearnResourceIdAndValid(learnResourceId);
            if (Objects.nonNull(traExamPaper)) {
                // 如果当前学习资源已经有考卷，则先失效
                invalidDate(traExamPaper.getId());
            }
            validFlag = 1;
        }
        return traExamPaperMapper.updateStatus(id, validFlag, status);
    }
}
