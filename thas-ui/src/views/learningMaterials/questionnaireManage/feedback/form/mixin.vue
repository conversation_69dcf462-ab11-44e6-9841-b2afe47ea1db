<template>
  <div class=''></div>
</template>

<script>
import request from "@/utils/request";

export default {
  name: '',
  props: {
    edit: {
      type: Boolean,
      default: () => false
    },

    reviewResultType: {
      type: [String, Number],
      default: () => 0
    },

    options: {
      type: Object,
      default: () => { }
    },
  },
  data() {
    return {
      formData: {
        traineesAssessorId: '',
        name: '',
        gender: '',
        major: '',
        majorDirection: '',
        courseFormat: '',
        courseStartTime: '',
        courseEndTime: '',
        courseTime: [],
        trainerId: '',
        trainerName: '',
        mobile: '',
        email: '',
        applyNo: '',
        hospital: '',
        reviewerList: [],
        trainerInfoList: [],
        virtualName: '',
      },
      reviewerList: [],
      courseTypeOptions: [],
      sexOptions: [],
      majorOptions: [],
      reviewerBaseInfo: {},
      hospitalList: [],
    };
  },

  components: {},

  computed: {
    validateForm() {
      return {};
    },

    courseFormat() {
      let data = this.courseTypeOptions.find(({ dictValue }) => dictValue == this.formData.courseFormat)
      return data ? data.dictLabel : ''
    },

    gender() {
      return (this.sexOptions.find(({ dictValue }) => dictValue == this.formData.gender) || {}).dictLabel || '-'
    },

    major() {
      let major = (this.formData.major || '').split(',')
      let majorDir = JSON.parse(this.formData.majorDirection || '{}')
      let list = major.map(id => {
        let str = ''
        if (id != 5) {
          str = (this.majorOptions.find(({ dictValue }) => dictValue == id) || {}).dictLabel || '-'
          str += majorDir[id] ? `(${majorDir[id]})` : ''
        } else {
          str += majorDir[id] ? `${majorDir[id]}` : ''
        }
        return str
      })
      return list.join(';')
    },

    reviewerNames() {
      let reviewerList = this.options?.reviewerList || [];
      let names = reviewerList.map(({ name, nickName }) => name || nickName);
      let vNames = (this.options?.virtualName || '').split(',')
      return [...names, ...vNames].filter(name => !!name).join(',');
    },

    dateOption() {
      return {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      }
    }
  },

  created() {
    this.initDicts();
    for (const key in this.options) {
      if (this.options[key]) {
        this.formData[key] = this.options[key]
      }
    }

    this.$nextTick(() => {
      if (this.reviewResultType == 6) {
        if (this.$store.getters.roles.includes('hospital')) {
          this.getHospital();
        }
      } else {
        this.getUser();
      }
    })
  },

  methods: {
    initDicts() {
      this.getDicts('course_form').then(res => {
        this.$set(this, 'courseTypeOptions', res.data)
      })

      this.getDicts('sys_user_sex').then(res => {
        this.$set(this, 'sexOptions', res.data)
      })

      this.getDicts('reviewer_major').then(res => {
        this.$set(this, 'majorOptions', res.data)
      })
    },

    getUser() {
      let id = (this.$store.getters.roles.includes('admin') ||
        this.$store.getters.roles.includes('common') ||
        this.$store.getters.roles.includes('common-admin')) ? this.formData.needId : this.$store.getters.userId
      request({
        url: "/reviewer/query/reviewer/detail?time=" + new Date().getTime(),
        method: "post",
        data: { commonId: id },
      }).then(res => {
        let { reviewerBaseInfo = {} } = res;
        this.$set(this.formData, 'gender', reviewerBaseInfo.reviewerGender)
        this.$set(this.formData, 'major', reviewerBaseInfo.majorField)
        this.$set(this.formData, 'majorDirection', reviewerBaseInfo.majorDirection)
        this.$set(this.formData, 'phoneNumber', reviewerBaseInfo.reviewerMobile)
        this.$set(this.formData, 'email', reviewerBaseInfo.reviewerEmail)

      })
    },


    getHospital() {
      request({
        url: "/hospital/query/cur/hos/info?time=" + new Date().getTime(),
        method: "post",
      }).then(reviewerBaseInfo => {
        this.$set(this, 'reviewerBaseInfo', reviewerBaseInfo)
        this.$set(this.formData, 'needName', reviewerBaseInfo.hospitalContacts)
        this.$set(this.formData, 'phoneNumber', reviewerBaseInfo.contactsPhone)
        this.$set(this.formData, 'email', reviewerBaseInfo.contactsEmail)

      })
    },

    getTeachers() {
      request({
        url: '/training-evaluate-result/trainer-valid-data-query',
        method: 'get',
        params: {
          roleName: '评审员'
        }
      }).then(res => {
        this.$set(this, 'reviewerList', res.data.find(({ roleName }) => roleName == '评审员')?.sysUserList || [])
      })
    },


    teacherSelect(value) {
      if (Array.isArray(value) && value.length) {
        let last = value.pop();
        let has = this.reviewerList.some(({ accountId, userId }) => last == accountId || last == userId)
        if (!has) {
          if (isNaN(last)) {
            value.push(last)
          } else {
            this.$message({
              type: 'warning',
              message: '姓名不能为纯数字'
            })
          }
        } else {
          value.push(last)
        }
      }
    },


    async getValue() {
      try {
        return this.formData;
      } catch {
        return null
      }
    }
  }
}

</script>
<style lang='scss'>
div[class$="-basic-form"] {
  padding-top: 10px;
  h1 {
    text-align: center;
  }
  .el-form .el-form-item {
    margin-bottom: 10px !important;

    .el-form-item__label::before {
      content: "";
    }
  }
}
</style>