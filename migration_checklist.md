# 认证自评审核查询功能迁移检查清单

## 迁移前准备 ✓

### 环境检查
- [ ] 确认二期项目的Java版本和Spring Boot版本
- [ ] 确认数据库版本和连接配置
- [ ] 确认Maven/Gradle依赖管理工具
- [ ] 确认包结构和命名规范

### 依赖框架检查
- [ ] BaseController 是否已迁移
- [ ] TableDataInfo 分页响应类是否已迁移
- [ ] BaseDTO 基础实体类是否已迁移
- [ ] ValidGroup 验证分组是否已迁移
- [ ] SecurityUtils 安全工具类是否已迁移
- [ ] SpringContextUtil Spring容器工具类是否已迁移

## 代码迁移 ✓

### 枚举类迁移
- [ ] AutSaAudRoleEnum.java - 认证自评审核角色枚举
- [ ] AutSaAudStatusEnum.java - 认证自评审核状态枚举  
- [ ] AutSaAudResultEnum.java - 认证自评审核结果枚举
- [ ] AutSaAudBusinessCodeEnum.java - 业务代码枚举

### Domain/DTO层迁移
- [ ] AutSaAudQueryDTO.java - 查询请求DTO
- [ ] AutSaAudList.java - 列表响应对象
- [ ] AutSaAud.java - 认证自评审核实体
- [ ] AutSaRelation.java - 认证自评关联实体
- [ ] AutSaAudReport.java - 评审报告实体
- [ ] GroupProgressInfo.java - 小组进度信息
- [ ] AutSaAudVo.java - 认证自评审核VO
- [ ] FileInfoVO.java - 文件信息VO
- [ ] HospitalReviewerVo.java - 医院评审员VO
- [ ] AutSaAudSaveDTO.java - 保存请求DTO
- [ ] MemberRevisionInfo.java - 组员修订信息

### Mapper层迁移
- [ ] AutSaAudMapper.java - 认证自评审核Mapper接口
- [ ] AutSaRelationMapper.java - 认证自评关联Mapper接口
- [ ] CommonProcessMapper.java - 通用流程Mapper接口
- [ ] AutSaAudMapper.xml - 认证自评审核SQL映射文件
- [ ] AutSaRelationMapper.xml - 认证自评关联SQL映射文件
- [ ] CommonProcessMapper.xml - 通用流程SQL映射文件

### Service层迁移
- [ ] BaseProcessService.java - 基础流程服务接口
- [ ] BaseProcessServiceImpl.java - 基础流程服务实现
- [ ] CommonProcessServiceImpl.java - 通用流程服务实现
- [ ] ValidationReviewer01ProcessServiceImpl.java - 验证评审员流程服务
- [ ] IAutSaAudService.java - 认证自评审核服务接口
- [ ] AutSaAudServiceImpl.java - 认证自评审核服务实现
- [ ] IAutSaRelationService.java - 认证自评关联服务接口
- [ ] AutSaRelationServiceImpl.java - 认证自评关联服务实现

### Controller层迁移
- [ ] AutSaAudController.java - 主控制器
- [ ] 调整@RequestMapping路径
- [ ] 调整@ApiOperation注解
- [ ] 验证@Validated注解工作正常

## 数据库迁移 ✓

### 核心业务表
- [ ] aut_sa_relation - 认证自评关联表
- [ ] aut_sa_aud - 认证自评审核表
- [ ] aut_sa_aud_report - 认证自评审核报告表
- [ ] aut_sa_aud_business_data - 认证自评审核业务数据表

### 配置表
- [ ] asa_status_config - 状态配置表
- [ ] aut_sa_aud_business_config - 业务配置表

### 关联表检查
- [ ] hospital_base_info - 医院基本信息表
- [ ] hospital_planned_distribution - 医院计划分配表
- [ ] sys_user - 用户表
- [ ] sys_user_hospital - 用户医院关联表
- [ ] upload_file_info - 文件信息表

### 索引和约束
- [ ] 主键索引
- [ ] 唯一键约束
- [ ] 外键约束（如果有）
- [ ] 业务查询索引

## 配置文件调整 ✓

### MyBatis配置
- [ ] mapper-locations 路径配置
- [ ] type-aliases-package 包路径配置
- [ ] 数据库连接配置

### Spring配置
- [ ] component-scan 包扫描路径
- [ ] 事务管理配置
- [ ] 缓存配置（如果有）

### 应用配置
- [ ] 日志配置
- [ ] 文件上传配置
- [ ] 分页插件配置

## 依赖服务检查 ✓

### 用户相关服务
- [ ] SysUserService - 用户服务
- [ ] ISysRoleService - 角色服务
- [ ] SecurityUtils - 安全工具类

### 业务相关服务
- [ ] CommonService - 通用服务
- [ ] IUploadFileInfoService - 文件服务
- [ ] ICstCertificationStandardsService - 认证标准服务
- [ ] IHospitalBaseInfoService - 医院信息服务

### 工具类
- [ ] DateUtils - 日期工具类
- [ ] StringUtils - 字符串工具类
- [ ] NumberGenUtils - 编号生成工具类

## 测试验证 ✓

### 单元测试
- [ ] Service层方法测试
- [ ] Mapper层SQL测试
- [ ] 工具类方法测试

### 集成测试
- [ ] Controller接口测试
- [ ] 数据库事务测试
- [ ] 权限校验测试

### 功能测试
- [ ] 正常查询流程测试
- [ ] 异常情况处理测试
- [ ] 分页功能测试
- [ ] 权限控制测试

### 性能测试
- [ ] 查询性能测试
- [ ] 并发访问测试
- [ ] 内存使用测试

## 部署验证 ✓

### 环境部署
- [ ] 开发环境部署
- [ ] 测试环境部署
- [ ] 预生产环境部署

### 数据验证
- [ ] 基础数据完整性
- [ ] 业务数据一致性
- [ ] 配置数据正确性

### 接口验证
- [ ] API接口可访问性
- [ ] 返回数据格式正确性
- [ ] 错误处理机制

## 文档更新 ✓

### 技术文档
- [ ] API接口文档
- [ ] 数据库设计文档
- [ ] 部署说明文档

### 业务文档
- [ ] 功能说明文档
- [ ] 用户操作手册
- [ ] 问题排查手册

## 风险控制 ✓

### 回滚准备
- [ ] 数据库备份
- [ ] 代码版本标记
- [ ] 回滚脚本准备

### 监控告警
- [ ] 接口调用监控
- [ ] 数据库性能监控
- [ ] 错误日志监控

### 应急预案
- [ ] 故障处理流程
- [ ] 联系人信息
- [ ] 紧急修复方案
