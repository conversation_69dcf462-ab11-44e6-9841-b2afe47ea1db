<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.AutSaAudBusinessDataMapper">

    <select id="selectAutSaAudBusinessData" parameterType="java.lang.String"
            resultType="com.thas.web.domain.AutSaAudBusinessData">
        select id id,
        aut_code autCode,
        business_code businessCode,
        data data,
        create_id createId,
        create_time createTime,
        update_id updateId,
        update_time updateTime
        from aut_sa_aud_business_data
        where 1 = 1
        <if test="businessCode != null  and businessCode != ''">
            and business_code in
            <foreach collection="businessCode.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="autCodes != null  and autCodes != ''">
            and aut_code in
            <foreach collection="autCodes.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <insert id="saveAutSaAudBusinessData" parameterType="com.thas.web.domain.AutSaAudBusinessData">
        INSERT INTO aut_sa_aud_business_data
            (aut_code, business_code, data)
        VALUES (#{autCode}, #{businessCode}, #{data}) ON DUPLICATE KEY
        UPDATE data=#{data};
    </insert>

    <delete id="deleteAutSaAudBusinessData" parameterType="java.lang.String">
        delete
        from aut_sa_aud_business_data
        where aut_code = #{autCode}
        <if test="businessCode != null  and businessCode != ''">
            and business_code = #{businessCode}
        </if>
    </delete>

    <update id="updateAutSaAudBusinessData" parameterType="com.thas.web.domain.AutSaAudBusinessData">
        update aut_sa_aud_business_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="data != null">data = #{data},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where aut_code = #{autCode}
        and business_code = #{businessCode}
    </update>
</mapper>
