<template>
  <div class="study-experience">
    <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="200px" class="elForm">
      <el-form-item label="是否参加过国际认证/医院等级评审员培训" prop="learnExperience" class="linehight">
        <el-radio-group v-model.trim="formData.learnExperience" size="medium" @change="(value) => clearValue(value, 4)">
          <el-radio v-for="(item, index) in truefalseOptions" :key="index" :label="
            isNaN(item.dictValue) ? item.dictValue : Number(item.dictValue)
          ">{{ item.dictLabel }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formData.learnExperience == 1" label="接受过哪个组织或单位或标准举办的培训" class="linehight"
        prop="organizationList">
        <el-checkbox-group v-model.trim="checkList" size="medium" @change="(value) => clearValue(value, 6)">
          <el-checkbox v-for="(item, index) in organizationOptions" :key="index" :label="
            isNaN(item.dictValue) ? item.dictValue : Number(item.dictValue)
          " style="display: block">
            {{ item.dictLabel }}
            <span v-if="item.dictValue == 0 && checkList.includes(Number(item.dictValue))" style="display: inline-block">
              <el-form-item :prop="`organizationList.${index}.abilityName`" :rules="rules.abilityName">
                <el-input v-model.trim="formData.organizationList[index].abilityName" :maxlength="50"></el-input>
              </el-form-item>
            </span>
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="是否是医院评审评价评审员" prop="hosRevIs">
        <el-radio-group v-model.trim="formData.hosRevIs" size="medium" @change="(value) => clearValue(value, 5)">
          <el-radio v-for="(item, index) in truefalseOptions" :key="index" :label="
            isNaN(item.dictValue) ? item.dictValue : Number(item.dictValue)
          ">{{ item.dictLabel }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formData.hosRevIs == 1" label="如是，具体为" prop="reviewerTypeList">
        <el-checkbox-group v-model.trim="reviewerTypeCheckList" size="medium" @change="(value) => clearValue(value, 7)">
          <el-checkbox v-for="(item, index) in reviewerTypeOptions" :key="index" :label="
            isNaN(item.dictValue) ? item.dictValue : Number(item.dictValue)
          " style="display: block">
            {{ item.dictLabel }}
            <span v-if="item.dictValue == 0 && reviewerTypeCheckList.includes(Number(item.dictValue))"
              style="display: inline-block">
              <el-form-item :prop="`reviewerTypeList.${index}.hosRevOther`" :rules="rules.hosRevOther">
                <el-input v-model.trim="formData.reviewerTypeList[index].hosRevOther" :maxlength="50"></el-input>
              </el-form-item>
            </span>
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <!-- <el-form-item v-if="formData.hosRevIs == 1" label="具体是哪个组织、单位聘用的" prop="organization" class="linehight">
        <el-input v-model.trim="formData.organization" placeholder="请填写具体是哪个组织、单位聘用的" clearable :style="{ width: '50%' }" maxlength="20"></el-input>
      </el-form-item> -->
      <el-form-item label="是否评审过医院" prop="reviewHospitalIs">
        <el-radio-group v-model.trim="formData.reviewHospitalIs" size="medium" @change="(value) => clearValue(value, 1)">
          <el-radio v-for="(item, index) in truefalseOptions" :key="index" :label="
            isNaN(item.dictValue) ? item.dictValue : Number(item.dictValue)
          ">{{ item.dictLabel }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formData.reviewHospitalIs == 1" label="评审年限" prop="reviewHospitalYear">
        <el-input-number controls-position="right" :min="0" :max="99" :precision='0'
          v-model.trim="formData.reviewHospitalYear" placeholder="请填写评审经验年限" clearable
          :style="{ width: '50%', display: 'inline-block' }"></el-input-number>
      </el-form-item>
      <el-form-item v-if="formData.reviewHospitalIs == 1" label="评审次数" prop="reviewHospitalNum">
        <el-input-number controls-position="right" :precision='0' :min="0" :step="1"
          v-model.trim="formData.reviewHospitalNum" placeholder="请填写评审过家多少医院" clearable
          :style="{ width: '50%', display: 'inline-block' }"></el-input-number>
      </el-form-item>
      <el-form-item v-if="formData.reviewHospitalIs == 1" label="专业方向" prop="reviewMajor">
        <el-input v-model.trim="formData.reviewMajor" placeholder="请填写专业方向" clearable
          :style="{ width: '50%', display: 'inline-block' }" :maxlength="50"></el-input>
      </el-form-item>
      <el-form-item label="是否对医院进行过标准/评审辅导" prop="reviewHospitalCoach" class="linehight">
        <el-radio-group v-model.trim="formData.reviewHospitalCoach" size="medium"
          @change="(value) => clearValue(value, 2)">
          <el-radio v-for="(item, index) in truefalseOptions" :key="index" :label="
            isNaN(item.dictValue) ? item.dictValue : Number(item.dictValue)
          ">{{ item.dictLabel }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formData.reviewHospitalCoach == 1" label="辅导场次" prop="coachHospitalNum">
        <el-input-number controls-position="right" :precision='0' :min="0" v-model.trim="formData.coachHospitalNum"
          placeholder="请填写辅导场次" clearable :style="{ width: '50%' }">
        </el-input-number>
      </el-form-item>
      <el-form-item label="是否对评审员进行过培训" prop="trainReviewerIs">
        <el-radio-group v-model.trim="formData.trainReviewerIs" size="medium" @change="(value) => clearValue(value, 3)">
          <el-radio v-for="(item, index) in truefalseOptions" :key="index" :label="
            isNaN(item.dictValue) ? item.dictValue : Number(item.dictValue)
          ">{{ item.dictLabel }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formData.trainReviewerIs == 1" label="培训场次" prop="trainNum">
        <el-input-number controls-position="right" :precision='0' :min="0" v-model.trim="formData.trainNum"
          placeholder="请填写培训场次" clearable :style="{ width: '50%' }">
        </el-input-number>
      </el-form-item>
      <el-form-item v-if="formData.trainReviewerIs == 1" label="具体是哪个组织或单位举办的评审员培训项目" class="linehight"
        prop="trainOrganization">
        <el-input v-model.trim="formData.trainOrganization" placeholder="请填写举办的组织、单位具体是哪个组织或单位举办的评审员培训项目" clearable
          :style="{ width: '50%' }" :maxlength="50"></el-input>
      </el-form-item>
      <el-form-item label="其他相关经历" prop="otherExperience">
        <el-input v-model.trim="formData.otherExperience" type="textarea" placeholder="请填写其他相关经历"
          :autosize="{ minRows: 4, maxRows: 4 }" :style="{ width: '50%' }" :maxlength="500"></el-input>
      </el-form-item>

      <el-form-item label="对培训、工作安排及生活照顾需要中心特别注意的事项" prop="trainWarning">
        <el-input v-model.trim="formData.trainWarning" type="textarea" placeholder="请填写其他相关经历"
          :autosize="{ minRows: 4, maxRows: 4 }" :style="{ width: '50%' }" :maxlength="500"></el-input>
      </el-form-item>

    </el-form>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    info: {
      type: Object,
      default: () => { },
    },
  },
  watch: {
    info: {
      immediate: true,
      deep: true,
      handler() {
        // console.log('我传了一个有值的info', this.info);
        if (this.info && Object.keys(this.info).length) {
          for (const key in this.info) {
            if (Object.hasOwnProperty.call(this.info, key)) {
              this.$set(this.formData, key, this.info[key]);
            }
          }
          if (this.info.checkList instanceof Array) {
            this.$set(this, 'checkList', this.info.checkList)
          }

          if (this.info.reviewerTypeCheckList instanceof Array) {
            this.$set(this, 'reviewerTypeCheckList', this.info.reviewerTypeCheckList)
          }
        }
      },
    },
  },
  data() {
    return {
      formData: {
        learnExperience: "",
        organizationList: [],
        reviewerTypeList: [],
        hosRevIs: "",
        organization: "",
        reviewHospitalIs: "",
        reviewHospitalCoach: "",
        coachHospitalNum: undefined,
        trainReviewerIs: "",
        trainNum: undefined,
        trainOrganization: "",
        otherExperience: "",
        reviewHospitalYear: undefined,
        reviewHospitalNum: undefined,
        reviewMajor: "",
        trainWarning: ''
      },
      rules: {
        learnExperience: [
          {
            required: true,
            message: "是否参加过国际认证/医院等级评审评审员培训不能为空",
            trigger: "change",
          },
        ],
        reviewerTypeList: [
          {
            required: true,
            validator: (rule, value, callback) =>
              this.checkListValidate2(callback),
          }
        ],
        hosRevOther: [{
          required: true,
          message: "请输入",
          trigger: "change",
        }],
        organizationList: [
          {
            required: true,
            validator: (rule, value, callback) =>
              this.checkListValidate(callback),
          },
        ],
        abilityName: [
          {
            required: true,
            message: "请输入培训项目",
            trigger: "change",
          },
        ],
        hosRevIs: [
          {
            required: true,
            message: "是否为评审员不能为空",
            trigger: "change",
          },
        ],
        organization: [
          {
            required: true,
            message: "请填写具体是哪个组织、单位聘用的",
            trigger: "blur",
          },
        ],
        reviewHospitalIs: [
          {
            required: true,
            message: "是否评审过医院不能为空",
            trigger: "change",
          },
        ],
        reviewHospitalCoach: [
          {
            required: true,
            message: "是否对医院进行过标准/评审辅导不能为空",
            trigger: "change",
          },
        ],
        coachHospitalNum: [
          {
            required: true,
            message: "请填写辅导场次",
            trigger: "blur",
          },
        ],
        trainReviewerIs: [
          {
            required: true,
            message: "是否对评审员进行过培训不能为空",
            trigger: "change",
          },
        ],
        trainNum: [
          {
            required: true,
            message: "请填写培训场次",
            trigger: "blur",
          },
        ],
        trainOrganization: [
          {
            required: true,
            message:
              "请填写举办的组织、单位具体是哪个组织或单位举办的评审员培训项目",
            trigger: "blur",
          },
        ],
        reviewHospitalYear: [
          {
            required: true,
            message: "请填写评审经验",
            trigger: "blur",
          },
        ],
        reviewHospitalNum: [
          {
            required: true,
            message: "请填写评审过家多少医院",
            trigger: "blur",
          },
        ],
        reviewMajor: [
          {
            required: true,
            message: "请填写专业方向",
            trigger: "blur",
          },
        ],
      },
      checkList: [],
      reviewerTypeCheckList: [],
      truefalseOptions: [],
      organizationOptions: [],
      reviewerTypeOptions: [],
    };
  },
  computed: {},
  created() { },
  mounted() {
    this.initDicts();
  },
  methods: {
    initDicts() {
      this.getDicts("true_false").then((response) => {
        this.truefalseOptions = response.data;
      });

      this.getDicts("reviewer_type").then((response) => {
        this.reviewerTypeOptions = response.data;
        this.formData.reviewerTypeList = this.reviewerTypeOptions.map(
          (organ) => {
            return {
              reviewerTypeType: organ.dictType,
              reviewerTypeValue: organ.dictValue,
              hosRevOther: organ.dictValue == 0 ? this.info.reviewer_zero_hos_result || "" : organ.dictLabel,
            };
          }
        );
      });

      this.getDicts("reviewer_hold_certificate").then((response) => {
        // console.log('获取 organization');
        this.organizationOptions = response.data;
        this.formData.organizationList = this.organizationOptions.map(
          (organ) => {
            return {
              abilityDictType: organ.dictType,
              abilityDictValue: organ.dictValue,
              abilityName: organ.dictValue == 0 ? this.info.organization_zero_label || '' : organ.dictLabel,
            };
          }
        );
        // console.log(this.formData.organizationList);
      });
    },
    checkListValidate(callback) {
      if (this.checkList.length == 0) callback("请至少选择一项");
      callback();
    },
    checkListValidate2(callback) {
      if (this.reviewerTypeCheckList.length == 0) callback("请至少选择一项");
      callback();
    },

    clearValue(value, type) {
      if (value == 1 && type != 6 && type != 7) return;
      if (type === 1) {
        this.$set(this.formData, 'reviewHospitalYear', undefined)
        this.$set(this.formData, 'reviewHospitalNum', undefined)
        this.$set(this.formData, 'reviewMajor', undefined)
      } else if (type === 2) {
        this.$set(this.formData, 'coachHospitalNum', undefined)
      } else if (type === 3) {
        this.$set(this.formData, 'trainNum', undefined)
        this.$set(this.formData, 'trainOrganization', undefined)
      } else if (type === 4) {
        let hasZero = this.checkList.some(val => val == 0)
        if (hasZero) {
          let orgList = this.formData.organizationList || []
          let org = orgList.find(item => item.abilityDictValue == 0)
          if (org) {
            this.$set(org, 'abilityName', '')
          }
        }
        this.$set(this, 'checkList', [])
      } else if (type === 6) {
        let hasZero = this.checkList.every(val => val != 0)
        if (hasZero) {
          let orgList = this.formData.organizationList || []
          let org = orgList.find(item => item.abilityDictValue == 0)
          if (org) {
            this.$set(org, 'abilityName', '')
          }
        }
      } else if (type === 5) {
        let hasZero = this.reviewerTypeCheckList.some(val => val == 0)
        if (hasZero) {
          let retList = this.formData.reviewerTypeList || []
          let ret = retList.find(item => item.reviewerTypeValue == 0)
          if (ret) {
            this.$set(ret, 'hosRevOther', '')
          }
        }
        this.$set(this, 'reviewerTypeCheckList', [])
      } else if (type === 7) {
        let hasZero = this.reviewerTypeCheckList.every(val => val != 0)
        if (hasZero) {
          let retList = this.formData.reviewerTypeList || []
          let ret = retList.find(item => item.reviewerTypeValue == 0)
          if (ret) {
            this.$set(ret, 'hosRevOther', '')
          }
        }
      }



    },

    async getValue() {
      try {
        const el = await this.$refs["elForm"].validate();
        if (el) {
          const { organizationList, reviewerTypeList, ...data } = this.formData;
          const list = data.learnExperience == 1 ? organizationList.filter((organization) =>
            this.checkList.includes(Number(organization.abilityDictValue))
          ) : [];
          const list2 = data.hosRevIs == 1 ? reviewerTypeList.filter((reviewerType) =>
            this.reviewerTypeCheckList.includes(Number(reviewerType.reviewerTypeValue))
          ) : [];
          return {
            organizationList: list,
            hosRevDetail: list2,
            data,
          };
        }
        return false;
      } catch (error) {
        return false;
      }
    },

    getDraft() {
      const { organizationList, ...data } = this.formData;
      return {
        organizationList,
        data,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.study-experience {
  padding: 24px;

  ::v-deep .el-form.elForm {
    &>.el-form-item.linehight>.el-form-item__label {
      line-height: 18px;
    }

    .el-input-number {
      input {
        text-align: left;
      }

      .el-input-number__decrease,
      .el-input-number__increase {
        display: none;
      }
    }
  }
}
</style>
