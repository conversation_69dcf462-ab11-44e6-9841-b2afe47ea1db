<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.MessageReceiveRecordMapper">

    <resultMap type="MessageReceiveRecord" id="MessageReceiveRecordResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="content"    column="content"    />
        <result property="isRead"    column="is_read"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMessageReceiveRecordVo">
        select id, user_id, content, is_read, del_flag, create_by, create_time, update_by, update_time from message_receive_record
    </sql>

    <select id="selectMessageReceiveRecordList" parameterType="MessageReceiveRecord" resultMap="MessageReceiveRecordResult">
        <include refid="selectMessageReceiveRecordVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="isRead != null  and isRead != ''"> and is_read = #{isRead}</if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectMessageReceiveRecordById" parameterType="Long" resultMap="MessageReceiveRecordResult">
        <include refid="selectMessageReceiveRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertMessageReceiveRecord" parameterType="MessageReceiveRecord" useGeneratedKeys="true" keyProperty="id">
        insert into message_receive_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="isRead != null and isRead != ''">is_read,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="isRead != null and isRead != ''">#{isRead},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMessageReceiveRecord" parameterType="MessageReceiveRecord">
        update message_receive_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="isRead != null and isRead != ''">is_read = #{isRead},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMessageReceiveRecordById" parameterType="Long">
        delete from message_receive_record where id = #{id}
    </delete>

    <delete id="deleteMessageReceiveRecordByIds" parameterType="String">
        delete from message_receive_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertMessageReceiveRecordList" parameterType="list">
        insert  into message_receive_record(user_id,content,create_by,create_time) values
        <foreach collection="list" item="item" separator=",">
            (#{item.userId},#{item.content},#{item.createBy},#{item.createTime})
        </foreach>
    </insert>
</mapper>
