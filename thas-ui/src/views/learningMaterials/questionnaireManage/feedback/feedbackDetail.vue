<template>
  <div class='feedback-detail'>

    <h1 style="text-align:center;color:black;font-weight:bolder">{{title || question.title}}</h1>

    <p>{{description || question.description}}</p>

    <BasicForm :key="type" ref="basicForm" :type="type" :options="options"></BasicForm>

    <div class="question" v-for="(data, index) in list" :key="data.id">
      <h4 class="title">{{toChinese(index + 1)}}、{{data.question}}</h4>

      <div v-for="(question, idx) in data.questionList" :key="idx">
        <h4 class="subTitle" v-if="data.questionList.length > 1">({{toChinese(idx + 1)}})、{{question.subtitle}}</h4>
        <div v-if="question.type == 1">
          <div v-if="question.circulation">
            <el-tabs v-model="select[`${index}${idx}`]" type="border-card" style="box-shadow:none;">
              <el-tab-pane v-for="(user, idxx) in circulation(question.cycleType)" :key="idxx" :label="user.name" :name="`${user.accountId}`">
                <el-table :data="question.questionList" border :span-method="arraySpanMethod">
                  <el-table-column label="序号" type="index" width="80"></el-table-column>
                  <el-table-column :label="question.questionTitle.theme" prop="label" width="220">
                    <template slot-scope="{row}">
                      <pre style="white-space: pre-line;margin:0;">{{row.label}}</pre>
                    </template>
                  </el-table-column>
                  <el-table-column :label="question.questionTitle.score">
                    <template slot-scope="{row}">
                      <span v-if="row.type == 1 || row.type == 3">{{row.value[user.accountId]}}分</span>
                      <span v-else>{{row.comment[user.accountId]}}</span>
                    </template>
                  </el-table-column>
                  <el-table-column :label="question.questionTitle.remark">
                    <template slot-scope="{row}">
                      <span v-if="row.type == 1 || row.type == 3">{{row.comment[user.accountId]}}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </div>
          <el-table v-else :data="question.questionList" border :span-method="arraySpanMethod">
            <el-table-column label="序号" type="index" width="80"></el-table-column>
            <el-table-column :label="question.questionTitle.theme" prop="label" width="220">
              <template slot-scope="{row}">
                <pre style="white-space: pre-line;margin:0;">{{row.label}}</pre>
              </template>
            </el-table-column>
            <el-table-column :label="question.questionTitle.score">
              <template slot-scope="{row}">
                <span v-if="row.type == 1 || row.type == 3">{{row.value}}分</span>
                <span v-else>{{row.comment}}</span>
              </template>
            </el-table-column>
            <el-table-column :label="question.questionTitle.remark">
              <template slot-scope="{row}">
                <span v-if="row.type == 1 || row.type == 3">{{row.comment}}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <span v-else class="answer">{{question.answer}}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { toChinese } from '@/utils/index.js'
import request from "@/utils/request";
import BasicForm from './form/basicForm.vue'

export default {
  name: 'FeedbackDetail',
  props: {
    detail: {
      default: () => { },
      type: Object,
    },

    title: {
      type: String,
      default: () => ''
    },

    description: {
      type: String,
      default: () => ''
    },
  },
  data() {
    return {
      question: {},
      answer: {},
      feed: [],
      basicForm: {},
      list: [],
      origin: [],
      circulationList: [],
      show: false,
      select: {},
      type: '',
    };
  },

  components: {
    BasicForm
  },

  computed: {
    options() {
      return this.basicForm
    },
    valueProp() {
      return (circulation, index, idx, $index, userId) => {
        if (circulation) {
          return `list.${index}.questionList.${idx}.questionList.${$index}.value.${userId}`
        } else {
          return `list.${index}.questionList.${idx}.questionList.${$index}.value`
        }
      }
    },

    commentProp() {
      return (circulation, index, idx, $index, userId) => {
        if (circulation) {
          return `list.${index}.questionList.${idx}.questionList.${$index}.comment.${userId}`
        } else {
          return `list.${index}.questionList.${idx}.questionList.${$index}.comment`
        }
      }
    },

    answerProp() {
      return (index, idx) => {
        return `list.${index}.questionList.${idx}.answer`
      }
    },

    circulation() {
      return type => {
        if (type == 'reviewer') {
          return this.reviewerList
        } else if (type == 'student') {
          return this.traineesList
        } else {
          return []
        }
      }
    }
  },

  async mounted() {
    try {
      if (this.detail) {
        let jsonData = this.detail.jsonData || '{}'
        this.answer = JSON.parse(jsonData)
        this.feed = this.answer.feedData || [];
        this.basicForm = this.answer.basicForm;
        this.type = this.detail.feedBackType || this.detail.type
        this.reviewerList = this.answer.reviewerList || []
        this.traineesList = this.answer.traineesList || []
        this.getPaperDetail()
        this.show = true;
      }
    } catch (error) {
      // console.log(error);
    }
  },

  methods: {
    toChinese,


    getReviewerInfo(autCode) {
      return request({
        url: "/tra/questionnaire/feed/back/select/reviewerInfo",
        method: "get",
        params: {
          autCode
        }
      })
    },

    getPaperDetail() {
      request({
        url: "/system/questionnaire/" + this.detail.questionnaireId,
        method: "get",
      }).then((res) => {
        this.question = res.data
        let { data, description, title, type, id } = res.data
        let list = data.map((que, i) => {
          let questionList = JSON.parse(que.questionOptions || '[]')
          let feed = this.feed.find(({ id }) => id == que.id);
          questionList.forEach((ques, idx) => {
            if (ques.circulation) {
              ques.questionList.forEach((quest, index) => {
                quest.value = {}
                quest.comment = {}
                this.circulation(ques.cycleType).forEach((user, idxx) => {
                  if (idxx === 0) {
                    this.select[`${i}${idx}`] = user.accountId + ''
                  }
                  if (feed) {
                    quest.value[user.accountId] = feed.questionList[idx].questionList[index].value[user.accountId]
                    quest.comment[user.accountId] = feed.questionList[idx].questionList[index].comment[user.accountId]
                  }
                })
              })
            } else {
              if (!feed) return;
              if (feed.questionList[idx].answer) {
                ques.answer = feed.questionList[idx].answer
              } else if (feed.questionList[idx].questionList) {
                ques.questionList.forEach((qt, ix) => {
                  qt.value = feed.questionList[idx].questionList[ix].value
                  qt.comment = feed.questionList[idx].questionList[ix].comment
                })
              }
            }
          })

          return {
            ...que,
            answer: '',
            questionList,
          }
        });
        this.form = {
          description,
          title,
          type,
          id
        }

        this.origin = list;
        this.list = list;
      });
    },

    arraySpanMethod({ row, columnIndex }) {
      if (row.type == 2) {
        if (columnIndex == 2) {
          return [1, 2];
        } else if (columnIndex == 3) {
          return [0, 0];
        }
      }
    },
  }
}

</script>

<style lang='scss' scoped>
.title {
  color: black;
  font-weight: bold;
  font-size: 17px;
  margin: 20px 0 10px 0;
  text-align: left;
}

.subTitle {
  color: black;
  // font-weight: bold;
  font-size: 16px;
  margin-bottom: 0;
}

.answer {
  padding-bottom: 3px;
  border-bottom: 1px solid #000;
  line-height: 30px;
}
</style>
