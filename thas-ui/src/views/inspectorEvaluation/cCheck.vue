<!--  -->
<template>
  <div class="audit-evaluation" ref="auditEvaluation" v-loading="loading">
    <div v-if="distributed">
      <!-- 医院名称 -->
      <el-card class="box-card" shadow="always" style="margin-bottom: 10px; margin-left: -5px">
        <h1 style="margin: 0">{{ detail.hospitalName }}</h1>
      </el-card>
      <!-- 总结详情 -->
      <el-card v-if="showSummary" class="box-card" shadow="never" style="margin-bottom: 10px; margin-left: -5px">
        <div slot="header" class="clearfix">
          <h4>报告审查结果</h4>
        </div>
        <el-form ref='summaryForm' :model="summaryForm" label-width="100px">
          <el-form-item label="需修改项：">
            <span v-if="!modifyClause.length">无需修改</span>
            <modify-table v-else :data="modifyClause" :versionId='relation.autCsId' :showAll='false'></modify-table>
          </el-form-item>
          <el-form-item label="审查描述：" :rules="autDescRule" prop="autDesc">
            <el-input type="textarea" v-model="summaryForm.autDesc" :rows="3" :maxlength="1000"
             ></el-input>
          </el-form-item>
          <el-form-item label=" ">
            <el-button type="primary" @click="submitSummary">提交报告审查结果</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 评价  -->
      <el-card class="box-card autScrollCard" shadow="never" v-if="!showSummary" :style="{ height: autCardHeight }">
        <div slot="header" class="clearfix">
          <h4>评审报告审查</h4>
          <div class="header-right" v-if="standardLoadedKey" :key="headerSlotKeys">
            <!-- <el-button v-if="showConvenientBtn" size="mini" @click="submitAllSa" style="margin:5px;">填充全部审查初查</el-button> -->

            <el-button type="primary" size="mini" @click="auditComplete">审查完成</el-button>
          </div>
        </div>

        <!-- 款项选择器 -->
        <standard-card ref="standardCard" :key="`base${loadingKeys}`" :clauseIds="clauseIds" :versionId="relation.autCsId"
          @selectChange="selectChange" @loaded="standardLoaded">
        </standard-card>

        <!-- 款项 -->
        <div v-show="clauseList.length" class="evaluation-content" :key="`ach${selectedKeys}`"
          :style="{ height: clauseItemHeight }">
          <el-form :key="loadingKeys" label-width="100px">
            <clause-item v-for="(clause, index) in clauseList" v-show="isThisPage(index + 1)" :clause="clause"
              :key="clause.clauseId">
              <!-- <evaluation-self :readonly="true" :type="AutSaAudCurrentStatusEnum.SA_CLAUSE" :clause="clause" :aut="saMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-self> -->
              <evaluation-review :readonly='true' :type="AutSaAudCurrentStatusEnum.SR_CLAUSE" :clause="clause"
                :aut="srMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-review>
              <evaluation-audit :readonly="checkReadonly(frrMap[clause.clauseId])"
                :type="AutSaAudCurrentStatusEnum.FR_REPORT_R_CLAUSE" :clause="clause" :aut="frrMap[clause.clauseId]"
                :status="autSaAudStatus"></evaluation-audit>
            </clause-item>
            <pagination v-show="total > limit" :total="total" :page.sync="pageNum" :limit.sync="limit"
              :pageSizes="[limit]" />
          </el-form>
        </div>
      </el-card>
      <!-- 总结提交 -->

    </div>
  </div>
</template>

<script>
import evaluationMixin from "@/mixins/evaluation.vue";
import SummaryEdit from './summaryEdit.vue'

export default {
  name: "CInspectorIndex",
  mixins: [evaluationMixin],
  components: {
    SummaryEdit,
  },
  props: {},
  watch: {
    detail: {
      deep: true,
      immediate: true,
      handler() {
        let statues = [
          this.AutSaAudCurrentStatusEnum.WAIT_FACTUAL_ACCURACY_REVIEW,
        ]
        if (this.detail && this.detail.autSaAudStatus && statues.includes(this.detail.autSaAudStatus)) {
          this.handleError();
        }
      },
    },
  },
  data() {
    return {
      show: false,
      loading: true,
      relation: {},
      detail: {},
      clauseIds: [],
      specialInit: true,
      summaryForm: {
        autDesc: '',
        autResult: '1'
      },
    };
  },

  computed: {
    checkReadonly() {
      return aut => {
        return aut && !aut.rejected && !!aut
      }
    },
    autDescRule() {
      return [
        {
          required: this.modifyClause.length != 0,
          message: '请填写审查描述',
          trigger: 'blur'
        }
      ]
    },
    showSummary() {
      return this.autSaAudStatus == this.AutSaAudCurrentStatusEnum.REVIEW_REVIEW_REPORT_SUM
    },
    modifyClause() {
      return Object.values(this.frrMap).filter(({ autResult }) => autResult == 2).map(({ clauseId }) => Number(clauseId));
    },

    showExamineClauseList() {
      return Object.values(this.lwmMap).map(({ clauseId }) => clauseId);
    },

    showAutFilter() {
      const list = Object.values(this.srMap);
      let frClauseIds = list.map(({ clauseId }) => Number(clauseId));
      let list2 = this.distributeClauseIds.reduce((list, next) => {
        if (!frClauseIds.includes(Number(next))) list.push(Number(next));
        return list;
      }, [])
      return list2;
    },

    // 被驳回
    showAutRjFilter() {
      const list = Object.values(this.srMap);
      const rjList = list.filter(({ rejected }) => rejected).map(({ clauseId }) => clauseId);
      return rjList;
    },
  },

  mounted() {
    this.init2(true, this.$route.query.autCode, this.$route.query.type);
  },

  methods: {
    handleError() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push({ path: "/fInspector/CheckReviewList" });
    },

    initFinalBack() {
      if (this.detail.autSaAudStatus === this.AutSaAudCurrentStatusEnum.WAIT_REVIEW_REVIEW_REPORT ||
        this.detail.autSaAudStatus === this.AutSaAudCurrentStatusEnum.REVIEW_REVIEW_REPORT_PROC) {
        let list = Object.values(this.lwmMap);
        if (!list.length) {
          list = Object.values(this.srMap);
        }
        if (list.length) {
          let wholeIds = list.map(item => item.clauseId)
          this.$set(this, "distributeClauseIds", wholeIds);
          this.$set(this, "clauseIds", wholeIds);
          this.headerSlotKeys++;
        }
      }
    },

    submitSummary() {
      this.$refs.summaryForm.validate(async valid => {
        if (!valid) return;
        let data = {
          autCode: this.relation.autCode,
          accountId: this.$store.getters.userId + "",
          submitType: this.AutSaAudCurrentStatusEnum.FR_REPORT_R_SUMMARY,
          autSaAudLists: [this.summaryForm]
        }
        try {
          let res = await this.submit(data);
          // 报错就不提示
          if (!res) return;
          this.$message({
            type: 'success',
            message: '提交成功'
          });
          this.handleError();
        } finally {
          // this.init2(false, this.$route.query.autCode, this.$route.query.type);
        }
      })
    },

    // 初审初查
    submitAllSa() { this.makeData(this.AutSaAudCurrentStatusEnum.FR_CLAUSE) },

    async auditComplete() {
      let data = {
        autCode: this.relation.autCode,
        accountId: this.$store.getters.userId + "",
        submitType: this.AutSaAudCurrentStatusEnum.FR_REPORT_R_CLAUSE_CONFIRM,
        autSaAudLists: []
      }
      try {
        let res = await this.submit(data);
        // 报错就不提示
        if (!res) return;
        this.$message({
          type: 'success',
          message: '审查完成'
        });
      } finally {
        let obj = {
          path: "/fInspector/reportReviewResults",
          query: {
            autCode: this.$route.query.autCode,
            type: this.$route.query.type
          },
        }
        this.$tab.closeOpenPage(obj);
        // this.init2(false, this.$route.query.autCode, this.$route.query.type);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.box-card+.box-card {
  margin-top: 15px;
}

h4 {
  margin: 10px 0;
}
</style>
