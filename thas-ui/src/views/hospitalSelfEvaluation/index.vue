<!--  -->
<template>
  <div class="audit-evaluation" ref="auditEvaluation" v-loading="loading">
    <div v-if="distributed">
      <!-- 医院名称 -->
      <el-card class="box-card" shadow="always" style="margin-bottom: 10px; margin-left: -5px">
        <h1 style="margin: 0">{{ detail.hospitalName }}</h1>
      </el-card>

      <!-- 自评报告 -->
      <summary-show v-if="showSummary" :formData="summaryData"></summary-show>
      <aut-sa-aud-sa-report style="margin-top:15px;" v-if="showReport || showSummary || (showSum && !backToSa)" :flag="showSummary" :key="headerSlotKeys" :report="detail.autSaAudSaReport"></aut-sa-aud-sa-report>

      <!-- 自评总结 -->
      <summary-edit v-show="showSum && !backToSa" :status="autSaAudStatus" :detail="detail" :cacheAutAdvantage="cacheAutAdvantage" :relation="relation" @summarySubmit="summarySubmit" @backToSa="backToSaFunc" @autAdvantageChange="autAdvantageChange" style="margin-top: 15px;"></summary-edit>

      <!-- 评价  -->
      <el-card class="box-card autScrollCard" shadow="never" v-show="!showSum || backToSa" :style="{height: autCardHeight}">
        <div slot="header" class="clearfix">
          <div style="float:left;">
            <h4>医院自评</h4>
          </div>
          <div class="header-right" v-if="standardLoadedKey" :key="headerSlotKeys">
            <span v-if="waitSaOrSaIng" @click="submitAll" type="text" size="mini">填充全部自评(测试) </span>

            <span v-if=" showAutFilter.length || (showAutRjFilter.length && showAutRjModifyFilter.length) || showAutCategoryFilter.length || (!showAutRjFilter.length && showAutCategoryFilter.length)">
              <!-- 自评 -->
              <span class="showAutFilter" v-if="showAutFilter.length" :class="{lightHigh: lightHigh == 10}" @click="handleClauseFilter(10, showAutFilter)">
                待自评 {{showAutFilter.length}} 款
              </span>
              <!-- 驳回 -->
              <span v-if="showAutRjFilter.length && showAutRjModifyFilter.length" :class="{lightHigh: lightHigh == 30}" @click="handleClauseFilter(30, showAutRjFilter)">
                被驳回 {{showAutRjFilter.length}} 款
              </span>
              <span v-if="showAutRjModifyFilter.length" :class="{lightHigh: lightHigh == 40}" @click="handleClauseFilter(40, showAutRjModifyFilter)">
                未修改 {{showAutRjModifyFilter.length}} 款
              </span>

              <!-- <span v-if="showCategory"> -->
              <span v-if="!showAutRjFilter.length && showAutCategoryFilter.length">
                <span v-for="(data, index) in showAutCategoryFilter" :key="index" class="aut-category-filter" :class="{lightHigh: lightHigh == (index + 1)}" @click="handleClauseFilter(index + 1, data.ids)">
                  {{data.dictLabel}} {{data.ids.length}} 款
                </span>
              </span>
            </span>
            <!-- </span> -->

            <el-button v-if="backToSa" type="primary" size="mini" style="margin-left:20px;" @click="backToSa = false">修改完毕</el-button>

            <!-- 完成 重修 -->
            <el-button v-if="showReport" type="primary" size="mini" style="margin-left:20px;" @click="selfConfirm">医院自评完成</el-button>
          </div>
        </div>
        <!-- 款项选择器 -->
        <standard-card ref="standardCard" :key="`base${loadingKeys}`" :clauseIds="clauseIds" :versionId="relation.autCsId" @selectChange="selectChange" @loaded="standardLoaded">
        </standard-card>

        <!-- 款项 -->
        <div v-show="clauseList.length" class="evaluation-content" :key="`ach${selectedKeys}`" :style="{height: clauseItemHeight}">
          <el-form class="evaluation-clause-form" :key="loadingKeys" label-width="100px">
            <clause-item v-for="(clause, index) in clauseList" v-show="isThisPage(index + 1)" :clause="clause" :key="clause.clauseId">
              <evaluation-self :readonly="statusOut" :backToSa="backToSa" :type="AutSaAudCurrentStatusEnum.SA_CLAUSE" :clause="clause" :aut="saMap[clause.clauseId]" :status="autSaAudStatus" :hideSubmit="hideSubmit"></evaluation-self>
              <evaluation-examine readonly v-if="showExamine" :clause="clause" :aut="frMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-examine>
            </clause-item>
          </el-form>
          <pagination v-show="total > limit" :total="total" :page.sync="pageNum" :limit.sync="limit"
            :pageSizes="[limit]" />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import evaluationMixin from "@/mixins/evaluation.vue";
import AutSaAudSaReport from './evaluationResult/autSaAudSaReport.vue'
import SummaryEdit from './summaryEdit/index.vue'
import SummaryShow from './summaryShow/index2.vue'

export default {
  name: "AuditEvaluation",
  mixins: [evaluationMixin],
  components: {
    AutSaAudSaReport,
    SummaryEdit,
    SummaryShow,
  },
  props: {},
  watch: {},
  data() {
    return {
      show: false,
      loading: true,
      relation: {},
      detail: {},
      termSubmitLoading: false,
      summaryData: {
        autDesc: '',
        fileName: '',
        files: []
      },
      reviewResultOptions: [],
      categoryKeys: 0,
      backToSa: false,
      // 缓存已填写自评总结
      cacheAutAdvantage: ''
    };
  },

  computed: {
    waitSaOrSaIng() {
      return (this.autSaAudStatus == this.AutSaAudCurrentStatusEnum.WAIT_TERM_SA || this.autSaAudStatus == this.AutSaAudCurrentStatusEnum.TERM_SA_PROC) && this.showConvenientBtn
    },

    showArticle() {
      return this.articleId && (this.showArtFilter['artSa'][this.articleId] || this.termMap[this.articleId]);
    },
    showArticleEdit() {
      return !this.termMap[this.articleId] || this.showReport;
    },

    showReport() {
      return this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.WAIT_CONFIRM_SA_SUM
    },
    showSum() {
      return this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.WAIT_TERM_SA_SUM ||
        this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.TERM_SA_SUM
    },
    showSummary() {
      let statuses = [
        this.AutSaAudCurrentStatusEnum.WAIT_TERM_SA,
        this.AutSaAudCurrentStatusEnum.TERM_SA_PROC,
        this.AutSaAudCurrentStatusEnum.WAIT_CONFIRM_SA_SUM,
        this.AutSaAudCurrentStatusEnum.WAIT_TERM_SA_SUM,
        this.AutSaAudCurrentStatusEnum.TERM_SA_SUM,
        this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_SUM_REJECT
      ]
      return !statuses.includes(this.autSaAudStatus)
    },
    showExamine() {
      let status = [
        this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_SUM_REJECT,
      ];
      return status.includes(this.autSaAudStatus)
    },

    // 除自评外的全部
    statusOut() {
      let status = [
        this.AutSaAudCurrentStatusEnum.WAIT_TERM_SA,
        this.AutSaAudCurrentStatusEnum.TERM_SA_PROC,
        this.AutSaAudCurrentStatusEnum.WAIT_CONFIRM_SA_SUM,
        this.AutSaAudCurrentStatusEnum.WAIT_TERM_SA_SUM,
        this.AutSaAudCurrentStatusEnum.TERM_SA_SUM,
      ];
      return !status.includes(this.autSaAudStatus)
    },

    rejected() {
      return this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_SUM_REJECT;
    },

    onTime2ShowArtSum() {
      if (!this.articleId) return false;
      let clauseIds = this.clauseList.map(({ clauseId }) => clauseId);
      clauseIds.reduce((flag, id) => {
        return flag && this.empty(this.saMap[id]);
      }, true)
    },

    // 是否是自评中
    isSaClauseStatus() { },

    wholeClauseIds() {
      return this.wholeClauseList.map(({ clauseId }) => Number(clauseId));
    },

    // 待自评
    showAutFilter() {
      if (this.wholeClauseList.length === 0) return [];
      const list = Object.values(this.saMap);
      let wholeClauseIds = this.wholeClauseIds;
      let saClauseIds = list.map(({ clauseId }) => Number(clauseId));
      let restOfClauseIds = wholeClauseIds.filter((id) => !saClauseIds.includes(id));
      return restOfClauseIds;
    },

    // 待小结
    showArtFilter() {
      let articleMap = this.$store.getters.standardTypeItemByVersionId(this.relation.autCsId, 'article', 'map');
      let articleSimpleMap = {}

      // 获取全部的条
      Object.values(articleMap).forEach(({ clauseVoList, articleId }) => {
        // 过滤已提交的条小结
        if (this.termMap[articleId]) return;
        articleSimpleMap[articleId] = {
          articleId,
          clauseIds: clauseVoList.map(({ clauseId }) => Number(clauseId))
        }
      })

      // 获取所有已提交的款
      let allReadySubmitClauseIds = Object.values(this.saMap).map(({ clauseId }) => Number(clauseId));
      let totalAr = [], totalSa = [], artSaFinishMap = {};
      Object.values(articleSimpleMap).forEach(({ articleId, clauseIds }) => {
        // 判断该条的所有款师傅都填充完毕
        let flag = clauseIds.reduce((flag, id) => {
          return flag && allReadySubmitClauseIds.includes(id);
        }, true)
        if (flag) {
          totalSa.push(...clauseIds);
          totalAr.push(articleId);
          artSaFinishMap[articleId] = true;
        }
      })
      return {
        art: totalAr,
        sa: totalSa,
        artSa: artSaFinishMap
      };
    },

    // 被驳回
    showAutRjFilter() {
      // const list = Object.values(this.saMap);
      // const rjList = list.filter(({ rejected }) => rejected).map(({ clauseId }) => clauseId);
      if (!this.detail.rejectAutSaAudListMap) return [];
      const rjMap = this.detail.rejectAutSaAudListMap[this.AutSaAudCurrentStatusEnum.SA_CLAUSE] || {}
      const list = Object.values(rjMap);
      return list.map(({ clauseId }) => clauseId);;
    },

    showAutRjModifyFilter() {
      const list = Object.values(this.saMap);
      const rjList = list.filter(({ rejected }) => rejected).map(({ clauseId }) => clauseId);
      return rjList
    },


    // 自评完毕分类
    showAutCategoryFilter() {
      if (!this.showCategory) return []
      let saList = Object.values(this.saMap);
      let list = this.reviewResultOptions.map(({ dictValue, dictLabel }) => {
        let ids = saList.filter(({ autResult }) => autResult == dictValue).map(({ clauseId }) => clauseId);
        return {
          dictValue,
          dictLabel,
          ids
        }
      }) || []
      return list.filter(({ ids }) => ids.length);
    },

    showCategory() {
      if (!this.detail.rejectAutSaAudListMap) return true;
      let saRejectMap = this.detail.rejectAutSaAudListMap['sa_clause']
      if (!saRejectMap) return true;
      const list = Object.values(saRejectMap);
      return !list.length
    },
    // 隐藏提交操作
    hideSubmit() {
      if (
        this.detail
        && this.detail
        && this.detail.autSaAudStatus
        && this.detail.autSaAudStatus === '010103'
        && this.detail.rejectAutSaAudListMap
        && this.detail.rejectAutSaAudListMap.fr_clause
        && this.detail.rejectAutSaAudListMap.fr_clause.length
        && this.detail.rejectAutSaAudListMap.sa_clause
        && this.detail.rejectAutSaAudListMap.sa_clause.length
      ) {
        return true
      } else {
        return false
      }
    }
  },

  mounted() {
    this.getDicts('review_result').then(res => {
      this.reviewResultOptions = res.data;
      this.categoryKeys++;
    })
    this.init();
  },

  methods: {
    // 返回修改自评内容
    backToSaFunc(val) {
      this.backToSa = true;
      // 缓存已填写自评总结
      this.cacheAutAdvantage = val;
    },
    autAdvantageChange(value) {
      // 缓存已填写自评总结
      this.cacheAutAdvantage = value;
    },
    initFinalBack() {
      if (this.showAutRjFilter.length && this.showAutRjModifyFilter.length) {
        this.$set(this, 'clauseIds', this.showAutRjFilter)
        this.$set(this, 'distributeClauseIds', this.showAutRjFilter)
        this.loadingKeys++;
      } else if (this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.WAIT_CONFIRM_SA_SUM && this.showAutRjFilter.length) {
        this.$set(this, 'clauseIds', [])
        this.$set(this, 'distributeClauseIds', [])
        this.loadingKeys++;
      }


      if (!this.detail || !this.detail.autSaAudSum) {
        return;
      }

      let { autDesc, fileIds } = this.detail.autSaAudSum;
      this.summaryData = {}
      let fileNames = ''
      if (fileIds) {
        let fileIdList = fileIds.split(',') || [];
        let fileList = fileIdList.map(id => this.detail.fileDetailMap[id][0]);
        if (fileList.length) {
          fileNames = fileList.map(file => file.fileName).join(',');
          this.$set(this.summaryData, 'files', fileList)
        }
      }
      try {
        let autDescParse = JSON.parse(autDesc)
        this.$set(this.summaryData, 'autAdvantage', autDescParse.autAdvantage)
        this.$set(this.summaryData, 'autEvaluate', autDescParse.autEvaluate)
        this.$set(this.summaryData, 'autProposal', autDescParse.autProposal)
      } catch (error) {
        // console.log(error);
      }
      this.$set(this.summaryData, 'autDesc', autDesc)
      this.$set(this.summaryData, 'fileName', fileNames)
    },
    empty(value) {
      let type = typeof value;
      if (type === 'object') {
        if (Array.isArray(value)) {
          return value.length === 0
        } else {
          return Object.keys(value).length === 0;
        }
      } else if (type === 'string') {
        return value.trim() === ""
      } else if (type === 'number') {
        return Number.isNaN(value);
      }
      return true;
    },

    summarySubmit() {
      this.init();
    },

    async selfConfirm() {
      if (this.autSaAudStatus !== this.AutSaAudCurrentStatusEnum.WAIT_CONFIRM_SA_SUM) return;
      let types = this.AutSaAudCurrentStatusEnum.getTypeByStatus(this.autSaAudStatus);
      let data = {
        autCode: this.relation.autCode,
        accountId: this.$store.getters.userId + "",
        submitType: types[1],
        autSaAudLists: []
      }
      try {
        let res = await this.submit(data);
        // 报错就不提示
        if (!res) return;
        this.$message({
          type: 'success',
          message: "确认成功"
        });
        this.init();
      } catch (error) {
        if (error.code == 1000003) { this.init(); }
      }
    },

    handleError() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push({ path: "/" });
    },

    // 自评
    submitAll() {
      let list = Object.values(this.saMap);
      if (list.length == 0) return this.$message({ type: 'warning', message: '请先提交最少一条自评！' });
      let hasFile = list.find(li => li.fileIds);
      this.makeData(this.AutSaAudCurrentStatusEnum.SA_CLAUSE, 'clause', hasFile ? hasFile.fileIds : '', [], true);
    },
  },
};
</script>
<style lang="scss" scoped>
.box-card + .box-card {
  margin-top: 15px;
}
.articleSum {
  margin-top: 15px;
}
</style>

<style lang="scss">
.audit-evaluation {
  & > div {
    background-color: #f9f9f9;
  }

  span.aut-category-filter::after {
    content: "|";
  }

  span.aut-category-filter:last-child::after {
    content: "";
  }

  .showAutFilter::after {
    content: "|";
  }
  .showAutFilter:last-child::after {
    content: "";
  }
}
</style>

