<!DOCTYPE html>
<html lang="en" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word"
  xmlns:dt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882" xmlns="http://www.w3.org/TR/REC-html40">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta name="ProgId" content="Word.Document" />
  <meta name="Generator" content="Microsoft Word 14" />
  <meta name="Originator" content="Microsoft Word 14" />
  <title></title>
  <style>
    @font-face {
      font-family: "SimSun";
    }

    @page {
      @top-left {
        content: element(header);
      }

      size: A4;
    }


    body {
      font-family: "SimSun";
    }
  </style>
  <style>
	 #detail,#scoreTable {
		-fs-table-paginate: paginate;
		border-spacing: 0;
		table-layout: fixed;
		word-break: break-strict;
	}
	
	#detail tr, #scoreTable tr{
		page-break-inside: avoid;
		page-break-after: auto:
	}
  
    #content,
    #content1 {
      overflow: hidden;
    }

    /* @media print {
            #header {
            display: block;
            position: running(header-top);
            margin-top: 20px;
        }
      } */
    #header>span:first-child {
      margin-left: 20px;
      float: left;
    }

    #header>span:last-child {
      margin-right: 20px;
      float: right;
    }

    #image {
      width: 45%;
      margin: 40px auto;
    }

    #image img {
      width: 100%;
    }

    #title {
      width: 80%;
      color: white;
      margin: 0 auto;
      margin-top: 40px;
      background-color: rgb(79, 129, 189);
      overflow: hidden;
    }

    #title p {
      text-align: center;
      margin: 5px;
      color: white;
      padding: 0 10px;
      font-size: 28px;
      /* font-weight: bold; */
      /* font-family: 'arialuni'; */
    }

    #title p:first-child {
      margin-left: 10px;
      margin-right: 10px;
    }

    #sign {
      margin: 0 auto;
      margin-top: 160px;
      width: 80%;
      font-size: 20px;
    }

    #sign div {
      margin: 20px 0;
    }

    #tip {
      width: 80%;
      margin: 0 auto;
      font-size: 19px;
      margin-top: 120px;
    }

    #tip span {
      font-weight: bold;
    }

    #explain {
      width: 80%;
      margin: 0 auto;
    }

    #explain .title {
      font-size: 30px;
      text-align: center;
      font-weight: normal;
    }
    #explain h3 {
      font-size: 20px;
      margin-top: 50px;
    }
    #explain .table-title {
      font-weight: bold;
      text-align: center;
    }

    #explain .summarize{
      font-size: 18px;
      text-indent: 2em;
      line-height: 2;
      word-break: break-all;
      word-wrap: break-word;
      white-space: pre-wrap;
    }

    #explain p {
      margin: 0px !important;
      font-size: 18px;
      line-height: 2;
      word-break: break-all;
      word-wrap: break-word;
      white-space: pre-wrap;
    }

    #explain .content .icon{
      padding-right: 10px;
    }

    #explain .explain-table {
      margin-bottom: 500px;
    }

    #explain .explain-table tr th {
      background: #EFEFEF;
      font-size: 18px;
      padding: 10px
    }

    #explain .explain-table tr td {
      word-break: break-all;
      word-wrap: break-word;
      white-space: pre-wrap;
      font-size: 18px;
      padding: 10px
    }

    #explain .pl30 {
      padding-left: 30px;
    }

    #explain .pl40 {
      padding-left: 40px;
    }

    #explain .pl60 {
      padding-left: 60px;
    }

    #message {
      width: 80%;
      margin: 0 auto;
      margin-top: 120px;
      background-color: rgb(79, 129, 189);
      color: white;
    }

    #message p {
      font-size: 18px;
      padding-top: 5px;
      padding-left: 5px;
      padding-bottom: 5px;
      margin: 0;
    }

    #selfReport {
      width: 80%;
      margin: 0 auto;
      margin-top: 82px;
      overflow: hidden;
    }

    #selfReport .report-title {
    }

    #selfReport h2 {
      text-align: center;
    }

    #selfReport .aut-desc {
      min-height: 850px;
      padding: 10px;
      border: 1px solid black;
      word-break: break-all;
      word-wrap: break-word;
      white-space: pre-wrap;
    }

    #selfReport #sum {
      width: 100%;
      table-layout:fixed;
    }
    #selfReport #sum .sum {
      min-height: 86px;
      border: 1px solid black;
      padding: 0 10px;
    }

    #selfReport #sum .sum+.sum {
      border-top: 0;
    }

    #selfReport #sum .sum span.sumTip {
      display: inline-block;
      margin-left: 10px;
    }

    #selfResult {
      width: 80%;
      margin: 0 auto;
      margin-top: 100px;
      overflow: hidden;
    }

    #selfResult .sub {
      font-size: 16px !important;
      font-weight: normal;
    }

    #selfResult #resultTable {
      width: 100%;
    }

    #selfResult #resultTable thead th {
      width: 100px;
      text-align: left;
      background: #EFEFEF;
    }

    #clauseDetail {
      width: 80%;
      margin: 0 auto;
      margin-top: 80px;
      overflow: hidden;
    }

    .detailTable {
      width: 100%;
      table-layout: fixed;
    }

    #detailTable thead th {
      text-align: center;
    }

    .detailTable thead tr:first-child th:first-child {
      width: 80px;
    }

    .detailTable thead tr:last-child th {
      width: 40px;
    }

    .detailTable tbody th {
      text-align: left;
      font-weight: lighter;
    }

    .detailTable tbody td {
      padding: 5px;
    }

    #resultTable th,
    #resultTable td {
      text-indent: 10px;
      word-break: break-word;
      ;
      word-wrap: break-word;
    }

    td {
      max-width: 80px;
      word-break: break-word;
      ;
      word-wrap: break-word;
    }

    .detailTable th {
      text-align: center;
      word-break: break-word;
      word-wrap: break-word;
      white-space: pre-wrap;
    }

    .detailTable th.yes {
      word-break: break-word;
      ;
      word-wrap: break-word;
    }
  </style>
</head>

<body>
  <div id="content">
    <div id="image">
      <img src="aud_sa_report_image/aud_sa_report_image.png" />
    </div>

    <div id="title">
      <p>《国际医院评审认证标准(中国)》（2021版）</p>
      <p>自评报告${topicSuffix}</p>
    </div>

    <div id="sign">
      <div>
        <span>医院名称（盖章）:</span>
        <span>${hospitalName ! "-"}</span>
      </div>
      <div>
        <span>医院级别:</span>
        <span>${hospitalLevelName ! "-"}</span>
      </div>
      <div>
        <span>医院等级:</span>
        <span>${BeforeReviewerConclusionLevelName ! "-"}</span>
      </div>
      <div>
        <span>医院属性:</span>
        <span>${NatureOperationName ! "-"}</span>
      </div>
      <div>
        <span>提交日期:</span>
        <span>${submitDate ! "-"}</span>
      </div>
    </div>

    <table id="tip">
      <thead>
        <tr></tr>
      </thead>
      <tbody>
        <td>
            《国际医院评审认证标准(中国)》（2021版）是获得国际医疗质量协会外
            部评审会( <span>International Society for Quality in Health
            Care External Evaluation  Association，IEEA</span> )认证的标准。
        </td>
      </tbody>
    </table>

    <div id="message">
      <p>深圳市卫健医院评审评价研究中心</p>
      <p>邮箱：<EMAIL></p>
    </div>

    <!-- 自评报告填写说明 -->
    <#compress>
    <div id="explain">
      <h2 class="title">自评报告填写说明</h2>
      <div>
        <h3>概述</h3>
        <div class="summarize">自评报告是由受评医院对照标准进行的自我评价的结果和总结。受评医院须保证自评报告中所提供的信息真实、准确,如有虚假或伪造信息的，一经核实，中心将中止受评医院的评审认证。</div>

        <h3>一、封面及页眉</h3>
        <div class="content">
          <p class="pl30">1.封面包含医院名称、级别、等级、属性等内容。其中：</p>
          <p class="pl40"><span class="icon">◇</span>医院名称：以医院执业许可证上标注的名称为准；</p>
          <p class="pl40"><span class="icon">◇</span>医院级别：以医院执业许可证上标注的级别为准，如三级、二级、一级；如未确定级别，则填写：未定级。</p>
          <p class="pl40"><span class="icon">◇</span>医院等级：以医院上一周期国家等级医院评审所确定的等级，目前执业许可证上标注的等级为准，如甲、乙、丙；如未确定等次，则填写：未定等。</p>
          <p class="pl40"><span class="icon">◇</span>医院属性：以医院执业许可证上标注的经营性质为准，如为政府办的医院则填写：公立医院，为非政府办医院则填写：民营医院或私立医院。</p>
          <p class="pl30">2.按医院填报当时的状态填写以上内容，在确认最终版自评报告后，填写报告提交日期及盖医院公章。</p>
          <p class="pl30">3.页眉处医院名称及医院编码由中心系统自动生成。</p>
        </div>
        <h3>二、自评报告正文</h3>
        <div class="content">
          <p class="pl30">自评报告正文包含自评总结和自评结果。其中：</p>
          <p><span style="font-weight:bold;">（一）自评总结：</span>请按以下提纲填写，总字数限制在5000字以内：</p>
          <p class="pl30">1.医院概况： 1）医疗服务与特色2）学科建设与发展</p>
          <p class="pl30">2.认证/评审历程：认证/评审准备、历程及具体举措</p>
          <p class="pl30">3.工作成效与亮点</p>
          <p class="pl30">4.存在问题</p>
          <p class="pl30">5.改进计划</p>
          <p class="pl30">6.请同步提供附件材料：</p>
          <p class="pl40">（1）医疗机构执业许可证影印本；</p>
          <p class="pl40">（2）医院组织架构，包含医院管理层、职能部门和临床、医技科室；</p>
          <p class="pl40">（3）医院建筑平面图及科室布局图；</p>
          <p class="pl40">（4）医院最新的、与认证工作相关的各项管理制度和各类应急预案及流程；</p>
          <p class="pl40">（5）医院近3年具有代表性的质量改进项目（3-5项），包括：项目内容简介，具体改进措施、自查自纠情况及成效；</p>
          <p class="pl40">（6）近四年医疗质量监测数据。</p>
          <p style="padding-top: 20px;"><span style="font-weight:bold;">（二）自评结果：</span>包括：评价结果、自评依据及证明材料，要求医院对照标准186款的要求，根据医院实际情况逐一对每一款进行自我评价，并按相关要求填写自评依据及提供证明材料。自评结果填写要求如下：</p>
          <p class="pl30">1.评价结果：医院根据实际情况对照标准，对每一款进行自我评价，评价结果从“优秀”、“良好”、“达标”、“部分达标”、“不达标”和“不适用”6项中选取一项，不得多选。</p>
          <p class="pl30">2.自评依据：对自评结果为优秀、部分达标、不达标、不适用的款进行简要说明，每款限制在200字以内，填写要求如下：</p>
          <p class="pl40">（1）优秀填写内容包括但不限于以下内容：</p>
          <p class="pl60">A.制度/流程建立情况；</p>
          <p class="pl60">B.组织实施情况；</p>
          <p class="pl60">C.自查自纠总结；（包括但不限于主要问题、改进方案、实施计划改进成效等）</p>
          <p class="pl60">D.主管部门监管、问题分析及向科室/部门反馈的情况；</p>
          <p class="pl60">E.医院实施持续改进及相关的成效。</p>
          <p class="pl40">（2）部分达标、不达标：简要说明未达到标准要求的情形，存在的主要问题及下一步整改计划。</p>
          <p class="pl40">（3）不适用：说明不适用的理由。</p>
        </div>
        <p>证明材料：自评为优秀、不适用款须提供佐证材料，具体要求见表1</p>
        <p class="table-title">表1 自评详情证明材料上传要求</p>
        <table class="explain-table" cellpadding="0" cellspacing="0" border="1">
          <thead>
            <tr>
              <th style="width: 120px;">评分结果</th>
              <th>证明材料</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style="font-weight: bold;">优秀</td>
              <td>A.制度/流程<br/>B.实施方案/计划<br/>C.自查自纠总结（包括但不限于主要问题、改进方案、实<br/>施计划、改进成效等）<br/>D.主管部门监督检查、具体落实情况、改进措施及医院持<br/>续改进有成效的佐证资料。<br/></td>
            </tr>
            <tr>
              <td style="font-weight: bold;">不适用</td>
              <td>上传可以说明医院不适用该款的相关资料，如医院执业许可<br/>证中未注册该项诊疗项目或内容或卫生行政部门批复件等。</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    </#compress>

    <div id="selfReport">
      <h2>自评报告</h2>
      <h3 class="report-title">一、自评总结</h3>
      <div class="aut-desc">${autAdvantage}</div>
    </div>

    <!--  -->
    <div id="selfResult">
      <h3>二、自评结果汇总<span class="sub">（此表信息平台自动计算，医院不用填写）</span></h3>
      <div id="result">
        <table id="resultTable" cellpadding="0" cellspacing="0" border="1" style="repeat-header:yes;">
          <thead>
            <!-- <tr>
                <th rowspan="2" colspan="1" ></th>
                <th colspan="2">评价结果</th>
                <th rowspan="2" colspan="1" >款数</th>
                <th rowspan="2" colspan="1" >占比</th>
              </tr> -->
            <tr>
              <th colspan="1"></th>
              <th colspan="2" style="text-indent: 0;">评价结果</th>
              <th colspan="1" style="text-indent: 0;">款数</th>
              <th colspan="1" style="text-indent: 0;">占比</th>
            </tr>
          </thead>
          <tbody>
            <#list autSaAudReportListVo as autSaAudReport>
              <#if autSaAudReport.isBasic==0>
                <#list autSaAudReport.reportList as report>
                  <#if report.autSaAudResult !=8 && report.autSaAudResult !=6>
                    <tr class="report1">
                      <#if report_index==0>
                        <td rowspan="7">基本款<br/>(共<span style="font-weight: bold;">${autSaAudReport.sumCount}</span>款)</td>
                      </#if>

                      <td colspan="2">
                        <#if report.autSaAudResult==1> 优秀 </#if>
                        <#if report.autSaAudResult==2> 良好 </#if>
                        <#if report.autSaAudResult==3> 达标 </#if>
                        <#if report.autSaAudResult==4> 部分达标 </#if>
                        <#if report.autSaAudResult==5> 不达标 </#if>
                        <#if report.autSaAudResult==7> <span style="font-weight: bold;">达标及以上</span> </#if>
                      </td>
                      <td>${report.count}款</td>
                      <td>${report.rate}%</td>
                    </tr>
                  </#if>
                </#list>

                <!-- “不适用”排在最后，前端排序 -->
                <#list autSaAudReport.reportList as report>
                  <#if report.autSaAudResult ==6>
                    <tr class="report1">
                      <#if report_index==0>
                        <td rowspan="7">基本款<br/> (共<span style="font-weight: bold;">${autSaAudReport.sumCount}</span>款)</td>
                      </#if>

                      <td colspan="2" style="font-weight: bold;">不适用</td>
                      <td>${report.count}款</td>
                      <td>${report.rate}%</td>
                    </tr>
                  </#if>
                </#list>
                
              </#if>

              <#if autSaAudReport.isBasic==1>
                <#list autSaAudReport.reportList as report>
                  <#if report.autSaAudResult !=6>
                    <tr class="report1">
                      <#if report_index==0>
                        <td rowspan="32">非基本款<br/> (共<span style="font-weight: bold;">${autSaAudReport.sumCount}</span>款)</td>
                      </#if>
                      <td colspan="2">
                        <#if report.autSaAudResult==1> 优秀 </#if>
                        <#if report.autSaAudResult==2> 良好 </#if>
                        <#if report.autSaAudResult==3> 达标 </#if>
                        <#if report.autSaAudResult==4> 部分达标 </#if>
                        <#if report.autSaAudResult==5> 不达标 </#if>
                        <#if report.autSaAudResult==7> <span style="font-weight: bold;">达标及以上</span> </#if>
                        <#if report.autSaAudResult==8> <span style="font-weight: bold;">部分达标及以上</span> </#if>
                      </td>
                      <td>${report.count}款</td>
                      <td>${report.rate}%</td>
                    </tr>
                  </#if>
                </#list>

                <!-- “不适用”排在最后，前端排序 -->
                <#list autSaAudReport.reportList as report>
                  <#if report.autSaAudResult == 6>
                    <tr class="report1">
                      <td colspan="2" style="font-weight: bold;">不适用</td>
                      <td>${report.count}款</td>
                      <td>${report.rate}%</td>
                    </tr>
                  </#if>
                </#list>
              </#if>

              <#if autSaAudReport.isBasic==1>
                <#list autSaAudReport.chapterReportList as chapter>
                  <#list chapter.reportList as report>
                    <#if report.autSaAudResult !=6>
                      <tr class="report1">
                        <#if report_index==0>
                          <!-- <td rowspan="7">非基本款<br/> (共<span style="font-weight: bold;">${autSaAudReport.sumCount}</span>款)</td> -->
                        </#if>
                        <#if report_index==0>
                          <td rowspan="8">
                            第
                            <span>
                              <#if chapter.chapterId=='1'> 一 </#if>
                              <#if chapter.chapterId=='2'> 二 </#if>
                              <#if chapter.chapterId=='3'> 三 </#if>
                            </span>
                            章<br/>(共<span style="font-weight: bold;">${chapter.sumCount}</span>款非基本款)
                          </td>
                        </#if>
                        <td>
                          <#if report.autSaAudResult==1> 优秀 </#if>
                          <#if report.autSaAudResult==2> 良好 </#if>
                          <#if report.autSaAudResult==3> 达标 </#if>
                          <#if report.autSaAudResult==4> 部分达标 </#if>
                          <#if report.autSaAudResult==5> 不达标 </#if>
                          <#if report.autSaAudResult==7> <span style="font-weight: bold;">达标及以上</span> </#if>
                          <#if report.autSaAudResult==8> <span style="font-weight: bold;">部分达标及以上</span> </#if>
                        </td>
                        <td>${report.count}款</td>
                        <td>${report.rate}%</td>
                      </tr>
                    </#if>
                  </#list>

                  <!-- “不适用”排在最后，前端排序 -->
                  <#list chapter.reportList as report>
                    <#if report.autSaAudResult ==6>
                      <tr class="report1">
                        <td style="font-weight: bold;">不适用</td>
                        <td>${report.count}款</td>
                        <td>${report.rate}%</td>
                      </tr>
                    </#if>
                  </#list>
                </#list>
              </#if>
            </#list>
          </tbody>
        </table>
      </div>
    </div>

    <!--  -->
    <div id="clauseDetail">
      <h3>三、自评结果明细</h3>
      <!--<p>
      <h4>（一）填写要求</h4>
      <table  class="detailTable" cellpadding="0" cellspacing="0" style="table-layout:fixed;">
        <tr>
          <td><b>1、款号栏：</b>与评审标准条款一致，并一一对应。
          </td>
        </tr>
        <tr>
          <td><b>2、评价结果栏：</b>所有条款均应给予“不达标”、“部分达标”、“达标”、“良好”、“优秀”、“不适用”评价，六项选其一，不得勾选多项，请在相应的方框勾选√。</td>
        </tr>
        <tr>
          <td><b>3、说明/建议栏目：</b>经过反复核对后，对该条款最终被评为“不达标”“部分达标”给予说明/建议，对“不适用”、“优秀”条款给予具体说明。例如：根据标准要求，描述贵单位建了什么制度，做了什么措施/事项，目前处在什么情况等。</td>
        </tr>
      </table>
      </p>-->
      <!--<h4>（二）条款评分情况</h4>-->
      <div id="detail">
        <#list chapterVosList as chapter>
          <div>
            <h4>${chapter.chapterNo} ${chapter.chapter}</h4>
            <table id="scoreTable" class="detailTable" cellpadding="0" cellspacing="0" border="1" style="repeat-header:yes;">
              <thead>
                <tr>
                  <th style="text-indent: 0;">款号</th>
                  <th style="text-indent: 0;width: 50px;">自评结果</th>
                  <th style="text-indent: 0;width: 200px;">自评依据<br/>（对自评为优秀、部分达标、不达标、不适用的款进行简要说明，每款限200字）</th>
                  <th style="text-indent: 0;width: 120px;">佐证材料<br/>（自评为优秀、不适用的款须上传佐证材料）</th>
                </tr>
                <!--<tr>
                  <th class="yes" style="text-indent: 0;">不达标</th>
                  <th class="yes" style="text-indent: 0;">部分达标</th>
                  <th class="yes" style="text-indent: 0;">达标</th>
                  <th class="yes" style="text-indent: 0;">良好</th>
                  <th class="yes" style="text-indent: 0;">优秀</th>
                  <th class="yes" style="text-indent: 0;">不适用</th>
                </tr>-->
              </thead>
              <tbody>
                <#list chapter.sectionVoList as section>
                  <tr>
                    <td colspan="4" style="font-weight: bold;">${section.sectionNo}${section.section}</td>
                  </tr>
                  <#list section.articleVoList as article>
                    <tr>
                      <td colspan="4">${article.articleNo}${article.article}</td>
                    </tr>
                    <#list article.clauseVoList as clause>
                      <tr>
                        <td>
                          <#if clause.isStar=='1'>★</#if>${clause.clauseNo}
                        </td>
                        <td>
                          <#if clause.clauseResult=='5'>不达标</#if>
                          <#if clause.clauseResult=='4'>部分达标</#if>
                          <#if clause.clauseResult=='3'>达标</#if>
                          <#if clause.clauseResult=='2'>良好</#if>
                          <#if clause.clauseResult=='1'>优秀</#if>
                          <#if clause.clauseResult=='6'>不适用</#if>
                        </td>
                        <!--<td class="yes">
                          <#if clause.clauseResult=='5'>√</#if>
                        </td>
                        <td class="yes">
                          <#if clause.clauseResult=='4'>√</#if>
                        </td>
                        <td class="yes">
                          <#if clause.clauseResult=='3'>√</#if>
                        </td>
                        <td class="yes">
                          <#if clause.clauseResult=='2'>√</#if>
                        </td>
                        <td class="yes">
                          <#if clause.clauseResult=='1'>√</#if>
                        </td>
                        <td class="yes">
                          <#if clause.clauseResult=='6'>√</#if>
                        </td>-->
                        <td>${clause.clauseDesc}</td>
                        <td>
                          <#if clause.clauseFileInfoList?? && (clause.clauseFileInfoList?size > 0)>
                          <#list clause.clauseFileInfoList as clauseFile>
                            <a href="${clauseFile.outFileDownloadUrl}" target="_blank">${clauseFile.downLoadFileName}</a><br/>
                          </#list>
                          <#else>
                          -
                          </#if>
                        </td>
                      </tr>
                    </#list>
                  </#list>
                </#list>
              </tbody>
            </table>
          </div>
        </#list>
      </div>
    </div>
  </div>
</body>

</html>