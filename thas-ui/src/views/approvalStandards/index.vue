<template>
  <div class="approvalStandards-used">
    <el-card shadow="never">
      <div class="title clearfix" slot="header">
        <h2>
          {{ title }}
        </h2>
      </div>
      <approval-standard-tree v-if="show" :key="versionId" :versionId="versionId"></approval-standard-tree>
    </el-card>
  </div>
</template>

<script>
import approvalStandardTree from "./components/approvalStandarTree";
export default {
  name: "ApprovalStandardDetail",
  components: {
    approvalStandardTree,
  },
  data() {
    return {
      versionId: "",
      versionName: '',
      show: false,
    };
  },
  computed: {
    title() {
      return this.versionName || this.$store.getters.standardVName(this.versionId)
    }
  },
  methods: {
    toVersion() {
      this.$router.push({
        path: "/approvalStandards/version",
      });
    },
  },

  mounted() {
    const { versionId, versionName } = this.$route.params;
    this.versionId = versionId;
    this.versionName = versionName;
    this.show = true;
  },

  activated() {
    const { versionId, versionName } = this.$route.params;
    if (this.versionId != versionId) {
      this.versionId = versionId;
      this.versionName = versionName;
      this.show = true;
    }
  }
};
</script>

<style lang="scss" scoped>
.approvalStandards-used {
  min-height: inherit;
  background: #fff;
  // padding: 18px;
  h2 {
    font-weight: bold;
    text-align: left;
    color: black;
    margin-top: 0;
    margin-bottom: 7px;
  }
  .title {
    margin-bottom: 0;
  }
  ::v-deep .el-card {
    border: 0;
  }
}
</style>