<template>
  <el-form-item size="small" :label="label(clause)" class="clause-item">
    <span class="clause-title">
      {{ clause.clause }}
    </span>
    <div class="clause-information">
      <el-popover placement="top" width="500" trigger="click" popper-class="clausePopperClass">
        <div class="scroll-div" :class="{showScrollbar:showScroll}" @mouseover="mouseover" @mouseout="mouseout">
          <div>
            <div>
              <h4>{{clause.detailRulesTitle}}</h4>
              <pre>{{clause.detailRulesDesc || '暂无细则'}}</pre>
            </div>

            <div>
              <span v-if="clause.evidenceMaterial">达标佐证材料</span>
              <pre style="margin-top:5px;">{{clause.evidenceMaterial}}</pre>
            </div>

            <div>
              <div v-for="(standard, index) in clause.cstEvaluationCriterionList" :key="index" v-show="standard.evaluate || standard.standard">
                <h4>{{standard.evaluate}}</h4>
                <pre>{{ standard.standard }}</pre>
              </div>
            </div>
          </div>
        </div>
        <el-button type="text" slot="reference">细则</el-button>
      </el-popover>

      <el-popover placement="top" trigger="click" :content="clause.regulationFile">
        <div class="scroll-div" :class="{showScrollbar:showScroll}" @mouseover="mouseover" @mouseout="mouseout">
          <pre>{{ clause.regulationFile || "暂无相关政策文件" }}</pre>
        </div>
        <el-button type="text" slot="reference">相关政策文件</el-button>
      </el-popover>

      <el-popover placement="top" trigger="click">
        <div class="scroll-div" :class="{showScrollbar:showScroll}" @mouseover="mouseover" @mouseout="mouseout">
          <pre>{{ clause.internationalReference || "暂无国际参考文献" }}</pre>
        </div>
        <el-button type="text" slot="reference">国际参考文献</el-button>
      </el-popover>
      <el-button type="text" class="clause-detail" @click="toDetail(clause)">条款详情<i class="el-icon-arrow-right el-icon--right"></i></el-button>
    </div>
    <slot class="clause-content"></slot>
  </el-form-item>
</template>

<script>
import Vue from "vue";
import request from "@/utils/request";
import EvaluationSelf from '../EvaluationForm/evaluationSelf.vue'
import EvaluationForm from "./evaluation.vue";
import EvaluationResult from "./evaluationResult.vue";
import EvaluationReview from "./evaluationReview.vue";
import EvaluationCheck from "./evaluationCheck.vue";
import EvaluationModify from "./evaluationModify.vue";

export default {
  name: "ClauseItem",
  components: {
    EvaluationForm,
    EvaluationResult,
    EvaluationCheck,
    EvaluationModify,
    EvaluationReview,
    EvaluationSelf
  },
  props: {
    readOnly: {
      require: true,
      type: Boolean,
      default: () => false,
    },
    clause: {
      require: true,
      type: Object,
      default: () => { },
    },
    autSaAudStatus: {
      type: [String, Number],
      default: () => "",
    },
    aut: {
      type: Object,
      default: () => { },
    },
    sa: {
      type: Object,
      default: () => { },
    },
    sas: {
      type: Object,
      default: () => { },
    },
    aud: {
      type: Object,
      default: () => { },
    },
    audS: {
      type: Object,
      default: () => { },
    },
    sac: {
      type: Object,
      default: () => { },
    },
    hat: {
      type: Object,
      default: () => { },
    },
    audF: { type: Object, default: () => { } },
    sAud: { type: Object, default: () => { } },
    audSS: { type: Object, default: () => { } },
    isLeader: {
      type: Boolean,
      default: () => false,
    },
    showOnlyHospital: {
      type: Boolean,
      default: () => false,
    },
    type: {
      type: String,
      default: () => "",
    },
  },
  watch: {
    aut: {
      immediate: true,
      deep: true,
      handler() {
        this.key = Date.now();
        if (
          this.aut &&
          this.aut instanceof Object &&
          Object.keys(this.aut).length
        ) {
          for (const key in this.formData) {
            if (Object.hasOwnProperty.call(this.formData, key)) {
              this.formData[key] = this.aut[key];
            }
          }
          if (this.aut.fileList) {
            this.fileList = [...this.aut.fileList];
          }
        }
      },
    },
    sa: {
      immediate: true,
      deep: true,
      handler() {
        this.key = Date.now();
      },
    },
  },

  data() {
    return {
      roles: [],
      formData: {
        autResult: "",
        autDesc: "",
        clauseId: "",
        fileIds: "",
      },
      defaultFormData: {
        autResult: "",
        autDesc: "",
        fileIds: "",
        clauseId: "",
      },
      editing: false,
      fileList: [],
      key: 0,
      showScroll: false,
    };
  },
  computed: {
    label() {
      return clause => {
        return `${clause.isStar == 1 ? "★" : ""}${clause.clauseNo}`
      }
    },
    isSelf() {
      return (
        this.$route.path ==
        "/hospitalSelfEvaluation/hospitalSelfEvaluationIndex"
      );
    },
    isHospital() {
      // 医疗机构
      return (
        this.$store.getters.roles.includes("hospital") || this.showOnlyHospital
      );
    },
    isAssessor() {
      // 评审员
      return (
        this.$store.getters.roles.includes("assessor") ||
        this.$store.getters.roles.includes("assessor-leader")
      );
    },
    isInspector() {
      // 审查员
      return (
        this.$store.getters.roles.includes("inspector") ||
        this.$store.getters.roles.includes("inspector-leader")
      );
    },

    isComplete() {
      let flag = true;
      for (const key in this.formData) {
        if (Object.hasOwnProperty.call(this.formData, key)) {
          const element = this.formData[key];
          if (!element || element.length == 0) {
            flag = false;
            break;
          }
        }
      }
      return flag;
    },

    formShow() {
      return this.isComplete || this.editing;
    },

    // 展示 形式审查信息
    showFirstCheck() {
      // 如果是医院端查看，只在审查总结(不论通过还是拒绝)之后可见初评初查信息
      const status = [
        this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_SUM_REJECT,
        this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_SUM_PASS,
      ];
      if (this.isHospital)
        return (
          status.includes(this.autSaAudStatus) ||
          this.autSaAudStatus >=
          this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_SUM_REJECT
        );

      if (this.isAssessor) {
        return (
          this.autSaAudStatus >=
          this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_SUM_PASS
        );
      }

      let state = Number(this.autSaAudStatus),
        status1 = this.AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL,
        status2 = this.AutSaAudStatusEnum.WAIT_REPORT;
      return state >= Number(status1) && state <= Number(status2);
    },

    // 展示 审查审核信息
    showSecondCheck() {
      // 医院端和评审端是看不了
      if (Number(this.autSaAudStatus) < this.AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL_REVIEW) return false;
      if (this.isHospital || this.isAssessor) return false;
      if (this.isInspector && !this.isLeader && !this.sas) return false;
      let statues = [
        this.AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL_REVIEW,
        this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_REVIEW_PROC,

      ]
      let entry = Number(this.$route.query.entry) || 0
      if (statues.includes(Number(this.autSaAudStatus))) {
        if (entry == 1 && this.sas) return true;
        if (entry != 1) return true;
      }
      if (this.autSaAudStatus == this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_REVIEW_REJECT) {
        return true;
      }
      return false;
    },
    showFirstAudit() {
      const status = [
        this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_SUM_REJECT,
        this.AutSaAudStatusEnum.AUD_PASS,
        this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_SUM_CONDITIONAL_PASS,
      ];
      if (this.isHospital)
        return (
          status.includes(Number(this.autSaAudStatus)) ||
          this.autSaAudStatus >=
          this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_SUM_REJECT
        );
      if (this.isInspector && !this.isLeader) return false;

      let state = Number(this.autSaAudStatus),
        status1 = this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_SUM_PASS,
        status2 = this.AutSaAudStatusEnum.WAIT_REVIEW_REVIEW_REPORT;
      if (this.isInspector && state >= status2) return true;
      return state >= Number(status1);
    },
    showSecondAudit() {
      if (this.isHospital || this.isInspector) return false;
      if (this.isAssessor && !this.isLeader && !this.audS) return false;

      let state = Number(this.autSaAudStatus),
        status = [
          this.AutSaAudStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW,
          this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC,
        ],
        entry = this.$route.query.entry || 0
      if (status.includes(state)) {
        if (entry == 3 && this.audS) return true;
        if (entry != 3) return true;
      }
      if (this.autSaAudStatus == this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_REVIEW_REJECT) {
        return true;
      }
      return false;
    },

    showCheckReport() {
      return (
        Number(this.autSaAudStatus) >=
        this.AutSaAudStatusEnum.WAIT_REVIEW_REVIEW_REPORT
      );
    },

    showReportTruth() {
      return (
        Number(this.autSaAudStatus) >=
        this.AutSaAudStatusEnum.WAIT_FACTUAL_ACCURACY_REVIEW
      );
    },

    showFirstModify() {
      if (
        Number(this.autSaAudStatus) ==
        this.AutSaAudStatusEnum.REVIEWER_MODIFICATION_REVIEW
      ) {
        return true;
      } else if (
        Number(this.autSaAudStatus) ==
        this.AutSaAudStatusEnum.REVIEWER_MODIFICATION_REVIEW
      ) {
        return !!this.audF;
      }
      return false;
    },

    showSeniorAudit() {
      return (
        Number(this.autSaAudStatus) >=
        this.AutSaAudStatusEnum.WAIT_SENIOR_REVIEWER_REVIEW
      );
    },

    showSecondModify() {
      if (
        Number(this.autSaAudStatus) ==
        this.AutSaAudStatusEnum.REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW
      ) {
        return true;
      } else if (
        Number(this.autSaAudStatus) >
        this.AutSaAudStatusEnum.REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW
      ) {
        return !!this.audSS;
      }
      return false;
    },

    saReadOnly() {
      return (
        (this.autSaAudStatus != this.AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL &&
          this.autSaAudStatus !=
          this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_INQUIRE_PROC &&
          this.autSaAudStatus !=
          this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_REVIEW_REJECT) ||
        this.readOnly
      );
    },
    saSReadOnly() {
      return (
        (this.autSaAudStatus !=
          this.AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL_REVIEW &&
          this.autSaAudStatus !=
          this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_REVIEW_PROC &&
          (Number(this.$route.query.entry) || 0 == 1 || Number(this.$route.query.entry) || 0 == 3)) ||
        this.readOnly
      );
    },
    audReadOnly() {
      return (
        (this.autSaAudStatus !=
          this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_SUM_PASS &&
          this.autSaAudStatus !=
          this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_INQUIRE_PROC &&
          this.autSaAudStatus !=
          this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_REVIEW_REJECT) ||
        this.readOnly
      );
    },
    audSReadOnly() {
      return (
        (this.autSaAudStatus !=
          this.AutSaAudStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW &&
          this.autSaAudStatus !=
          this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC) ||
        this.readOnly
      );
    },
  },

  mounted() {
    // 用户类型：0医院 1审查员 2审查组长 3评审员 4评审组长
    // this.$refs["evaluation"].init({
    //   clause: this.clause,
    //   formData: this.aut,
    //   fileList: this.fileList,
    //   status: this.autSaAudStatus,
    // });
  },

  methods: {
    getValue() { },

    query() {
      request({
        url: "/system/criterion/queryCriterionList",
        method: "post",
        data: {
          id: this.clause.clauseId,
          versionId: this.clause.versionId,
        },
      }).then((res) => {
        // console.log(res);
      });
    },

    handleEvaluate() {
      this.editing = true;
    },

    handleComplete(formData, clauseId) {
      this.editing = false;
      this.$emit("complete", formData, clauseId);
    },

    otherComplete(event, formData, clauseId, type) {
      this.$emit("complete", event, formData, clauseId, type);
    },

    handleCancel() {
      this.editing = false;
      this.formData = {
        ...JSON.parse(JSON.stringify(this.defaultFormData)),
      };
    },

    toDetail({ clauseId, versionId }) {
      this.$router.push({
        name: "ClauseParticulars",
        params: {
          clauseId,
          versionId,
        },
      });
    },
    mouseover(event) {
      this.$nextTick(() => {
        if (event.currentTarget && event.currentTarget.children && event.currentTarget.children[0]) {
          let child = event.currentTarget.children[0];
          if (child.clientHeight > 300) {
            this.showScroll = true;
          }
        }
      })
    },
    mouseout() {
      this.showScroll = false;
    }
  },
};
</script>

<style lang="scss" scoped>
.clause-item {
  ::v-deep .el-form-item {
    .el-form-item__label {
      font-size: 12px;
      color: #6f6f6f;
      font-weight: normal;
      padding-right: 9px;
      // text-align: left;
    }
    &.readonly .el-form-item__label::before {
      content: "" !important;
    }
  }
  .clause-title {
    color: #272727;
  }

  .clause-detail {
    float: right;
  }
  .el-icon--right {
    margin-left: 0;
  }
  .clause-content {
    background-color: #fbfbfb;
    padding: 20px;
    position: relative;
  }
  .clause-information {
    ::v-deep .el-button {
      font-size: 14px;
      margin-right: 15px;
    }
  }
}
.clause-item {
  & > ::v-deep .el-form-item__label {
    // text-align: left;
    text-indent: 15px;
    font-size: 12px;
    font-weight: bold;
    line-height: 24px;
  }

  & > ::v-deep .el-form-item__content {
    line-height: 24px;
  }
}
.scroll-div {
  max-height: 300px;
  width: 100%;
  & > div {
    width: 98%;
  }
  overflow-y: scroll;
  h4 {
    color: black;
  }
  &::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
  }
}
.scroll-div.showScrollbar {
  &::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #535353;
  }
  &::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    background: #ededed;
  }
}
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  line-height: 16px;
}
</style>
