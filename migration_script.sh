#!/bin/bash

# 认证自评审核查询功能迁移脚本
# 使用方法: ./migration_script.sh <源项目路径> <目标项目路径>

SOURCE_PROJECT=$1
TARGET_PROJECT=$2

if [ -z "$SOURCE_PROJECT" ] || [ -z "$TARGET_PROJECT" ]; then
    echo "使用方法: $0 <源项目路径> <目标项目路径>"
    exit 1
fi

echo "开始迁移认证自评审核查询功能..."
echo "源项目: $SOURCE_PROJECT"
echo "目标项目: $TARGET_PROJECT"

# 创建目标目录结构
create_directories() {
    echo "创建目标目录结构..."
    
    # Controller目录
    mkdir -p "$TARGET_PROJECT/src/main/java/com/thas/web/controller/common"
    
    # Service目录
    mkdir -p "$TARGET_PROJECT/src/main/java/com/thas/web/service"
    mkdir -p "$TARGET_PROJECT/src/main/java/com/thas/web/service/impl"
    mkdir -p "$TARGET_PROJECT/src/main/java/com/thas/web/service/process"
    mkdir -p "$TARGET_PROJECT/src/main/java/com/thas/web/service/process/impl"
    
    # Domain目录
    mkdir -p "$TARGET_PROJECT/src/main/java/com/thas/web/domain"
    mkdir -p "$TARGET_PROJECT/src/main/java/com/thas/web/domain/vo"
    mkdir -p "$TARGET_PROJECT/src/main/java/com/thas/web/dto"
    
    # Mapper目录
    mkdir -p "$TARGET_PROJECT/src/main/java/com/thas/web/mapper"
    mkdir -p "$TARGET_PROJECT/src/main/resources/mapper/common"
    
    # 枚举目录
    mkdir -p "$TARGET_PROJECT/src/main/java/com/thas/common/enums"
    
    echo "目录结构创建完成"
}

# 迁移枚举类
migrate_enums() {
    echo "迁移枚举类..."
    
    cp "$SOURCE_PROJECT/thas-common/src/main/java/com/thas/common/enums/AutSaAudRoleEnum.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/common/enums/"
    
    cp "$SOURCE_PROJECT/thas-common/src/main/java/com/thas/common/enums/AutSaAudStatusEnum.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/common/enums/"
    
    cp "$SOURCE_PROJECT/thas-common/src/main/java/com/thas/common/enums/AutSaAudResultEnum.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/common/enums/"
    
    echo "枚举类迁移完成"
}

# 迁移Domain对象
migrate_domains() {
    echo "迁移Domain对象..."
    
    # 主要Domain对象
    cp "$SOURCE_PROJECT/thas-admin/src/main/java/com/thas/web/domain/AutSaAudQueryDTO.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/web/domain/"
    
    cp "$SOURCE_PROJECT/thas-admin/src/main/java/com/thas/web/domain/AutSaAudList.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/web/domain/"
    
    cp "$SOURCE_PROJECT/thas-admin/src/main/java/com/thas/web/domain/AutSaAud.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/web/domain/"
    
    cp "$SOURCE_PROJECT/thas-admin/src/main/java/com/thas/web/domain/AutSaRelation.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/web/domain/"
    
    cp "$SOURCE_PROJECT/thas-admin/src/main/java/com/thas/web/domain/AutSaAudReport.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/web/domain/"
    
    cp "$SOURCE_PROJECT/thas-admin/src/main/java/com/thas/web/domain/GroupProgressInfo.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/web/domain/"
    
    # VO对象
    cp "$SOURCE_PROJECT/thas-admin/src/main/java/com/thas/web/domain/vo/AutSaAudVo.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/web/domain/vo/"
    
    cp "$SOURCE_PROJECT/thas-admin/src/main/java/com/thas/web/domain/vo/FileInfoVO.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/web/domain/vo/"
    
    cp "$SOURCE_PROJECT/thas-admin/src/main/java/com/thas/web/domain/vo/HospitalReviewerVo.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/web/domain/vo/"
    
    echo "Domain对象迁移完成"
}

# 迁移Mapper
migrate_mappers() {
    echo "迁移Mapper..."
    
    # Mapper接口
    cp "$SOURCE_PROJECT/thas-admin/src/main/java/com/thas/web/mapper/AutSaAudMapper.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/web/mapper/"
    
    cp "$SOURCE_PROJECT/thas-admin/src/main/java/com/thas/web/mapper/AutSaRelationMapper.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/web/mapper/"
    
    cp "$SOURCE_PROJECT/thas-admin/src/main/java/com/thas/web/mapper/CommonProcessMapper.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/web/mapper/"
    
    # Mapper XML文件
    cp "$SOURCE_PROJECT/thas-admin/src/main/resources/mapper/common/AutSaAudMapper.xml" \
       "$TARGET_PROJECT/src/main/resources/mapper/common/"
    
    cp "$SOURCE_PROJECT/thas-admin/src/main/resources/mapper/common/AutSaRelationMapper.xml" \
       "$TARGET_PROJECT/src/main/resources/mapper/common/"
    
    cp "$SOURCE_PROJECT/thas-admin/src/main/resources/mapper/common/CommonProcessMapper.xml" \
       "$TARGET_PROJECT/src/main/resources/mapper/common/"
    
    echo "Mapper迁移完成"
}

# 迁移Service
migrate_services() {
    echo "迁移Service..."
    
    # Service接口
    cp "$SOURCE_PROJECT/thas-admin/src/main/java/com/thas/web/service/IAutSaAudService.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/web/service/"
    
    cp "$SOURCE_PROJECT/thas-admin/src/main/java/com/thas/web/service/IAutSaRelationService.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/web/service/"
    
    # Service实现
    cp "$SOURCE_PROJECT/thas-admin/src/main/java/com/thas/web/service/impl/AutSaAudServiceImpl.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/web/service/impl/"
    
    cp "$SOURCE_PROJECT/thas-admin/src/main/java/com/thas/web/service/impl/AutSaRelationServiceImpl.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/web/service/impl/"
    
    # 流程Service
    cp "$SOURCE_PROJECT/thas-admin/src/main/java/com/thas/web/service/process/BaseProcessService.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/web/service/process/"
    
    cp "$SOURCE_PROJECT/thas-admin/src/main/java/com/thas/web/service/process/impl/BaseProcessServiceImpl.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/web/service/process/impl/"
    
    cp "$SOURCE_PROJECT/thas-admin/src/main/java/com/thas/web/service/process/impl/CommonProcessServiceImpl.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/web/service/process/impl/"
    
    cp "$SOURCE_PROJECT/thas-admin/src/main/java/com/thas/web/service/process/impl/ValidationReviewer01ProcessServiceImpl.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/web/service/process/impl/"
    
    echo "Service迁移完成"
}

# 迁移Controller
migrate_controllers() {
    echo "迁移Controller..."
    
    cp "$SOURCE_PROJECT/thas-admin/src/main/java/com/thas/web/controller/common/AutSaAudController.java" \
       "$TARGET_PROJECT/src/main/java/com/thas/web/controller/common/"
    
    echo "Controller迁移完成"
}

# 生成数据库迁移脚本
generate_db_script() {
    echo "生成数据库迁移脚本..."
    
    cat > "$TARGET_PROJECT/db_migration.sql" << 'EOF'
-- 认证自评审核功能数据库迁移脚本

-- 1. 认证自评关联表
CREATE TABLE IF NOT EXISTS `aut_sa_relation` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `aut_code` varchar(64) NOT NULL COMMENT '自评编码',
    `aut_cs_id` varchar(64) NOT NULL COMMENT '自评认证标准id',
    `hospital_apply_no` varchar(64) NOT NULL COMMENT '医院编号',
    `aut_status` varchar(64) NOT NULL DEFAULT '' COMMENT '认证状态',
    `status` tinyint NOT NULL DEFAULT 1 COMMENT '是否有效（0:无效,1：有效）',
    `create_id` int(11) NOT NULL DEFAULT 0 COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_id` int(11) NOT NULL DEFAULT 0 COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `evaluate_flag` tinyint DEFAULT 0 COMMENT '医院评审学员是否可现场评审带教培训评估',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_aut_code` (`aut_code`) USING BTREE,
    KEY `idx_hospital_apply_no` (`hospital_apply_no`) USING BTREE,
    KEY `idx_aut_status` (`aut_status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci ROW_FORMAT=DYNAMIC COMMENT='认证自评关联表';

-- 2. 认证自评审核表
CREATE TABLE IF NOT EXISTS `aut_sa_aud` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `aut_code` varchar(64) NOT NULL COMMENT '自评编码',
    `submit_type` varchar(64) NOT NULL DEFAULT '' COMMENT '提交类型',
    `clause_id` varchar(64) COMMENT '条款id',
    `account_id` varchar(64) NOT NULL COMMENT '医院id(条款自评/自评总结)/审核员id(初审初查/初审复查)/评审员id(评审初查/评审复查)',
    `aut_result` varchar(64) NOT NULL DEFAULT '' COMMENT '自评打分/审核结果',
    `aut_desc` BLOB COMMENT '自评描述（条款自评/自评总结）/审核描述',
    `file_ids` varchar(1024) COMMENT '材料文件Id列表',
    `submit_date` datetime COMMENT '提交时间',
    `status` tinyint NOT NULL DEFAULT 1 COMMENT '审核状态（0:无效,1：有效）',
    `risk_impact` int COMMENT '风险影响',
    `risk_possibility` int COMMENT '风险可能性',
    `reviewer_respond` text COMMENT '验证评审员回应',
    `before_aut_desc` BLOB COMMENT '修改前的自评描述',
    `before_aut_result` varchar(64) COMMENT '修改前的评审结果',
    `is_share_update` tinyint DEFAULT 0 COMMENT '共享评审是否需修改',
    `proposer_ids` varchar(255) COMMENT '共享需修改提出人Id',
    `share_desc` text COMMENT '共享修改描述',
    `create_id` int(11) NOT NULL DEFAULT 0 COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_id` int(11) NOT NULL DEFAULT 0 COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_aut_code` (`aut_code`) USING BTREE,
    KEY `idx_account_id` (`account_id`) USING BTREE,
    KEY `idx_clause_id` (`clause_id`) USING BTREE,
    KEY `idx_submit_type` (`submit_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci ROW_FORMAT=DYNAMIC COMMENT='认证自评审核表';

-- 3. 状态配置表
CREATE TABLE IF NOT EXISTS `asa_status_config` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `current_status` varchar(64) NOT NULL COMMENT '当前状态',
    `status_desc` varchar(255) COMMENT '状态描述',
    `next_status_config` text COMMENT '下一状态配置',
    `service_name` varchar(255) COMMENT '服务名称',
    `process_method` varchar(255) COMMENT '处理方法',
    `check_method` varchar(255) COMMENT '校验方法',
    `submit_type` varchar(64) COMMENT '提交类型',
    `cycle_stage` varchar(64) COMMENT '周期阶段',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_current_status` (`current_status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci COMMENT='状态配置表';

-- 4. 业务配置表
CREATE TABLE IF NOT EXISTS `aut_sa_aud_business_config` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `item_type` varchar(64) NOT NULL COMMENT '配置项类型',
    `item_key` varchar(64) NOT NULL COMMENT '配置项键',
    `item_arr1` text COMMENT '配置项数组1',
    `item_arr2` text COMMENT '配置项数组2',
    `item_desc` varchar(255) COMMENT '配置项描述',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_item_type_key` (`item_type`, `item_key`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci COMMENT='业务配置表';
EOF

    echo "数据库迁移脚本生成完成: $TARGET_PROJECT/db_migration.sql"
}

# 生成配置文件模板
generate_config_template() {
    echo "生成配置文件模板..."
    
    cat > "$TARGET_PROJECT/application-migration.yml" << 'EOF'
# 认证自评审核功能相关配置

mybatis:
  mapper-locations: 
    - classpath:mapper/common/*.xml
  type-aliases-package: com.thas.web.domain

spring:
  datasource:
    # 请根据实际情况配置数据库连接
    url: ******************************************************************************************************************************************************
    username: your_username
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver

# 分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

# 日志配置
logging:
  level:
    com.thas.web.mapper: debug
EOF

    echo "配置文件模板生成完成: $TARGET_PROJECT/application-migration.yml"
}

# 主执行流程
main() {
    create_directories
    migrate_enums
    migrate_domains
    migrate_mappers
    migrate_services
    migrate_controllers
    generate_db_script
    generate_config_template
    
    echo ""
    echo "迁移完成！"
    echo ""
    echo "后续步骤："
    echo "1. 检查并调整包名和导入路径"
    echo "2. 执行数据库迁移脚本: $TARGET_PROJECT/db_migration.sql"
    echo "3. 配置数据库连接: $TARGET_PROJECT/application-migration.yml"
    echo "4. 运行测试验证功能"
    echo ""
    echo "请参考 migration_checklist.md 进行详细的迁移验证"
}

# 执行主流程
main
