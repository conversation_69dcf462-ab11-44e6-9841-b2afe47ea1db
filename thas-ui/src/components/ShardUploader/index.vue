<!--  -->
<template>
  <div class='shard-uploader'>
    <uploader ref="uploader" :options="options" :autoStart="false" @file-added="onFileAdded" @file-success="onFileSuccess" @file-progress="onFileProgress" @file-error="onFileError" class="uploader-app">
      <uploader-unsupport></uploader-unsupport>
      <uploader-btn id="global-uploader-btn" :attrs="attrs" single ref="uploadBtn" :style="{pointerEvents: uploading ? 'none': 'all'}">
        <span class="el-button el-button--primary el-button--medium">
          <i v-if="!uploading" class="el-icon-document-add"></i>
          <i v-else class="el-icon-loading"></i>
          上传视频
        </span>
      </uploader-btn>
      <div class="upload-list">
        <span v-if="uploading == false && successFile">
          <el-button type="text" @click="download">{{successFile.filename}}</el-button>
          <el-button class="hoverClose" type="text" size="mini" icon="el-icon-close" @click="clear"></el-button>
        </span>
        <el-progress v-if="uploading == true" :stroke-width="20" :format="format" :percentage="percentage" text-inside style="width:200px"></el-progress>
      </div>

    </uploader>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import request, { download } from '@/utils/request'
import SparkMD5 from 'spark-md5';

const baseUrl = localStorage.baseUrl || process.env.VUE_APP_BASE_API

export default {
  name: 'ShardUploader',
  components: {},
  props: {
    files: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    files: {
      immediate: true,
      deep: true,
      handler() {
        if (this.files && this.files.length) {
          this.uploading = false;
          this.successFile = this.files[0]
          this.successFile.filename = this.successFile.name;
        }
      }
    }
  },
  data() {
    const chunkSize = 10 * 1024 * 1000;
    return {
      chunkSize: chunkSize,
      options: {
        forceChunkSize: true,
        target: baseUrl + '/bigFileUpload', // 目标上传 URL
        chunkSize: chunkSize,   //分块大小
        fileParameterName: 'file', //上传文件时文件的参数名，默认file
        maxChunkRetries: 3,  //最大自动失败重试上传次数
        testChunks: true,     //是否开启服务器分片校验
        simultaneousUploads: 1,
        // 服务器分片校验函数，秒传及断点续传基础
        checkChunkUploadedByResponse: function (chunk, message) {
          let objMessage = JSON.parse(message);
          if (objMessage.data.skipUpload) {
            return true;
          }
          if (objMessage.data.mergeFile) {
            return true
          }
          let list = (objMessage.data.uploadedChunks || []).map(chunk => Number(chunk));
          return list.indexOf(chunk.offset + 1) >= 0
        },
        headers: {
          // 在header中添加的验证，请根据实际业务来
          Authorization: "Bearer " + getToken()
        },
        // 额外的自定义查询参数
        query: (file, chunk) => {
          return {
            ...file.params,
          }
        },

      },
      attrs: {
        // 接受的文件类型，形如['.png', '.jpg', '.jpeg', '.gif', '.bmp'...] 这里我封装了一下
        accept: '.mp4'
      },
      panelShow: false,   //选择文件后，展示上传panel
      errorFileList: [],
      percentage: 0,
      tip: '',
      fileName: '',
      uploading: null,
      mergeCount: 0,
      successFile: null,
    }
  },

  computed: {
    uploader() {
      return this.$refs.uploader
    }
  },

  created() { },

  mounted() {
    this.$nextTick(() => {
      // this.$refs.uploadBtn.$el.dispatchEvent(new MouseEvent('click'))
    })
  },

  methods: {
    clear() {
      this.deleteUploadFile()
    },

    deleteUploadFile() {
      this.successFile = null;
      this.uploading = null;
      this.uploader.files.forEach(file => file.cancel())
      this.$emit('fileRemove');
    },

    download() {
      download(this.successFile.id, {}, this.successFile.name);
    },
    format(percentage) {
      return `${this.tip} : ${percentage}%`;
    },
    mergeFile(data) {
      const form = new FormData()
      for (const key in data) {
        if (Object.hasOwnProperty.call(data, key)) {
          const element = data[key];
          form.append(key, element);
        }
      }
      this.tip = '合并中';
      this.percentage = 99;
      request({
        url: '/bigFileUpload/mergeFile',
        method: 'post',
        data: form,
        timeout: 0
      }).then(res => {
        if (res.code == 200) {
          this.percentage = 100;
          this.successFile = {
            ...data,
            ...res.data
          };
          this.$emit('uploadSuccess', res.data.fileId);
        }
      }).finally(() => {
        this.uploading = false;
      })
    },
    //添加文件到列表还未上传,每添加一个文件，就会调用一次,在这里过滤并收集文件夹中文件格式不正确信息，同时把所有文件的状态设为暂停中
    onFileAdded(file) {
      if (file.fileType.indexOf('mp4') == -1) {
        return this.$message({
          type: 'warning',
          message: '上传的文件格式不正确'
        })
      }
      this.uploading = true;
      this.computeMD5(file);
      this.panelShow = true;
    },
    // 文件进度的回调
    onFileProgress(rootFile, file, chunk) {
      // // console.log(`上传中 ${file.name}，chunk：${chunk.startByte / 1024 / 1024} ~ ${chunk.endByte / 1024 / 1024}`)
      this.percentage = Math.round(chunk.endByte * 100 / file.size);
    },
    //每个文件传输给后端之后，返回的信息
    onFileSuccess(rootFile, file, response, chunk) {
      // console.log(response);
      let res = JSON.parse(response)
      this.tip = '';
      this.percentage = 0;
      if (res.code == 200) {
        // 上传 + 合并完成
        if (res.data.skipUpload) {
          this.successFile = {
            filename: file.name,
            ...res.data
          };
          this.uploading = false;
          this.$emit('uploadSuccess', res.data.fileId);
          return;
        };
        // 上传完成 + 未合并
        if (res.data.mergeFile) {
          let { uniqueIdentifier, name, size } = file;
          this.mergeFile({
            identifier: uniqueIdentifier,
            filename: name,
            totalSize: size,
          })
          return;
        }

        if (!res.data.skipUpload && !res.data.mergeFile) {
          this.$message({
            type: 'error',
            message: '文件上传失败,请重新上传'
          })
          this.uploading = false;
          if (this.uploader.cancel) {
            this.uploader.cancel();
          }
        }
      }
    },
    // 上传错误触发，文件还未传输到后端
    onFileError(rootFile, file, response, chunk) {
      this.$message({
        type: 'error',
        message: '文件上传失败,请重新上传'
      })
      this.uploading = false;
      if (this.uploader.cancel) {
        this.uploader.cancel();
      }
    },
    // 移除文件
    fileRemoved(file) {
      this.$nextTick(() => {
        this.file_total = this.$refs['uploader'].files.length
      });
    },
    //点击开始上传按钮
    submitUpload() {
      this.$nextTick(() => {
        for (var i = 0; i < this.$refs['uploader'].files.length; i++) {
          this.$refs['uploader'].files[i].resume()
        }
      });
    },
    //关闭错误文件提示框口，知道上传对话框被关闭时才会被清空
    closeErrorDialog() {
      this.errorDialog = false;
    },
    // 上传弹框关闭
    handelClose() {
      this.clearCache()
      this.thirdDialog = false
    },
    // 清除缓存
    clearCache() {
      this.file_total = 0;
      this.errorFileList = []
      this.controllerErrorFileDialog = false
      this.$refs.uploader.uploader.cancel()
    },
    //取消上传
    cancelUpload() {
      this.thirdDialog = false;
      this.clearCache();
    },

    /**
    * 计算md5，实现断点续传及秒传
    * @param file
    */
    computeMD5(file) {
      this.tip = '分片中'
      let fileReader = new FileReader();
      let time = new Date().getTime();
      let blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice;
      let currentChunk = 0;
      const chunkSize = this.chunkSize;
      let chunks = Math.ceil(file.size / chunkSize);
      let spark = new SparkMD5.ArrayBuffer();

      // 文件状态设为"计算MD5"
      //   this.statusSet(file.id, 'md5');

      file.pause();

      loadNext();

      fileReader.onload = (e => {
        spark.append(e.target.result);
        if (currentChunk < chunks) {
          currentChunk++;
          loadNext();
          // 实时展示MD5的计算进度
          this.$nextTick(() => {
            this.percentage = Math.round((currentChunk / chunks) * 100)
            // console.log('校验MD5 ' + ((currentChunk / chunks) * 100).toFixed(0) + '%');
          })
        } else {
          let md5 = spark.end();
          this.computeMD5Success(md5, file);
          // console.log(`MD5计算完毕：${file.name} \nMD5：${md5} \n分片：${chunks} 大小:${file.size} 用时：${new Date().getTime() - time} ms`);
        }
      });
      fileReader.onerror = function () {
        this.error(`文件${file.name}读取出错，请检查该文件`)
        file.cancel();
      };
      function loadNext() {
        let start = currentChunk * chunkSize;
        let end = ((start + chunkSize) >= file.size) ? file.size : start + chunkSize;
        fileReader.readAsArrayBuffer(blobSlice.call(file.file, start, end));
      }
    },

    computeMD5Success(md5, file) {
      this.tip = '上传中'
      this.percentage = 0;
      setTimeout(() => {
        file.uniqueIdentifier = md5;
        file.resume();
      }, 500);
      //   this.statusRemove(file.id);
    },
  }
}

</script>
<style lang='scss' scoped>
.shard-uploader {
  // 文件上传组件
  .uploader-example {
    width: 90%;
    padding: 15px;
    margin: 0 auto 0;
    font-size: 14px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.4);
  }
  .uploader-btn {
    margin-right: 8px;
    color: #ffffff;
    border: 0;
    padding: 0;
  }

  ::v-deep .uploader-example .uploader-list {
    max-height: 300px;
    overflow: auto;
    overflow-x: hidden;
    overflow-y: auto;
    .uploader-file[status="uploading"] > .uploader-file-info {
      > .uploader-file-status > span > i {
        visibility: hidden;
      }
      > .uploader-file-status > span > em {
        visibility: hidden;
      }
    }
  }
  .upload-list {
    display: inline-block;
    span {
      &:hover .hoverClose {
        visibility: visible;
      }
      .hoverClose {
        visibility: hidden;
      }
    }
  }
}
</style>