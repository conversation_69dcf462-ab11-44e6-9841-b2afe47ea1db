package com.thas.web.controller.common;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.thas.common.utils.poi.ExcelUtil;
import com.thas.web.domain.HospitalAuthContact;
import com.thas.web.service.IHospitalAuthContactService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.enums.BusinessType;
import com.thas.common.core.page.TableDataInfo;

/**
 * 医疗机构被授权人信息Controller
 * 
 * <AUTHOR>
 * @date 2022-01-19
 */
@RestController
@RequestMapping("/system/contact")
@Api(value = "HospitalAuthContactController", tags = "医疗机构被授权人信息Controller")
public class HospitalAuthContactController extends BaseController
{
    @Autowired
    private IHospitalAuthContactService hospitalAuthContactService;

    /**
     * 查询医疗机构被授权人信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:contact:list')")
    @GetMapping("/list")
    @ApiOperation("list")
    public TableDataInfo list(HospitalAuthContact hospitalAuthContact)
    {
        startPage();
        List<HospitalAuthContact> list = hospitalAuthContactService.selectHospitalAuthContactList(hospitalAuthContact);
        return getDataTable(list);
    }

    /**
     * 导出医疗机构被授权人信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:contact:export')")
    @Log(title = "医疗机构被授权人信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HospitalAuthContact hospitalAuthContact)
    {
        List<HospitalAuthContact> list = hospitalAuthContactService.selectHospitalAuthContactList(hospitalAuthContact);
        ExcelUtil<HospitalAuthContact> util = new ExcelUtil<HospitalAuthContact>(HospitalAuthContact.class);
        util.exportExcel(response, list, "医疗机构被授权人信息数据");
    }

    /**
     * 获取医疗机构被授权人信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:contact:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(hospitalAuthContactService.selectHospitalAuthContactById(id));
    }

    /**
     * 新增医疗机构被授权人信息
     */
    @PreAuthorize("@ss.hasPermi('system:contact:add')")
    @Log(title = "医疗机构被授权人信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("add")
    public AjaxResult add(@RequestBody HospitalAuthContact hospitalAuthContact)
    {
        return toAjax(hospitalAuthContactService.insertHospitalAuthContact(hospitalAuthContact));
    }

    /**
     * 修改医疗机构被授权人信息
     */
    @PreAuthorize("@ss.hasPermi('system:contact:edit')")
    @Log(title = "医疗机构被授权人信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("edit")
    public AjaxResult edit(@RequestBody HospitalAuthContact hospitalAuthContact)
    {
        return toAjax(hospitalAuthContactService.updateHospitalAuthContact(hospitalAuthContact));
    }

    /**
     * 删除医疗机构被授权人信息
     */
    @PreAuthorize("@ss.hasPermi('system:contact:remove')")
    @Log(title = "医疗机构被授权人信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(hospitalAuthContactService.deleteHospitalAuthContactByIds(ids));
    }
}
