package com.thas.web.controller.common;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.thas.system.domain.vo.TraineesAssessorListVo;
import com.thas.web.domain.TraQuestionnaireFeedBack;
import com.thas.web.domain.dto.TraQuestionnaireFeedBackDTO;
import com.thas.web.service.ITraQuestionnaireFeedBackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.enums.BusinessType;

import java.util.List;

/**
 * 反馈问卷Controller
 * 
 * <AUTHOR>
 * @date 2022-10-26
 */
@RestController
@RequestMapping("/tra/questionnaire/feed/back")
@Api(value = "API - TraQuestionnaireController", tags = "反馈问卷Controller")
public class TraQuestionnaireFeedBackController extends BaseController
{
    @Autowired
    private ITraQuestionnaireFeedBackService traQuestionnaireFeedBackService;

    /**
     * 查询反馈问卷列表
     */
    @GetMapping("/list")
    @ApiOperation(httpMethod = "GET", value = "填写反馈问卷操作（校验是否允许操作和返回对应数据）")
    public AjaxResult list(@ApiParam(value = "调查问卷id") @NotNull(message = "调查问卷id为空") Long questionnaireId,
                              @ApiParam(value = "反馈问卷类型-对应字典值") @NotNull(message = "反馈问卷类型为空") Long feedBackType){
        TraineesAssessorListVo traineesAssessorListVo = traQuestionnaireFeedBackService.selectTraQuestionnaireFeedBackList(questionnaireId, feedBackType);
        return AjaxResult.success(traineesAssessorListVo);
    }

    /**
     * 新增反馈问卷
     */
    @Log(title = "提交反馈问卷", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation(value = "提交反馈问卷")
    public AjaxResult add(@RequestBody @Valid TraQuestionnaireFeedBackDTO req)
    {
        return toAjax(traQuestionnaireFeedBackService.insertTraQuestionnaireFeedBack(req));
    }

    /**
     * 获取当前登录用户对应的反馈问卷信息
     */
    @GetMapping("/details")
    @ApiOperation(httpMethod = "GET", value = "获取当前登录用户反馈问卷信息 ")
    public AjaxResult details(@ApiParam(value = "反馈问卷类型-对应字典值") @NotNull(message = "反馈问卷类型为空") Long feedBackType,
                              @ApiParam(value = "调查问卷id") @NotNull(message = "调查问卷id为空") Long questionnaireId) {
        List<TraQuestionnaireFeedBack> traQuestionnaireFeedBackDTO =  traQuestionnaireFeedBackService.details(feedBackType,questionnaireId);
        return AjaxResult.success(traQuestionnaireFeedBackDTO);
    }

    /**
     * 查询自评编码对应的医院获取对应的用户信息
     */
    @GetMapping("/select/reviewerInfo")
    @ApiOperation(httpMethod = "GET", value = "根据自评编码查询医院对应的评审员信息")
    public AjaxResult selectReviewerInfo(@ApiParam(value = "自评编码") @NotNull(message = "自评编码为空") String autCode){
        return AjaxResult.success(traQuestionnaireFeedBackService.selectReviewerInfo(autCode));
    }

}
