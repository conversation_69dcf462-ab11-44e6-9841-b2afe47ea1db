<!--  -->
<template>
  <div class="base-clause-reject" v-if="autSaAudReport">
    <el-card class="box-card" shadow="never">
      <span style="color:red;">{{ statusName }}</span>
    </el-card>
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>基本款“有条件通过认证”及“不通过认证”的风险等级条件</span>
      </div>
      <el-table :data="dataSource" border stripe :span-method="arraySpanMethod">
        <el-table-column label="基本款" prop="rejectedNum"></el-table-column>
        <el-table-column label="风险等级">
          <el-table-column label="总数" prop="total"></el-table-column>
          <el-table-column label="严重风险" prop="serious"></el-table-column>
          <el-table-column label="高风险" prop="high"></el-table-column>
          <el-table-column label="中风险" prop="middle"></el-table-column>
          <el-table-column label="低风险" prop="low"></el-table-column>
        </el-table-column>
        <el-table-column label="认证结果" prop="result"></el-table-column>
        <el-table-column label="实际基本款风险数">
          <el-table-column label="总数" prop="truthTotal"></el-table-column>
          <el-table-column label="严重风险" prop="truthSerious"></el-table-column>
          <el-table-column label="高风险" prop="truthHigh"></el-table-column>
          <el-table-column label="中风险" prop="truthMiddle"></el-table-column>
          <el-table-column label="低风险" prop="truthLow"></el-table-column>
        </el-table-column>
        <el-table-column label="实际认证结果" prop="truthResult" width="200">
          <template slot-scope="{row}">
            <span style="color:red;">{{ row.truthResult }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!--  -->
    <el-card v-if="!showWho" class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>“有条件通过认证”的条件</span>
      </div>
      <el-table :data="dataSource4" border stripe :header-cell-style="headerMethod2" :span-method="arraySpanMethod2">
        <el-table-column prop="title">
          <template slot-scope="scope">
            <div v-html="scope.row.title"></div>
          </template>
        </el-table-column>
        <el-table-column prop="chapter" width="140"></el-table-column>
        <el-table-column label="各条款评价结果" prop="result"></el-table-column>
        <el-table-column label="达标款数" prop="number"></el-table-column>
        <el-table-column label="达标占比" prop="range"></el-table-column>
        <el-table-column label="风险等级要求" prop="risk"></el-table-column>
        <el-table-column label="实际达标款数" prop="truthNumber"></el-table-column>
        <el-table-column label="实际达标占比" prop="truthRange"></el-table-column>
        <el-table-column label="实际风险等级" prop="truthRisk"></el-table-column>
        <el-table-column v-if="allFitClauseIds.length != 0" label="备注" prop="desc"></el-table-column>
      </el-table>
    </el-card>
    <!--  -->
    <el-card v-if="showWho" class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>非基本款“有条件通过认证”的条件</span>
      </div>
      <el-table :data="dataSource5" border stripe :header-cell-style="headerMethod3" :span-method="arraySpanMethod5">
        <el-table-column prop="title"></el-table-column>
        <el-table-column prop="chapter"></el-table-column>
        <el-table-column label="各条款评价结果" prop="result"></el-table-column>
        <el-table-column label="达标款数" prop="number"></el-table-column>
        <el-table-column label="达标占比" prop="range"></el-table-column>
        <el-table-column label="实际达标款数" prop="truthNumber"></el-table-column>
        <el-table-column label="实际达标占比" prop="truthRange"></el-table-column>
        <el-table-column v-if="allFitClauseIds.length != 0" label="备注" prop="desc"></el-table-column>
      </el-table>
    </el-card>
    <div style="height: 20px;"></div>
  </div>
</template>

<script>
import request from "@/utils/request";
export default {
  name: "BaseClauseReject",
  components: {},
  props: {},
  watch: {},
  data() {
    return {
      dataSource: [
        {
          rejectedNum: "未达标款数",
          total: "≤6款",
          serious: "0",
          high: "0",
          middle: "≤6款",
          low: "≤6款",
          result: "有条件通过认证",
          truthTotal: "0",
          truthSerious: "0",
          truthHigh: "0",
          truthMiddle: "0",
          truthLow: "0",
          truthResult: "有条件通过认证",
        },
        {
          rejectedNum: "未达标款数",
          total: "≥1款",
          serious: "≥1款",
          high: "0",
          middle: "0",
          low: "0",
          result: "不通过认证",
        },
        {
          rejectedNum: "未达标款数",
          total: "≥1款",
          serious: "0",
          high: "≥1款",
          middle: "≤2款",
          low: "0",
          result: "不通过认证",
        },
        {
          rejectedNum: "未达标款数",
          total: "≥7款",
          serious: "0",
          high: "0",
          middle: "≥4款",
          low: "≥7款",
          result: "不通过认证",
        },
      ],
      dataSource4: [],
      dataSource5: [],
      autSaAudReport: null,
      allFitClauseIds: [],
      showWho: false,
      versionId: '',
      clauseList: [],
      clauseMap: {},
    };
  },
  computed: {
    statusName() {
      return this.autSaAudReport.autSaAudResult == 2 ? "有条件通过认证" : "不通过认证";
    },
    desc() {
      if (this.allFitClauseIds.length == 0) return {}
      let basic = 0, minor = 0;
      this.allFitClauseIds.forEach(id => {
        let clause = this.clauseMap[id];
        if (!clause) return;
        if (clause.isStar == 1) {
          basic++
        } else {
          minor++
        }
      })
      return {
        basic,
        minor
      }
    },
    isHospital() {
      // 医疗机构
      return this.$store.getters.roles.includes("hospital");
    },
  },
  created() { },
  mounted() {
    this.query();
  },
  methods: {
    query() {
      let loading = this.$loading();
      if (this.isHospital) {
        request({
          url: "/aut/sa/aud/selectAutSaAudDetailList",
          method: "get",
          params: {
            autCode: this.$route.query.autCode
          },
        }).then((res) => {
          this.dealWith(res)
        }).finally(() => {
          loading.close()
        });
      } else {
        request({
          url: '/aut/sa/aud/queryList',
          method: "post",
          data: this.$route.query,
        }).then((res) => {
          this.dealWith(res)
        }).finally(() => {
          loading.close()
        });
      }
    },
    async dealWith(res) {
      let data = (res.rows || res.data)[0];
      if (data && data.autSaAudReport) {
        this.autSaAudReport = data.autSaAudReport
        if (data.allFitClauseIds && data.autCsId) {
          this.allFitClauseIds = data.allFitClauseIds || []
          this.clauseMap = this.$store.getters.standardTypeItemByVersionId(data.autCsId, 'clause', 'map')
          if (Object.values(this.clauseMap).length == 0) {
            await this.getClauseByIds(data.autCsId, this.allFitClauseIds)
          }
        }
        this.$nextTick(() => {
          this.table1();
          let pass = (this.autSaAudReport.autSaAudReportListVos.find(({ isBasic }) => isBasic == 0).reportList.find(({ autSaAudResult }) => autSaAudResult == 7) || {}).count;
          let total = this.autSaAudReport.autSaAudReportListVos.find(({ isBasic }) => isBasic == 0).sumCount
          if (pass == total) {
            this.table5Plus();
            this.showWho = true;
          } else {
            this.showWho = false;
            // 筛选基础款不达标款号
            let substandardClauseIdList = []
            if (data.autSaAud) {
              for (let index = 0; index < data.autSaAud.length; index++) {
                if (data.autSaAud[index] && data.autSaAud[index].autResult === '5') {
                  substandardClauseIdList.push(data.autSaAud[index].clauseId)
                }
              }
            }
            this.table4Plus(substandardClauseIdList)
          }
        });
      }
    },
    getClauseByIds(versionId, clauseIds) {
      return new Promise(resolve => {
        request({
          url: '/system/standards/selectByClauseIdsAndVersionId',
          method: "post",
          data: {
            versionId,
            clauseIds
          },
        }).then(res => {
          let list = res.data
          this.clauseList = list
          list.forEach(clause => {
            this.$set(this.clauseMap, clause.clauseId, clause)
          })
          resolve();
        })
      })
    },
    table1() {
      this.dataSource[0].truthResult = this.statusName;
      let { autSaAudReportListVos } = this.autSaAudReport;
      let total = 0;
      let basicReport = autSaAudReportListVos.find(data => data.isBasic == 0);
      let map = basicReport.riskLevelMap;
      let levelMap = {
        0: 0,
        1: 0,
        2: 0,
        3: 0,
        4: 0,
      };
      for (const key in map) {
        if (Object.hasOwnProperty.call(map, key)) {
          levelMap[map[key]]++;
          total++;
        }
      }
      this.dataSource[0].truthTotal = total;
      this.dataSource[0].truthSerious = levelMap[4];
      this.dataSource[0].truthHigh = levelMap[3];
      this.dataSource[0].truthMiddle = levelMap[2];
      this.dataSource[0].truthLow = levelMap[1];
    },
    table4Plus(substandardClauseIdList) {
      const { autSaAudReportListVos } = this.autSaAudReport;
      let basic = autSaAudReportListVos.find(({ isBasic }) => isBasic == 0);
      let minor = autSaAudReportListVos.find(({ isBasic }) => isBasic == 1);
      let col_data = {
        title: '',
        result: '',
        number: '',
        range: '-',
        truthNumber: '',
        truthRange: '-',
        risk: '-',
        truthRisk: '-'
      }
      let dataSource = []
      let map = basic.riskLevelMap;
      let levelMap = {
        0: 0,
        1: 0,
        2: 0,
        3: 0,
        4: 0,
      };
      let substandardLevelMap = {
        0: 0,
        1: 0,
        2: 0,
        3: 0,
        4: 0,
      };
      for (const key in map) {
        if (Object.hasOwnProperty.call(map, key)) {
          levelMap[map[key]]++;
          if (substandardClauseIdList.indexOf(key) > -1) {
            substandardLevelMap[map[key]]++;
          }
        }
      }
      // * 审核结果: 1: 优秀 2: 良好 3: 达标 4: 部分达标  5: 不达标  6: 不适用   7: 达标之上（1,2,3） 8：部分达标以上（1,2,3,4） 9:不适用（管理员操作的不适用）
      // 基本款
      {
        let sumCount = basic.sumCount
        let reportList = basic.reportList
        // 从 basic中的 reportList中提取相应的数据 如果reportList没有想要的单独数据，则使用数组合集
        let extracts = [
          {
            result: '达标',
            value: 7,
            _range: 89.6551724137931,
            compare: '≥',
          },
          {
            result: '部分达标或不达标',
            value: [4, 5],
            _range: 10.344827586206896,
            compare: '≤',
            risk: '中、低风险',
          },
          {
            result: '其中：不达标',
            value: 5,
            _range: 5.172413793103448,
            compare: '≤',
            risk: '中、低风险',
          }
        ]
        let list = this.assemble({
          topic: '基本款',
          sumCount,
          reportList,
          levelMap,
          col_data,
          extracts,
          isStar: 1,
          substandardLevelMap
        })
        dataSource.push(...list)
      }
      // 非基本款 总览
      {
        let sumCount = minor.sumCount
        let reportList = minor.reportList
        let extracts = [
          // {
          //   result: '达标或部分达标',
          //   value: 8,
          //   range: 60,
          //   compare: '≥',
          // },
          {
            // result: '其中：达标',
            result: '达标',
            value: 7,
            range: 50,
            compare: '≥',
          },
        ]
        let list = this.assemble({
          topic: '非基本款',
          sumCount,
          reportList,
          levelMap,
          col_data,
          extracts,
          isStar: 0
        })
        dataSource.push(...list)
      }
      // 非基本款 单章
      {
        let topicSumCount = minor.sumCount
        let names = ['', '第一章', '第二章', '第三章']
        let chapterReportList = minor.chapterReportList
        chapterReportList.map(chapterData => {
          let { chapterId, sumCount, reportList } = chapterData
          let extracts = [
            // {
            //   result: '达标或部分达标',
            //   value: 8,
            //   range: 40,
            //   compare: '≥',
            // },
            {
              // result: '其中：达标',
              result: '达标',
              value: 7,
              range: 40,
              compare: '≥',
            },
          ]
          let list = this.assemble({
            topic: '非基本款',
            topicSumCount,
            chapter: names[chapterId],
            sumCount,
            reportList,
            levelMap,
            col_data,
            extracts,
            isStar: 0
          })
          dataSource.push(...list)
        })
      }
      this.dataSource4 = dataSource;
    },
    table5Plus() {
      const { autSaAudReportListVos } = this.autSaAudReport;
      let basic = autSaAudReportListVos.find(({ isBasic }) => isBasic == 0);
      let minor = autSaAudReportListVos.find(({ isBasic }) => isBasic == 1);
      let col_data = {
        title: '-',
        result: '-',
        chapter: "-",
        number: '-',
        range: '-',
        truthNumber: '-',
        truthRange: '-',
        risk: '-',
        truthRisk: '-'
      }
      let dataSource = []
      // * 审核结果: 1: 优秀 2: 良好 3: 达标 4: 部分达标  5: 不达标  6: 不适用   7: 达标之上（1,2,3） 8：部分达标以上（1,2,3,4） 9:不适用（管理员操作的不适用）
      {
        let sumCount = basic.sumCount
        let reportList = basic.reportList
        let extracts = [
          {
            result: '达标',
            value: 7,
            range: 100,
            compare: '',
          }
        ]
        let list = this.assemble({
          topic: '基本款',
          sumCount,
          reportList,
          col_data,
          extracts,
          isStar: 1
        })
        dataSource.push(...list)
      }
      {
        let sumCount = minor.sumCount
        let reportList = minor.reportList
        let extracts = [
          {
            result: '达标或部分达标',
            value: 8,
            range: 60,
            compare: '≥',
          },
          {
            result: '其中：达标',
            value: 7,
            range: 40,
            compare: '≥',
          },
        ]
        let list = this.assemble({
          topic: '非基本款',
          sumCount,
          reportList,
          col_data,
          extracts,
          isStar: 0
        })
        dataSource.push(...list)
      }
      // 非基本款 单章
      {
        let topicSumCount = minor.sumCount
        let names = ['', '第一章', '第二章', '第三章']
        let chapterReportList = minor.chapterReportList
        chapterReportList.map(chapterData => {
          let { chapterId, sumCount, reportList } = chapterData
          let extracts = [
            {
              result: '达标或部分达标',
              value: 8,
              range: 40,
              compare: '≥',
            },
            {
              result: '其中：达标',
              value: 7,
              range: 30,
              compare: '≥',
            },
          ]
          let list = this.assemble({
            topic: '非基本款',
            topicSumCount,
            chapter: names[chapterId],
            sumCount,
            reportList,
            col_data,
            extracts,
            isStar: 0
          })
          dataSource.push(...list)
        })
      }
      this.dataSource5 = dataSource;
    },
    table5() {
      const { autSaAudReportListVos } = this.autSaAudReport;
      {
        let normal = autSaAudReportListVos.find(({ isBasic }) => isBasic == 1);
        let pass = (normal.reportList.find(({ autSaAudResult }) => autSaAudResult == 7) || {}).count;
        let halfPass = (normal.reportList.find(({ autSaAudResult }) => autSaAudResult == 8) || {}).count;
        let passRate = (normal.reportList.find(({ autSaAudResult }) => autSaAudResult == 7) || {}).rate;
        let halfPassRate = (normal.reportList.find(({ autSaAudResult }) => autSaAudResult == 8) || {}).rate;
        this.dataSource5[2].truthNumber = pass + '款'
        this.dataSource5[2].truthRange = passRate + '%'
        this.dataSource5[1].truthNumber = halfPass + '款'
        this.dataSource5[1].truthRange = halfPassRate + '%'
        normal.chapterReportList.forEach(({ chapterId, reportList }) => {
          let index = 3 + (chapterId - 1) * 2
          let firstPass = (reportList.find(({ autSaAudResult }) => autSaAudResult == 7) || {}).count
          let firstPassRate = (reportList.find(({ autSaAudResult }) => autSaAudResult == 7) || {}).rate
          let firstHalfPass = (reportList.find(({ autSaAudResult }) => autSaAudResult == 8) || {}).count
          let firstHalfPassRate = (reportList.find(({ autSaAudResult }) => autSaAudResult == 8) || {}).rate
          this.dataSource5[index + 1].truthNumber = firstPass + '款'
          this.dataSource5[index + 1].truthRange = firstPassRate + '%'
          this.dataSource5[index].truthNumber = firstHalfPass + '款'
          this.dataSource5[index].truthRange = firstHalfPassRate + '%'
        })
      }
    },
    assemble(option) {
      let {
        topic,
        topicSumCount,
        chapter,
        sumCount,
        reportList,
        col_data,
        extracts,
        levelMap,
        isStar,
        substandardLevelMap
      } = option;
      let list = extracts.map(extract => {
        let { result, value, compare, _range, range, risk } = extract
        let truthNumber, truthRange, truthRisk = '-';
        if (value instanceof Array) {
          let reports = value.map(key => {
            return reportList.find(({ autSaAudResult }) => autSaAudResult == key)
          })
          let { count, rate } = reports.reduce((total, data) => {
            return {
              count: total.count + data.count,
              rate: total.rate + data.rate,
            }
          }, {
            count: 0,
            rate: 0
          })
          truthNumber = count + '款'
          truthRange = Math.ceil(rate) + '%'
        } else {
          let { count, rate } = reportList.find(({ autSaAudResult }) => autSaAudResult == value);
          truthNumber = count + '款'
          truthRange = rate + '%'
        }
        if (risk && levelMap && value !== 5) {
          truthRisk = ''
          let risks = ['', '低风险', '中风险', '高风险', '严重风险'];
          for (const key in levelMap) {
            if (Object.hasOwnProperty.call(levelMap, key)) {
              const element = levelMap[key];
              if (element) {
                truthRisk += element + '款' + risks[key] + ' '
              }
            }
          }
        } else {
          truthRisk = ''
          let risks = ['', '低风险', '中风险', '高风险', '严重风险'];
          for (const key in substandardLevelMap) {
            if (Object.hasOwnProperty.call(substandardLevelMap, key)) {
              const element = substandardLevelMap[key];
              if (element) {
                truthRisk += element + '款' + risks[key] + ' '
              }
            }
          }
        }
        let col = Object.assign({
          ...col_data
        }, {
          title: `${topic}<br/>（共${topicSumCount || sumCount}款）`,
          result,
          number: `${compare}${Math.floor((_range || range) * sumCount / 100)}/${sumCount}款`,
          truthNumber,
          truthRisk,
          risk: risk || '-',
          truthRange: _range ? '-' : truthRange,
          range: range ? compare + range + '%' : '-',
          chapter: chapter ? `${chapter}（共${sumCount}款）` : '-',
          desc: `管理员将${isStar == 1 ? this.desc.basic : this.desc.minor}款置为不适用，评审员无需评审`,
        });
        return col
      })
      return list
    },
    toFixed(number, fix) {
      if (isNaN(number)) return 0;
      let str = `${number}`;
      if (str.includes('.')) {
        let [main, decimal] = str.split('.');
        if (decimal) {
          let len = decimal.length;
          let final = decimal;
          if (len < fix) {
          } else if (len == fix) {
          } else if (len > fix) {
            final = [...decimal].slice(0, fix).join('')
          } else {
            return `${main}`
          }
          return `${main}.${final}`
        } else {
          return main
        }
      } else {
        return str;
      }
    },
    arraySpanMethod({ rowIndex, columnIndex }) {
      if (columnIndex == 0) {
        if (rowIndex == 0) {
          return {
            rowspan: 4,
            colspan: 1,
          };
        } else {
          return [1, 0];
        }
      }
      let columns = [7, 8, 9, 10, 11, 12];
      if (columns.includes(columnIndex)) {
        if (rowIndex == 0) {
          return {
            rowspan: 4,
            colspan: 1,
          };
        } else {
          return [1, 0];
        }
      }
      if (columnIndex == 4) {
        return [0, 1];
      }
      if (columnIndex == 5) {
        return [1, 2];
      }
    },
    arraySpanMethod2({ rowIndex, columnIndex }) {
      if (columnIndex == 0) {
        if (rowIndex == 0) {
          return [3, 1];
        } else if (rowIndex == 3) {
          return [8, 1]
        } else {
          return [0, 1]
        }
      } else if (columnIndex == 1) {
        if (rowIndex < 4) {
          return [0, 0]
        } else {
          // if (rowIndex % 2 == 0) {
          //   return [0, 1]
          // } else {
          //   return [2, 1]
          // }
        }
      } else if (columnIndex == 2) {
        if (rowIndex < 4) {
          return [1, 2]
        }
      } else if (columnIndex == 9) {
        if (rowIndex == 0) {
          return [3, 1];
        } else if (rowIndex == 3) {
          return [8, 1]
        } else {
          return [0, 1]
        }
      }
      return [1, 1]
    },
    arraySpanMethod3({ rowIndex, columnIndex }) {
      if (columnIndex == 0) {
        if (rowIndex == 0 || rowIndex == 3 || rowIndex == 6) {
          return [3, 1];
        }
        return [1, 0]
      }
    },
    arraySpanMethod5({ rowIndex, columnIndex }) {
      if (columnIndex == 0) {
        if (rowIndex == 0) return [1, 1]
        if (rowIndex == 1) {
          return [8, 1]
        } else {
          return [0, 1]
        }
      } else if (columnIndex == 1) {
        if (rowIndex < 3 && rowIndex != 0) {
          return [0, 0]
        } else {
          if (rowIndex % 2 == 0) {
            return [0, 1]
          } else {
            return [2, 1]
          }
        }
      } else if (columnIndex == 2) {
        if (rowIndex < 3) {
          return [1, 2]
        }
      } else if (columnIndex == 7) {
        if (rowIndex == 0) return [1, 1]
        if (rowIndex == 1) {
          return [8, 1]
        } else {
          return [0, 1]
        }
      }
      return [1, 1]
    },
    headerMethod2({ row, columnIndex }) {
      row[1].colSpan = 0
      row[2].colSpan = 2
      if (columnIndex == 1) {
        return { display: 'none' }
      }
      return;
    },
    headerMethod3({ row, columnIndex }) {
      row[1].colSpan = 0
      row[2].colSpan = 2
      if (columnIndex == 1) {
        return { display: 'none' }
      }
      return;
    }
  },
};
</script>

<style lang='scss' scoped>
.base-clause-reject {
  ::v-deep .el-card+.el-card {
    margin-top: 15px;
  }
}
</style>
