package com.thas.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 未被分配的领域列表
 */
@Data
@ApiModel(value = "未被分配的领域列表")
public class UnDomainListVO {

    @ApiModelProperty("领域id")
    private String id;

    @ApiModelProperty("领域名称")
    private String domainName;

    @ApiModelProperty("组名")
    private String groupName;

    @ApiModelProperty(value = "领域对应的评审员列表 {'accountId':'', 'name':''}")
    List<SysUserBaseInfo> reviewerList;
}
