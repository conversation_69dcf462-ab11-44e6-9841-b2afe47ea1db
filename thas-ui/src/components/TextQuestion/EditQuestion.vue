<template>
  <div class="edit-question">
    <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="100px">
      <el-form-item :label="formLabelList[0]" prop="question">
        <el-input v-model.trim="formData.question" :placeholder="'请输入' + formLabelList[0]" maxlength="128" clearable style="width: 240px;"></el-input>
        <el-checkbox v-if="isQuestionnaire" v-model.trim="formData.hasRequired" :true-label="1" :false-label="0" style="display: inline-block; width: 50px; margin-left: 10px; padding: 0;">必答</el-checkbox>
      </el-form-item>
      <el-form-item :label="formLabelList[1]" prop="questionType">
        <el-select v-model.trim="formData.questionType" clearable filterable :placeholder="'请选择' + formLabelList[1]" style="width: 100%">
          <el-option v-for="(item, index) in getQuestionTypeOptions" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="!isQuestionnaire" label="得分" prop="score">
        <el-input v-model.trim="formData.score" placeholder="请输入分数" type="number" min="0" clearable :style="{width: '100%'}">
        </el-input>
      </el-form-item>
      <div v-if="formData.questionType == 1">
        <el-radio-group v-model.trim="formData.singleAnswer" size="medium" :key="formData.singleAnswerOptions.length" @change="singleAnswerOptionsChange">
          <el-form-item v-for="(item, index) in formData.singleAnswerOptions" :key="index" class="options-item" :label="index == 0 ? '选项' : ''" :prop="'singleAnswerOptions.' + index + '.label'" :rules="{ validator: optionLabelRules, trigger: 'blur,change' }">
            <el-radio :label="item.label">
              <el-input v-model.trim="item.label" class="questionLabel" placeholder="选项名" maxlength="128"></el-input>
              <el-button icon="el-icon-remove-outline" class="removeOption" circle size="mini" type="text" v-if="getOptions && getOptions.length > 1" @click.stop="handleRemoveOption($event, index)"></el-button>
            </el-radio>
          </el-form-item>
        </el-radio-group>
      </div>
      <div v-if="formData.questionType == 2">
        <el-checkbox-group v-model.trim="formData.multipleAnswer" size="medium" @change="singleAnswerOptionsChange">
          <el-form-item v-for="(item, index) in formData.multipleAnswerOptions" :key="index" class="options-item" :label="index == 0 ? '选项' : ''" :prop="'multipleAnswerOptions.' + index + '.label'" :rules="{ validator: optionLabelRules, trigger: 'blur,change' }">
            <el-checkbox :label="item.label">
              <el-input v-model.trim="item.label" class="questionLabel" placeholder="选项名" maxlength="128"></el-input>
              <el-button v-if="getOptions && getOptions.length > 1" icon="el-icon-remove-outline" class="removeOption" circle size="mini" type="text" @click.stop="handleRemoveOption($event, index)"></el-button>
            </el-checkbox>
          </el-form-item>
        </el-checkbox-group>
      </div>
      <!-- <el-form-item class="options-item" label="是否校验" prop="checkout" v-if="isTextBox">
        <el-select v-model.trim="formData.checkout" placeholder="请选择">
          <el-option v-for="item in checkoutOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button type="text" icon="el-icon-circle-plus-outline" class="addOption" v-if="isSelected" @click="handleAddOption">新增选项</el-button>
        <el-button @click="submit">提交</el-button>
        <el-button @click="cancel">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'EditQuestion',
  props: {
    isQuestionnaire: {
      type: Boolean,
      default: () => false
    },
    questionInfo: {
      type: Object,
      default: () => null
    },
    index: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      showSubmit: false,
      formData: {
        question: '',
        questionType: null,
        score: '',
        singleAnswer: '',
        singleAnswerOptions: [{
          "label": "选项1",
          "value": undefined
        }],
        multipleAnswer: [],
        multipleAnswerOptions: [{
          "label": "选项1",
          "value": undefined
        }],
        checkout: '',
        hasRequired: 1
      },
      questionTypeOptions: [{
        "label": "单选题",
        "value": 1
      }, {
        "label": "多选题",
        "value": 2
      }],
      // singleAnswerOptions: [{
      //   "label": "选项1",
      //   "value": undefined
      // }],
      // multipleAnswerOptions: [{
      //   "label": "选项1",
      //   "value": undefined
      // }],
      questionnaireOptions: [{
        "label": "简答题-单行",
        "value": 3
      }, {
        "label": "简答题-多行",
        "value": 4
      }],
      checkoutOptions: [{
        label: '不校验',
        value: '',
      }, {
        label: '校验手机号码',
        value: '^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\\d{8}$',
      }, {
        label: '校验电子邮箱',
        value: '^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$'
      }]
    }
  },
  computed: {
    getQuestionTypeOptions() {
      let options = this.isQuestionnaire ? this.questionnaireOptions : [];
      return this.questionTypeOptions.concat(options);
    },
    getOptions() {
      let options;
      if (this.formData.questionType == 1) {
        options = this.formData.singleAnswerOptions;
      } else if (this.formData.questionType == 2) {
        options = this.formData.multipleAnswerOptions;
      }
      return options;
    },
    isSelected() {
      return this.formData.questionType == 1 || this.formData.questionType == 2
    },
    isTextBox() {
      return this.formData.questionType == 3 || this.formData.questionType == 4
    },
    formLabelList() {
      return this.isQuestionnaire ? ['标题', '类型'] : ['题目', '题型']
    },
    rules() {
      if (this.isQuestionnaire) {
        return {
          question: [{
            required: true,
            message: '请输入题目',
            trigger: 'blur'
          }],
          questionType: [{
            required: true,
            message: '请选择题型',
            trigger: 'change'
          }],
          score: [{
            required: true,
            message: '请输入分数',
            trigger: 'blur'
          }],
          singleAnswer: [{
            required: true,
            validator: (rule, value, callback) => this.optionValidator(rule, value, callback),
            trigger: ['change', 'blur']
          }],
          multipleAnswer: [{
            required: true,
            validator: (rule, value, callback) => this.optionValidator(rule, value, callback),
            trigger: ['change', 'blur']
          }]
        }
      } else {
        return {
          question: [{
            required: true,
            message: '请输入题目',
            trigger: 'blur'
          }],
          questionType: [{
            required: true,
            message: '请选择题型',
            trigger: 'change'
          }],
          score: [{
            required: true,
            message: '请输入分数',
            trigger: 'blur'
          }],
          singleAnswer: [{
            required: true,
            message: '请选择正确答案',
            trigger: 'change'
          }, {
            required: true,
            validator: (rule, value, callback) => this.optionValidator(rule, value, callback),
            trigger: ['change', 'blur']
          }],
          multipleAnswer: [{
            required: true,
            type: 'array',
            message: '请至少选择一项',
            trigger: 'change'
          },
          {
            required: true,
            validator: (rule, value, callback) => this.optionValidator(rule, value, callback),
            trigger: ['change', 'blur']
          }]
        }
      }
    }
  },
  created() {
    if (this.questionInfo) {
      this.formData = JSON.parse(JSON.stringify(this.questionInfo))
      const { question, questionType, score, singleAnswer, singleAnswerOptions, multipleAnswer, multipleAnswerOptions, checkout } = JSON.parse(JSON.stringify(this.questionInfo))
      if (questionType == 1) {
        // 单选
        this.formData.singleAnswer = singleAnswer
        this.formData.singleAnswerOptions = singleAnswerOptions
      } else if (questionType == 2) {
        // 多选
        if (typeof multipleAnswer == 'string') {
          this.formData.multipleAnswer = JSON.parse(multipleAnswer)
        } else {
          this.formData.multipleAnswer = multipleAnswer || []
        }
        this.formData.multipleAnswerOptions = multipleAnswerOptions
      } else {
        this.formData.checkout = checkout
      }
    }
  },
  methods: {
    hasDuplicateProperty(array, property) {
      const valuesSet = new Set();

      for (let i = 0; i < array.length; i++) {
        const value = array[i][property];
        if (valuesSet.has(value)) {
          return true; // 发现重复属性值，返回 true
        }
        valuesSet.add(value);
      }

      return false; // 没有重复属性值，返回 false
    },
    handleAddOption() {
      let options = this.getOptions;
      if (options && options.length && options.length === 10) {
        this.$message({
          type: 'warning',
          message: '最多10个选项哦~'
        })
        return
      }
      options.push({
        "label": "",
        "value": ''
      })
    },
    handleRemoveOption(event, index) {
      try {
        event.stopPropagation();//非IE浏览器
      }
      catch (e) {
        window.event.cancelBubble = true;//IE浏览器
      }
      let options = this.getOptions;
      options.splice(index, 1)
      if (this.formData.questionType == 1) {
        this.formData.singleAnswer = undefined
      } else if (this.formData.questionType == 2) {
        this.$set(this.formData, 'multipleAnswer', []);
      }
    },

    optionValidator(rule, value, callback) {
      if (this.isQuestionnaire) {
        callback()
      } else {
        let options = this.getOptions;
        if (!(options instanceof Array)) callback();
        let flag = true;
        options.forEach(option => {
          const { label } = option;
          if (!label) {
            // const { label, value } = option;
            // if (!label || !value) {
            flag = false;
          }
        })
        if (!flag) {
          callback('请输入选项名')
        }
        let hasSelectAnswer = false
        if (this.formData.questionType == 1) {
          for (let index = 0; index < this.formData.singleAnswerOptions.length; index++) {
            if (this.formData.singleAnswer == this.formData.singleAnswerOptions[index].label) {
              hasSelectAnswer = true
              break
            }
          }
        } else if (this.formData.questionType == 2) {
          for (let index = 0; index < this.formData.multipleAnswerOptions.length; index++) {
            if (this.formData.multipleAnswer.includes(this.formData.multipleAnswerOptions[index].label)) {
              hasSelectAnswer = true
              break
            }
          }
        }
        if (!hasSelectAnswer) {
          callback('请选择正确答案')
        }
        callback();
      }
    },

    async getValue() {
      try {
        if (await this.$refs['elForm'].validate()) {
          const { question, questionType, score, singleAnswer, multipleAnswer, checkout, hasRequired } = this.formData
          let form = {
            question,
            questionType
          }
          if (this.isQuestionnaire) {
            form.hasRequired = hasRequired
            form.type = questionType
          } else {
            form.score = score
            form.questionType = questionType
          }
          if (this.questionInfo) {
            form.index = this.index
            form.id = this.formData.id
          }
          if (questionType == 1) {
            // 单选
            form.singleAnswer = singleAnswer
            form.singleAnswerOptions = this.formData.singleAnswerOptions
          } else if (questionType == 2) {
            let sortArray = []
            // 多选
            for (let i = 0; i < this.formData.multipleAnswerOptions.length; i++) {
              for (let j = 0; j < multipleAnswer.length; j++) {
                if (multipleAnswer[j] == this.formData.multipleAnswerOptions[i].label) {
                  sortArray.push({
                    label: multipleAnswer[j],
                    sort: i
                  })
                  break
                }
              }
            }
            sortArray.sort((a, b) => {
              if (a.sort < b.sort) {
                return -1
              } else if (a.sort > b.sort) {
                return 1
              }
              return 0
            })
            form.multipleAnswer = sortArray.map((item) => item.label)
            form.multipleAnswerOptions = this.formData.multipleAnswerOptions
          } else {
            form.checkout = checkout
          }
          return form
        }
      } catch (error) {
        // console.log(error)
        return false
      }
    },
    async submit() {
      const info = await this.getValue()
      if (!info) return;
      // 单选题
      if (info.questionType == 1 && this.hasDuplicateProperty(info.singleAnswerOptions, 'label')) {
        this.$message({
          type: 'warning',
          message: '选项名不能重复～'
        })
        return;
      }
      // 多选题
      if (info.questionType == 2 && this.hasDuplicateProperty(info.multipleAnswerOptions, 'label')) {
        this.$message({
          type: 'warning',
          message: '选项名不能重复～'
        })
        return;
      }
      this.$emit('add', info)
    },
    resetForm() {
      this.$refs.elForm.resetFields()
      this.formData.singleAnswerOptions = [{
        "label": "选项1",
        "value": ''
      }]
      this.formData.multipleAnswerOptions = [{
        "label": "选项1",
        "value": ''
      }]
    },
    cancel() {
      this.$emit('cancel', this.index)
    },
    optionLabelRules(rule, value, callback) {
      if (!value) {
        callback("请输入选项名");
      } else {
        if (this.isQuestionnaire) {
          callback();
        } else {
          let hasSelectAnswer = false;
          if (this.formData.questionType == 1) {
            for (
              let index = 0;
              index < this.formData.singleAnswerOptions.length;
              index++
            ) {
              if (
                this.formData.singleAnswer ==
                this.formData.singleAnswerOptions[index].label
              ) {
                hasSelectAnswer = true;
                break;
              }
            }
          } else if (this.formData.questionType == 2) {
            for (
              let index = 0;
              index < this.formData.multipleAnswerOptions.length;
              index++
            ) {
              if (
                this.formData.multipleAnswer.includes(
                  this.formData.multipleAnswerOptions[index].label
                )
              ) {
                hasSelectAnswer = true;
                break;
              }
            }
          }
          if (!hasSelectAnswer) {
            callback("请选择正确答案");
          } else {
            callback();
          }
        }
        callback();
      }
    },
    singleAnswerOptionsChange(value) {
      this.$refs["elForm"].validate();
    },
  }
}
</script>

<style lang="scss" scoped>
.edit-question {
  margin-top: 18px;
  ::v-deep .el-radio {
    display: block;
    padding: 10px 0 0 0;
    &:nth-child(1) {
      padding: 0;
    }
  }
  ::v-deep .el-checkbox {
    padding: 10px 0 0 0;
    display: block;
  }
}
.addOption {
  display: block;
}
.questionLabel {
  width: 200px;
  display: inline-block;
  margin-right: 4px;
}
.questionValue {
  width: 125px;
}
.removeOption {
  border: 0;
  display: inline-block;
  vertical-align: bottom;
  ::v-deep .el-icon-remove-outline {
    color: red;
    display: inline-block;
    font-size: 22px;
  }
}
.el-form-item {
  width: 400px;
}
</style>
