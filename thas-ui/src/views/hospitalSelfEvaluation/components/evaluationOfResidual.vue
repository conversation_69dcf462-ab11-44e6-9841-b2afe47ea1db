
<!--  -->
<template>
  <div class='evaluation-Residual'>
    <!-- 自评完毕 或者 评审完毕 医院端 (isHospital || isAssessor) && isAuditComplete -->
    <!--  -->
    <span v-if="!isSelf && !isSelfByRejected && isHospital && !isTruth">
      <!-- 优秀款 | 良好款 | 达标款 | 部分达标款 | 不达标款 | 不适用款 -->
      <span class="firstCheckNumTip" :class="{ lightHigh: clauseIds4SelectIndex == index }" v-for="(item, index) in reviewResultItemsNum" v-show="index != 0" :key="index" @click="changeClauseListReviewResult(index)" :style="{cursor: item.length ? 'pointer': 'default'}">
        {{reviewResultItemsName(index)}}{{item.length}}款
        <a>|</a>
      </span>
    </span>
    <!-- 医院自评中 -->
    <span v-else-if="isSelf && isHospital" style="cursor: default;">
      已完成{{clauseNum(AutSaAudSubmitTypeEnum.TERM_SA_PROC).length}}款
      <span class="firstCheckNumTip" :class="{ lightHigh: clauseIds4SelectIndex == 1 }" v-if="clauseNumResidue.length" @click="changeUnFinishClauseListInHospital">
        | 未完成{{ clauseNumResidue.length }}款</span>
      <span v-if="unFinishArticlesByAllReadyClause(true).length" style="cursor: default;">
        | 已小结{{unFinishArticlesByAllReadyClause(true).length}}条</span>
      <span class="firstCheckNumTip" v-if="unFinishArticlesByAllReadyClause(false).length" :class="{ lightHigh: clauseIds4SelectIndex == 2 }" @click="changeClauseListForArticle">
        | 未小结{{unFinishArticlesByAllReadyClause(false).length}}条</span>
    </span>
    <span class="firstCheckNumTip" v-else-if="isSelfByRejected && isHospital">
      <span class="firstCheckNumTip" v-if="autRejectedResultItemsNum.length" :class="{ lightHigh: clauseIds4SelectIndex == 1 }" @click="changeClauseListInHospital">
        被驳回{{ autRejectedResultItemsNum.length }}款</span>
    </span>

    <!-- 形式审查中 -->
    <span v-else-if="isInspector && $route.query.entry == 1">
      <!-- 待审查, 已审查, 已复查, 未复查, 被驳回, 已修改驳回, 已复查驳回 -->
      <span class="firstCheckNumTip" :class="{ lightHigh: clauseIds4SelectIndex == 0 }" v-if="firstCheckResultItemsNum[0].length" @click="changeClauseListInInspector(0)">
        待审查{{ firstCheckResultItemsNum[0].length }}款
        <a>｜</a></span>
      <!-- ⬇️ 加个状态 -->
      <span class="firstCheckNumTip" :class="{ lightHigh: clauseIds4SelectIndex == 1 }" v-if="firstCheckResultItemsNum[1].length || isCheck" @click="changeClauseListInInspector(1)">
        已审查{{ firstCheckResultItemsNum[1].length }}款
        <a>｜</a></span>
      <span class="firstCheckNumTip" :class="{ lightHigh: clauseIds4SelectIndex == 4 }" v-if="firstCheckResultItemsNum[4].length" @click="changeClauseListInInspector(4)">
        被驳回{{ firstCheckResultItemsNum[4].length }}款
        <a>｜</a></span>
      <span class="firstCheckNumTip" :class="{ lightHigh: clauseIds4SelectIndex == 6 }" v-if="firstCheckResultItemsNum[6].length" @click="changeClauseListInInspector(6)">
        已完成{{ firstCheckResultItemsNum[6].length }}款
        <a>｜</a></span>
    </span>
    <span v-else-if="isInspector && $route.query.entry == 2" style="cursor: default;">
      已复查{{ clauseNum(AutSaAudSubmitTypeEnum.AUD_FIRST_TRIAL_REVIEW_PROC) .length }}款
      <span class="firstCheckNumTip" v-if="secondCheckResultItemsNum.length" :class="{ lightHigh: clauseIds4SelectIndex == 1 }" @click="changeClauseListInInspectorByLeader">
        ｜待复查{{ secondCheckResultItemsNum.length }}款</span>
    </span>

    <!-- 现场评审中 -->
    <span v-else-if="isAssessor && $route.query.entry == 3">
      <span class="firstCheckNumTip" :class="{ lightHigh: clauseIds4SelectIndex == 0 }" v-if="firstReviewResultItemsNum[0].length" @click="changeClauseListInAssessor(0)">
        待评价{{ firstReviewResultItemsNum[0].length }}款
        <a>｜</a>
      </span>
      <span class="firstCheckNumTip" style="cursor: default;">
        已评价{{ firstReviewResultItemsNum[1].length }}款
        <a>｜</a>
      </span>
      <span class="firstCheckNumTip" :class="{ lightHigh: clauseIds4SelectIndex == 3 }" v-if=" firstReviewResultItemsNum[3].length && !firstReviewResultItemsNum[0].length " @click="changeClauseListInAssessor(3)">
        待复查{{ firstReviewResultItemsNum[3].length }}款
        <a>｜</a>
      </span>
      <span class="firstCheckNumTip" :class="{ lightHigh: clauseIds4SelectIndex == 2 }" v-if="firstReviewResultItemsNum[2].length" @click="changeClauseListInAssessor(2)">
        已复查{{ firstReviewResultItemsNum[2].length }}款
        <a>｜</a>
      </span>
      <span class="firstCheckNumTip" :class="{ lightHigh: clauseIds4SelectIndex == 4 }" v-if="firstReviewResultItemsNum[4].length" @click="changeClauseListInAssessor(4)">
        被驳回{{ firstReviewResultItemsNum[4].length }}款
        <a>｜</a>
      </span>
    </span>

    <span v-else-if="isAssessor && $route.query.entry == 4" style="cursor: default;">
      已复查{{  clauseNum(AutSaAudSubmitTypeEnum.AUD_SECOND_TRIAL_REVIEW_PROC) .length }}款
      <span class="firstCheckNumTip" v-if="secondReviewResultItemsNum.length" :class="{ lightHigh: clauseIds4SelectIndex == 1 }" @click="changeClauseListInAssessorByLeader">
        | 待复查{{ secondReviewResultItemsNum.length }}款</span>
    </span>

    <!-- 审查评审报告 -->
    <span v-else-if="isReviewReview && isInspector" style="cursor: default;">
      已审查{{ reviewReviewReportResultItemsNum[0].length }}款
      <span class="firstCheckNumTip" v-if="reviewReviewReportResultItemsNum[1].length" :class="{ lightHigh: clauseIds4SelectIndex == 1 }" @click="changeClauseListByReviewReview">
        ｜待审查{{ reviewReviewReportResultItemsNum[1].length }}款</span>
    </span>

    <!-- 事实准确性确认 -->
    <span v-else-if="isFactual && isHospital" style="cursor: default;">
      已审查{{ factualAccuracyResultItemsNum[0].length }}款
      <span class="firstCheckNumTip" v-if="factualAccuracyResultItemsNum[1].length" :class="{ lightHigh: clauseIds4SelectIndex == 1 }" @click="changeClauseListByFactualAccuracy">
        ｜待审查{{ factualAccuracyResultItemsNum[1].length }}款</span>
    </span>

    <!-- 验证审查 -->
    <span v-else-if="isSeniorReview && isSeniorAssessor" style="cursor: default;">
      已评价{{ seniorReviewResultItemsNum[0].length }}款
      <span class="firstCheckNumTip" v-if="seniorReviewResultItemsNum[1].length" :class="{ lightHigh: clauseIds4SelectIndex == 1 }" @click="changeClauseListBySeniorReview">
        ｜待评价{{ seniorReviewResultItemsNum[1].length }}款</span>
    </span>

    <!-- 第一次 -->
    <span v-else-if="isFirstModify && isAssessor" style="cursor: default;">
      已修改{{ modifyResultItemsNum(1)[0].length }}款
      <span class="firstCheckNumTip" v-if="modifyResultItemsNum(1)[1].length" :class="{ lightHigh: clauseIds4SelectIndex == 1 }" @click="changeClauseListByModify(1)">
        ｜待修改{{ modifyResultItemsNum(1)[1].length }}款</span>
    </span>

    <!-- 第二次 -->
    <span v-else-if="isSecondModify && isAssessor" style="cursor: default;">
      已修改{{  modifyResultItemsNum(2)[0].length }}款
      <span class="firstCheckNumTip" v-if="modifyResultItemsNum(2)[1].length" :class="{ lightHigh: clauseIds4SelectIndex == 1 }" @click="changeClauseListByModify(2)">
        ｜待修改{{ modifyResultItemsNum(2)[1].length }}款</span>
    </span>
  </div>
</template>

<script>
export default {
  name: 'EvaluationOfResidual',
  components: {},
  props: {
    autSaAudStatus: {
      type: Number,
      default: () => 0
    },
    detail: {
      type: Object,
      default: () => { }
    },
    relation: {
      type: Object,
      default: () => { }
    },
    termMap: {
      type: Object,
      default: () => { }
    },
    allArticleId: {
      type: Array,
      default: () => []
    },
    reviewResultOptions: {
      type: Array,
      default: () => []
    },
    showTerm: {
      type: Function,
      default: () => false,
    },
    clauseIds: {
      type: Array,
      default: () => []
    },
    isHospital: {
      type: Boolean,
      default: () => false,
    },
    isInspector: {
      type: Boolean,
      default: () => false,
    },
    isAssessor: {
      type: Boolean,
      default: () => false,
    },
    isSeniorAssessor: {
      type: Boolean,
      default: () => false,
    },
  },
  watch: {},
  data() {
    return {
      clauseIds4Select: [],
      clauseIds4SelectIndex: -1,
    };
  },

  computed: {
    isSelf() {
      // 自评
      return (
        this.autSaAudStatus >= this.AutSaAudStatusEnum.WAIT_TERM_SA &&
        this.autSaAudStatus <= this.AutSaAudStatusEnum.TERM_SA_SUM
      );
    },

    isTruth() {
      return (
        this.autSaAudStatus >=
        this.AutSaAudStatusEnum.WAIT_FACTUAL_ACCURACY_REVIEW &&
        this.autSaAudStatus <= this.AutSaAudStatusEnum.CONFIRM_REVIEW_FACTUAL
      );
    },

    // 形式审查
    isCheck() {
      return (
        this.autSaAudStatus >= this.AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL &&
        this.autSaAudStatus <=
        this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_INQUIRE_PROC
      );
    },

    isReviewReview() {
      return this.autSaAudStatus == this.AutSaAudStatusEnum.REVIEW_REVIEW_REPORT_PROC ||
        this.autSaAudStatus == this.AutSaAudStatusEnum.WAIT_REVIEW_REVIEW_REPORT
    },

    isFactual() {
      return this.autSaAudStatus == this.AutSaAudStatusEnum.WAIT_FACTUAL_ACCURACY_REVIEW ||
        this.autSaAudStatus == this.AutSaAudStatusEnum.FACTUAL_ACCURACY_REVIEW_PROC
    },

    isSeniorReview() {
      return this.autSaAudStatus == this.AutSaAudStatusEnum.WAIT_SENIOR_REVIEWER_REVIEW ||
        this.autSaAudStatus == this.AutSaAudStatusEnum.SENIOR_REVIEWER_REVIEW_PROC
    },

    // 被驳回后的自评
    isSelfByRejected() {
      return (
        this.autSaAudStatus ==
        this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_SUM_REJECT
      );
    },

    isFirstModify() {
      return this.autSaAudStatus == this.AutSaAudStatusEnum.REVIEWER_MODIFICATION_REVIEW
    },
    isSecondModify() {
      return this.autSaAudStatus == this.AutSaAudStatusEnum.REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW
    },

    clauseTotal() {
      try {
        const versionId =
          this.relation.autCsId || this.$store.getters.standardBaseVersionId;
        const list =
          this.$store.getters.standardTypeItemByVersionId(
            versionId,
            "clause",
            "list"
          ) || [];
        return list.map((item) => item.clauseId);
      } catch (error) {
        return [];
      }
    },
    clauseNumResidue() {
      const type = this.AutSaAudSubmitTypeEnum.TERM_SA_PROC;
      const totalClauseList = this.clauseTotal;
      const finishedClauseList = this.clauseNum(type);
      const list = totalClauseList.filter((item) =>
        finishedClauseList.every((clause) => item != Number(clause))
      );
      return list;
    },

    clauseNumResidue2() {
      const type = this.AutSaAudSubmitTypeEnum.FACTUAL_ACCURACY_REVIEW;
      const totalClauseList = this.clauseTotal;
      const finishedClauseList = this.clauseNum(type);
      const list = totalClauseList.filter((item) =>
        finishedClauseList.every((clause) => item != Number(clause))
      );
      return list;
    },

    clauseNum() {
      return (type) =>
        ((this.detail.autSaAudListMap || {})[type] || []).map(
          (item) => item.clauseId
        );
    },
    // 评价剩余： 优秀xxx款 | 良好xxx款 | 达标xxx款 | 部分达标xxx款 | 不达标xxx款 | 不适用xxx款
    reviewResultItemsNum() {
      const itemsNumMap = {};
      const itemsIdsList = [[]];
      this.reviewResultOptions.forEach(
        (option) => {
          itemsNumMap[option.dictValue] = 0;
          itemsIdsList[Number(option.dictValue)] = [];
        }
      );
      let type = this.AutSaAudSubmitTypeEnum.TERM_SA_PROC;
      if (this.isAuditComplete) {
        type = this.AutSaAudSubmitTypeEnum.AUD_SECOND_TRIAL_INQUIRE_PROC;
      }
      if (this.detail.autSaAudListMap && this.detail.autSaAudListMap[type]) {
        this.detail.autSaAudListMap[type].forEach(
          (autSa) => {
            itemsNumMap[autSa.autResult]++;
            itemsIdsList[autSa.autResult].push(Number(autSa.clauseId));
          }
        );
      }
      return itemsIdsList
    },

    reviewResultItemsName() {
      return (index) => {
        return (this.reviewResultOptions.find(item => item.dictValue == index) || {}).dictLabel;
      }
    },

    // 已完成的所有款项自评的条列表
    articlesByAllReadyClause() {
      try {
        const articleIds = this.allArticleId;
        const allReadyList = articleIds.filter(articleId => this.showTerm(articleId));
        return allReadyList || []
      } catch (error) {
        // console.log(error);
      }
    },

    // 未完成小结的列表
    unFinishArticlesByAllReadyClause() {
      return turn => {
        const list = this.articlesByAllReadyClause;
        const unFinish = list.filter(id => !this.termMap[id]);
        const finish = list.filter(id => this.termMap[id]);
        return turn ? finish : unFinish
      }
    },

    // 这里返回的是已完成的款项id 为什么呢？ 因为这里的款项id其实是根据某个已完成所有款项的条来遍历的
    allReadyClause4Article() {
      const versionId = this.relation.autCsId || this.$store.getters.standardBaseVersionId;
      const map =
        this.$store.getters.standardTypeItemByVersionId(
          versionId,
          "article",
          "map"
        ) || [];
      const allReadyArticleIds = this.unFinishArticlesByAllReadyClause(false);
      const list = allReadyArticleIds.map(id => {
        const cls = map[id].clauseVoList.map(clause => clause.clauseId);
        return cls
      })
      return list.flat(Infinity);
    },


    autRejectedResultItemsNum() {
      const type = this.AutSaAudSubmitTypeEnum.TERM_SA_PROC;
      const beLongTotalRejectedItems =
        (this.detail.rejectAutSaAudListMap || {})[type] || [];
      const beLongFinishedItems = this.detail.autSaAudListMap[type] || [];
      const unfinishedItems = beLongTotalRejectedItems.filter((item) =>
        beLongFinishedItems.every((bItem) => bItem.clauseId != item.clauseId)
      );
      return unfinishedItems.map((item) =>
        typeof item == "string" ? `${item}` : `${item.clauseId}`
      );
    },
    // 形式审查剩余 ：待评价, 已评价, 已复查, 未复查, 被驳回, 已修改驳回, 已复查驳回
    firstCheckResultItemsNum() {
      // 形式审查类型
      const type = this.AutSaAudSubmitTypeEnum.AUD_FIRST_TRIAL_INQUIRE_PROC;
      // 审查审核类型
      const type1 = this.AutSaAudSubmitTypeEnum.AUD_FIRST_TRIAL_REVIEW_PROC;
      if (!Object.keys(this.detail).length) return [[], [], [], [], [], [], []];
      // 审查员被分配的款
      const totalItems = this.clauseIds;
      // 所有审查员已完成的款评价
      const finishedItems = this.detail.autSaAudListMap[type] || [];
      // 组长复查所有的款评价
      const reviewFinishedItems = this.detail.autSaAudListMap[type1] || [];
      // 所有审查员被驳回的款评价， 以及组长驳回所有审查员的款评价的款评价
      const rejectedItems =
        (this.detail.rejectAutSaAudListMap || {})[type] || [];
      const rejectedDescItems =
        (this.detail.rejectAutSaAudListMap || {})[type1] || [];

      // 当前审查员所有的款
      const beLongItems = finishedItems.filter((item) =>
        totalItems.includes(item.clauseId)
      );
      // 当前审查员未完成的款
      const beLongUnfinishedItems = totalItems.filter((id) =>
        beLongItems.every((item) => item.clauseId != id)
      );
      // 当前审查员已完成的款
      const beLongFinishedItems = totalItems.filter((id) =>
        beLongItems.some((item) => item.clauseId == id)
      );
      // 组长复查当前审查员的款
      const beLongReviewByLeaderItems = reviewFinishedItems.filter((item) =>
        totalItems.includes(item.clauseId)
      );
      // 组长未复查当前审查员已完成的款
      const beLongUnReviewByLeaderItems = totalItems
        .filter((id) =>
          beLongReviewByLeaderItems.every((item) => id != item.clauseId)
        )
        .filter((id) =>
          beLongFinishedItems.every((item) => id != item.clauseId)
        );
      // 组长驳回当前审查员的款
      const beLongRejectedByLeaderItems = rejectedDescItems.filter((item) =>
        totalItems.includes(item.clauseId)
      );
      // 当前审查员被驳回的款
      const beLongRejectedItems = rejectedItems.filter((item) =>
        totalItems.includes(item.clauseId)
      );
      // 当前审查员已完成的被驳回的款
      const beLongFinishedRejectedItems = beLongItems.filter((item) =>
        beLongRejectedByLeaderItems.some(
          (LItem) => item.clauseId == LItem.clauseId
        )
      );
      // 组长复查当前审查员被驳回的款
      const beLongModifiedByLeaderItems = beLongRejectedByLeaderItems.filter(
        (item) =>
          beLongFinishedItems.some((BItem) => BItem.clauseId == item.clauseId)
      );

      let unfinishedNum = [], // 待审查
        finishedNum = [], // 已审查
        reviewedNum = [], // 已复查
        unReviewedNum = [], // 未复查
        rejectedNum = [], // 被驳回
        modifiedNum = [], // 已修改驳回
        reviewedRejectedNum = []; // 已复查被驳回

      unfinishedNum = beLongUnfinishedItems;
      finishedNum = beLongFinishedItems.map((item) =>
        typeof item == "string" ? item : item.clauseId
      );
      reviewedNum = beLongReviewByLeaderItems.map((item) =>
        typeof item == "string" ? item : item.clauseId
      );
      unReviewedNum = beLongUnReviewByLeaderItems.map((item) =>
        typeof item == "string" ? item : item.clauseId
      );
      rejectedNum = beLongRejectedItems.map((item) =>
        typeof item == "string" ? item : item.clauseId
      );
      modifiedNum = beLongFinishedRejectedItems.map((item) =>
        typeof item == "string" ? item : item.clauseId
      );
      reviewedRejectedNum = beLongModifiedByLeaderItems.map((item) =>
        typeof item == "string" ? item : item.clauseId
      );
      // 返回ids
      // 待审查, 已审查, 已复查, 未复查, 被驳回, 已修改驳回, 已复查驳回
      return [
        unfinishedNum,
        finishedNum,
        reviewedNum,
        unReviewedNum,
        rejectedNum,
        modifiedNum,
        reviewedRejectedNum,
      ];
    },
    secondCheckResultItemsNum() {
      const type = this.AutSaAudSubmitTypeEnum.AUD_FIRST_TRIAL_REVIEW_PROC;
      const totalClauseList = this.clauseTotal;
      const finishedClauseList = this.clauseNum(type);
      return totalClauseList
        .filter((id) => finishedClauseList.every((cId) => `${id}` != cId))
        .map((id) => `${id}`);
    },
    secondReviewResultItemsNum() {
      const type = this.AutSaAudSubmitTypeEnum.AUD_SECOND_TRIAL_REVIEW_PROC;
      const totalClauseList = this.clauseTotal;
      const finishedClauseList = this.clauseNum(type);
      return totalClauseList
        .filter((id) => finishedClauseList.every((cId) => `${id}` != cId))
        .map((id) => `${id}`);
    },

    firstReviewResultItemsNum() {
      // 现场评审类型
      const type = this.AutSaAudSubmitTypeEnum.AUD_SECOND_TRIAL_INQUIRE_PROC;
      // 现场评审审核类型
      const type1 = this.AutSaAudSubmitTypeEnum.AUD_SECOND_TRIAL_REVIEW_PROC;
      if (!Object.keys(this.detail).length) return [[], [], [], [], [], [], []];
      // 评审员被分配的款
      const totalItems = this.clauseIds;
      // 所有评审员已完成的款评价
      const finishedItems = this.detail.autSaAudListMap[type] || [];
      // 组长复查所有的款评价
      const reviewFinishedItems = this.detail.autSaAudListMap[type1] || [];
      // 所有评审员被驳回的款评价， 以及组长驳回所有评审员的款评价的款评价
      const rejectedItems =
        (this.detail.rejectAutSaAudListMap || {})[type] || [];
      const rejectedDescItems =
        (this.detail.rejectAutSaAudListMap || {})[type1] || [];

      // 当前评审员所有的款
      const beLongItems = finishedItems.filter((item) =>
        totalItems.includes(item.clauseId)
      );
      // 当前评审员未完成的款
      const beLongUnfinishedItems = totalItems.filter((id) =>
        beLongItems.every((item) => item.clauseId != id)
      );
      // 当前评审员已完成的款
      const beLongFinishedItems = totalItems.filter((id) =>
        beLongItems.some((item) => item.clauseId == id)
      );
      // 组长复查当前评审员的款
      const beLongReviewByLeaderItems = reviewFinishedItems.filter((item) =>
        totalItems.includes(item.clauseId)
      );
      // 组长未复查当前评审员已完成的款
      const beLongUnReviewByLeaderItems = totalItems
        .filter((id) =>
          beLongReviewByLeaderItems.every((item) => id != item.clauseId)
        )
        .filter((id) =>
          beLongFinishedItems.every((item) => id != item.clauseId)
        );
      // 组长驳回当前评审员的款
      const beLongRejectedByLeaderItems = rejectedDescItems.filter((item) =>
        totalItems.includes(item.clauseId)
      );
      // 当前评审员被驳回的款
      const beLongRejectedItems = rejectedItems.filter(
        (item) => totalItems.includes(item.clauseId)
      );
      // 当前评审员已完成的被驳回的款
      const beLongFinishedRejectedItems = beLongItems.filter((item) =>
        beLongRejectedByLeaderItems.some(
          (LItem) => item.clauseId == LItem.clauseId
        )
      );
      // 组长复查当前评审员被驳回的款
      const beLongModifiedByLeaderItems = beLongRejectedByLeaderItems.filter(
        (item) =>
          beLongFinishedItems.some((BItem) => BItem.clauseId == item.clauseId)
      );

      let unfinishedNum = [], // 待评价
        finishedNum = [], // 已评价
        reviewedNum = [], // 已复查
        unReviewedNum = [], // 未复查
        rejectedNum = [], // 被驳回
        modifiedNum = [], // 已修改驳回
        reviewedRejectedNum = []; // 已复查被驳回

      unfinishedNum = beLongUnfinishedItems;
      finishedNum = beLongFinishedItems.map((item) =>
        typeof item == "string" ? item : item.clauseId
      );
      reviewedNum = beLongReviewByLeaderItems.map((item) =>
        typeof item == "string" ? item : item.clauseId
      );
      unReviewedNum = beLongUnReviewByLeaderItems.map((item) =>
        typeof item == "string" ? item : item.clauseId
      );
      rejectedNum = beLongRejectedItems.map((item) =>
        typeof item == "string" ? item : item.clauseId
      );
      modifiedNum = beLongFinishedRejectedItems.map((item) =>
        typeof item == "string" ? item : item.clauseId
      );
      reviewedRejectedNum = beLongModifiedByLeaderItems.map((item) =>
        typeof item == "string" ? item : item.clauseId
      );
      // 返回ids
      // 待评价, 已评价, 已复查, 未复查, 被驳回, 已修改驳回, 已复查驳回
      return [
        unfinishedNum,
        finishedNum,
        reviewedNum,
        unReviewedNum,
        rejectedNum,
        modifiedNum,
        reviewedRejectedNum,
      ];
    },

    reviewReviewReportResultItemsNum() {
      const type = this.AutSaAudSubmitTypeEnum.REVIEW_REVIEW_REPORT;
      const reviewIds = this.clauseNum(type).map(id => Number(id));
      const list = this.$store.getters.standardTypeItemByVersionId(
        this.relation.autCsId,
        "clause",
        "list"
      ).map(clause => Number(clause.clauseId));
      const unFinishIds = list.filter(id => !reviewIds.includes(id));
      return [reviewIds, unFinishIds]
    },

    factualAccuracyResultItemsNum() {
      const type = this.AutSaAudSubmitTypeEnum.FACTUAL_ACCURACY_REVIEW;
      const reviewIds = this.clauseNum(type).map(id => Number(id));
      const list = this.$store.getters.standardTypeItemByVersionId(
        this.relation.autCsId,
        "clause",
        "list"
      ).map(clause => Number(clause.clauseId));
      const unFinishIds = list.filter(id => !reviewIds.includes(id));
      return [reviewIds, unFinishIds]
    },

    seniorReviewResultItemsNum() {
      const type = this.AutSaAudSubmitTypeEnum.SENIOR_REVIEWER_REVIEW;
      const reviewIds = this.clauseNum(type).map(id => Number(id));
      const list = this.$store.getters.standardTypeItemByVersionId(
        this.relation.autCsId,
        "clause",
        "list"
      ).map(clause => Number(clause.clauseId));
      const unFinishIds = list.filter(id => !reviewIds.includes(id));
      return [reviewIds, unFinishIds]
    },

    modifyResultItemsNum() {
      return (index) => {
        const type = index == 1 ? this.AutSaAudSubmitTypeEnum.REVIEWER_MODIFICATION_REVIEW : this.AutSaAudSubmitTypeEnum.REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW;
        const reviewIds = this.clauseNum(type).map(id => Number(id)).filter(id => this.clauseIds.includes(`${id}`));
        const list = this.$store.getters.standardTypeItemByVersionId(
          this.relation.autCsId,
          "clause",
          "list"
        ).map(clause => Number(clause.clauseId));
        const unFinishIds = list.filter(id => this.clauseIds.includes(`${id}`)).filter(id => !reviewIds.includes(id));
        return [reviewIds, unFinishIds]
      }
    },
  },

  created() { },

  mounted() { },

  methods: {
    changeClauseList(list, value1 = 1, value2 = -1) {
      let flag = this.clauseIds4Select == list;
      this.clauseIds4SelectIndex = flag ? value2 : value1;
      this.$set(this, "clauseIds4Select", flag ? [] : list);
      this.$emit('clearClauseList', this.clauseIds4Select);
    },

    changeClauseListInHospital() {
      this.changeClauseList(this.autRejectedResultItemsNum)
    },

    changeClauseListReviewResult(index) {
      this.changeClauseList(this.reviewResultItemsNum[index] || [], index)
    },

    changeClauseListForArticle() {
      this.changeClauseList(this.allReadyClause4Article, 2)
    },

    changeUnFinishClauseListInHospital() {
      const list = this.isSelf ? this.clauseNumResidue : this.clauseNumResidue2;
      this.changeClauseList(list)
    },

    changeClauseListInInspector(index) {
      this.changeClauseList(this.firstCheckResultItemsNum[index], index)
    },

    changeClauseListInInspectorByLeader() {
      this.changeClauseList(this.secondCheckResultItemsNum)
    },

    changeClauseListInAssessor(index) {
      this.changeClauseList(this.firstReviewResultItemsNum[index], index)
    },

    changeClauseListInAssessorByLeader() {
      this.changeClauseList(this.secondReviewResultItemsNum)
    },

    changeClauseListByReviewReview() {
      this.changeClauseList(this.reviewReviewReportResultItemsNum[1])
    },


    changeClauseListByFactualAccuracy() {
      this.changeClauseList(this.factualAccuracyResultItemsNum[1])
    },

    changeClauseListBySeniorReview() {
      this.changeClauseList(this.seniorReviewResultItemsNum[1])
    },

    changeClauseListByModify(index) {
      this.changeClauseList(this.modifyResultItemsNum(index)[1])
    },
  }
}

</script>
<style lang='scss' scoped>
.evaluation-Residual {
  float: right;
  padding: 3px 0;
  font-size: 12px;
  cursor: pointer;

  .firstCheckNumTip {
    cursor: pointer;
  }
  .firstCheckNumTip:last-child > a {
    display: none;
  }
  .firstCheckNumTip.lightHigh {
    color: blue;
  }
}
</style>