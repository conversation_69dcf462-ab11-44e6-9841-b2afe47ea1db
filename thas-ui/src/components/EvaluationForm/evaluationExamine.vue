<!--  -->
<template>
  <div class="evaluation-examine evaluation" v-loading="submitLoading"  >
    <el-button v-if="noReject && showEdit && !edit" class="objection" size="small" type="text" @click="edit = true">对审查结果有异议？去修改</el-button>
    <el-button v-if="noReject && showEdit && edit" class="objection" size="small" type="text" @click="edit = false">放弃修改</el-button>
    <el-form ref="evaluationForm" :model="formData" :label-width="labelWidth" :rules="rules">
      <el-form-item class="form-item" :class="{ readonly: !modifying && !edit }" :label="examineResultLabel" prop="autResult">
        <el-select v-if="modifying || edit" v-model="formData.autResult" placeholder="请选择审查结果" clearable filterable @change="resultChange">
          <el-option v-for="(option, index) in checkSummaryOptions" :key="index" :value="option.dictValue" :label="option.dictLabel"></el-option>
        </el-select>
        <dict-span :noTag='true' v-else :key="checkSummaryOptions.length+ Number(formData.autResult)" :value="formData.autResult" :options="checkSummaryOptions" type="default"></dict-span>
      </el-form-item>
      <el-form-item class="form-item" :class="{ readonly: !modifying && !edit }" label="审查描述：" prop="autDesc">
        <el-input v-if="modifying || edit" type="textarea" v-model="formData.autDesc" placeholder="请填写审查描述" :rows="4" :maxlength="descMaxLength"></el-input>
        <span v-else>{{ formData.autDesc || '--' }}</span>
      </el-form-item>
      <el-form-item class="form-item" label=" " v-if="modifying || edit">
        <el-button type="primary" @click="submit">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import mixin from "./mixin";

const defaultForm = {
  autResult: "",
  autDesc: "",
};
export default {
  name: "EvaluationExamine",
  mixins: [mixin],
  components: {},
  props: {
    isLeader: {
      type: Boolean,
      default: () => false
    },
    showEdit: {
      type: Boolean,
      default: () => false
    },
  },
  watch: {
    edit() {
      if (!this.edit) {
        this.reply();
      }
    }
  },
  data() {
    return {
      formData: {},
      edit: false,
      noReject: true,
      myName: '审查'
      // type: this.AutSaAudCurrentStatusEnum.FR_CLAUSE
    };
  },

  computed: {
    examineResultLabel() {
      return this.isLeader ? "组员审查结果：" : "审查结果：";
    },
  },

  created() {
    this.autResultMessage = '请选择审查结果'
    this.autDescMessage = '请填写审查描述'
  },

  mounted() {
    let statuses = [
      this.AutSaAudCurrentStatusEnum.WAIT_AUD_FIRST_TRIAL,
      this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_INQUIRE_PROC,
    ]
    if (statuses.includes(this.status)) {
      if (!this.aut || this.aut.rejected) {
        this.modifying = true;
      }
    }

    if (this.readonly) {
      this.modifying = false;
    }
  },

  methods: {
    submit() {
      const evaluationForm = this.$refs.evaluationForm;
      if (evaluationForm && evaluationForm.validate) {
        evaluationForm.validate(valid => {
          if (!valid) return;
          this.submitEmit()
        })
      }
    },

    componentCallBack() {
      if (this.showEdit && !this.modifying) this.edit = false;
    },

    clearDesc() {
      this.formData.autDesc = '';
    }
  },
};
</script>
<style lang="scss">
.evaluation {
  position: relative;
  .objection {
    position: absolute;
    right: 20px;
    top: -10px;
    color: red;
    transform: none;
  }
}
</style>
