<template>
  <div>
    <el-button v-if="firstEditBtnShow" class="self-evaluation" icon="el-icon-edit" @click="handleEvaluate(true)">开始评测</el-button>
    <el-button v-if="nextEditBtnShow" class="self-evaluation-edit" type="text" icon="el-icon-edit" @click="handleEvaluate(true)">编辑</el-button>

    <el-form v-if="formShow" class="evaluation-form" ref="evaluationForm" label-width="110px" :model="formData" :rules="rules" :key="allowEdit">
      <el-form-item label="自评结果：" prop="autResult" :class="{ readonly: !allowEdit }">
        <el-select v-if="allowEdit" clearable filterable v-model.trim="formData.autResult" placeholder="请自评结果">
          <el-option v-for="item in gradeOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue">
          </el-option>
        </el-select>
        <span v-else>{{ evaluate(formData.autResult) }}</span>
      </el-form-item>
      <el-form-item label="自评依据：" prop="autDesc" :class="{ readonly: !allowEdit }">
        <el-input v-if="allowEdit" type="textarea" v-model.trim="formData.autDesc" :rows="4" :maxlength="50"></el-input>
        <span v-else>{{ formData.autDesc }}</span>
      </el-form-item>
      <el-form-item label="证明材料：" prop="fileIds" :class="{ readonly: !allowEdit }">
        <upload v-if="allowEdit" class="evaluation-upload" :files="files" :data="{ type: '1' }" @uploadSuccess="onSuccess" @fileRemove="onError"></upload>
        <span v-else>
          <!-- @click="download(file)" -->
          <div v-for="(file, index) in files" :key="index" style="margin-right: 15px">
            <a class="file-readonly" @click="showPreview(index)">
              {{ file.name }}
            </a>
            <el-image :ref="`file${index}`" style="display:none;" :src="file.url" :preview-src-list="[file.url]">
            </el-image>
          </div>

        </span>
      </el-form-item>

      <el-form-item v-if="allowEdit" label="表单操作：" class="evaluation-action">
        <el-button type="primary" size="small" @click="handleSave" v-loading="loading" :disabled="loading">保存</el-button>
        <el-button size="small" @click="handleCancel">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { download } from "@/utils/request";

const defaultForm = {
  complete: false,
  autResult: "",
  autDesc: "",
  clauseId: "",
  fileIds: "",
};
const formFields = ["autResult", "autDesc", "clauseId", "fileIds"];
export default {
  name: "EvaluationForm",
  props: {},
  watch: {},
  computed: {
    isHospital() {
      // 医疗机构
      return this.roles.includes("hospital");
    },
    readOnly() {
      const types = [
        this.AutSaAudStatusEnum.WAIT_TERM_SA,
        this.AutSaAudStatusEnum.TERM_SA_PROC,
        this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_SUM_REJECT,
      ];
      return !types.includes(Number(this.status));
    },

    allowEdit() {
      if (this.readOnly) return false;
      return (
        this.isHospital && (!this.validateResult || this.formData.isReject)
      );
    },

    evaluate() {
      return (id) => {
        return (this.gradeOptions.find((grade) => grade.dictValue == id) || {})
          .dictLabel;
      };
    },

    showForm() {
      let flag = true;
      for (const key in this.formData) {
        if (
          Object.hasOwnProperty.call(this.formData, key) &&
          formFields.includes(key)
        ) {
          if (!this.formData[key]) flag = false;
        }
      }
      return flag;
    },

    editBtnShow() {
      return !this.readOnly && !this.editing;
    },

    firstEditBtnShow() {
      return this.editBtnShow && !this.validateResult && this.isHospital;
    },
    nextEditBtnShow() {
      return (
        this.editBtnShow &&
        this.validateResult &&
        this.isHospital &&
        !(this.formData.isReject == true)
      );
    },
  },
  data() {
    return {
      loading: false,
      formData: {},
      formBack: JSON.stringify(defaultForm),
      rules: {
        autResult: [
          {
            required: true,
            message: "请自评结果",
            trigger: "change",
          },
        ],
        autDesc: [
          {
            required: true,
            message: "请填写自评依据",
            trigger: ["change", "blur"],
          },
        ],
        fileIds: [
          {
            required: true,
            validator: (rule, value, callback) =>
              this.filesValidator(rule, value, callback),
            trigger: ["change", "blur"],
          },
        ],
      },
      gradeOptions: [],
      validateResult: false,
      files: [],
      idUidMap: {},
      editing: false,

      clause: {},
      clauseId: "",
      status: "",
      fileList: [],
      formShow: false,
      roles: [],
    };
  },
  destroyed() {
    this.bus.$off("autBack");
  },
  mounted() {
    this.roles = this.$store.getters.roles;
    this.initDicts();
    this.bus.$on("autBack", (result, clauseId) => {
      // console.log(result, clauseId);
      if (this.clauseId == clauseId) {
        this.loading = false;
        if (result) {
          this.editing = false;
          this.validateResult = true;
          this.formData.clauseId = this.clauseId + "";
          this.formData.complete = true;
          this.formBack = JSON.stringify(this.formData);
          this.formData.isReject = false;
        }
      }
    });
  },
  methods: {
    init(data) {
      this.validateResult = !!data.formData;
      this.formShow = !!data.formData;
      //
      this.clause = data.clause;
      this.clauseId = data.clause.clauseId;
      this.formData = data.formData || { ...defaultForm };
      this.fileList = data.fileList;
      this.status = data.status;
      this.files = data.fileList.map((file) => {
        return {
          ...file,
          name: file.fileName,
        };
      });
      this.formBack = JSON.stringify(this.formData);
    },
    initDicts() {
      this.getDicts("review_result").then((response) => {
        this.gradeOptions = response.data;
      });
    },
    filesValidator(rule, value, callback) {
      if (this.formData.fileIds.length === 0) {
        callback("请上传证明材料");
      }
      callback();
    },

    handleEvaluate(showForm) {
      this.editing = true;
      this.validateResult = !showForm;
      this.formShow = true;
    },

    handleSave() {
      this.$refs.evaluationForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.bus.$emit("autSubmit", "autBack", this.formData, this.clauseId);
        }
      });
    },

    handleCancel() {
      const formData = JSON.parse(this.formBack);
      for (const key in formData) {
        if (
          Object.hasOwnProperty.call(formData, key) &&
          formFields.includes(key)
        ) {
          this.formData[key] = formData[key];
        }
      }
      this.validateResult = this.showForm;
      this.editing = false;
      this.formShow = this.showForm;
    },

    onSuccess(res, file) {
      this.idUidMap[file.uid] = res.data.fileId;
      this.files.push(file);
      this.formData.fileIds = (
        this.files.map((file) => this.idUidMap[file.uid]) || []
      ).join(",");
      this.$refs.evaluationForm.validateField("fileIds");
    },
    onError(error, file, length, index) {
      delete this.idUidMap[file.uid];
      this.files.splice(index, 1);
    },

    showPreview(index) {
      this.$nextTick(() => {
        this.$refs[`file${index}`][0].$el.children[0].click();
      })
    },


    download(file) {
      download(
        "/common/downloadFile",
        { fileId: Number(file.fileId) },
        file.fileName
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.evaluation-form {
  position: relative;
  ::v-deep .el-form-item {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
    .el-form-item__content {
      overflow: inherit;
    }
  }

  .file-readonly {
    text-decoration: underline;
    color: #649af6;
  }
}

.evaluation-upload {
  overflow: hidden;
  ::v-deep .el-upload {
    display: inline-block;
    text-align: left;
  }
  .el-upload__tip {
    display: block;
    margin-top: 0;
    span {
      font-size: 12px;
      display: block;
      line-height: 18px;
    }
  }
}

::v-deep .evaluation-action.el-form-item {
  margin-bottom: 0;
  .el-form-item__label {
    visibility: hidden;
  }
}
.self-evaluation {
  border-color: #79a8f8;
  color: #659bf7;
  ::v-deep .el-icon-edit {
    color: #3d81f5;
  }
}
.self-evaluation-edit {
  cursor: pointer;
  position: absolute;
  right: 20px;
  top: 0;
  color: #659bf7;
  ::v-deep .el-icon-edit {
    color: #3d81f5;
  }
}
</style>
