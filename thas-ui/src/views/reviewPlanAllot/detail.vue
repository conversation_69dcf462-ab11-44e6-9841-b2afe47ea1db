<template>
  <div class="review-plan-allot-detail" v-if="showAll">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>医院基本详情</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="toHospitalDetail">详细信息
          <i class="el-icon-arrow-right" />
        </el-button>
      </div>
      <detail-message :form="hospitalForm"></detail-message>
    </el-card>

    <el-card class="box-card" shadow="never">
      <el-tabs v-model.trim="activeName" @tab-click="tabClick">
        <el-tab-pane v-for="(item, index) in hos" :key="index" :label="`${item.name}(${item.clauseNum})`" :name="`${item.accountId}`">
          <span v-if="!clauseList.length">此分组暂无款项，可前往分组管理分配款项!</span>
          <div v-else>
            <modify-table :key="index" :data="clauseList.map(clause => clause.clauseId)" :versionId="versionId" :showAll="false"></modify-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script>
import ModifyTable from "@/views/hospitalSelfEvaluation/components/modifyTable.vue";
let that = this;
import request, { download } from "@/utils/request";
export default {
  name: "ReviewPlanAllotExamineDetail",
  data() {
    return {
      showAll: false,
      url: {
        detail: "/hospital/hos/plan/detail",
        submit: "",
      },
      hospital: {},
      allot: {},
      leader: null,
      userId: null,
      user: {},
      userList: [],
      clauseList: [],
      isIndeterminate: true,
      pageNum: 1,
      pageSize: 10,
      total: 0,
      hosReviewClauseNumInfos: [],
      hospitalForm: {},
      versionId: "",
      iAccountId: "",
      activeName: "",
      anuStatus: "",
      traineesList: [],
      revFileId: '',
      fileDetailMap: {},
      allDomainListVOList: [],
    };
  },

  watch: {
    $route: (to) => {
      if (to.path != "/reviewPlanAllot/reviewPlanAllotDetail") return;
      if (!this && !that) return;
      if (
        to.query.applyNo != that.applyNo ||
        that.applyNo == "" ||
        to.query.personForm != that.personForm ||
        that.personForm == ""
      ) {
        let view = that.$store.state.tagsView.visitedViews.find(
          (view) => view.fullPath == that.$route.fullPath
        );
        if (view) {
          that.$set(
            view,
            "title",
            that.$route.query.personForm == 2
              ? "评审分配详情"
              : "审查分配详情"
          );
        }

        that.getDetail();
      }
    },
  },

  components: {
    ModifyTable
  },

  computed: {
    revFile() {
      if (!this.revFileId) return false;
      let fileList = this.fileDetailMap[this.revFileId]
      if (!fileList || fileList.length == 0) return false;
      return fileList[0];
    },
    hos() {
      return this.hosReviewClauseNumInfos.filter((hos) => hos.clauseNum >= 0);
    },

    hos2() {
      return this.traineesList.filter(hos => hos.clauseNum === null)
    },

    hosReviewers() {
      let hosReviews = this.hosReviewClauseNumInfos.filter(reviewer => reviewer.accountId)
      console.log(hosReviews)
      let list = [{}]
      hosReviews.forEach(({ fieldIds, name }) => {
        let data = list[0];
        if (!fieldIds) return [];
        let fieldIdList = fieldIds.split(',')
        if (!fieldIdList) {
          return [];
        }
       fieldIdList.forEach(id => {
          data[id] = name
        })
      })
      return list
    },

    fileName() {
      return id => {
        let list = this.fileDetailMap[id];
        let file = Array.isArray(list) ? list[0] : {}
        return file ? file.fileName : ''
      }
    },
  },

  created() {

  },

  async mounted() {
    const versionId = await this.getEnabledVersionId();
    this.versionId = versionId || this.$store.getters.standardBaseVersionId;

    this.anuStatus = this.$route.query.anuStatus

    that = this;
    let view = this.$store.state.tagsView.visitedViews.find(
      (view) => view.fullPath == this.$route.fullPath
    );
    if (view) {
      this.$set(
        view,
        "title",
        this.$route.query.personForm == 2 ? "评审分配详情" : "审查分配详情"
      );
    }

    this.getDetail();
    this.getDetail2();
  },

  methods: {
    download(fileId, name) {
      download("/common/downloadFile", { fileId }, `${name}-评审员保密承诺与利益冲突申报表.pdf`);
    },
    download2(fileId, name) {
      download("/common/downloadFile", { fileId }, name);
    },
    preview(url) {
      window.open(url, '_blank');
    },
    tabClick() {
      this.pageNum = 1;
      this.getClause2();
    },

    toHospitalDetail() {
      this.$router.push({
        path: "/auditManagement/hospitalDetailMessage",
        query: { applyNo: this.$route.query.applyNo },
      });
    },
    handleClick(tab, event) {
      console.log(tab, event);
    },

    getDetail() {
      this.applyNo = this.$route.query.applyNo;
      this.personForm = this.$route.query.personForm;
      this.hospitalName = this.$route.query.hospitalName;
      return request({
        url: this.url.detail,
        method: "post",
        data: {
          applyNo: this.applyNo,
          versionId: this.versionId,
          personForm: this.personForm,
        },
      }).then((res) => {
        this.hospital = res.hospitalAuthContact;
        this.userList = res.preExamList || [];
        this.fileDetailMap = res.fileDetailMap || {}
        this.hosReviewClauseNumInfos = res.hosReviewClauseNumInfoVOList || [];
        this.allDomainListVOList = res.allDomainListVOList || [];
        this.activeName = this.hosReviewClauseNumInfos[0].accountId;
        this.revFileId = res.revFileId
        this.fileDetailMap = res.fileDetailMap
        this.userId = "";
        this.hospitalForm = {
          shadow: "never",
          list: [
            {
              label: "医院名称",
              value: this.hospitalName,
              type: "text",
            },
            {
              label: "联系人姓名",
              value: res.hospitalAuthContact.authContactName,
              type: "text",
            },
            {
              label: "联系人手机号",
              value: res.hospitalAuthContact.authContactMobile,
              type: "text",
            },
            {
              label: "联系人邮箱",
              value: res.hospitalAuthContact.authContactEmail,
              type: "text",
            },
          ],
        };

        this.getClause2();

        //
        this.showAll = true;
      });
    },

    getDetail2() {
      this.applyNo = this.$route.query.applyNo;
      this.personForm = this.$route.query.personForm;
      this.hospitalName = this.$route.query.hospitalName;
      return request({
        url: this.url.detail,
        method: "post",
        data: {
          applyNo: this.applyNo,
          versionId: this.versionId,
          personForm: 3,
        },
      }).then((res) => {
        this.traineesList = res.hosReviewClauseNumInfoVOList
      });
    },


    getClause2() {
      let accountId = this.activeName;
      request({
        url:
          "/hospital/hos/plan/clause/detail?" +
          `&time=${Date.now()}`,
        method: "post",
        data: {
          personForm: this.personForm,
          applyNo: this.applyNo,
          versionId: this.versionId,
          accountId,
        },
      }).then((res) => {
        if (Array.isArray(res.rows) && res.rows[0]) {
          this.versionId = res.rows[0].versionId;
        }
        (this.pageNum1 = 1) &&
          this.$set(this, "clauseList", res.rows) &&
          (this.total = res.total);
      });
    },
  },
};
</script>
<style lang='scss' scoped>
.review-plan-allot-detail {
  background-color: transparent !important;
  .box-card + .box-card {
    margin-top: 10px;
  }

  p {
    margin: 24px 0;
    padding: 0 15px;
    font-size: 14px;
    display: flex;
    span:first-child {
      min-width: 60px;
    }
  }
}
</style>