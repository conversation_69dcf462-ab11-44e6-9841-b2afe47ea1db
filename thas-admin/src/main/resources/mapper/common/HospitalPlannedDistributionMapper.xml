<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.HospitalPlannedDistributionMapper">

    <resultMap type="HospitalPlannedDistribution" id="HospitalPlannedDistributionResult">
        <result property="id"    column="id"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="versionId"    column="version_id"    />
        <result property="hosStatus"    column="hos_status"    />
        <result property="cycleStatus"    column="cycle_status"    />
        <result property="revFileId"    column="rev_file_id"    />
        <result property="description"    column="description"    />
        <result property="refuseStudentReason"    column="refuse_student_reason"    />
        <result property="confirmTime"    column="confirm_time"    />
        <result property="preDisComplete"    column="pre_dis_complete"    />
        <result property="reviewDisComplete"    column="review_dis_complete"    />
        <result property="seniorReviewDisComplete"    column="senior_review_dis_complete"    />
        <result property="status"    column="status"    />
        <result property="creator"    column="creator"    />
        <result property="createTime"    column="create_time"    />
        <result property="updater"    column="updater"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>



    <sql id="selectHospitalPlannedDistributionVo">
        select id, apply_no, version_id, hos_status, cycle_status, rev_file_id, description,refuse_student_reason, confirm_time, pre_dis_complete, review_dis_complete,senior_review_dis_complete,
         status, creator, create_time, updater, update_time from hospital_planned_distribution
    </sql>

    <select id="selectHospitalPlannedDistributionList" parameterType="HospitalPlannedDistribution" resultMap="HospitalPlannedDistributionResult">
        <include refid="selectHospitalPlannedDistributionVo"/>
        <where>
            <if test="applyNo != null  and applyNo != ''"> and apply_no = #{applyNo}</if>
            <if test="versionId != null "> and version_id = #{versionId}</if>
            <if test="hosStatus != null "> and hos_status = #{hosStatus}</if>
            <if test="cycleStatus != null "> and cycle_status = #{cycleStatus}</if>
            <if test="revFileId != null "> and rev_file_id = #{revFileId}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="confirmTime != null  and confirmTime != ''"> and confirm_time = #{confirmTime}</if>
            <if test="preDisComplete != null "> and pre_dis_complete = #{preDisComplete}</if>
            <if test="reviewDisComplete != null "> and review_dis_complete = #{reviewDisComplete}</if>
            <if test="seniorReviewDisComplete != null "> and senior_review_dis_complete = #{seniorReviewDisComplete}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="creator != null  and creator != ''"> and creator = #{creator}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
        </where>
    </select>

    <select id="selectHospitalPlannedDistributionById" parameterType="Long" resultMap="HospitalPlannedDistributionResult">
        <include refid="selectHospitalPlannedDistributionVo"/>
        where id = #{id}
    </select>

    <select id="selectHospitalPlannedDistributionByApplyNo" parameterType="String" resultMap="HospitalPlannedDistributionResult">
        <include refid="selectHospitalPlannedDistributionVo"/>
        where apply_no = #{applyNo} and status = 1
    </select>

    <insert id="insertHospitalPlannedDistribution" parameterType="HospitalPlannedDistribution" useGeneratedKeys="true" keyProperty="id">
        insert into hospital_planned_distribution
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">apply_no,</if>
            <if test="versionId != null">version_id,</if>
            <if test="hosStatus != null">hos_status,</if>
            <if test="cycleStatus != null">cycle_status,</if>
            <if test="revFileId != null">rev_file_id,</if>
            <if test="description != null">description,</if>
            <if test="refuseStudentReason != null">refuse_student_reason,</if>
            <if test="confirmTime != null and confirmTime != ''">confirm_time,</if>
            <if test="preDisComplete != null">pre_dis_complete,</if>
            <if test="reviewDisComplete != null">review_dis_complete,</if>
            <if test="seniorReviewDisComplete != null">senior_review_dis_complete,</if>
            <if test="status != null">status,</if>
            <if test="creator != null and creator != ''">creator,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updater != null and updater != ''">updater,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">#{applyNo},</if>
            <if test="versionId != null">#{versionId},</if>
            <if test="hosStatus != null">#{hosStatus},</if>
            <if test="cycleStatus != null">#{cycleStatus},</if>
            <if test="revFileId != null">#{revFileId},</if>
            <if test="description != null">#{description},</if>
            <if test="refuseStudentReason != null">#{refuseStudentReason},</if>
            <if test="confirmTime != null and confirmTime != ''">#{confirmTime},</if>
            <if test="preDisComplete != null">#{preDisComplete},</if>
            <if test="reviewDisComplete != null">#{reviewDisComplete},</if>
            <if test="seniorReviewDisComplete != null">#{seniorReviewDisComplete},</if>
            <if test="status != null">#{status},</if>
            <if test="creator != null and creator != ''">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updater != null and updater != ''">#{updater},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateHospitalPlannedDistribution" parameterType="HospitalPlannedDistribution">
        update hospital_planned_distribution
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">apply_no = #{applyNo},</if>
            <if test="versionId != null">version_id = #{versionId},</if>
            <if test="hosStatus != null">hos_status = #{hosStatus},</if>
            <if test="cycleStatus != null">cycle_status = #{cycleStatus},</if>
            <if test="revFileId != null">rev_file_id = #{revFileId},</if>
            <if test="description != null">description = #{description},</if>
            <if test="refuseStudentReason != null">refuse_student_reason = #{refuseStudentReason},</if>
            <if test="confirmTime != null ">confirm_time = #{confirmTime},</if>
            <if test="preDisComplete != null">pre_dis_complete = #{preDisComplete},</if>
            <if test="reviewDisComplete != null">review_dis_complete = #{reviewDisComplete},</if>
            <if test="seniorReviewDisComplete != null">senior_review_dis_complete = #{seniorReviewDisComplete},</if>
            <if test="status != null">status = #{status},</if>
            <if test="creator != null and creator != ''">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateHospitalPlannedDistributionByApplyNo" parameterType="HospitalPlannedDistribution">
        update hospital_planned_distribution
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">apply_no = #{applyNo},</if>
            <if test="versionId != null">version_id = #{versionId},</if>
            <if test="hosStatus != null">hos_status = #{hosStatus},</if>
            <if test="cycleStatus != null">cycle_status = #{cycleStatus},</if>
            <if test="revFileId != null">rev_file_id = #{revFileId},</if>
            <if test="description != null">description = #{description},</if>
            <if test="refuseStudentReason != null">refuse_student_reason = #{refuseStudentReason},</if>
            <if test="confirmTime != null ">confirm_time = #{confirmTime},</if>
            <if test="preDisComplete != null">pre_dis_complete = #{preDisComplete},</if>
            <if test="reviewDisComplete != null">review_dis_complete = #{reviewDisComplete},</if>
            <if test="seniorReviewDisComplete != null">senior_review_dis_complete = #{seniorReviewDisComplete},</if>
            <if test="status != null">status = #{status},</if>
            <if test="creator != null and creator != ''">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where apply_no = #{applyNo} and status = 1
    </update>

    <delete id="deleteHospitalPlannedDistributionById" parameterType="Long">
        delete from hospital_planned_distribution where id = #{id}
    </delete>

    <delete id="deleteHospitalPlannedDistributionByIds" parameterType="String">
        delete from hospital_planned_distribution where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <resultMap type="HospitalPlannedDistributionVo" id="HospitalPlannedDistributionResult2">
        <result property="id"    column="id"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="versionId"    column="version_id"    />
        <result property="hosStatus"    column="hos_status"    />
        <result property="cycleStatus"    column="cycle_status"    />
        <result property="reviewDisComplete"    column="review_dis_complete"    />
        <result property="preDisComplete"    column="pre_dis_complete"    />
        <result property="description"    column="description"    />
        <result property="confirmTime"    column="confirm_time"    />
        <collection  property="hospitalReviewCycleVoList"  javaType="java.util.List"  resultMap="HospitalReviewCycleResult" fetchType="lazy"/>
        <collection  property="hospitalReviewerVoList"  javaType="java.util.List"  resultMap="HospitalReviewerResult" fetchType="lazy"/>
    </resultMap>

    <resultMap id="HospitalReviewCycleResult" type="HospitalReviewCycleVo">
        <result property="id"    column="review_cycle_id"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="stageValue"    column="stage_value"    />
        <result property="cycle"    column="cycle"    />
        <association property="sysDictDataVo" javaType="SysDictDataVo" column="stage_value" resultMap="SysDicDataResult"/>
    </resultMap>
    <resultMap id="SysDicDataResult" type="SysDictDataVo">
        <result property="dictLabel"  column="dict_label"  />
        <result property="dictValue"  column="dict_value"  />
        <result property="dictType"   column="dict_type"   />
    </resultMap>


    <resultMap id="HospitalReviewerResult" type="HospitalReviewerVo">
        <result property="id"    column="reviewer_id"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="reviewerId"    column="reviewer_id"    />
        <result property="fieldIdList"    column="field_id_list"    />
        <result property="leaderIs"    column="leader_is"    />
        <result property="reviewerStatus" column="reviewer_status"/>
        <association property="reviewerBaseInfoVo" javaType="ReviewerBaseInfoVo" column="reviewer_id" resultMap="ReviewerBaseInfoResult"/>
    </resultMap>

    <resultMap id="ReviewerBaseInfoResult" type="ReviewerBaseInfoVo">
        <result property="id"    column="reviewer_base_info_id"    />
        <result property="accountId"    column="account_id"    />
        <result property="userName"    column="user_name"    />
        <result property="reviewerGender"    column="reviewer_gender"    />
        <result property="reviewerName"    column="reviewer_name"    />
        <result property="company"    column="company"    />
        <result property="companyPost"    column="company_post"    />
        <result property="headPortrait" column="head_portrait"/>
        <result property="profile" column="profile"/>
    </resultMap>

    <sql id="SQL_AES_ENCRYPT">'${@com.thas.web.config.SqlAesEncryptConfig@SQL_AES_ENCRYPT}'</sql>


    <select id="selectPlannedDistributionInfoByApplyNo" parameterType="String" resultMap="HospitalPlannedDistributionResult2">
        SELECT
            hpd.id,
            hpd.apply_no,
            hpd.version_id,
            hpd.hos_status,
            hpd.cycle_status,
            hpd.description,
            hpd.confirm_time,
            hpd.pre_dis_complete,
            hpd.review_dis_complete,
            hrc.id review_cycle_id,
            hrc.apply_no,
            hrc.stage_value,
            hrc.cycle,
            hr.id reviewer_id,
            hr.field_id_list,
            hr.apply_no,
            hr.leader_is,
            hr.reviewer_status,
            rbi.id reviewer_base_info_id,
            AES_DECRYPT(unhex(rbi.reviewer_name),<include refid="SQL_AES_ENCRYPT"/>) reviewer_name,
            rbi.company,
            rbi.company_post,
            rbi.account_id,
            su.user_name,
            rbi.reviewer_gender,
            sdd.dict_value,
            sdd.dict_type,
            sdd.dict_label,
            rbi.head_portrait,
            rbi.profile
        FROM
            hospital_planned_distribution hpd
                LEFT JOIN hospital_review_cycle hrc ON hpd.apply_no = hrc.apply_no AND hrc.status = 1
                LEFT JOIN hospital_reviewer hr ON hpd.apply_no = hr.apply_no AND hr.status = 1
                LEFT JOIN reviewer_base_info rbi ON hr.reviewer_id = rbi.account_id
                LEFT JOIN sys_user su ON su.user_id = rbi.account_id
                LEFT JOIN sys_dict_data sdd ON sdd.dict_type = 'review_cycle'
                AND hrc.stage_value = sdd.dict_value
        WHERE
            hpd.apply_no = #{applyNo}
--           AND hpd.pre_dis_complete = 1
--           AND hpd.review_dis_complete = 1
--           AND hpd.senior_review_dis_complete = 1
          AND hpd.STATUS =1
    </select>

    <update id="updateCycleStatusByApplyNo" parameterType="String">
        update hospital_planned_distribution set cycle_status = 1 where apply_no = #{applyNo} and status = 1
    </update>

    <update id="updateHospitalPlannedDistributionByApplyNoList">
         update hospital_planned_distribution set status =2 where apply_no in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
         and status = 1

    </update>

    <select id="selectPlannedDistributionInfoByLast" resultMap="HospitalPlannedDistributionResult2">
        select hpd.id, hpd.apply_no, hpd.version_id, hpd.hos_status, hpd.cycle_status, hpd.description, hpd.confirm_time, hpd.pre_dis_complete, hpd.review_dis_complete,
               hrc.id review_cycle_id, hrc.apply_no, hrc.stage_value, hrc.cycle,
               hr.id reviewer_id, hr.apply_no, hr.leader_is, hr.reviewer_status,
               rbi.id reviewer_base_info_id,
               AES_DECRYPT(unhex(rbi.reviewer_name),<include refid="SQL_AES_ENCRYPT"/>) reviewer_name,
               rbi.company, rbi.company_post,rbi.account_id,rbi.reviewer_gender,
               sdd.dict_value,sdd.dict_type,sdd.dict_label
        from (select id, apply_no,version_id,hos_status,cycle_status,description,confirm_time,pre_dis_complete,review_dis_complete from hospital_planned_distribution order by create_time desc limit 1) hpd
                 left join hospital_review_cycle hrc on hpd.apply_no=hrc.apply_no
                 left join hospital_reviewer hr on hpd.apply_no=hr.apply_no
                 left join reviewer_base_info rbi on hr.reviewer_id=rbi.account_id
                 left join sys_dict_data    sdd on sdd.dict_type='review_cycle' and hrc.stage_value=sdd.dict_value
    </select>

</mapper>
