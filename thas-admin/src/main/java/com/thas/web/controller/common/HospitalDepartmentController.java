package com.thas.web.controller.common;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.thas.common.utils.poi.ExcelUtil;
import com.thas.web.domain.HospitalDepartment;
import com.thas.web.service.IHospitalDepartmentService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.enums.BusinessType;
import com.thas.common.core.page.TableDataInfo;

/**
 * 医疗结构-临床服务/医技服务开放关联Controller
 * 
 * <AUTHOR>
 * @date 2022-01-19
 */
@RestController
@RequestMapping("/system/department")
public class HospitalDepartmentController extends BaseController
{

}
