<!--  -->
<template>
  <div class='evaluation-base'>
    <!-- 选择款项 :key="clauseIds4Select.toString() || versionId" -->
    <standard-card ref="standardCard" :clauseIds="clauseIds" :versionId="versionId" @selectChange="selectChange" @loaded="standardLoaded">
    </standard-card>
  </div>
</template>

<script>
export default {
  name: 'EvaluationBase',
  components: {},
  props: {
    clauseIds: {
      // 最多能展示的款项id集合 
      type: Array,
      default: () => [],
    },
    versionId: {
      type: [String, Number],
      default: () => ""
    }
  },
  watch: {},
  data() {
    return {
      clauseIds4Select: [], // 当前要展示的款项id集合
    };
  },

  computed: {},

  created() { },

  mounted() { },

  methods: {
    selectChange(tier, data, { chapter, section, article }) {
      this.$emit('selectChange', data, article)
    },
    standardLoaded() {
      this.$emit('loaded', true,)
    },
  }
}

</script>
<style lang='scss' scoped>
.evaluation-base {
}
</style>