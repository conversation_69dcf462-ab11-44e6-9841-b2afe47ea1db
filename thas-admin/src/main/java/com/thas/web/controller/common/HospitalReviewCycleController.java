package com.thas.web.controller.common;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.enums.BusinessType;
import com.thas.web.domain.HospitalReviewCycle;
import com.thas.web.service.IHospitalReviewCycleService;
import com.thas.common.utils.poi.ExcelUtil;
import com.thas.common.core.page.TableDataInfo;

/**
 * 医院评审阶段周期Controller
 *
 * <AUTHOR>
 * @date 2022-01-27
 */
@Api(value="HospitalReviewCycleController",tags = "医院评审阶段周期Controller")
@RestController
@RequestMapping("/system/cycle")
public class HospitalReviewCycleController extends BaseController
{
    @Autowired
    private IHospitalReviewCycleService hospitalReviewCycleService;


    /**
     * 医疗机构评审周期评审
     *
     * @param hospitalReviewCycle 评审结果
     * @return 提交结果
     */
    @PostMapping("/hos/review")
    public AjaxResult refuseHospitalCycle(@Valid @RequestBody HospitalReviewCycle hospitalReviewCycle) {
        logger.info("/review 医疗机构评审周期评审入参:{}", hospitalReviewCycle);
        return hospitalReviewCycleService.refuseHospitalCycle(hospitalReviewCycle);
    }

    /**
     * 查询医院评审阶段周期列表
     */
    //@PreAuthorize("@ss.hasPermi('system:cycle:list')")
    @GetMapping("/list")
    public TableDataInfo list(HospitalReviewCycle hospitalReviewCycle)
    {
        startPage();
        List<HospitalReviewCycle> list = hospitalReviewCycleService.selectHospitalReviewCycleList(hospitalReviewCycle);
        return getDataTable(list);
    }

    /**
     * 导出医院评审阶段周期列表
     */
    //@PreAuthorize("@ss.hasPermi('system:cycle:export')")
    @Log(title = "医院评审阶段周期", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HospitalReviewCycle hospitalReviewCycle)
    {
        List<HospitalReviewCycle> list = hospitalReviewCycleService.selectHospitalReviewCycleList(hospitalReviewCycle);
        ExcelUtil<HospitalReviewCycle> util = new ExcelUtil<>(HospitalReviewCycle.class);
        util.exportExcel(response, list, "医院评审阶段周期数据");
    }

    /**
     * 获取医院评审阶段周期详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:cycle:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(hospitalReviewCycleService.selectHospitalReviewCycleById(id));
    }

    /**
     * 新增医院评审阶段周期
     */
    //@PreAuthorize("@ss.hasPermi('system:cycle:add')")
    @Log(title = "医院评审阶段周期", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HospitalReviewCycle hospitalReviewCycle)
    {
        return toAjax(hospitalReviewCycleService.insertHospitalReviewCycle(hospitalReviewCycle));
    }

    /**
     * 修改医院评审阶段周期
     */
    //@PreAuthorize("@ss.hasPermi('system:cycle:edit')")
    @Log(title = "医院评审阶段周期", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HospitalReviewCycle hospitalReviewCycle)
    {
        return toAjax(hospitalReviewCycleService.updateHospitalReviewCycle(hospitalReviewCycle));
    }

    /**
     * 删除医院评审阶段周期
     */
    //@PreAuthorize("@ss.hasPermi('system:cycle:remove')")
    @Log(title = "医院评审阶段周期", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(hospitalReviewCycleService.deleteHospitalReviewCycleByIds(ids));
    }

    /**
     * 批量插入
     */
    //@PreAuthorize("@ss.hasPermi('system:cycle:add')")
    @ApiOperation("批量插入")
    @Log(title = "批量插入", businessType = BusinessType.INSERT)
    @PostMapping("/insertHospitalReviewCycleList")
    public AjaxResult insertHospitalReviewCycleList(@RequestBody List<HospitalReviewCycle> hospitalReviewCycleList)
    {
        return toAjax(hospitalReviewCycleService.insertHospitalReviewCycleList(hospitalReviewCycleList));
    }

    /**
     * 批量修改-医院评审阶段周期
     */
    @ApiOperation("批量修改-医院评审阶段周期")
    @Log(title = "批量修改-医院评审阶段周期", businessType = BusinessType.UPDATE)
    @PostMapping("/updateHospitalReviewCycleList")
    public AjaxResult updateHospitalReviewCycleList( @RequestBody List<HospitalReviewCycle> hospitalReviewCycleList)
    {
       return AjaxResult.success(hospitalReviewCycleService.updateHospitalReviewCycleList(hospitalReviewCycleList));
    }
}
