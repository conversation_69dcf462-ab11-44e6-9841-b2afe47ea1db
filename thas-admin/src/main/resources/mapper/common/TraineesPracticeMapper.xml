<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.TraineesPracticeMapper">

    <resultMap type="TraineesPractice" id="TraineesPracticeResult">
        <result property="id"    column="id"    />
        <result property="accountId"    column="account_id"    />
        <result property="praId"    column="pra_id"    />
        <result property="traName"    column="tra_name"    />
        <result property="traGender"    column="tra_gender"    />
        <result property="major"    column="major"    />
        <result property="company"    column="company"    />
        <result property="companyAddress"    column="company_address"    />
        <result property="signFileId"    column="sign_file_id"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTraineesPracticeVo">
        select id, account_id, pra_id, AES_DECRYPT(unhex(tra_name),<include refid="SQL_AES_ENCRYPT"/>) tra_name, tra_gender, major, company, company_address, sign_file_id, status, create_time, update_time from trainees_practice
    </sql>

    <sql id="SQL_AES_ENCRYPT">'${@com.thas.web.config.SqlAesEncryptConfig@SQL_AES_ENCRYPT}'</sql>

    <select id="selectTraineesPracticeList" parameterType="TraineesPractice" resultMap="TraineesPracticeResult">
        select * from (
        <include refid="selectTraineesPracticeVo"/>
        <where>
            <if test="accountId != null  and accountId != ''">and account_id = #{accountId}</if>
            <if test="praId != null  and praId != ''"> and pra_id = #{praId}</if>
            <if test="traGender != null "> and tra_gender = #{traGender}</if>
            <if test="major != null  and major != ''"> and major = #{major}</if>
            <if test="company != null  and company != ''"> and company = #{company}</if>
            <if test="companyAddress != null  and companyAddress != ''"> and company_address = #{companyAddress}</if>
            <if test="signFileId != null  and signFileId != ''"> and sign_file_id = #{signFileId}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
        ) aa
        where
        1=1
        <if test="traName != null  and traName != ''">and tra_name like concat('%',#{traName}, '%')</if>

    </select>

    <select id="selectTraineesPracticeById" parameterType="Long" resultMap="TraineesPracticeResult">
        <include refid="selectTraineesPracticeVo"/>
        where id = #{id}
    </select>

    <select id="selectTraineesPracticeByAccountId" parameterType="String" resultMap="TraineesPracticeResult">
        <include refid="selectTraineesPracticeVo"/>
        where account_id = #{accountId}
    </select>

    <insert id="insertTraineesPractice" parameterType="TraineesPractice" useGeneratedKeys="true" keyProperty="id">
        insert into trainees_practice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountId != null and accountId != ''">account_id,</if>
            <if test="praId != null and praId != ''">pra_id,</if>
            <if test="traName != null and traName != ''">tra_name,</if>
            <if test="traGender != null">tra_gender,</if>
            <if test="major != null and major != ''">major,</if>
            <if test="company != null and company != ''">company,</if>
            <if test="companyAddress != null and companyAddress != ''">company_address,</if>
            <if test="signFileId != null and signFileId != ''">sign_file_id,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountId != null and accountId != ''">#{accountId},</if>
            <if test="praId != null and praId != ''">#{praId},</if>
            <if test="traName != null and traName != ''">HEX(AES_ENCRYPT(#{traName},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="traGender != null">#{traGender},</if>
            <if test="major != null and major != ''">#{major},</if>
            <if test="company != null and company != ''">#{company},</if>
            <if test="companyAddress != null and companyAddress != ''">#{companyAddress},</if>
            <if test="signFileId != null and signFileId != ''">#{signFileId},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <insert id="batchInsertTraineesPractice" parameterType="com.thas.web.dto.TraineesPracticeDTO" useGeneratedKeys="true" keyProperty="id">
        insert into trainees_practice (account_id, pra_id, tra_name, tra_gender, major,
                                       company, company_address, sign_file_id) values
        <foreach collection="list" item="item" index="index" separator=","  close=";">
            (#{item.accountId}, #{item.praId}, HEX(AES_ENCRYPT(#{item.traName},<include refid="SQL_AES_ENCRYPT"/>)), #{item.traGender}, #{item.major},
             #{item.company}, #{item.companyAddress}, #{item.signFileId})
        </foreach>
    </insert>

    <update id="updateTraineesPractice" parameterType="TraineesPractice">
        update trainees_practice
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountId != null and accountId != ''">account_id = #{accountId},</if>
            <if test="praId != null and praId != ''">pra_id = #{praId},</if>
            <if test="traName != null and traName != ''">tra_name = HEX(AES_ENCRYPT(#{traName},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="traGender != null">tra_gender = #{traGender},</if>
            <if test="major != null and major != ''">major = #{major},</if>
            <if test="company != null and company != ''">company = #{company},</if>
            <if test="companyAddress != null and companyAddress != ''">company_address = #{companyAddress},</if>
            <if test="signFileId != null and signFileId != ''">sign_file_id = #{signFileId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTraineesPracticeById" parameterType="Long">
        delete from trainees_practice where id = #{id}
    </delete>

    <delete id="deleteTraineesPracticeByPraId" parameterType="String">
        delete from trainees_practice where pra_id = #{praId}
    </delete>

    <delete id="deleteTraineesPracticeByIds" parameterType="String">
        delete from trainees_practice where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>