<!DOCTYPE html>
<html
  lang="en"
  xmlns:o="urn:schemas-microsoft-com:office:office"
  xmlns:w="urn:schemas-microsoft-com:office:word"
  xmlns:dt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882"
  xmlns="http://www.w3.org/TR/REC-html40"
>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="ProgId" content="Word.Document" />
    <meta name="Generator" content="Microsoft Word 14" />
    <meta name="Originator" content="Microsoft Word 14" />
    <title></title>
    <style>
      @font-face {
        font-family: "SimSun";
      }
      @page {
        @top-left {
          content: element(header);
        }
        size: A4;
      }
      body {
        font-family: "SimSun";
      }
    </style>

    <!-- 事实准确性查询表 表单 -->
    <style>
      /* Fact accuracy query form */
      .faqf {
        width: calc(90% - 2px);
        margin: 0 auto;
        border: 1px solid black;
      }
      .faqf > p {
        margin: 0;
        border-bottom: 1px solid black;
      }
      .faqf > p:first-child {
        text-align: center;
        padding: 7px 0;
        /* height: 36px; */
        /* vertical-align: middle; */
      }
      .faqf > p:last-child {
        border-bottom: 0;
        /* border-left: 1px solid #000; */
      }
      .faqf > p > span {
        display: inline-block;
        text-align: center;
        font-size: 14px;
        vertical-align: middle;
      }

      .faqf > p > span.form-label {
        /* height: 36px; */
        padding: 8px 0;
        border-right: 1px solid black;
        width: 120px;
      }
      .faqf > p:nth-child(3) > span.form-label {
        padding: 18px 0;
      }

      .faqf > p:nth-child(5) > span.form-label {
        padding: 0;
        line-height: 18px;
      }

      .faqf > p.form-inline {
        width: 49%;
        display: inline-block;
        border-bottom: 0;
      }

      .faqf > p > span.form-content {
        /* height: 36px; */
        width: calc(100% - 130px);
        padding: 8px 0;
        /* border: 1px solid black; */
      }
    </style>

    <!-- 事实准确性查询表 表格 -->
    <style>
      /* Fact accuracy query table */
      .faqt {
        width: calc(90% - 2px);
        margin: 0 auto;
        /* margin-top: 15px; */
        border: 1px solid black;
        border-top: 0;
      }
      .faqt > .grid {
        display: grid;
        grid-template-columns: auto auto auto;
      }

      .faqt > .table {
        width: 100%;
      }
      .faqt > .table > .thead {
        width: 100%;
        border-bottom: 1px solid black;
      }
      .faqt > .table > .thead > div {
        display: inline-block;
        /* width: calc(25% - 4px); */
        width: 24%;
        border-right: 1px solid black;
        /* text-align: center; */
      }
      .faqt > .table > .thead > div > span {
        margin-left: 5px;
      }
      .faqt > .table > .thead > div:last-child {
        border-right: 0;
      }

      /* .faqt > .table > .tbody {
      } */

      .faqt > .table > .tbody > div {
        width: 100%;
        border-bottom: 1px solid black;
        display: flex;
      }
      .faqt > .table > .tbody > div:last-child {
        border-bottom: 0;
      }

      .faqt > .table > .tbody > div > div {
        display: inline-block;
        width: 24%;
        max-width: 24%;
        border-right: 1px solid black;
        text-align: center;
      }
      .faqt > .table > .tbody > div > div:last-child {
        border-right: 0;
      }
    </style>

    <!-- 事实准确性查询表 表格2 -->
    <style>
      .table2 {
        width: 100%;
      }
      .table2 table {
        width: 100%;
        table-layout: fixed;
      }

      .table2 > table th {
        text-align: left;
        border: 1px solid black;
        border-right: 0;
        font-weight: normal;
      }

      .table2 > table th,
      .table2 > table td {
        word-break: break-all;
        word-wrap: break-word;
      }

      .table2 > table tr td:first-child {
        width: 100px;
      }

      .table2 > table th,
      .table2 > table td {
        padding: 5px 0;
        padding-left: 2px;
      }
      .table2 > table th:first-child {
        border-right: 0;
        border-left: 0;
      }
      .table2 > table tr:first-child th {
        border-top: 0;
      }
      .table2 > table td {
        text-align: left;
        border-left: 1px solid black;
        border-bottom: 1px solid #000;
      }
      .table2 > table td:first-child {
        border-right: 0;
        border-left: 0;
      }
      .table2 > table tr:last-child td {
        border-bottom: 0;
      }
    </style>

    <!-- 事实准确性查询表  盖章-->
    <style>
      /* Fact accuracy query sign */
      .faqs {
        width: calc(90% - 2px);
        margin: 0 auto;
        /* margin-top: 15px; */
        border: 1px solid black;
        border-top: 0;
      }
      .faqs > div {
        margin: 0;
        font-size: 14px;
        border-bottom: 1px solid #000;
        /* height: 130px; */
      }
      .faqs > div:last-child {
        border-bottom: 0;
      }
      .faqs > div > div {
        display: inline-block;
        /* height: 100%; */
        vertical-align: middle;
      }
      .faqs > div > div.form-label {
        width: 60px;
        letter-spacing: 3px;
        border-right: 1px solid #000;
        padding: 35px 5px;
      }
      .faqs > div > div.form-content {
        width: calc(100% - 87px);
      }
      .faqs > div > div.form-content > span {
        display: block;
        margin: 0 auto;
        width: 50%;
      }
    </style>

    <style>
      table {
        width: 80%;
        margin: 0 auto;
        table-layout: fixed;
        word-wrap: break-word;
        word-break: break-all;
      }
      table td {
        text-align: center;
		padding:12px 0;
      }

	  .white {
		color:white;
	  }
	  .bcDark{
		background-color:#2f5496;
	  }
	  .bcLight {
		background-color:#8eaadb;
	  }
	  .bcWhite {
		background-color:#fff;
	  }
	  .bold {
		font-weight:bold;
	  }
    </style>
  </head>
  <body>
    <div>
      <h3>附件5：事实准确性查询表</h3>
      <table border="1" cellspacing="0" cellpadding="0">
        <tr>
          <td colspan="4" class="white bcDark">事实准确性查询表</td>
        </tr>
        <tr class="bcLight bold">
          <td>医院</td>
          <td colspan="3">${hospitalName}</td>
        </tr>
        <tr class="bcLight bold">
          <td>评审日期</td>
          <td colspan="3">${reviewCycleBegin} - ${reviewCycleEnd}</td>
        </tr>
        <tr class="bold">
          <td class="bcLight">评审团队成员</td>
          <td colspan="3">${groupName}</td>
        </tr>
        <tr class="bcLight">
          <td>款号</td>
          <td>评价结果</td>
          <td>评审员评价</td>
          <td>医院反馈：<br />
               事实准确性审查报告中的错误/需要注意/澄清的地方：
          </td>
        </tr>

        <#if (farClauseMapList?size>0)>
        <#list farClauseMapList as farClause>
        <tr class="bcLight">
          <td><#if farClause.isStar == '1'>★</#if> ${farClause.clauseNo} </td>
          <td>
           <#if (farClause.autResult == '1')> 优秀
              <#elseif (farClause.autResult == '2')> 良好
              <#elseif (farClause.autResult == '3')> 达标
              <#elseif (farClause.autResult == '4')> 部分达标
              <#elseif (farClause.autResult == '5')> 不达标
              <#else> 不适用
              </#if>
          </td>
          <td> ${farClause.srClause}</td>
          <td class="bcWhite">${farClause.farClause}</td>
        </tr>
         </#list>
         <#else>
        <tr>
          <td colspan="4">本单位对评审报告(事实准确性)无异议</td>
        </tr>
        </#if>
        <tr>
          <td class="bcLight">备注</td>
          <td colspan="3">
            ${autDesc}
          </td>
        </tr>
        <tr>
          <td class="bcLight">提交单位确认</td>
          <td colspan="3">
            <br />
            单位（盖公章）： <br /><br /><br /><br />
            法定代表人（签字）： <br /><br /><br /><br />
            日期： <br /><br /><br />
            <br />
          </td>
        </tr>
      </table>
    </div>
  </body>
</html>
