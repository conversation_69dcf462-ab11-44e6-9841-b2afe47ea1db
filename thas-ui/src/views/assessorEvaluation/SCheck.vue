<!--  -->
<template>
  <div class="audit-evaluation" ref="auditEvaluation" v-loading="loading">
    <div v-if="distributed">
      <!-- 医院名称 -->
      <el-card class="box-card" shadow="always" style="margin-bottom: 10px; margin-left: -5px">
        <h1 style="margin: 0">{{ detail.hospitalName }}</h1>
      </el-card>

      <!-- 总结详情 -->
      <aut-sa-aud-sa-report v-if="showSummary" :title='`评价结果表`' style="margin-top:15px;" :key="headerSlotKeys"
        :report="detail.autSaAudReport"></aut-sa-aud-sa-report>

      <el-card v-if="showSummary" class="box-card" shadow="never">
        <div slot="header" class="clearfix">
          <h4>评价总结</h4>
        </div>
        <div>
          <el-form ref="summaryForm" :model="summaryForm" label-width="120px" :rules="rules">
            <el-form-item label="评价结果：">
              {{ ['', '通过认证', '有条件通过认证', '不通过认证'][summaryForm.autResult] }}
            </el-form-item>
            <el-form-item label="亮点：" prop="autAdvantage">
              <el-input type="textarea" v-model="summaryForm.autAdvantage" :maxlength="10000" :rows="6"
               ></el-input>
            </el-form-item>
            <el-form-item label="不足：" prop="autEvaluate">
              <el-input type="textarea" v-model="summaryForm.autEvaluate" :maxlength="10000" :rows="6"
               ></el-input>
            </el-form-item>
            <el-form-item label="整改建议：" prop="autProposal">
              <el-input type="textarea" v-model="summaryForm.autProposal" :maxlength="10000" :rows="6"
               ></el-input>
            </el-form-item>
            <el-form-item label="改进机会：" prop="autImprove">
              <el-input type="textarea" v-model="summaryForm.autImprove" :maxlength="10000" :rows="6"
               ></el-input>
            </el-form-item>

            <el-form-item label=" " v-if="autSaAudStatus == AutSaAudCurrentStatusEnum.WAIT_AUD_SECOND_TRIAL_SUM">
              <el-button type="primary" @click="summarySubmit"
                :disabled="summaryLoading">提交评价总结</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>

      <!-- 评价  -->
      <el-card class="box-card autScrollCard" shadow="never"
        v-if="autSaAudStatus != AutSaAudCurrentStatusEnum.WAIT_AUD_SECOND_TRIAL_SUM" :style="{ height: autCardHeight }">
        <div slot="header" class="clearfix">
          <h4>评价审核</h4>
          <div class="header-right" v-if="standardLoadedKey" :key="headerSlotKeys">

            <span v-if="showAutCheckFilter.length" :class="{ lightHigh: lightHigh == 10 }"
              @click="handleClauseFilter(10, showAutCheckFilter)">
              已审核 {{ showAutCheckFilter.length }} 款
            </span>
            <!-- 驳回 -->
            <span v-if="showAutRjFilter.length" :class="{ lightHigh: lightHigh == 30 }"
              @click="handleClauseFilter(30, showAutRjFilter)">
              已驳回 {{ showAutRjFilter.length }} 款
            </span>

            <span v-if="showAutCategoryFilter.length">
              <span v-for="(data, index) in showAutCategoryFilter" :key="index" class="aut-category-filter"
                :class="{ lightHigh: lightHigh == (index + 1) }" @click="handleClauseFilter(index + 1, data.ids)">
                {{ data.dictLabel }} {{ data.ids.length }} 款
              </span>
            </span>

            <span v-if="hasDomainGroups && showStBtGroup" @click="visible = true">
              {{ showStBtGroupText }}
            </span>

            <el-button type="primary" size="mini" v-if="showConvenientBtn" @click="submitAllAudS">评价审核完成</el-button>

          </div>
        </div>
        <!-- 款项选择器 -->
        <standard-card ref="standardCard" :key="`base${loadingKeys}`" :clauseIds="clauseIds" :fitClauseIds="allFitClauseIds" :versionId="relation.autCsId"
          @selectChange="selectChange" @loaded="standardLoaded">
        </standard-card>
        <!-- 条小结 -->

        <!-- 款项 -->
        <div v-show="clauseList.length" class="evaluation-content" :key="`ach${selectedKeys}`"
          :style="{ height: clauseItemHeight }">
          <el-form :key="loadingKeys" label-width="100px">
            <clause-item v-for="(clause, index) in clauseList" v-show="isThisPage(index + 1)" :clause="clause"
              :key="clause.clauseId">
              <evaluation-self :readonly="true" :type="AutSaAudCurrentStatusEnum.SA_CLAUSE" :clause="clause"
                :aut="saMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-self>
              <evaluation-review isLeader :readonly="!!srvMap[clause.clauseId]"
                :type="AutSaAudCurrentStatusEnum.SR_CLAUSE" :clause="clause" :aut="srMap[clause.clauseId]"
                :status="autSaAudStatus">
              </evaluation-review>
              <evaluation-slot v-if="srMap[clause.clauseId] && srMap[clause.clauseId].shareDescList && srMap[clause.clauseId].shareDescList.length">
                <div class="share-item" v-for="item in srMap[clause.clauseId].shareDescList" :key="item.userId">
                  <el-form-item label="修改意见：">
                    <span class="break">{{ item.desc || '-' }}</span>
                  </el-form-item>
                  <el-form-item label="提出人：">
                    <span class="break">{{ item.userName || '-' }}</span>
                  </el-form-item>
                </div>
              </evaluation-slot>
              <evaluation-check v-if="showReviewCheck" :readonly="checkReadonly(srvMap[clause.clauseId])"
                :type="AutSaAudCurrentStatusEnum.SR_V_CLAUSE" :clause="clause" :aut="srvMap[clause.clauseId]"
                :status="autSaAudStatus"></evaluation-check>
            </clause-item>
            <pagination v-show="total > limit" :total="total" :page.sync="pageNum" :limit.sync="limit"
              :pageSizes="[limit]" />
          </el-form>
        </div>
      </el-card>
      <!-- 总结提交 -->

    </div>

    <el-dialog title="小结(非必填)" :visible.sync="visible" width="90%" :close-on-click-modal="false" destroy-on-close
      custom-class="elDialog">
      <srt-summary ref="srtSummary" :srtMap='srtMap' :domainGroups="detail.domainGroups" :flags="stBtGroup"
        @srtSubmit="srtSubmit"></srt-summary>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="visible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import evaluationMixin from "@/mixins/evaluation.vue";
import AutSaAudSaReport from '@/views/hospitalSelfEvaluation/evaluationResult/autSaAudSaReport.vue'
import SrtSummary from './srtSummary.vue'
import { deepClone } from "@/utils/index";

export default {
  name: "SAssessorIndex",
  mixins: [evaluationMixin],
  components: {
    AutSaAudSaReport,
    SrtSummary
  },
  props: {},
  watch: {
    detail: {
      deep: true,
      immediate: true,
      handler() {
        if (this.detail && this.detail.autSaAudStatus && this.detail.autSaAudStatus == this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_REJECT) {
          this.handleError()
        }
      },
    },
  },
  data() {
    return {
      show: false,
      loading: true,
      relation: {},
      detail: {},
      clauseIds: [],
      summaryLoading: false,
      summaryForm: {
        autResult: 0,
        autDesc: '',
        autAdvantage: '',
        autEvaluate: '',
        autProposal: '',
        autImprove: '',
      },
      visible: false,
      rules: {
        autAdvantage: [{
          required: true,
          validator: (rule, value, callback) => {
            if (this.isAutEmpty('autAdvantage')) callback('请填写亮点')
            callback()
          },
          trigger: ['blur'],
        }],
        autEvaluate: [{
          required: true,
          validator: (rule, value, callback) => {
            if (this.isAutEmpty('autEvaluate')) callback('请填写不足')
            callback()
          },
          trigger: ['blur'],
        }],
        autProposal: [{
          required: true,
          validator: (rule, value, callback) => {
            if (this.isAutEmpty('autProposal')) callback('请填写整改建议')
            callback()
          },
          trigger: ['blur'],
        }],
        autImprove: [{
          required: true,
          validator: (rule, value, callback) => {
            if (this.isAutEmpty('autImprove')) callback('请填写改进机会')
            callback()
          },
          trigger: ['blur'],
        }],
      },
      reviewResultOptions: [],
      categoryKeys: 0,
      specialInit: true
    };
  },

  computed: {
    checkReadonly() {
      return aut => {
        return aut && !aut.rejected && !!aut
      }
    },
    showExamine() {
      return true;
    },
    showReviewCheck() {
      // 待审核  审核中  审核驳回
      let status = [
        this.AutSaAudCurrentStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_REJECT,
      ];
      return status.includes(this.autSaAudStatus) || true;
    },

    showSummary() {
      let statuses = [
        this.AutSaAudCurrentStatusEnum.WAIT_AUD_SECOND_TRIAL_SUM,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_SUM_REJECT,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_SUM_CONDITIONAL_PASS,
        this.AutSaAudCurrentStatusEnum.AUD_PASS
      ]
      return statuses.includes(this.autSaAudStatus);
    },

    // 分类
    showAutCategoryFilter() {
      let saList = Object.values(this.srMap);
      let list = this.reviewResultOptions.map(({ dictValue, dictLabel }) => {
        let ids = saList.filter(({ autResult }) => autResult == dictValue).map(({ clauseId }) => clauseId);
        return {
          dictValue,
          dictLabel,
          ids
        }
      }) || []
      return list.filter(({ ids }) => ids.length);
    },

    showAutFilter() {
      if (this.wholeClauseList.length === 0) return [];
      const list = Object.values(this.srvMap);
      let wholeClauseIds = this.wholeClauseIds;
      let frClauseIds = list.map(({ clauseId }) => Number(clauseId));
      let list2 = wholeClauseIds.reduce((list, next) => {
        if (!frClauseIds.includes(Number(next))) list.push(Number(next));
        return list;
      }, [])
      return list2;
    },

    // 被驳回
    showAutRjFilter() {
      const list = Object.values(this.srvMap);
      const rjList = list.filter(({ rejected }) => rejected).map(({ clauseId }) => clauseId);
      return rjList;
    },

    showAutCheckFilter() {
      const list = Object.values(this.srvMap);
      const rjList = list.filter(({ rejected }) => !rejected).map(({ clauseId }) => clauseId);
      return rjList;
    },

    hasDomainGroups() {
      return this.detail && this.detail.domainGroups
    },
    stBtGroup() {
      let list = this.domainClauseIds;
      let clauseIds = Object.values(this.srMap).map(obj => Number(obj.clauseId));
      return list.map(li => li.reduce((flag, id) => {
        return flag && clauseIds.includes(id);
      }, true))
    },

    showStBtGroup() {
      let status = [
        this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_SUM_PASS,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_INQUIRE_PROC,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_REJECT,
        this.AutSaAudCurrentStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC
      ];
      return this.stBtGroup.includes(true) && status.includes(this.autSaAudStatus);
    },

    showStBtGroupText() {
      let status = [
        this.AutSaAudCurrentStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC
      ];
      return status.includes(this.autSaAudStatus) ? '小结' : '待小结'
    },

  },

  mounted() {
    this.init2(true, this.$route.query.autCode, this.$route.query.type);

    this.getDicts('review_result').then(res => {
      this.reviewResultOptions = res.data;
      this.categoryKeys++;
    })
  },

  methods: {
    isAutEmpty(field) {
      let initStr = ''
      this.detail.domainGroupNodes.forEach(item => initStr += `${item.groupDetail}:\n`)
      let cloneAutStr = initStr.replace(/[\n\s]+/g, '');
      let autStr = this.summaryForm[field].replace(/[\n\s]+/g, '');
      return !autStr || autStr === cloneAutStr
    },
    initFinalBack() {
      // {
      //   this.$set(this, 'clauseIds', [])
      //   this.$set(this, 'distributeClauseIds', [])
      //   this.loadingKeys++;
      // }

      let summary = this.detail.audSecondTrialSum;
      if (summary) {
        this.summaryForm.autResult = summary.autResult
        this.summaryForm.autDesc = summary.autDesc
      } else {
        this.summaryForm.autResult = this.detail.autSaAudReport ? this.detail.autSaAudReport.autSaAudResult : 0
      }

      if (this.autSaAudStatus == this.AutSaAudCurrentStatusEnum.WAIT_AUD_SECOND_TRIAL_SUM) {
        this.summaryForm.autAdvantage = this.detail.autAdvantage || ''
        this.summaryForm.autEvaluate = this.detail.autEvaluate || ''
        this.summaryForm.autProposal = this.detail.autProposal || ''
        this.summaryForm.autImprove = this.detail.autImprove || ''
      }

      if (this.showAutRjFilter.length) {
        this.$set(this, 'clauseIds', this.showAutRjFilter)
        this.$set(this, 'distributeClauseIds', this.showAutRjFilter)
        this.loadingKeys++;
      }


      let { domainGroups } = this.detail;
      if (!domainGroups || !domainGroups.length) return;
      let domainClauseIds = []
      domainGroups.forEach(one => {
        let list = []
        let { children } = one;
        children.forEach(two => {
          let { children } = two;
          children.forEach(three => {
            let { children } = three;
            list.push(...children.map(child => Number(child.groupDetail)))
          })
        })
        domainClauseIds.push(list)
      })
      this.domainClauseIds = domainClauseIds;
    },

    handleError() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push({ path: "/fAssessor/s/assessorEvaluationList?preliminary=0" });
    },
    // 评审复查
    submitAllAudS() {
      let ids = Object.values(this.srvMap).filter(({ rejected }) => !rejected).map(({ clauseId }) => clauseId)
      this.makeData(this.AutSaAudCurrentStatusEnum.SR_V_CLAUSE, undefined, undefined, ids, false,'submitAllAudS')
    },

    async summarySubmit() {
      if (this.summaryLoading) return;
      this.summaryLoading = true;
      this.$refs.summaryForm.validate(async valid => {
        if (!valid) {
          this.summaryLoading = false;
          return;
        };
        let { autAdvantage,
          autEvaluate,
          autProposal,
          autImprove, ...form } = this.summaryForm;
        form.autDesc = JSON.stringify({
          autAdvantage,
          autEvaluate,
          autProposal,
          autImprove
        })
        let data = {
          autCode: this.relation.autCode,
          accountId: this.$store.getters.userId + "",
          submitType: this.AutSaAudCurrentStatusEnum.SR_SUMMARY,
          autSaAudLists: [{
            ...form,
            clauseId: '1'
          }]
        };
        try {
          let res = await this.submit(data);
          // 报错就不提示
          if (!res) return;

          let reportData = {
            submitType: this.AutSaAudCurrentStatusEnum.SR_REPORT_SUBMIT,
            autCode: this.relation.autCode,
            accountId: this.$store.getters.userId + "",
            autSaAudLists: [
              {
                autResult: "0",
                autDesc: "--",
              },
            ],
          }
          let reportRes = await this.submit(reportData);
          // 报错就不提示
          if (!reportRes) return;
          this.$message({
            type: "success",
            message: "报告已生成",
          });

          this.init2(true, this.$route.query.autCode, this.$route.query.type);
          this.handleError();
        } catch (error) {

        } finally {
          this.summaryLoading = false;
        }
      })
    },

    async srtSubmit(srtObject, submitType) {
      if (Array.isArray(srtObject) && srtObject.length) {
        try {
          for (let [index, item] of srtObject.entries()) {
            await this.submit({
              autCode: this.relation.autCode,
              accountId: this.$store.getters.userId + "",
              submitType: item[index].submitType,
              autSaAudLists: item
            });
          }

          if (this.$refs.srtSummary) {
            this.$refs.srtSummary.readonly(true)
            this.visible = false;
            this.init2(false, this.$route.query.autCode, this.$route.query.type);
          }
        } catch (error) {
          if (this.$refs.srtSummary) {
            this.$refs.srtSummary.readonly(false)
          }
        }
      } else {
        const data = {
          autCode: this.relation.autCode,
          accountId: this.$store.getters.userId + "",
          submitType,
          autSaAudLists: [srtObject]
        }

        try {
          let res = await this.submit(data);
          // 报错就不提示
          if (!res) return;
          if (this.$refs.srtSummary) {
            this.$refs.srtSummary.readonly(true)
            this.init2(false, this.$route.query.autCode, this.$route.query.type);
          }
        } catch (error) {
          if (this.$refs.srtSummary) {
            this.$refs.srtSummary.readonly(false)
          }
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.box-card+.box-card {
  margin-top: 15px;
}

h4 {
  margin: 10px 0;
}

::v-deep .elDialog {
  height: 80%;

  .el-dialog__body {
    margin-top: 0;
    overflow: hidden;
    height: 80%;
    padding-top: 0;
  }
}

span.aut-category-filter::after {
  content: "|";
}

span.aut-category-filter:last-child::after {
  content: "";
}
</style>
