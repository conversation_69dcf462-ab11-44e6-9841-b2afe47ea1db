<template>
  <el-collapse
    class="approvalStandar-tree-article article"
    @change="handleClauseChange"
    v-model.trim="collapse"
    accordion
  >
    <el-collapse-item
      v-for="(article, index) in articleVoList"
      :key="index"
      :name="index"
    >
      <template slot="title">
        <i
          class="arrow"
          :class="[
            arrow(index) ? 'el-icon-caret-bottom' : 'el-icon-caret-right',
          ]"
        ></i>
        <label style="font-weight: lighter">条：</label>
        <span class="clause-node-title" style="font-weight: lighter">{{
          article.articleNo
        }}</span>
        <span class="clause-content-title">{{ article.article }}</span>
      </template>
      <clause-detail
        class="clause-content"
        :clauseVoList="article.clauseVoList"
        :render='arrow(index)'
      ></clause-detail>
    </el-collapse-item>
  </el-collapse>
</template>

<script>
import ClauseDetail from "./clauseDetail.vue";
export default {
  name: "clauseCollapse",
  props: {
    articleVoList: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    ClauseDetail,
  },
  data() {
    return {
      collapse: -1,
      activeName: -1,
    };
  },
  computed: {
    arrow() {
      return (name) => {
        return  this.activeName == name
      }
    },
  },
  methods: {
    handleClauseChange(activeNames) {
      this.activeName = activeNames || 0;
    },
  },
};
</script>

<style lang="scss" scoped>
.section .el-collapse.approvalStandar-tree-article.article {
  ::v-deep .el-collapse-item__header {
    color: #2c1f1f;
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    min-height: 58px;
    line-height: 24px;
    span {
      color: black;
      display: inline-block;
    }
    label {
      color: #888888;
      width: 70px;
    }
    .clause-node-title {
      width: 120px;
    }
    .clause-content-title {
      font-weight: lighter;
      flex: 1;
    }
    .arrow {
      margin-left: 16px;
      margin-right: 16px;
      color: #888888;
    }
  }
  ::v-deep .el-collapse-item__wrap {
    border-left: 1px solid #e0e0e0;
    border-right: 1px solid #e0e0e0;
    border-bottom: 1px solid #e0e0e0;
    .el-collapse-item__content {
      padding-bottom: 0;
    }
  }
}
</style>