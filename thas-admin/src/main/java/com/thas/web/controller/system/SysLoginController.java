package com.thas.web.controller.system;

import java.util.List;
import java.util.Set;

import com.thas.common.exception.LoginException;
import com.thas.system.domain.SysUserOnline;
import com.thas.system.service.ISysPasswordManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.thas.common.constant.Constants;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.domain.entity.SysMenu;
import com.thas.common.core.domain.entity.SysUser;
import com.thas.common.core.domain.model.LoginBody;
import com.thas.common.utils.SecurityUtils;
import com.thas.framework.web.service.SysLoginService;
import com.thas.framework.web.service.SysPermissionService;
import com.thas.system.service.ISysMenuService;

import javax.servlet.http.HttpServletRequest;

/**
 * 登录验证
 * 
 * <AUTHOR>
 */
@RestController
public class SysLoginController
{
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private ISysPasswordManagementService sysPasswordManagementService;

    /**是否允许账户多终端同时登录（true允许 false不允许）*/
    @Value("${token.soloLogin}")
    private Boolean soloLogin;

    /**
     * 登录方法
     * 
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody, HttpServletRequest request)
    {
        AjaxResult ajax = AjaxResult.success();

        // 判断当前user_name是否为锁定时间
        sysPasswordManagementService.checkSysUserIsLock(loginBody.getUsername());

        // 判断当前登录用户是否需要检验ip
        loginService.adminCheckIp(loginBody.getUsername(), request);

        //登录前，查询当前用户是否登录，获取TOKEN
        List<SysUserOnline> sysUserOnlines = loginService.sysUserOnlineList(loginBody.getUsername());

        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());

        // 如果登录成功删除对应的错误次数
        sysPasswordManagementService.delPasswordErrorNumCache(loginBody.getUsername());

        // 校验是否需要改密码
        ajax.put(Constants.TOKEN, token);
        try {
            sysPasswordManagementService.checkSysUserPassword(loginBody.getUsername());
        } catch (LoginException e) {
            e.printStackTrace();
            ajax.put("code", e.getCode());
            ajax.put("message", e.getMessage());
        }

        //登录成功，强制退出之前登录的用户
        if(!soloLogin){
            loginService.forceLogout(sysUserOnlines);
        }
        return ajax;
    }

    /**
     * 获取用户信息
     * 
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo()
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     * 
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters()
    {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }
}
