<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.ReviewTraPracticeMapper">

    <resultMap type="ReviewTraPractice" id="ReviewTraPracticeResult">
        <result property="id"    column="id"    />
        <result property="subjectCode"    column="subject_code"    />
        <result property="subjectName"    column="subject_name"    />
        <result property="subjectMode"    column="subject_mode"    />
        <result property="subjectTime"    column="subject_time"    />
        <result property="sceneReviewTime"    column="scene_review_time"    />
        <result property="teaReviewerId"    column="tea_reviewer_id"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectReviewTraPracticeVo">
        select id, subject_code, subject_name, subject_mode, subject_time, scene_review_time, tea_reviewer_id, status, create_time, update_time from review_tra_practice
    </sql>

    <select id="selectReviewTraPracticeList" parameterType="ReviewTraPractice" resultMap="ReviewTraPracticeResult">
        <include refid="selectReviewTraPracticeVo"/>
        <where>
            <if test="subjectCode != null  and subjectCode != ''"> and subject_code = #{subjectCode}</if>
            <if test="subjectName != null  and subjectName != ''"> and subject_name like concat('%', #{subjectName}, '%')</if>
            <if test="subjectMode != null  and subjectMode != ''"> and subject_mode = #{subjectMode}</if>
            <if test="subjectTime != null  and subjectTime != ''"> and subject_time = #{subjectTime}</if>
            <if test="sceneReviewTime != null  and sceneReviewTime != ''"> and scene_review_time = #{sceneReviewTime}</if>
            <if test="teaReviewerId != null  and teaReviewerId != ''"> and tea_reviewer_id = #{teaReviewerId}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectReviewTraPracticeById" parameterType="Long" resultMap="ReviewTraPracticeResult">
        <include refid="selectReviewTraPracticeVo"/>
        where id = #{id}
    </select>

    <insert id="insertReviewTraPractice" parameterType="ReviewTraPractice" useGeneratedKeys="true" keyProperty="id">
        insert into review_tra_practice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="subjectCode != null and subjectCode != ''">subject_code,</if>
            <if test="subjectName != null and subjectName != ''">subject_name,</if>
            <if test="subjectMode != null and subjectMode != ''">subject_mode,</if>
            <if test="subjectTime != null and subjectTime != ''">subject_time,</if>
            <if test="sceneReviewTime != null and sceneReviewTime != ''">scene_review_time,</if>
            <if test="teaReviewerId != null and teaReviewerId != ''">tea_reviewer_id,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="subjectCode != null and subjectCode != ''">#{subjectCode},</if>
            <if test="subjectName != null and subjectName != ''">#{subjectName},</if>
            <if test="subjectMode != null and subjectMode != ''">#{subjectMode},</if>
            <if test="subjectTime != null and subjectTime != ''">#{subjectTime},</if>
            <if test="sceneReviewTime != null and sceneReviewTime != ''">#{sceneReviewTime},</if>
            <if test="teaReviewerId != null and teaReviewerId != ''">#{teaReviewerId},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateReviewTraPractice" parameterType="ReviewTraPractice">
        update review_tra_practice
        <trim prefix="SET" suffixOverrides=",">
            <if test="subjectCode != null and subjectCode != ''">subject_code = #{subjectCode},</if>
            <if test="subjectName != null and subjectName != ''">subject_name = #{subjectName},</if>
            <if test="subjectMode != null and subjectMode != ''">subject_mode = #{subjectMode},</if>
            <if test="subjectTime != null and subjectTime != ''">subject_time = #{subjectTime},</if>
            <if test="sceneReviewTime != null and sceneReviewTime != ''">scene_review_time = #{sceneReviewTime},</if>
            <if test="teaReviewerId != null and teaReviewerId != ''">tea_reviewer_id = #{teaReviewerId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteReviewTraPracticeById" parameterType="Long">
        delete from review_tra_practice where id = #{id}
    </delete>

    <delete id="deleteReviewTraPracticeByIds" parameterType="String">
        delete from review_tra_practice where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>