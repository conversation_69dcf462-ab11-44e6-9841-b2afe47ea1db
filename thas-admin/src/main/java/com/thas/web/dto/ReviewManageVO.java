package com.thas.web.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 医疗结构初审员/评审员管理列表入出参
 */
@Data
@ApiModel(value = "ReviewManageDTO")
public class ReviewManageVO {

    @ApiModelProperty("医疗机构认证编码")
    private String applyNo;

    @ApiModelProperty("医疗机构名称")
    private String hospitalName;

    @ApiModelProperty("初审员/评审员id")
    private String accountId;

    @ApiModelProperty("初审员/评审员姓名")
    private String reviewerName;

    @ApiModelProperty("手机号码")
    private String mobile;

    @ApiModelProperty("是否是组长")
    private Integer leaderIs;

    @ApiModelProperty("待评审项")
    private Integer notReviewNum;

    @ApiModelProperty("已评审项")
    private Integer reviewNum;

    @ApiModelProperty("分配人")
    private String disPersonName;

    @ApiModelProperty(value = "分配时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String disTime;

    @ApiModelProperty("是否请假(0正常，1请假)")
    private String leaveIs;
}
