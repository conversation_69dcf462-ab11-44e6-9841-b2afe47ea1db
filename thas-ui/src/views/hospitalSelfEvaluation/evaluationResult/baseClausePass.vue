<!--  -->
<template>
  <div class="base-clause" v-if="autSaAudReport">
    <el-card class="box-card" shadow="never">
      <span style="color:red;">通过认证</span>
    </el-card>
    <el-card class="box-card" shadow="never">
      <el-table :data="dataSource" border stripe :header-cell-style="headerMethod2" :span-method="arraySpanMethod2">
        <el-table-column prop="title"></el-table-column>
        <el-table-column prop="chapter"></el-table-column>
        <el-table-column label="评价结果" prop="result">
          <template slot-scope="{row}">
            <span :class="[row.class]">{{ row.result }}</span>
          </template>
        </el-table-column>
        <el-table-column label="款数" prop="truthNumber"></el-table-column>
        <el-table-column label="达标占比" prop="truthRange"></el-table-column>
        <el-table-column v-if="allFitClauseIds.length != 0" label="备注" prop="desc"></el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import AutSaAudSaReport from './autSaAudSaReport.vue'
import request from "@/utils/request";

export default {
  name: "BaseClausePass",
  components: {
    AutSaAudSaReport
  },
  props: {},
  watch: {},
  data() {
    return {
      dataSource: [],
      allFitClauseIds: [],
      autSaAudReport: {},
      clauseList: [],
      clauseMap: {},
    };
  },

  computed: {
    desc() {
      if (this.allFitClauseIds.length == 0) return {}
      let basic = 0, minor = 0;
      this.allFitClauseIds.forEach(id => {
        let clause = this.clauseMap[id];
        if (!clause) return;
        if (clause.isStar == 1) {
          basic++
        } else {
          minor++
        }
      })
      console.log(basic, minor);
      return {
        basic,
        minor
      }
    },
    isHospital() {
      // 医疗机构
      return this.$store.getters.roles.includes("hospital");
    },
  },

  async created() { },

  mounted() {
    this.query()
  },

  methods: {

    query() {
      let loading = this.$loading();
      if (this.isHospital) {
        request({
          url: "/aut/sa/aud/selectAutSaAudDetailList",
          method: "get",
          params: {
            autCode: this.$route.query.autCode
          },
        }).then((res) => {
          this.dealWith(res)
        }).finally(() => {
          // this.loading = false;
          loading.close()
        });
      } else {
        request({
          url: '/aut/sa/aud/queryList',
          method: "post",
          data: this.$route.query,
        }).then((res) => {
          this.dealWith(res)
        }).finally(() => {
          // this.loading = false;
          loading.close()
        });
      }
    },

    async dealWith(res) {
      let data = (res.rows || res.data)[0];
      if (data && data.autSaAudReport) {
        this.autSaAudReport = data.autSaAudReport

        if (data.allFitClauseIds && data.autCsId) {
          this.allFitClauseIds = data.allFitClauseIds || []
          this.clauseMap = this.$store.getters.standardTypeItemByVersionId(data.autCsId, 'clause', 'map')
          if (Object.values(this.clauseMap).length == 0) {
            await this.getClauseByIds(data.autCsId, this.allFitClauseIds)
          }
        }

        this.$nextTick(() => {
          this.table();
          // let pass = (this.autSaAudReport.autSaAudReportListVos.find(({ isBasic }) => isBasic == 0).reportList.find(({ autSaAudResult }) => autSaAudResult == 7) || {}).count;
          // let total = this.autSaAudReport.autSaAudReportListVos.find(({ isBasic }) => isBasic == 0).sumCount
          // if (pass == total) {
          //   // this.table5();
          //   this.table5Plus();
          //   this.showWho = true;
          // } else {
          //   this.showWho = false;
          //   // this.table4();
          //   this.table4Plus()
          // }
        });
      }
    },

    getClauseByIds(versionId, clauseIds) {
      return new Promise(resolve => {
        request({
          url: '/system/standards/selectByClauseIdsAndVersionId',
          method: "post",
          data: {
            versionId,
            clauseIds
          },
        }).then(res => {
          let list = res.data
          this.clauseList = list
          list.forEach(clause => {
            this.$set(this.clauseMap, clause.clauseId, clause)
          })
          resolve();
        })
      })
    },

    table() {
      const { autSaAudReportListVos } = this.autSaAudReport;
      let basic = autSaAudReportListVos.find(({ isBasic }) => isBasic == 0);
      let minor = autSaAudReportListVos.find(({ isBasic }) => isBasic == 1);
      let col_data = {
        title: '-',
        result: '-',
        chapter: "-",
        number: '-',
        range: '-',
        truthNumber: '-',
        truthRange: '-',
        risk: '-',
        truthRisk: '-'
      }
      let dataSource = []
      // * 审核结果: 1: 优秀 2: 良好 3: 达标 4: 部分达标  5: 不达标  6: 不适用   7: 达标之上（1,2,3） 8：部分达标以上（1,2,3,4） 9:不适用（管理员操作的不适用）

      {
        let sumCount = basic.sumCount
        let reportList = basic.reportList
        let extracts = [
          {
            result: '优秀',
            value: 1,
          },
          {
            result: '良好',
            value: 2,
          },
          {
            result: '达标',
            value: 3,
          },
          {
            result: '部分达标',
            value: 4,
          },
          {
            result: '不达标',
            value: 5,
          },
          {
            result: '不适用',
            value: 6,
            class: 'light',
          },
          {
            result: '达标及以上',
            value: 7,
            class: 'weight',
          },
        ]

        let list = this.assemble({
          topic: '基本款',
          sumCount,
          reportList,
          col_data,
          extracts,
          isStar: 1
        })
        dataSource.push(...list)
      }

      {
        let sumCount = minor.sumCount
        let reportList = minor.reportList
        let extracts = [
          {
            result: '优秀',
            value: 1,
          },
          {
            result: '良好',
            value: 2,
          },
          {
            result: '达标',
            value: 3,
          },
          {
            result: '部分达标',
            value: 4,
          },
          {
            result: '不达标',
            value: 5,
          },
          {
            result: '不适用',
            value: 6,
            class: 'light',
          },
          {
            result: '达标及以上',
            value: 7,
            class: 'weight',
          },
          {
            result: '部分达标及以上',
            value: 8,
            class: 'weight',
          },
        ]

        let list = this.assemble({
          topic: '非基本款',
          sumCount,
          reportList,
          col_data,
          extracts,
          isStar: 0
        })
        dataSource.push(...list)
      }

      {
        let topicSumCount = minor.sumCount
        let names = ['', '第一章', '第二章', '第三章']
        let chapterReportList = minor.chapterReportList
        chapterReportList.map(chapterData => {
          let { chapterId, sumCount, reportList } = chapterData

          let extracts = [
            {
              result: '优秀',
              value: 1,
            },
            {
              result: '良好',
              value: 2,
            },
            {
              result: '达标',
              value: 3,
            },
            {
              result: '部分达标',
              value: 4,
            },
            {
              result: '不达标',
              value: 5,
            },
            {
              result: '不适用',
              value: 6,
              class: 'light',
            },
            {
              result: '达标及以上',
              value: 7,
              class: 'weight',
            },
            {
              result: '部分达标及以上',
              value: 8,
              class: 'weight',
            },
          ]

          let list = this.assemble({
            topic: '非基本款',
            topicSumCount,
            chapter: names[chapterId],
            sumCount,
            reportList,
            col_data,
            extracts,
            isStar: 0
          })
          dataSource.push(...list)
        })
      }


      this.dataSource = dataSource
    },

    assemble(option) {
      let {
        topic,
        topicSumCount,
        chapter,
        sumCount,
        reportList,
        col_data,
        extracts,
        levelMap,
        isStar
      } = option;

      let list = extracts.map(extract => {
        let { result, value, compare, _range, range, risk, ...other } = extract
        let truthNumber, truthRange, truthRisk = '-';
        if (value instanceof Array) {
          let reports = value.map(key => {
            return reportList.find(({ autSaAudResult }) => autSaAudResult == key)
          })
          let { count, rate } = reports.reduce((total, data) => {
            return {
              count: total.count + data.count,
              rate: total.rate + data.rate,
            }
          }, {
            count: 0,
            rate: 0
          })
          truthNumber = count + '款'
          truthRange = rate + '%'
        } else {
          let { count, rate } = reportList.find(({ autSaAudResult }) => autSaAudResult == value);
          truthNumber = count + '款'
          // truthRange = Math.ceil(rate) + '%'
          truthRange = rate + '%'
        }

        if (risk && levelMap) {
          truthRisk = ''
          let risks = ['', '低风险', '中风险', '高风险', '严重风险'];
          for (const key in levelMap) {
            if (Object.hasOwnProperty.call(levelMap, key)) {
              const element = levelMap[key];
              if (element) {
                truthRisk += element + '款' + risks[key] + ' '
              }
            }
          }
        }

        let col = Object.assign({
          ...col_data
        }, {
          title: `${topic}（共${topicSumCount || sumCount}款）`,
          result,
          number: `${compare}${Math.floor((_range || range) * sumCount / 100)}/${sumCount}款`,
          truthNumber,
          truthRisk,
          risk: risk || '-',
          truthRange: _range ? '-' : truthRange,
          range: range ? compare + range + '%' : '-',
          chapter: chapter ? `${chapter}（共${sumCount}款）` : '-',
          desc: `管理员将${isStar == 1 ? this.desc.basic : this.desc.minor}款置为不适用，评审员无需评审`,
          ...other,
        });
        return col
      })

      return list
    },


    headerMethod2({ row, columnIndex }) {
      row[1].colSpan = 0
      row[2].colSpan = 2
      if (columnIndex == 1) {
        return { display: 'none' }
      }
      return;
    },

    arraySpanMethod2({ rowIndex, columnIndex }) {
      if (columnIndex == 0) {
        if (rowIndex == 0) {
          return [7, 1];
        } else if (rowIndex == 7) {
          return [32, 1]
        } else {
          return [0, 1]
        }
      } else if (columnIndex == 1) {
        if (rowIndex < 15) {
          return [0, 0]
        } else {
          if (rowIndex == 15 || rowIndex == 23 || rowIndex == 31) {
            return [8, 1]
          } else {
            return [0, 1]
          }
        }
      } else if (columnIndex == 2) {
        if (rowIndex < 15) {
          return [1, 2]
        }
      } else if (columnIndex == 5) {
        if (rowIndex == 0) {
          return [7, 1];
        } else if (rowIndex == 7) {
          return [32, 1]
        } else {
          return [0, 1]
        }
      }
      return [1, 1]
    },
  },
};
</script>
<style lang='scss' scoped>
.base-clause {
  padding: 12px;
  min-height: inherit;
  background-color: white;

  ::v-deep .el-card {
    width: 90%;
    margin: 0 auto;
  }


  .light {
    color: #CC9900;
  }

  .weight {
    font-weight: 600;
  }
}
</style>