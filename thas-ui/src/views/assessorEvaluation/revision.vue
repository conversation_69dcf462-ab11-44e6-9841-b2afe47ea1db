<!--  -->
<template>
  <div v-loading="loading" class="revision">
    <el-table :data="tableData" border stripe>
      <el-table-column prop="memberName" label="组员信息">
        <template slot-scope="scope">
          <div>组员：{{ scope.row.memberName || "--" }}</div>
          <div>电话：{{ scope.row.memberPhone || "--" }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="rejectClauseNos" label="驳回款">
        <template slot-scope="scope">
          <div v-for="(item, index) in scope.row.rejectClauseNos" :key="index">
            {{ item.clauseNo || "--" }}
          </div>
          <span v-if="
            !scope.row.rejectClauseNos ||
            scope.row.rejectClauseNos.length == 0
          ">--</span>
        </template>
      </el-table-column>
      <el-table-column prop="rejectClauseNos" label="修订情况">
        <template slot-scope="scope">
          <div v-for="(item, index) in scope.row.rejectClauseNos" :key="index">
            <el-button v-if="item.isModify == 'Y'" type="text" @click="showModify(item)">{{ item.clauseNo + ":已修改"
            }}</el-button>
            <span v-else @click="showModify(item)">{{ item.clauseNo }}:未修改</span>
          </div>
          <span v-if="
            !scope.row.rejectClauseNos ||
            scope.row.rejectClauseNos.length == 0
          ">--</span>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注">
        <template slot-scope="scope">
          <el-input v-model="scope.row.remark" type="textarea" placeholder="" maxlength="200"
            @blur="editRemark(scope.row)"></el-input>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog title="修改情况" :visible.sync="dialogVisible">
      <el-form ref="evaluationForm" label-width="110px" label-position="left">
        <el-form-item :label="currentClause.clauseNo"></el-form-item>
        <el-form-item label="自评结果：">
          <dict-span :key="sa_clause_item.autResult" :value="sa_clause_item.autResult" :options="reviewResultOptions"
            type="default"></dict-span>
        </el-form-item>
        <el-form-item label="自评依据：">
          <span>{{ sa_clause_item.autDesc || "--" }}</span>
        </el-form-item>
        <el-form-item label="佐证材料：">
          <div v-for="(file, index) in files" :key="index" style="margin-right: 15px">
            <a class="file-readonly" :href="file.url" target="_blank">{{
              file.fileName || file.name
            }}</a>
          </div>
        </el-form-item>
        <el-form-item v-if="sr_clause_item" label="评价结果：">
          <dict-span :key="sr_clause_item.autResult" :value="sr_clause_item.autResult" :options="reviewResultOptions"
            type="default"></dict-span>
        </el-form-item>
        <el-form-item v-if="sr_clause_item" label="评价描述：">
          <span>{{ autDesc || "--" }}</span>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import request from "@/utils/request";
export default {
  name: "Revision",
  data() {
    return {
      loading: false,
      tableData: [],
      dialogVisible: false,
      reviewResultOptions: [],
      files: [],
      sa_clause: {},
      sa_clause_item: {},
      sr_clause: {},
      sr_clause_item: {},
      latestAutSaAudList: [],
      currentClause: {},
      fileDetailMap: [],
    };
  },
  created() {
    this.initDicts();
    this.query();
    this.queryDetail();
  },
  computed: {
    autDesc() {
      try {
        let autDesc = JSON.parse(this.sr_clause_item.autDesc);
        let str = [];
        let ti = "";
        for (const key in autDesc) {
          if (Object.hasOwnProperty.call(autDesc, key) && autDesc[key]) {
            if (key == "autAdvantage") {
              ti = "亮点：";
            } else if (key == "autEvaluate") {
              ti = "不足：";
            } else if (key == "autProposal") {
              ti = "整改建议：";
            } else if (key == "autImprove") {
              ti = "改进机会：";
            } else {
              ti = "描述：";
            }
            str.push(ti + autDesc[key]);
          }
        }
        return str.join("; ");
      } catch (error) {
        return this.sr_clause_item.autDesc;
      }
    },
  },
  methods: {
    initDicts() {
      this.getDicts("review_result").then((response) => {
        this.reviewResultOptions = response.data;
      });
    },
    query() {
      this.loading = true;
      if (!this.$route.query.autCode) return;
      request({
        url: "/aut/sa/aud/memberRevisionInfo/" + this.$route.query.autCode,
        method: "get",
      })
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data || [];
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    editRemark(data) {
      this.loading = true;
      data.remark = data.remark || "";
      request({
        url: "/aut/sa/aud/memberRevisionInfo",
        method: "post",
        data,
      })
        .then((res) => {
          if (res.code == 200) {
            this.query();
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    queryDetail() {
      this.loading = true;
      request({
        url: "/aut/sa/aud/queryDetail",
        method: "post",
        data: {
          autCode: this.$route.query.autCode,
          accountId: this.$store.getters.userId,
        },
      })
        .then((res) => {
          if (res && res.code == 200) {
            this.sa_clause = res.data.autSaAudListMap.sa_clause;
            this.sr_clause = res.data.autSaAudListMap.sr_clause;
            this.latestAutSaAudList = res.data.latestAutSaAudList;
            this.fileDetailMap = res.data.fileDetailMap;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    showModify(data) {
      this.currentClause = data;
      this.sa_clause_item = this.sa_clause.find((item) => {
        return item.clauseId == data.clauseId;
      });

      this.sr_clause_item = this.latestAutSaAudList.find((item) => {
        return item.clauseId == data.clauseId;
      });

      let arr = [];
      for (const key in this.fileDetailMap) {
        arr.push(key);
      }
      this.files = [];
      this.sa_clause_item.fileIds
        .split(",")
        .filter((item) => {
          return arr.includes(item);
        })
        .forEach((item) => {
          this.files.push(this.fileDetailMap[item][0]);
        });
      this.dialogVisible = true;
    },
  },
};
</script>
<style lang="scss" scoped>
.revision {
  height: 100%;
}
</style>
