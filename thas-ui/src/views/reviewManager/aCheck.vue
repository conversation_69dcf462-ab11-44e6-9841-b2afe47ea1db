<!--  -->
<template>
  <div class="audit-evaluation" v-loading="loading">
    <div v-if="distributed">
      <!-- 医院名称 -->
      <el-card class="box-card" shadow="always" style="margin-bottom: 10px; margin-left: -5px">
        <h1 style="margin: 0">{{ detail.hospitalName }}</h1>
      </el-card>

      <el-card v-if='clauseIds.length' class="box-card" shadow="never" style="margin-bottom: 10px;">
        <div slot="header" class="clearfix">
          <h4>{{ isConfirm || !isSeniorConfirm ? needModifyTitle : '需修改项' }}</h4>
        </div>
        <modify-table :versionId="detail.versionId" :key='clauseIds.length' :data="clauseIds" :showAll="false">
        </modify-table>
      </el-card>

      <!-- 评价  -->
      <el-card class="box-card" shadow="never" style="margin-bottom: 10px;">
        <div slot="header" class="clearfix">
          <h4>{{ isConfirm || !isSeniorConfirm ? showConfirmName : '评审报告验证结果' }}</h4>

          <div class="header-right" v-if="standardLoadedKey" :key="headerSlotKeys">
            <el-button v-if="isSrM" type="primary" size="mini" @click="checkLeaderModifyFinish"
              style="margin-right: 10px;">审查完成</el-button>

            <template v-if="isFarConfirm || isSeniorConfirm">
              <span v-if="showAllCheckIds.length" class="showAutNum" :class="{ lightHigh: lightHigh == 1 }"
                @click="handleClauseFilter(1, showAllCheckIds)">
                {{ rejectWho2 }}疑问 {{ showAllCheckIds.length }}款
              </span>

              <span v-if="showRejectIds.length" class="showAutNum" :class="{ lightHigh: lightHigh == 2 }"
                @click="handleClauseFilter(2, showRejectIds)">
                驳回 {{ relation.rejectIds.length }}款
              </span>
              <span class="showAutNum">
                共 {{ showRejectIds.length ? (showRejectIds.length + showAllCheckIds.length) : showAllCheckIds.length }}款
              </span>

            </template>
          </div>
        </div>
        <!-- 款项选择器 -->
        <standard-card ref="standardCard" :key="`base${loadingKeys}`" :clauseIds="clauseIds" :versionId="relation.autCsId"
          @selectChange="selectChange" @loaded="standardLoaded">
        </standard-card>

        <el-form v-if="isFinal" :key="loadingKeys" label-width="100px">
          <!-- <clause-item v-for="(clause, index) in clauseList" v-show="isThisPage(index + 1)" :clause="clause" :key="clause.clauseId"> -->
          <clause-item v-for="(clause) in clauseList" :clause="clause" :key="clause.clauseId">
            <evaluation-review :readonly='true' :type="AutSaAudCurrentStatusEnum.SR_CLAUSE" :clause="clause"
              :aut="lastMap[clause.clauseId]" :status="autSaAudStatus">
            </evaluation-review>
            <evaluation-verify :readonly='true' :type="AutSaAudCurrentStatusEnum.TR_CLAUSE" :clause="clause"
              :aut="trMap[clause.clauseId]" :status="autSaAudStatus">
            </evaluation-verify>
          </clause-item>
        </el-form>

        <!-- 款项  事实准确性-->
        <el-form v-else-if="(isConfirm || isFarM)" :key="loadingKeys" label-width="100px">
          <!-- <clause-item v-for="(clause, index) in clauseList" v-show="isThisPage(index + 1)" :clause="clause" :key="clause.clauseId"> -->
          <clause-item v-for="(clause) in clauseList" :clause="clause" :key="clause.clauseId">
            <evaluation-review :readonly='true' :type="AutSaAudCurrentStatusEnum.SR_CLAUSE" :clause="clause"
              :aut="lastMap[clause.clauseId]" :status="autSaAudStatus">
            </evaluation-review>
            <evaluation-confirm v-if="farMap[clause.clauseId]" :readonly='true'
              :type="AutSaAudCurrentStatusEnum.FAR_CLAUSE" :clause="clause" :aut="farMap[clause.clauseId]"
              :status="autSaAudStatus">
            </evaluation-confirm>
            <evaluation-query v-if="!isFarM && doesNotRj(clause.clauseId)" :type="AutSaAudCurrentStatusEnum.FAR_CLAUSE_RJ_E" :clause="clause"
              :aut="frjMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-query>
            <!-- <evaluation-verify v-if="isSeniorConfirm && trMap[clause.clauseId]" :readonly='true'
              :type="AutSaAudCurrentStatusEnum.TR_CLAUSE" :clause="clause" :aut="trMap[clause.clauseId]"
              :status="autSaAudStatus">
            </evaluation-verify> -->
          </clause-item>
        </el-form>

        <!-- 款项 验证评审 -->
        <el-form v-else-if="(isSeniorConfirm || isTrM)" :key="loadingKeys" label-width="100px">
          <!-- <clause-item v-for="(clause, index) in clauseList" v-show="isThisPage(index + 1)" :clause="clause" :key="clause.clauseId"> -->
          <clause-item v-for="(clause) in clauseList" :clause="clause" :key="clause.clauseId">
            <evaluation-review :readonly='true' :type="AutSaAudCurrentStatusEnum.SR_CLAUSE" :clause="clause"
              :aut="lastMap[clause.clauseId]" :status="autSaAudStatus">
            </evaluation-review>
            <evaluation-verify v-if="trMap[clause.clauseId]" :readonly='true' :type="AutSaAudCurrentStatusEnum.TR_CLAUSE"
              :clause="clause" :aut="trMap[clause.clauseId]" :status="autSaAudStatus">
            </evaluation-verify>
            <evaluation-query v-if="!isTrM && doesNotRj(clause.clauseId)" :type="AutSaAudCurrentStatusEnum.TR_CLAUSE_RJ_E" :clause="clause"
              :aut="trjMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-query>
          </clause-item>
        </el-form>

        <el-form v-else-if="isSrM" :key="loadingKeys" label-width="100px">
          <!-- <clause-item v-for="(clause, index) in clauseList" v-show="isThisPage(index + 1)" :clause="clause" :key="clause.clauseId"> -->
          <clause-item v-for="(clause) in clauseList" :clause="clause" :key="clause.clauseId">
            <evaluation-review :readonly='true' :type="AutSaAudCurrentStatusEnum.SR_CLAUSE" :clause="clause"
              :aut="autSr(clause.clauseId, AutSaAudCurrentStatusEnum.SR_CLAUSE)" :status="autSaAudStatus">
            </evaluation-review>
            <evaluation-confirm v-if="farMap[clause.clauseId]" :readonly='true'
              :type="AutSaAudCurrentStatusEnum.FAR_CLAUSE" :clause="clause" :aut="farMap[clause.clauseId]"
              :status="autSaAudStatus">
            </evaluation-confirm>

            <template v-if="farMap[clause.clauseId]">
              <evaluation-modify v-if="fwmMap[clause.clauseId]" isLeader :type="AutSaAudCurrentStatusEnum.FAR_CLAUSE_M"
                :rejectType="AutSaAudCurrentStatusEnum.FAR_CLAUSE_M_SKIP" :clause="clause" :aut="fwmMap[clause.clauseId]"
                :status="autSaAudStatus">
              </evaluation-modify>
              <evaluation-slot v-else>
                <el-form-item label="评审组长修改：">
                  <span class="break"> 评审组长拒绝修改</span>
                </el-form-item>
              </evaluation-slot>
            </template>
            <evaluation-query v-if="farMap[clause.clauseId]" :type="AutSaAudCurrentStatusEnum.FAR_CLAUSE_RJ_E_M" :clause="clause"
              :aut="fmRjMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-query>
          </clause-item>
        </el-form>
      </el-card>
      <!-- 总结提交 -->

      <el-card class="box-card" v-if="isConfirm || isSeniorConfirm">
        <el-button type="primary" v-if="noRarTrReject" @click="turnToWho" v-loading='turning'
          :disabled="turning">转给评审组长</el-button>
        <el-button type="danger" v-else @click="turnToWho" v-loading='turning' :disabled="turning">驳回{{ rejectWho
        }}</el-button>
      </el-card>

      <el-card class="box-card" v-if="showModify">
        <el-button v-if="clauseIds.length" type="danger" @click="modifyConfirm(1, 'reject')" v-loading='turning'
          :disabled="turning">拒绝所有修改</el-button>
        <el-button v-else type="primary" @click="modifyConfirm(1)" v-loading='turning' :disabled="turning">无需修改</el-button>
        <el-button v-if="clauseIds.length" type="primary" @click="modifyConfirm(2)" v-loading='turning'
          :disabled="turning">去修改</el-button>
      </el-card>

      <el-card class="box-card" v-if="isFinal">
        <el-button type="primary" v-loading='turning' :disabled="turning">评审报告完毕</el-button>
        <el-button type="primary" v-loading='turning' :disabled="turning">转给审查组长</el-button>
      </el-card>

    </div>
  </div>
</template>

<script>
import request from "@/utils/request";
import evaluationMixin from "@/mixins/evaluation.vue";


export default {
  name: "ModificationConfirm",
  mixins: [evaluationMixin],
  components: {
  },
  props: {},
  watch: {
    detail: {
      deep: true,
      immediate: true,
      handler() {
        if (this.detail && this.detail.autSaAudStatus && this.detail.autSaAudStatus == this.AutSaAudCurrentStatusEnum.WAIT_REPORT) {
          this.handleError();
        }
      },
    }
  },
  data() {
    return {
      relation: {},
      detail: {},
      clauseIds: [],
      specialInit: true,
      turning: false,
      rejected: false,

      allCheckIds: []
    };
  },

  computed: {
    isFinal() {
      return this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.AUD_REPORT_CONFIRM
    },

    isConfirm() {
      return this.isFarConfirm
    },

    isFarConfirm() {
      return this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.CONFIRM_REVIEW_FACTUAL
    },

    isSeniorConfirm() {
      return this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.CONFIRM_REVIEW_VERIFY
    },

    isFarM() {
      return this.autSaAudStatus == this.AutSaAudCurrentStatusEnum.CHECK_MODIFIED_TERMS_FIRST
    },

    isTrM() {
      return this.autSaAudStatus == this.AutSaAudCurrentStatusEnum.CHECK_MODIFIED_TERMS_SECOND
    },

    isSrM() {
      return this.autSaAudStatus == this.AutSaAudCurrentStatusEnum.CONFIRM_REVIEW_FACTUAL_MODIFY
    },

    showModifyConfirm() {
      return this.relation.isLeader == 1 && this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_REVIEW_CONFIRM
    },

    showModify() {
      let status = [
        this.AutSaAudCurrentStatusEnum.CHECK_MODIFIED_TERMS_FIRST,
        this.AutSaAudCurrentStatusEnum.CHECK_MODIFIED_TERMS_SECOND
      ]
      return status.includes(this.detail.autSaAudStatus)
    },

    // 评审员修改医院有意见的款项
    isSecond() {
      return this.detail.autSaAudStatus === this.AutSaAudCurrentStatusEnum.CHECK_MODIFIED_TERMS_SECOND
    },

    isCheck() {
      return this.detail.autSaAudStatus === this.AutSaAudCurrentStatusEnum.CONFIRM_REVIEW_VERIFY
    },

    showConfirmName() {
      if (this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.CONFIRM_REVIEW_FACTUAL) {
        return '事实准确性确认结果'
      } else if (this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.CONFIRM_REVIEW_VERIFY) {
        return '验证确认结果'
      } else if (this.isSrM) {
        return '评审结果修改审查'
      }
      return ''
    },

    needModifyTitle() {
      if (this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.CONFIRM_REVIEW_FACTUAL) {
        return '医院需修改项'
      } else if (this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.CONFIRM_REVIEW_VERIFY) {
        return '验证评审员需修改项'
      } else if (this.isSrM) {
        return '需审查项'
      }
      return '需修改项'
    },

    rejectWho() {
      if (this.isConfirm) return '医院'
      if (this.isSeniorConfirm) return '验证评审员'
      // if (this.isCheck) return '评审组长'
      return ''
    },

    rejectWho2() {
      if (this.isConfirm) return '医院'
      if (this.isSeniorConfirm) return '验证'
      // if (this.isCheck) return '评审组长'
      return ''
    },

    noRarTrReject() {
      let far = this.isConfirm && Object.values(this.frjMap).length === 0
      let tr = this.isSeniorConfirm && Object.values(this.trjMap).length === 0
      return far || tr
    },

    showRejectIds() {
      return this.relation?.rejectIds || []
    },

    showAllCheckIds() {
      return this.allCheckIds || []
    },

    doesNotRj() {
      return (clauseId) => {
        if (this.isConfirm) {
          return this.farMap[clauseId]
        } else if (this.isSeniorConfirm) {
          return this.trMap[clauseId]
        } else {
          return true
        }
      }
    }
  },

  created() { },

  mounted() {
    this.init2(true, this.$route.query.autCode, this.$route.query.type);

    this.getDicts("review_result").then((response) => {
      this.reviewResultOptions = response.data;
    });
  },

  methods: {
    initFinalBack() {
      let wholeIds = [];
      if (this.isConfirm || !this.isSecond) {
        let list = []
        if (this.isSeniorConfirm) {
          list = Object.values(this.trMap);
        } else {
          list = Object.values(this.farMap);
        }
        wholeIds = list.map(({ clauseId }) => clauseId);
        this.allCheckIds = wholeIds
      } else {
        let ffrList = Object.values(this.trMap);
        wholeIds = ffrList.map(({ clauseId }) => clauseId);
      }

      if ((this.isFarConfirm || this.isSeniorConfirm) && this.showRejectIds.length) {
        // this.$set(this, 'distributeClauseIds', this.showRejectIds);
        // this.$set(this, 'clauseIds', this.showRejectIds);
        this.$set(this, 'distributeClauseIds', wholeIds);
        this.$set(this, 'clauseIds', wholeIds);
        this.lightHigh = 1
      } else {
        this.$set(this, 'distributeClauseIds', wholeIds);
        this.$set(this, 'clauseIds', wholeIds);
      }

      this.headerSlotKeys++;


      this.getStandard(this.relation.autCsId, false, true, () => { })
    },

    handleError() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push({ path: "/fInspector/CheckReviewList" });
    },

    turnToWho() {
      this.turning = true;
      let submitType = this.AutSaAudCurrentStatusEnum.FAR_SUMMARY_CONFIRM
      if (this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.CONFIRM_REVIEW_VERIFY) {
        submitType = this.AutSaAudCurrentStatusEnum.TR_SUMMARY_CONFIRM
      }
      request({
        url: "/aut/sa/aud/submitAutSaAud",
        method: "post",
        data: {
          autCode: this.$route.query.autCode,
          accountId: this.$store.getters.userId + "",
          submitType,
          autSaAudLists: [],
        },
      }).then((res) => {
        this.handleError();
      }).finally(() => {
        this.turning = false;
      });
    },

    modifyConfirm(type, choose) {
      let submitType = ''
      if (!this.isSecond) {
        submitType = type == 1 ? this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_M_SKIP : this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_W_M
      } else {
        submitType = type == 1 ? this.AutSaAudCurrentStatusEnum.TR_CLAUSE_M_SKIP : this.AutSaAudCurrentStatusEnum.TR_CLAUSE_W_M
      }
      if (type == 1 && choose == 'reject') {
        let who = !this.isSecond ? '医院' : '验证评审员'
        this.$confirm(`请确认是否维持原有评价，拒绝${who}提出的所有疑问？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.nextProcess(submitType, type)
        })
      } else {
        this.nextProcess(submitType, type)
      }
    },

    async checkLeaderModifyFinish() {
      let data = {
        autCode: this.relation.autCode,
        accountId: this.$store.getters.userId + "",
        submitType: this.AutSaAudCurrentStatusEnum.FAR_SUMMARY_CONFIRM_M,
        autSaAudLists: [],
      };

      try {
        let res = await this.submit(data);
        // 报错就不提示
        if (!res) return;
        this.$message({
          type: 'success',
          message: "审查完成"
        });
      } finally {
        this.handleError();
      }
    },

    nextProcess(submitType, type) {
      request({
        url: "/aut/sa/aud/submitAutSaAud",
        method: "post",
        data: {
          autCode: this.$route.query.autCode,
          accountId: this.$store.getters.userId,
          submitType,
          autSaAudLists: [],
        },
      }).then((res) => {
        this.$store.dispatch("tagsView/delView", this.$route);
        if (type == 2) {
          let path = !this.isSecond ? '/fAssessor/CAssessorIndex' : '/fAssessor/modifyVerifyReportResults';
          this.$router.push({
            path,
            query: {
              autCode: this.$route.query.autCode,
              type: this.relation.status === this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW ? this.AutSaAudCurrentStatusEnum.TR_CLAUSE_W_M : this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_W_M,
            },
          });
        } else {
          this.$router.push({ path: "/fAssessor/m/assessorEvaluationList" });
        }
      });
    },

  },
};
</script>
<style lang='scss' scoped></style>

<style lang='scss' >
.header-right>span::after {
  content: " | ";
}

.header-right>span:last-child::after {
  content: "";
}

.firstModifyShowForm {
  .el-form-item {
    margin-bottom: 0;

    .el-form-item__label {
      border: 1px solid black;
      height: 38px;
    }

    .el-form-item__content {
      text-align: center;
      border: 1px solid black;
      border-left: 0;
      height: 38px;
    }

    &+.el-form-item {
      .el-form-item__label {
        border-top: 0;
      }

      .el-form-item__content {
        border-top: 0;
      }
    }
  }

  .getFileDateLabel {
    .el-form-item__label {
      line-height: 18px;
      // border-top: 1px solid black !important;
      border-left: 0;
    }
  }

  .emptyLabel {
    .el-form-item__label {
      height: 38px;
    }
  }
}
</style>
