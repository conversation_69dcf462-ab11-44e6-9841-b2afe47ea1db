package com.thas.web.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.thas.common.constant.Constants;
import com.thas.common.core.domain.entity.SysUser;
import com.thas.common.enums.*;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.DateUtils;
import com.thas.common.utils.SecurityUtils;
import com.thas.system.domain.SysUserHospital;
import com.thas.system.mapper.SysUserHospitalMapper;
import com.thas.system.service.ISysUserHospitalService;
import com.thas.system.service.ISysUserService;
import com.thas.web.domain.*;
import com.thas.web.domain.dto.ReviewFitMoveClauseReq;
import com.thas.web.domain.dto.SelectStandardsByClauseIdsDTO;
import com.thas.web.domain.vo.AutSaAudDetailListVO;
import com.thas.web.domain.vo.AutSaAudVo;
import com.thas.web.domain.vo.FileInfoVO;
import com.thas.web.dto.CstCertificationStandardVO;
import com.thas.web.dto.HosPlanUserInfoVO;
import com.thas.web.dto.HosReviewPlanVO;
import com.thas.web.mapper.AutSaAudMapper;
import com.thas.web.mapper.HospitalPlannedDistributionMapper;
import com.thas.web.mapper.HospitalReviewCycleMapper;
import com.thas.web.mapper.ReviewFitMoveClauseMapper;
import com.thas.web.service.CommonService;
import com.thas.web.service.IAutSaAudBusinessDataService;
import com.thas.web.service.IAutSaAudService;
import com.thas.web.service.IAutSaRelationService;
import com.thas.web.service.ICstCertificationStandardsService;
import com.thas.web.service.IHospitalBaseInfoService;
import com.thas.web.service.IHospitalPlannedDistributionService;
import com.thas.web.service.IHospitalPreExamService;
import com.thas.web.service.IHospitalReviewerService;
import com.thas.web.service.IUploadFileInfoService;
import com.thas.web.service.process.CommonProcessService;

import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 认证自评审核Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
@Validated
@Component
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class AutSaAudServiceImpl implements IAutSaAudService {

    @Resource
    private AutSaAudMapper autSaAudMapper;

    @Resource
    private IAutSaRelationService autSaRelationService;

    @Resource
    private IUploadFileInfoService uploadFileInfoService;

    @Resource
    private CommonService commonService;

    @Resource
    private ICstCertificationStandardsService cstCertificationStandardsService;

    @Resource
    private ISysUserHospitalService sysUserHospitalService;

    @Resource
    private IHospitalBaseInfoService hospitalBaseInfoService;

    @Resource
    private ISysUserService sysUserService;

    @Resource
    private IHospitalPreExamService hospitalPreExamService;

    @Resource
    private IHospitalReviewerService hospitalReviewerService;

    @Resource
    private IAutSaAudBusinessDataService autSaAudBusinessDataService;

    @Resource
    private SysUserHospitalMapper sysUserHospitalMapper;

    @Resource
    private HospitalPlannedDistributionMapper hospitalPlannedDistributionMapper;

    @Resource
    private HospitalReviewCycleMapper hospitalReviewCycleMapper;

    @Resource
    private CommonProcessService commonProcessService;

    @Resource
    private IHospitalPlannedDistributionService hospitalPlannedDistributionService;

    @Autowired
    private ReviewFitMoveClauseMapper reviewFitMoveClauseMapper;

    @Autowired
    private IUploadFileInfoService iUploadFileInfoService;

    /**
     * 总结类型列表
     */
    private static final List<String> SUM_TYPE_LIST = Arrays.asList(AutSaAudSubmitTypeEnum.SA_SUMMARY.getSubmitType(),
            AutSaAudSubmitTypeEnum.SR_SUMMARY.getSubmitType(),
            AutSaAudSubmitTypeEnum.FR_REPORT_R_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_SUMMARY.getSubmitType(),
            AutSaAudSubmitTypeEnum.TR_SUMMARY.getSubmitType());

    @Override
    public Map<String, Object> selectCurrentStageSaAud() {
        // 校验角色
        SysUser sysUser = SecurityUtils.getSysUser();
        if (StringUtils.isBlank(sysUser.getRoleKey())) {
            log.error("用户：{} 账户未配置角色", sysUser.getUserId());
            throw new ServiceException(ServiceExceptionEnum.SYS_USER_ERROR_1000001);
        }
        if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(sysUser.getRoleKey(), AutSaAudRoleEnum.ADMIN, AutSaAudRoleEnum.COMMON_ADMIN)) {
            log.error("账户:{} 是管理端， 不需要查询相关信息", sysUser.getUserId());
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_ERROR_1000006);
        }
        SysUserHospital sysUserHospital = sysUserHospitalMapper.selectSysUserHospitalByUserId(sysUser.getUserId());
        if (ObjectUtils.isEmpty(sysUserHospital)) {
            throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000000);
        }
        String hospitalApplyNo = sysUserHospital.getHospitalApplyNo();
        //获取确认时间
        HospitalPlannedDistribution hospitalPlannedDistribution = hospitalPlannedDistributionMapper.selectHospitalPlannedDistributionByApplyNo(hospitalApplyNo);
        Map<String, Object> resultMap = new HashMap<>();
        List<HospitalReviewCycle> hospitalReviewCycles = hospitalReviewCycleMapper.selectHospitalReviewCycleByApplyNo(hospitalApplyNo);
        resultMap.put("hospitalReviewCycles", hospitalReviewCycles);
        //判断医院分配计划数据为空 或 分配审核状态不为1（1为通过），
        // 判断医院分配计划是否分配完成 未完成 -1
        // 分配完成时 判断医院未审核计划或者拒绝计划 -2
        // 返回值 放到 nowStageStatus 中
        String nowStageStatus ;
        if (hospitalPlannedDistribution == null || CollectionUtils.isEmpty(hospitalReviewCycles)) {
            nowStageStatus = "-1";
            log.info("分配评审周期中,未完成分配");
        } else if (hospitalPlannedDistribution.getCycleStatus() == 0) {
            nowStageStatus = "-2";
            log.info("分配评审周期完成,待医疗机构完成评审周期审核");
        } else {
            AutSaRelation autSaRelation = autSaRelationService.selectAutSaRelationByHospitalApplyNo(hospitalApplyNo, true);
            String autCode = autSaRelation.getAutCode();
            List<AutSaAud> autSaAudsList = autSaAudMapper.selectAutSaAudListByAutCodeAndTypes(autCode, String.join(",", SUM_TYPE_LIST));
            if (CollectionUtils.isNotEmpty(autSaAudsList)) {
                //所有阶段提交时间
                resultMap.put("statusDate", autSaAudsList.stream().collect(Collectors.toMap(AutSaAud::getSubmitType, AutSaAud::getSubmitDate)));
            }
            //当前阶段的信息
            nowStageStatus = autSaRelation.getAutStatus();
            resultMap.put("confirmTime", hospitalPlannedDistribution.getConfirmTime());
            List<AutSaAudBusinessData> autSaAudBusinessDataList = autSaAudBusinessDataService.selectAutSaAudBusinessData(autSaRelation.getAutCode(), AutSaAudBusinessCodeEnum.AUT_SA_AUD_REPORT.getCode());
            if (CollectionUtils.isNotEmpty(autSaAudBusinessDataList)) {
                resultMap.put("reportGenerateTime", autSaAudBusinessDataList.get(0).getUpdateTime());
            }
        }
        if(StringUtils.isNotBlank(nowStageStatus)){
            resultMap.put("nowStageStatus", nowStageStatus);
        }

        return resultMap;
    }

    @Override
    public List<AutSaAudDetailListVO> selectAutSaAudDetailList(String autCode) {
        // 校验角色
        SysUser sysUser = SecurityUtils.getSysUser();
        if (StringUtils.isBlank(sysUser.getRoleKey())) {
            log.error("用户：{} 账户未配置角色", sysUser.getUserId());
            throw new ServiceException(ServiceExceptionEnum.SYS_USER_ERROR_1000001);
        }
        if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(sysUser.getRoleKey(), AutSaAudRoleEnum.ADMIN, AutSaAudRoleEnum.COMMON_ADMIN)) {
            log.error("账户:{} 是管理端， 不需要查询相关信息", sysUser.getUserId());
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_ERROR_1000006);
        }
        SysUserHospital sysUserHospital = sysUserHospitalMapper.selectSysUserHospitalByUserId(sysUser.getUserId());
        if (ObjectUtils.isEmpty(sysUserHospital)) {
            throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000000);
        }
        String hospitalApplyNo = sysUserHospital.getHospitalApplyNo();
        List<AutSaAudDetailListVO> autSaAudDetailListVOList = autSaAudMapper.selectAutSaAudDetailListByApplyNo(hospitalApplyNo, autCode);
        if (ObjectUtils.isNotEmpty(autSaAudDetailListVOList)) {
            autSaAudDetailListVOList.forEach(a -> {
                List<AutSaAudVo> autSaAudVoList = a.getAutSaAudVoList();
                if (CollectionUtils.isNotEmpty(autSaAudVoList)) {
                    Map<String, List<AutSaAudVo>> tmpMap = autSaAudVoList.stream().filter(b -> b.getSubmitType() != null).collect(Collectors.groupingBy(AutSaAudVo::getSubmitType));
                    if (tmpMap != null && tmpMap.size() > 0) {
                        autSaAudVoList = tmpMap.entrySet().stream().map(bb -> bb.getValue().stream().sorted(Comparator.comparing(AutSaAudVo::getSubmitDate)).reduce((first, second) -> second).orElse(bb.getValue().get(0))).collect(Collectors.toList());
                        a.setAutSaAudVoList(autSaAudVoList);
                    } else {
                        a.setAutSaAudVoList(null);
                    }
                }
                // 评审报告生成时间
                List<AutSaAudBusinessData> autSaAudReportBusinessDatas = autSaAudBusinessDataService.selectAutSaAudBusinessData(a.getAutCode(), AutSaAudBusinessCodeEnum.AUT_SA_AUD_REPORT.getCode());
                log.info("查询到评审报告信息：{}", JSON.toJSONString(autSaAudReportBusinessDatas));
                if (CollectionUtils.isNotEmpty(autSaAudReportBusinessDatas) && StringUtils.isNotBlank(autSaAudReportBusinessDatas.get(0).getData())) {
                    a.setAutSaAudReport(JSON.parseObject(autSaAudReportBusinessDatas.get(0).getData(), AutSaAudReport.class));
                }
                //评审流程结束时，封装评审报告
                if( AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(a.getLastStageStatus(),AutSaAudStatusEnum.REVIEW_PROCESS_END)){
                    List<AutSaAudBusinessData> reviewReportBusinessDatas = autSaAudBusinessDataService.selectAutSaAudBusinessData(a.getAutCode(),
                            AutSaAudBusinessCodeEnum.REVIEW_REPORT.getCode());
                    if(CollectionUtils.isNotEmpty(reviewReportBusinessDatas) && StringUtils.isNotBlank(reviewReportBusinessDatas.get(0).getData())){
                        FileInfoVO fileInfoVO = JSON.parseObject(reviewReportBusinessDatas.get(0).getData(), FileInfoVO.class);
                        Map<String, List<FileInfoVO>> fileDetailMap = new HashMap<>();
                        List<FileInfoDTO> fileInfoDTOList = iUploadFileInfoService.getUploadFileInfoByIds(fileInfoVO.getFileId());
                        List<FileInfoVO> fileInfoVOList = commonService.fileInfoDtoToVo(fileInfoDTOList);
                        fileDetailMap.putAll(fileInfoVOList.stream().map(o -> {
                            o.setFileType(FtlToPdfEnum.REVIEW_REPORT.getCode());
                            return o;
                        }).collect(Collectors.groupingBy(FileInfoVO::getFileId)));
                        a.setFileDetailMap(fileDetailMap);
                    }
                }

                //封装不适用款
                ReviewFitMoveClauseReq reviewFitMoveClauseReq = new ReviewFitMoveClauseReq();
                reviewFitMoveClauseReq.setAutCode(a.getAutCode());
                reviewFitMoveClauseReq.setFitStatus(Long.valueOf(Constants.STR_NUM_1));
                List<ReviewFitMoveClause> reviewFitMoveClauseList = reviewFitMoveClauseMapper.qryReviewFitMoveClauseList(reviewFitMoveClauseReq);
                if(CollectionUtils.isNotEmpty(reviewFitMoveClauseList)){
                    a.setAllFitClauseIds(reviewFitMoveClauseList.stream().map(ReviewFitMoveClause::getClauseId).collect(Collectors.toList()));
                }
            });
        }
        return autSaAudDetailListVOList;
    }

    /**
     * 根据自评编码和提交类型查询相关数据
     *
     * @param autCode     自评编码
     * @param submitTypes 提交类型
     * @return 审核信息
     */
    @Override
    public List<AutSaAud> selectAutSaAudListByAutCodeAndTypes(String autCode, String submitTypes) {
        // 查询审核
        return autSaAudMapper.selectAutSaAudListByAutCodeAndTypes(autCode, submitTypes);
    }

    /**
     * 获取组员修订信息列表
     *
     * @param autCode 自评编码
     * @return 组员修订信息列表
     */
    @Override
    public List<MemberRevisionInfo> selectMemberRevisionInfos(String autCode) {
        // 校验角色
        SysUser sysUser = SecurityUtils.getSysUser();
        // 组员修订信息时，根据自评编码取关联信息
        AutSaRelation autSaRelation = this.memberRevisionInfoGetAutSaRelation(autCode, sysUser);
        // 组员修订信息时，根据医疗机构编码获取分配组员信息
        List<HosPlanUserInfoVO> memberList = this.memberRevisionInfoGetMemberList(autSaRelation.getHospitalApplyNo(), sysUser);
        // 查询当前驳回信息
        List<AutSaAudBusinessData> rejectBusinessDatas = autSaAudBusinessDataService.selectAutSaAudBusinessData(autCode, AutSaAudBusinessCodeEnum.AUT_SA_AUD_REJECT.getCode());
        log.info("查询到驳回信息：{}", JSON.toJSONString(rejectBusinessDatas));
        Map<String, List<CstCertificationStandards>> rejectClauseMap = new HashMap<>();
        Map<String, List<AutSaAud>> rejectSubmitMap = new HashMap<>();
        List<AutSaAud> rejectInfo = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(rejectBusinessDatas) && StringUtils.isNotBlank(rejectBusinessDatas.get(0).getData())){
            rejectInfo = JSON.parseArray(rejectBusinessDatas.get(0).getData(), AutSaAud.class);
        }
        boolean srClauseSubmitTypeFlag = rejectInfo.stream().anyMatch(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE));
        if (srClauseSubmitTypeFlag) {
            List<Long> rejectClauseIds1 = Lists.newArrayList();
            List<String> rejectClauseIds2 = Lists.newArrayList();
            rejectInfo.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE)).map(AutSaAud::getClauseId).distinct().forEach(a -> {
                rejectClauseIds1.add(Long.valueOf(a));
                rejectClauseIds2.add(a);
            });
            // 根据驳回款获取对应款号信息
            SelectStandardsByClauseIdsDTO selectStandardsByClauseIdsDTO = new SelectStandardsByClauseIdsDTO();
            selectStandardsByClauseIdsDTO.setVersionId(Long.valueOf(autSaRelation.getAutCsId()));
            selectStandardsByClauseIdsDTO.setClauseIds(rejectClauseIds1);
            List<CstCertificationStandards> rejectClauseNos = cstCertificationStandardsService.selectByClauseIdsAndVersionId(selectStandardsByClauseIdsDTO);
            rejectClauseMap = rejectClauseNos.stream().collect(Collectors.groupingBy(a -> a.getClauseId().toString()));
            // 查询当前已提交的驳回信息
            AutSaAud autSaAud = new AutSaAud();
            autSaAud.setSubmitType(AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType());
            autSaAud.setClauseId(String.join(",", rejectClauseIds2));
            autSaAud.setStatus(Constants.HospitalConstants.NUM_1);
            autSaAud.setAutCode(autCode);
            List<AutSaAud> rejectSubmits = autSaAudMapper.selectAutSaAudList(autSaAud);
            if (CollectionUtils.isNotEmpty(rejectSubmits)) {
                rejectSubmitMap = rejectSubmits.stream().collect(Collectors.groupingBy(AutSaAud::getClauseId));
            }
        }

        // 查询当前修订备注信息
        List<AutSaAudBusinessData> memberRevisionInfoRemarkDatas = autSaAudBusinessDataService.selectAutSaAudBusinessData(autCode, packRemarkBusinessCode(autSaRelation.getAutStatus()));
        log.info("查询当前修订备注信息：{}", JSON.toJSONString(memberRevisionInfoRemarkDatas));
        Map<String, String> memberRevisionInfoRemarkMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(memberRevisionInfoRemarkDatas) && StringUtils.isNotBlank(memberRevisionInfoRemarkDatas.get(0).getData())) {
            memberRevisionInfoRemarkMap = JSON.parseObject(memberRevisionInfoRemarkDatas.get(0).getData(), Map.class);
        }

        // 获取组员修订信息列表
        List<AutSaAud> finalRejectInfo = rejectInfo;
        Map<String, String> finalMemberRevisionInfoRemarkMap = memberRevisionInfoRemarkMap;
        Map<String, List<CstCertificationStandards>> finalRejectClauseMap = rejectClauseMap;
        Map<String, List<AutSaAud>> finalRejectSubmitMap = rejectSubmitMap;
        return memberList.stream().map(a -> {
            MemberRevisionInfo memberRevisionInfo = new MemberRevisionInfo();
            memberRevisionInfo.setAutCode(autCode);
            SysUser memberUser = sysUserService.selectUserById(Long.valueOf(a.getAccountId()));
            memberRevisionInfo.setMemberName(memberUser.getNickName());
            memberRevisionInfo.setMemberUserId(memberUser.getUserId().toString());
            memberRevisionInfo.setMemberPhone(memberUser.getPhonenumber());
            List<Map<String, Object>> rejectClauseMaps = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(finalRejectInfo) && srClauseSubmitTypeFlag) {
                // 归属于该用户的驳回数据
                List<String> rejectClauses = finalRejectInfo.stream().filter(b -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(b.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE))
                        .filter(b -> StringUtils.equals(a.getAccountId(), b.getAccountId())).map(AutSaAud::getClauseId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(rejectClauses)) {
                    rejectClauseMaps = rejectClauses.stream().map(c -> {
                        Map<String, Object> temMap = new HashMap<>();
                        temMap.put("clauseNo", finalRejectClauseMap.get(c).get(0).getClauseNo());
                        temMap.put("clauseId", finalRejectClauseMap.get(c).get(0).getClauseId().toString());
                        List<AutSaAud> autSaAuds = finalRejectSubmitMap.get(c);
                        String isModify = Constants.FLAG_VALUE_N;
                        if (CollectionUtils.isNotEmpty(autSaAuds)) {
                            isModify = Constants.FLAG_VALUE_Y;
                        }
                        temMap.put("isModify", isModify);
                        return temMap;
                    }).collect(Collectors.toList());
                }
            } else {
                //1当自评报告环节驳回信息为空时，此时在现场评审或验证评审驳回环节触发接口，封装驳回数据
                //2根据自评编码，查询认证自评关联表，获取当前节点在【0304011-评审修改中】或【0305011-修改验证评审数据】 用于区别现场评审还是验证评审环节
                //3根据aut_code，submit_type提交类型为：1-现场评审:far_clause和far_clause_m或2-验证评审:tr_clause和tr_clause_m 和status 查询认证自评表获取驳回数据
                //根据memberList分配组员信息，封装对应组员驳回信息
                String autStatus = autSaRelation.getAutStatus();
                if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autStatus, AutSaAudStatusEnum.FAR_CLAUSE_M)) {
                    //0304011-评审修改中
                    rejectClauseMaps = packRejectClauseMaps(a, autSaRelation, AutSaAudSubmitTypeEnum.FAR_CLAUSE, AutSaAudSubmitTypeEnum.FAR_CLAUSE_M);
                } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autStatus, AutSaAudStatusEnum.TR_CLAUSE_M)) {
                    //0305011-修改验证评审数据
                    rejectClauseMaps = packRejectClauseMaps(a, autSaRelation, AutSaAudSubmitTypeEnum.TR_CLAUSE, AutSaAudSubmitTypeEnum.TR_CLAUSE_M);
                } else {
                    throw new ServiceException("查看组员修订信息列表暂不支持此节点：" + autStatus);
                }
            }
            memberRevisionInfo.setRejectClauseNos(rejectClauseMaps);
            memberRevisionInfo.setRemark(MapUtils.getString(finalMemberRevisionInfoRemarkMap, a.getAccountId(), ""));
            return memberRevisionInfo;
        }).collect(Collectors.toList());
    }

    /**
     * @param hosPlanUserInfoVO 分配组员信息
     * @param autSaRelation 自评审核信息
     * @param rejectSubmitType 驳回提交类型枚举
     * @param revisedSubmitType 已修订提交类型枚举
     * @return
     */
    private List<Map<String, Object>> packRejectClauseMaps(HosPlanUserInfoVO hosPlanUserInfoVO, AutSaRelation autSaRelation,
                                                           AutSaAudSubmitTypeEnum rejectSubmitType, AutSaAudSubmitTypeEnum revisedSubmitType) {
        List<Map<String, Object>> rejectClauseMaps = Lists.newArrayList();
        //查询
        AutSaAud autSaAud = new AutSaAud();
        autSaAud.setAutCode(autSaRelation.getAutCode());
        autSaAud.setStatus(Constants.INT_ONE);
        List<String> submitTypeList = new ArrayList<>();
        submitTypeList.add(rejectSubmitType.getSubmitType());
        submitTypeList.add(revisedSubmitType.getSubmitType());
        autSaAud.setSubmitType(String.join(",", submitTypeList));
        List<AutSaAud> farAutSaAuds = autSaAudMapper.selectAutSaAudList(autSaAud);
        //获取驳回款Id
        List<String> rejectClauses = farAutSaAuds.stream().filter(o -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(o.getSubmitType(), rejectSubmitType)
                && ObjectUtil.equal(o.getAutResult(), Constants.STR_NUM_2)).map(AutSaAud::getClauseId).collect(Collectors.toList());
        //获取驳回已修订款id
        List<String> revisedClauses = farAutSaAuds.stream().filter(o -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(o.getSubmitType(), revisedSubmitType)
                /*&& ObjectUtil.equal(o.getAutResult(), Constants.STR_NUM_1)*/).map(AutSaAud::getClauseId).collect(Collectors.toList());
        //获取组员分配的款id，对应封装驳回有几款，修订情况：多少款未填，多少款已填
        Map<String, String> clauseIdAndNoMap =
                hosPlanUserInfoVO.getCstCertificationStandardVOList().stream().collect(Collectors.toMap(k -> k.getClauseId().toString(), CstCertificationStandardVO::getClauseNo));
        if (MapUtils.isNotEmpty(clauseIdAndNoMap)) {
            rejectClauseMaps = rejectClauses.stream().map(clauseId -> {
                Map<String, Object> temMap = new HashMap<>();
                if (StringUtils.isNotBlank(clauseIdAndNoMap.get(clauseId))) {
                    //驳回款
                    temMap.put("clauseNo", clauseIdAndNoMap.get(clauseId));
                    temMap.put("clauseId", clauseId);

                    //是否已修订
                    String isModify = Constants.FLAG_VALUE_N;
                    if (CollectionUtils.isNotEmpty(revisedClauses) && revisedClauses.contains(clauseId)) {
                        isModify = Constants.FLAG_VALUE_Y;
                    }
                    temMap.put("isModify", isModify);
                }

                return temMap;
            }).filter(MapUtils::isNotEmpty).collect(Collectors.toList());
        }
        return rejectClauseMaps;
    }

    /**
     * 组员修订信息时，根据自评编码取关联信息
     *
     * @param autCode 自评编码
     * @param sysUser 用户信息
     * @return 关联信息
     */
    private AutSaRelation memberRevisionInfoGetAutSaRelation(String autCode, SysUser sysUser) {
        // 获取该自评编码关联信息
        AutSaRelation autSaRelation = autSaRelationService.selectAutSaRelationByAutCode(autCode, Constants.HospitalConstants.NUM_1);
        // 校验是否处于评审复查驳回SR_V_CLAUSE_REJECT节点(01-10改为0304011和0305011两个节点)
        if (!AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.SR_V_CLAUSE_REJECT, AutSaAudStatusEnum.FAR_CLAUSE_M, AutSaAudStatusEnum.TR_CLAUSE_M)) {
            log.error("登录用户:{} 获取组员修订信息列表时 节点：{} 不正确", sysUser.getUserId(), autSaRelation.getAutStatus());
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_ERROR_1000006);
        }
        //不改变原逻辑，当是评审复查驳回节点需校验（改动节点只做查询功能不校验）
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.SR_V_CLAUSE_REJECT)) {
            AutSaAudSaveDTO req = new AutSaAudSaveDTO();
            req.setAutSaRelation(autSaRelation);
            req.setAccountId(sysUser.getUserId().toString());
            // 校验是否过期
            commonProcessService.checkQueryCycle(autSaRelation.getHospitalApplyNo(), autSaRelation.getAutCode(), autSaRelation.getAutStatus());
            // 校验用户分配给该医疗机构
            commonProcessService.checkSubmitPermission(req);
        }
        return autSaRelation;
    }

    /**
     * 组员修订信息时，根据医疗机构编码获取分配组员信息
     *
     * @param applyNo 医疗机构编码
     * @param sysUser 用户信息
     * @return
     */
    private List<HosPlanUserInfoVO> memberRevisionInfoGetMemberList(String applyNo, SysUser sysUser) {
        // 查询医院分配详情记录
        List<HosReviewPlanVO> hosPlanClauseList = hospitalBaseInfoService.selectHosPlanClauseDetail(applyNo);
        HosReviewPlanVO hosReviewPlanVO = hosPlanClauseList.get(0);
        // 获取当前分配的组员的信息
        List<HosPlanUserInfoVO> memberList = commonProcessService.getHosPlanMemberList(hosReviewPlanVO, sysUser.getRoleKey());
        HosPlanUserInfoVO hosPlanUserInfoVO = commonProcessService.getHosPlanUserInfoVOFromList(sysUser.getUserId().toString(), memberList);
        if (!commonProcessService.checkIsLeader(hosPlanUserInfoVO)) {
            log.error("登录用户:{} 获取组员修订信息列表时 该用户分配的不是组长，没有权限查询", sysUser.getUserId());
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_ERROR_1000006);
        }
        return memberList;
    }

    /**
     * 更新组员修订信息备注
     *
     * @param memberRevisionInfo 修订信息
     */
    @Override
    public void updateMemberRevisionInfo(MemberRevisionInfo memberRevisionInfo) {
        // 校验角色
        SysUser sysUser = SecurityUtils.getSysUser();
        // 组员修订信息时，根据自评编码取关联信息
        AutSaRelation autSaRelation = this.memberRevisionInfoGetAutSaRelation(memberRevisionInfo.getAutCode(), sysUser);
        // 组员修订信息时，根据医疗机构编码获取分配组员信息
        this.memberRevisionInfoGetMemberList(autSaRelation.getHospitalApplyNo(), sysUser);
        //根据节点状态，封装对应的备注枚举
        String businessCode = packRemarkBusinessCode(autSaRelation.getAutStatus());
        // 查询当前修订备注信息
        List<AutSaAudBusinessData> memberRevisionInfoRemarkDatas = autSaAudBusinessDataService.selectAutSaAudBusinessData(memberRevisionInfo.getAutCode(), businessCode);
        Map<String, String> memberRevisionInfoRemarkMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(memberRevisionInfoRemarkDatas) && StringUtils.isNotBlank(memberRevisionInfoRemarkDatas.get(0).getData())) {
            memberRevisionInfoRemarkMap = JSON.parseObject(memberRevisionInfoRemarkDatas.get(0).getData(), Map.class);
        }
        memberRevisionInfoRemarkMap.put(memberRevisionInfo.getMemberUserId(), memberRevisionInfo.getRemark());
        AutSaAudBusinessData autSaAudBusinessData = new AutSaAudBusinessData();
        autSaAudBusinessData.setAutCode(memberRevisionInfo.getAutCode());
        autSaAudBusinessData.setBusinessCode(businessCode);
        autSaAudBusinessData.setData(JSON.toJSONString(memberRevisionInfoRemarkMap));
        autSaAudBusinessData.setUpdateTime(DateUtils.getNowDate());
        autSaAudBusinessDataService.saveAutSaAudBusinessData(autSaAudBusinessData);
    }

    /**
     * 根据节点状态，封装对应的备注枚举
     *
     * @param autStatus
     * @return
     */
    private String packRemarkBusinessCode(String autStatus) {
        if (StringUtils.isBlank(autStatus)) {
            throw new ServiceException("认证状态为空，获取备注枚举失败！");
        }
        String businessCode = "";
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autStatus, AutSaAudStatusEnum.SR_V_CLAUSE_REJECT)) {
            businessCode = AutSaAudBusinessCodeEnum.MEMBER_REVISION_INFO_REMARK.getCode();
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autStatus, AutSaAudStatusEnum.FAR_CLAUSE_M)) {
            businessCode = AutSaAudBusinessCodeEnum.FAR_MEMBER_REVISION_INFO_REMARK.getCode();
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autStatus, AutSaAudStatusEnum.TR_CLAUSE_M)) {
            businessCode = AutSaAudBusinessCodeEnum.TR_MEMBER_REVISION_INFO_REMARK.getCode();
        } else {
            throw new ServiceException("不支持获取备注枚举,当前节点状态：" + autStatus);
        }
        return businessCode;


    }

}
