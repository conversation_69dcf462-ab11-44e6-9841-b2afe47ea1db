<template>
  <div class="clinical">
    <el-divider content-position="left"
      >临床服务
      <!-- <el-button type="text" icon="el-icon-circle-plus" size="mini" style="margin-left: 23px" @click="addClinical">新增</el-button> -->
    </el-divider>
    <el-form ref="clinicalForm" :model="formData">
      <el-table
        :data="formData.clinicalList"
        border
        height="600"
        :span-method="clinicalSpanMethod"
        :row-style="{ height: '50px' }"
      >
        <el-table-column prop="departmentName" label="科室名称" width="200">
          <template slot-scope="scope">
            <span
              v-if="scope.row.departmentDictValue !== 'otherNewDepartments'"
              >{{ scope.row.departmentName }}</span
            >
            <el-form-item
              v-else
              :prop="`clinicalList.${scope.$index}.departmentName`"
              :rules="rules.departmentName"
            >
              <el-input
                v-model.trim="scope.row.departmentName"
                placeholder="请输入科室名称"
                maxlength="20"
              ></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          prop="outpatientService"
          label="是否提供门诊服务"
          width="200"
        >
          <template slot-scope="scope">
            <el-form-item
              :prop="`clinicalList.${scope.$index}.outpatientService`"
              :rules="rules.outpatientService"
            >
              <el-radio-group v-model.trim="scope.row.outpatientService">
                <el-radio
                  v-for="item in provideOptions"
                  :key="item.dictValue"
                  :label="item.dictValue"
                  >{{ item.dictLabel }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          prop="inpatientService"
          label="是否提供住院服务"
          width="200"
        >
          <template slot-scope="scope">
            <el-form-item
              :prop="`clinicalList.${scope.$index}.inpatientService`"
              :rules="rules.inpatientService"
            >
              <el-radio-group v-model.trim="scope.row.inpatientService">
                <el-radio
                  v-for="item in provideOptions"
                  :key="item.dictValue"
                  :label="item.dictValue"
                  >{{ item.dictLabel }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="备注">
          <!-- <template slot="header">
            <el-tooltip
              effect="dark"
              content="开设少于3年的科室请注明"
              placement="top"
            >
              <span>备注 <i class="el-icon-info"></i></span>
            </el-tooltip>
          </template> -->
          <template slot-scope="scope">
            <el-form-item>
              <el-input
                v-model.trim="scope.row.departmentDesc"
                :maxlength="50"
                placeholder="如开科少于3年请注明开科日期及年限"
              ></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="outpatientService" label="操作" width="120">
          <template slot-scope="scope">
            <div class="table-btn">
              <el-button
                v-if="scope.$index == formData.clinicalList.length - 1"
                type="success"
                icon="el-icon-circle-plus"
                @click="addClinical"
                circle
              ></el-button>
              <el-button
                v-if="scope.row.isAdd"
                type="danger"
                icon="el-icon-delete"
                @click="removeClinical(scope.$index)"
                circle
              ></el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-form>

    <el-divider content-position="left" class="medical"
      >医技服务
      <!-- <el-button
        type="text"
        icon="el-icon-circle-plus"
        size="mini"
        style="margin-left: 23px"
        @click="addMedical"
        >新增</el-button
      > -->
    </el-divider>
    <el-form ref="medicalForm" :model="formData">
      <el-table
        :data="formData.medicalList"
        border
        :span-method="medicalSpanMethod"
        :row-style="{ height: '50px' }"
      >
        <el-table-column prop="departmentName" label="科室名称" width="200">
          <template slot-scope="scope">
            <span
              v-if="scope.row.departmentDictValue !== 'otherNewDepartments'"
              >{{ scope.row.departmentName }}</span
            >
            <el-form-item
              v-else
              :prop="`medicalList.${scope.$index}.departmentName`"
              :rules="rules.departmentName"
            >
              <el-input
                v-model.trim="scope.row.departmentName"
                placeholder="请输入科室名称"
                maxlength="20"
              ></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="outpatientService" label="是否提供服务" width="200">
          <template slot-scope="scope">
            <el-form-item
              :prop="`medicalList.${scope.$index}.outpatientService`"
              :rules="rules.outpatientService"
            >
              <el-radio-group v-model.trim="scope.row.outpatientService">
                <el-radio
                  v-for="item in provideOptions"
                  :key="item.dictValue"
                  :label="item.dictValue"
                  >{{ item.dictLabel }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="备注">
          <!-- <template slot="header">
            <el-tooltip
              effect="dark"
              content="开设少于3年的科室请注明"
              placement="top"
            >
              <span> 备注 <i class="el-icon-info"></i></span>
            </el-tooltip>
          </template> -->
          <template slot-scope="scope">
            <el-form-item>
              <el-input
                v-model.trim="scope.row.departmentDesc"
                :maxlength="50"
                placeholder="如开科少于3年请注明开科日期及年限"
              ></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="outpatientService" label="操作" width="120">
          <template slot-scope="scope">
            <div class="table-btn">
              <el-button
                v-if="scope.$index == formData.medicalList.length - 1"
                type="success"
                icon="el-icon-circle-plus"
                @click="addMedical"
                circle
              ></el-button>
              <el-button
                v-if="scope.row.isAdd"
                type="danger"
                icon="el-icon-delete"
                @click="removeMedical(scope.$index)"
                circle
              ></el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!-- <el-button @click="valid">sdfasd</el-button> -->
    </el-form>
  </div>
</template>

<script>
export default {
  name: "Clinical",
  props: {
    initData: {
      type: [Object, null],
      default: () => null,
    }
  },
  data() {
    return {
      formData: {
        clinicalList: [
          {
            departmentDictType: "",
            departmentDictValue: "",
            departmentName: "",
            departmentDesc: "",
            inpatientService: null,
            outpatientService: null,
          },
        ],
        medicalList: [
          {
            departmentDictType: "",
            departmentDictValue: "",
            departmentName: "",
            departmentDesc: "",
            outpatientService: null,
          },
        ],
      },
      rules: {
        outpatientService: [
          {
            required: true,
            message: "请选择",
            trigger: ["blur", "change"],
          },
        ],
        inpatientService: [
          {
            required: true,
            message: "请选择",
            trigger: ["blur", "change"],
          },
        ],
        departmentDesc: [
          {
            required: true,
            message: "请选择",
            trigger: ["blur", "change"],
          },
        ],
        departmentName: [
          {
            required: true,
            message: "请输入科室名称",
            trigger: ["blur", "change"],
          },
        ],
      },
      defaultClinicalData: {
        departmentDictType: "clinical_service",
        departmentDictValue: "otherNewDepartments",
        departmentName: "",
        outpatientService: "",
        inpatientService: "",
        departmentDesc: "",
      },
      defaultMedicalData: {
        departmentDictType: "medical_service",
        departmentDictValue: "otherNewDepartments",
        departmentName: "",
        outpatientService: "",
        departmentDesc: "",
      },
      provideOptions: [],
      clinicalList: [],
      medicalList: [],

      initDataDepartmentCliMap: {},
      initDataDepartmentMedMap: {},
    };
  },
  async mounted() {
    let dicts = await this.initDicts();
    this.provideOptions = dicts[0]?.data;
    if (!this.initData?.hospitalDepartmentList?.length) {
      let clinicalList = dicts[1]?.data.map((data) => {
        return {
          departmentDictType: data.dictType,
          departmentDictValue: data.dictValue,
          departmentName: data.dictLabel,
          departmentDesc: '',
          inpatientService: "",
          outpatientService: "",
        };
      });
      this.$set(this.formData, 'clinicalList', clinicalList)

      let medicalList = dicts[2]?.data.map((data) => {
        return {
          departmentDictType: data.dictType,
          departmentDictValue: data.dictValue,
          departmentName: data.dictLabel,
          departmentDesc: '',
          outpatientService: "",
        };
      });
      this.$set(this.formData, 'medicalList', medicalList)
      // 清除表单校验
      this.$nextTick(() => {
        this.$refs["clinicalForm"].clearValidate();
        this.$refs["medicalForm"].clearValidate();
      })
      return
    };
    let { hospitalDepartmentList = [] } = this.initData;
    this.formData.clinicalList = hospitalDepartmentList.filter(item => item.departmentDictType === 'clinical_service').map(dep => {
      if (isNaN(dep.departmentDictValue)) {
        dep.departmentDictValue = 'otherNewDepartments';
      }
      let findDict = dicts[1]?.data.filter(dict => dict.dictValue === dep.departmentDictValue)
      return {
        departmentDictType: 'clinical_service',
        departmentDictValue: dep.departmentDictValue === 'otherNewDepartments' ? 'otherNewDepartments' : findDict[0].dictValue,
        departmentName: dep.departmentDictValue === 'otherNewDepartments' ? dep.departmentName : findDict[0].dictLabel,
        departmentDesc: dep.departmentDesc,
        inpatientService: `${dep.inpatientService}` || "",
        outpatientService: `${dep.outpatientService}` || "",
        isAdd: dep.departmentDictValue === 'otherNewDepartments'
      }
    })

    this.formData.medicalList = hospitalDepartmentList.filter(item => item.departmentDictType === 'medical_service').map(dep => {
      if (isNaN(dep.departmentDictValue)) {
        dep.departmentDictValue = 'otherNewDepartments';
      }
      let findDict = dicts[2]?.data.filter(dict => dict.dictValue === dep.departmentDictValue)
      return {
        departmentDictType: 'medical_service',
        departmentDictValue: dep.departmentDictValue === 'otherNewDepartments' ? 'otherNewDepartments' : findDict[0].dictValue,
        departmentName: dep.departmentDictValue === 'otherNewDepartments' ? dep.departmentName : findDict[0].dictLabel,
        departmentDesc: dep.departmentDesc,
        outpatientService: `${dep.outpatientService}` || "",
        isAdd: dep.departmentDictValue === 'otherNewDepartments'
      }
    })
  },
  methods: {
    initDicts() {
      let promises = [this.getDicts("provide_ornot"), this.getDicts("clinical_service"), this.getDicts("medical_service")];
      return Promise.all(promises)
    },
    clinicalSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 4) {
        if (rowIndex <= 18) {
          return {
            rowspan: 19,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 1,
            colspan: 1,
          };
        }
      }
    },
    medicalSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 3) {
        if (rowIndex <= 8) {
          return {
            rowspan: 9,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 1,
            colspan: 1,
          };
        }
      }
    },
    // 新增临床服务
    addClinical() {
      const data = Object.assign({isAdd: true}, this.defaultClinicalData);
      this.formData.clinicalList.push(data);
      this.clinicalList.push(data);
      this.$nextTick(() => {
        let height = document.getElementsByClassName(
          "el-table__body-wrapper"
        )[0].scrollHeight;
        document
          .getElementsByClassName("el-table__body-wrapper")[0]
          .scrollTo(0, height);
      });
    },
    // 删除临床服务
    removeClinical(index) {
      this.formData.clinicalList.splice(index, 1);
      this.clinicalList.splice(index - 18, 1);
    },
    // 新增医技服务
    addMedical() {
      const data = Object.assign({isAdd: true}, this.defaultMedicalData);
      this.formData.medicalList.push(data);
      this.medicalList.push(data);
      this.$nextTick(() => {
        let height = document.getElementsByClassName(
          "el-table__body-wrapper"
        )[1].scrollHeight;
        document
          .getElementsByClassName("el-table__body-wrapper")[1]
          .scrollTo(0, height);
      });
    },
    // 删除医技服务
    removeMedical(index) {
      this.formData.medicalList.splice(index, 1);
      this.medicalList.splice(index - 10, 1);
    },
    valid() {
      try {
        this.$refs["clinicalForm"].validate();
        this.$refs["medicalForm"].validate();
      } catch (error) {
        // console.log(error);
      }
    },

    async getValue() {
      try {
        const cl = await this.$refs["clinicalForm"].validate();
        const me = await this.$refs["medicalForm"].validate();
        if (cl && me) {
          const { medicalList, clinicalList } = this.formData;
          let medFlag = medicalList.every((med) => med.outpatientService == 2);
          let cliFlag = clinicalList.every(
            (cli) =>
              cli.outpatientService == 2 &&
              cli.outpatientService == cli.inpatientService
          );
          if (medFlag && cliFlag) {
            this.$message({
              type: "warning",
              message: "请确认临床服务与医技服务是否提供！",
            });
            return null;
          }
          return [...medicalList, ...clinicalList];
        }
      } catch (error) {
        return null;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-table .cell {
  overflow: visible;
}
::v-deep .el-table .el-table__cell {
  padding: 0;
}
::v-deep .el-form-item {
  margin-bottom: 0;
  .el-form-item__error {
    top: 70%;
  }
}

::v-deep .el-divider.medical {
  margin-top: 48px;
}
::v-deep .cell .el-form-item .el-form-item__error {
  top: 70%;
}
</style>
