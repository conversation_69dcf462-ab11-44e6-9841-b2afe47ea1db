package com.thas.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户基本信息
 */
@Data
@ApiModel(value = "用户基本信息")
public class SysUserBaseInfo implements Serializable {

    private static final long serialVersionUID = -9084373271125374031L;

    @ApiModelProperty(value = "用户id")
    private String accountId;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "用户邮箱")
    private String email;

    @ApiModelProperty(value = "用户电话")
    private String phoneNumber;

    /**
     * 领域id
     */
    private String domainId;
}
