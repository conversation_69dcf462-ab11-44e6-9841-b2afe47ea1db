<!--  -->
<template>
  <div class="practice">
    <div class="training-records">
      <!-- <el-table ref="multipleTable" :data="[]" stripe border :row-key="getKey">
        <el-table-column type="selection" width="55" :reserve-selection="true">
        </el-table-column>
        <el-table-column label="培训名称" prop="trainingName"></el-table-column>
        <el-table-column label="举办时间" prop="trainingTime"></el-table-column>
        <el-table-column label="举办地点" prop="trainingLocation"></el-table-column>
        <el-table-column label="培训类型" prop="trainingType"></el-table-column>
        <el-table-column label="举办方" prop="organizers"></el-table-column>
      </el-table> -->
      <!-- <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query" /> -->
    </div>
  </div>
</template>

<script>
export default {
  name: "",
  components: {},
  props: {},
  watch: {},
  data() {
    return {
      total: 0,
      queryData: {
        pageNum: 0,
        pageSize: 0,
      }
    };
  },

  computed: {},

  created() { },

  mounted() { },

  methods: {

    getKey(row) {
      return row.id;
    },

    getValue() {
        return true;
    }
  },
};
</script>
<style lang="scss" scoped>
.training-records {
  // padding: 24px;
}
</style>
