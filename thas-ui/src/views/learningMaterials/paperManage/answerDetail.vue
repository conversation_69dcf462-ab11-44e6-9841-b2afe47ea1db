<template>
  <div class="answer-detail">
    <el-card class="box-card">
      <div class="paper-name">{{answerDetail.examPaperName}}</div>
      <div class="learn-resource">{{answerDetail.learnResourceName}}</div>
      <div class="paper-description">{{answerDetail.paperDiscription}}</div>
      <div class="statistics">
        <div class="item">
          <div class="score">&nbsp;{{totalCorrectScore || 0}}&nbsp;</div>
          <div class="score-title">得分</div>
          <div class="total">总分{{totalScore || 0}}分</div>
        </div>
        <div class="item">
          <div class="score">&nbsp;{{totalCorrect || 0}}&nbsp;</div>
          <div class="score-title">答对</div>
          <div class="total">共{{answerDetail.examDetails && answerDetail.examDetails.length || 0}}题</div>
        </div>
      </div>
      <div v-for="(item, index) in answerDetail.examDetails" :key="index" class="exam-question-item">
        <el-form size="medium" label-width="0px">
          <el-form-item label="">
            <div style="font-size: 16px; font-weight: bold;">
              <span>{{index + 1 + "、" + item.examQuestion}}</span>
              <span :style="{'margin-left': '30px', 'color': item.correct == '1' ? '#00ae22' : '#f52e3a'}">（{{item.correct == '1' ? "正确：" + item.score : '错误：0'}}分）</span>
            </div>
            <el-radio-group v-if="item.type == 1" v-model.trim="item.answer" disabled>
              <el-radio v-for="(question, i) in JSON.parse(item.questionOptions)" :key="i" :label="question.label">
                {{question.label}}
              </el-radio>
            </el-radio-group>
            <el-checkbox-group v-else-if="item.type == 2" v-model.trim="item.answer" disabled>
              <el-checkbox v-for="(question, i) in JSON.parse(item.questionOptions)" :key="i" :label="question.label">
                {{question.label}}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>
      <div style="text-align: center;">
        <el-button plain @click="back">返回</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import request from "@/utils/request"
export default {
  name: 'AnswerDetail',
  data() {
    return {
      answerDetail: {},
      totalScore: 0,
      totalCorrect: 0,
      totalCorrectScore: 0
    }
  },
  created() {
    this.getAnswerDetail()
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (to.query.id != vm.answerDetail.id) {
        vm.getAnswerDetail()
      }
    })
  },
  beforeRouteUpdate(to, from, next) {
    if (to.query.id != this.answerDetail.id) {
      this.getAnswerDetail(to.query.id)
    }
    next()
  },
  methods: {
    getAnswerDetail(id) {
      request({
        url: '/system/sheet/' + (id || this.$route.query.id),
        method: 'get'
      }).then((res) => {
        this.totalScore = 0
        this.totalCorrect = 0
        this.totalCorrectScore = 0
        for (let index = 0; index < res.data.examDetails.length; index++) {
          this.totalScore += (+res.data.examDetails[index].score)
          if (res.data.examDetails[index].correct == 1) {
            this.totalCorrect++
            this.totalCorrectScore += (+res.data.examDetails[index].score)
          }
          if (res.data.examDetails[index].type === 2) {
            res.data.examDetails[index].answer = JSON.parse(res.data.examDetails[index].answer)
          }
        }
        this.answerDetail = res.data
      }).catch((error) => {
        throw error
      })
    },
    back() {
      const routeName = this.$route.query.from
      if (this.$store.dispatch("tagsView/delView", this.$route)) {
        this.$router.push({ path: routeName })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.answer-detail {
  .el-radio {
    display: block;
    padding: 10px 0 0 0;
    line-height: 36px;
    &:nth-child(1) {
      padding: 0;
    }
  }
  .el-checkbox {
    padding: 10px 0 0 0;
    display: block;
    &:nth-child(1) {
      padding: 0;
    }
  }
  .paper-name {
    margin-top: 40px;
    text-align: center;
    font-size: 24px;
    font-weight: bold;
  }
  .learn-resource {
    margin: 10px 0;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
  }
  .paper-description {
    text-align: center;
    font-size: 12px;
    font-weight: bold;
    color: #747774;
  }
  .statistics {
    display: flex;
    justify-content: center;
    margin-top: 14px;
    margin-bottom: 60px;
    text-align: center;
    .item {
      width: 440px;
      border: 1px solid #ccc;
      &:nth-child(2) {
        border-left: 0;
      }
      .score {
        height: 42px;
        margin-top: 14px;
        font-size: 36px;
        color: #1683fd;
      }
      .score-title {
        margin: 12px 0;
      }
      .total {
        line-height: 40px;
        background-color: #f8f8f8;
      }
    }
  }
  .exam-question-item {
    width: 880px;
    margin: 0 auto;
  }
  ::v-deep .el-checkbox__input.is-disabled .el-checkbox__inner {
    background-color: #fff;
    border-color: #dcdfe6;
  }
  ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #1890ff;
    border-color: #1890ff;
  }
  ::v-deep
    .el-checkbox__input.is-disabled.is-checked
    .el-checkbox__inner::after {
    border-color: #fff;
  }
  ::v-deep .el-checkbox__input.is-disabled + span.el-checkbox__label {
    color: #606266;
  }
  ::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #409eff;
  }

  ::v-deep .el-radio__input.is-disabled .el-radio__inner {
    background-color: #fff;
    border-color: #dcdfe6;
  }
  ::v-deep .el-radio__input.is-checked .el-radio__inner {
    background-color: #1890ff;
    border-color: #1890ff;
  }
  ::v-deep .el-radio__input.is-disabled.is-checked .el-radio__inner::after {
    border-color: #fff;
    background-color: #fff;
  }
  ::v-deep .el-radio__input.is-disabled + span.el-radio__label {
    color: #606266;
  }
  ::v-deep .el-radio__input.is-checked + .el-radio__label {
    color: #409eff;
  }
}
</style>