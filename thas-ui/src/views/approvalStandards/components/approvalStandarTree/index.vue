<template>
  <el-collapse accordion class="approvalStandar-tree chapter" @change="handleChapterChange" v-model.trim="collapse" :key="key">
    <el-collapse-item v-for="(chapter, index) in standard" :key="index" :name="index">
      <template slot="title">
        <i class="arrow" :class="[arrow(index) ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"></i>
        <span>{{ chapter.chapterNo }} {{ chapter.chapter }}</span>
        <span class="detail">共{{ sectionNum(chapter) }}节
          {{ articleNum(chapter.sectionVoList) }}条
          {{ clauseTotalNum(chapter.sectionVoList) }}款， 基本款{{
            clauseBaseNum(chapter.sectionVoList)
          }}/{{ clauseTotalNum(chapter.sectionVoList) }}</span>
      </template>
      <div class="chapter-content">
        <section-collapse :sectionVoList="chapter.sectionVoList"></section-collapse>
      </div>
    </el-collapse-item>
  </el-collapse>
</template>

<script>
import sectionCollapse from "./sectionCollapse.vue";
import { getStandard } from "@/api/system/standard";

export default {
  name: "approvalStandarTree",
  components: {
    sectionCollapse,
  },
  props: {
    versionId: {
      type: [String, Number],
      default: () => "",
    },
  },
  data() {
    return {
      collapse: "",
      activeName: "",
      standard: null,
      key: 0,
    };
  },
  computed: {
    arrow() {
      return (name) => name === this.activeName;
    },
    sectionNum() {
      return (chapter) => chapter.sectionVoList.length;
    },
    articleNum() {
      return (sectionVoList) =>
        sectionVoList
          .reduce((list, section) => {
            list.push(section.articleVoList.length);
            return list;
          }, [])
          .reduce((m, n) => {
            return m + n;
          }, 0);
    },
    clauseNumList() {
      return (sectionVoList) => {
        return sectionVoList
          .reduce((list, section) => {
            list.push(...section.articleVoList);
            return list;
          }, [])
          .reduce((list, article) => {
            list.push(...article.clauseVoList);
            return list;
          }, []);
      };
    },
    clauseBaseNum() {
      return (sectionVoList) => {
        return this.clauseNumList(sectionVoList).filter(
          (clause) => clause.isStar == "1"
        ).length;
      };
    },
    clauseTotalNum() {
      return (sectionVoList) => {
        return this.clauseNumList(sectionVoList).length;
      };
    },
  },
  async mounted() {
    const versionId = this.versionId;
    const data = await getStandard(versionId);
    this.standard = data;
    this.key++;
  },
  methods: {
    handleChapterChange(activeNames) {
      this.activeName = activeNames;
    },
  },
};
</script>

<style lang="scss" scoped>
.approvalStandar-tree.chapter {
  ::v-deep .el-collapse-item__wrap {
    border-bottom: 0;
    .el-collapse {
      border: 0;
    }
  }
  ::v-deep .el-collapse-item__header {
    border-bottom: 0;
    background-color: #3d81f5;
    color: white;
    font-size: 18px;
    font-weight: 600;
    min-height: 50px;
    line-height: 50px;
    .detail {
      flex: 1;
      flex-grow: 1;
      text-align: right;
      font-size: 14px;
      font-weight: lighter;
      padding-right: 20px;
    }
    .arrow {
      display: inline-block;
      margin: 0 10px;
      margin-left: 30px;
      font-size: 20px;
      font-weight: bold;
    }
    .el-collapse-item__arrow {
      display: none;
    }
  }
}
</style>