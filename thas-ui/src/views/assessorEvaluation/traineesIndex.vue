<template>
  <div v-loading="loading" class="audit-evaluation" ref="auditEvaluation">
    <!-- 医院名称 -->
    <el-card v-if="clauseIds && clauseIds.length" class="box-card" shadow="always" style="margin-bottom: 10px; margin-left: -5px;">
      <h1 style="margin: 0">{{ detail.hospitalName }}</h1>
    </el-card>
    <!-- 评价  -->
    <el-card v-if="clauseIds && clauseIds.length" class="box-card autScrollCard" shadow="never" style="margin-top: 15px;"
      :style="{ height: autCardHeight }">
      <div slot="header" class="clearfix">
        <h4>现场评审</h4>
        <div class="header-right" :key="headerSlotKeys">
          <el-select v-model="queryData.domainAccountId" clearable filterable class="select-reviewer" size="mini"
            placeholder="请选择分组" @change="selectDomain" style="margin-right: 10px; width: 130px;">
            <el-option v-for="({ reviewerId, domainName }) in domainReviewerList" :key="reviewerId" :label="domainName"
              :value="reviewerId"></el-option>
          </el-select>
        </div>
      </div>
      <!-- 款项选择器 -->
      <standard-card ref="standardCard" :key="`base${loadingKeys}`" :clauseIds="clauseIds" :versionId="relation.autCsId"
        @selectChange="selectChange" @loaded="standardLoaded">
      </standard-card>
      <!-- 款项 -->
      <div v-show="clauseList.length" class="evaluation-content" :key="`ach${selectedKeys}`"
        :style="{ height: clauseItemHeight }">
        <el-form :key="loadingKeys" label-width="100px">
          <clause-item v-for="(clause) in clauseList" :clause="clause" :key="clause.clauseId"></clause-item>
        </el-form>
        <pagination v-show="total > limit" :total="total" :page.sync="pageNum" :limit.sync="limit" :pageSizes="[limit]" />
      </div>
    </el-card>
    <el-empty v-if="!clauseIds || !clauseIds.length" description="暂无数据"></el-empty>
  </div>
</template>

<script>
import request from "@/utils/request";
import evaluationMixin from "@/mixins/evaluation.vue";
// 组件
import SummaryShow from '@/views/hospitalSelfEvaluation/summaryShow/index2.vue'
export default {
  name: "TraineesIndex",
  mixins: [evaluationMixin],
  components: {
    SummaryShow
  },
  data() {
    return {
      loading: true,
      relation: {},
      detail: {
        hospitalName: ''
      },
      clauseIds: [],
      // 查询参数
      queryData: {
        distributeHospitalName: "",
        distributeStatus: "",
        domainAccountId: "",
        pageSize: 10,
        pageNum: 1,
      },
      // 分组选择器选项
      domainReviewerList: []
    };
  },
  mounted() {
    this.query()
  },
  methods: {
    query() {
      this.loading = true;
      let data = {
        accountId: this.$store.getters.userId + "",
        ...this.queryData,
        pageType: this.preliminary ? "sr_clause" : "sr_v_clause",
      };
      request({
        url: '/aut/sa/aud/queryList',
        method: "post",
        data,
      }).then((res) => {
        if (res.code === 200 && res.rows) {
          this.domainReviewerList = res.rows[0] ? res.rows[0].domainReviewerList : []
          this.clauseIds = res.rows[0] ? res.rows[0].distributeClauseIdList : []
          this.detail.hospitalName = res.rows[0] ? res.rows[0].hospitalName : []
          this.loadingKeys++
        }
      }).catch((error) => {
        console.log(error);
      }).finally(() => {
        this.loading = false;
      });
    },
    selectDomain(value) {
      this.queryData.domainAccountId = value || ''
      this.query()
    }
  }
};
</script>

<style lang="scss" scoped>
.box-card+.box-card {
  margin-top: 15px;
}

h4 {
  margin: 10px 0;
}

.clearfix {
  color: black;
}

.select-reviewer {
  ::v-deep .el-input__suffix span.el-input__suffix-inner>i {
    visibility: visible !important;
  }
}</style>
