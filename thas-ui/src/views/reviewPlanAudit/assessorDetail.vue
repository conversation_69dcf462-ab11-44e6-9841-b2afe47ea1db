<!--  -->
<template>
  <ReviewerDetailMessage></ReviewerDetailMessage>
</template>

<script>
import ReviewerDetailMessage from '@/views/auditManagement/reviewerAudit/detail.vue'
export default {
  name: 'assessorDetail',
  components: {
    ReviewerDetailMessage
  },
  props: {},
  watch: {},
  data() {
    return {
    };
  },

  computed: {},

  created() { },

  mounted() { },

  methods: {}
}

</script>
<style lang='scss' scoped>
</style>