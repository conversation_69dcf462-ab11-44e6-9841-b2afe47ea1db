<template>
  <div class="reviewer_detail_message">
    <detail-message :form="defaultForm" :showEdit="showEdit" v-if="isBaseAudit || genre1"
      @handelSave="(component) => handelSaveInfo('baseInfo', component)">
      <div slot="majorField" slot-scope="scope">
        <div v-show="scope.data.major.length > 0" v-for="(major, index) in scope.data.major" :key="index">
          {{ getMajorName(major) }}
          <span v-if="scope.data.majorMsg[major]">
            <span v-if="getMajorName(major)">，</span>
            <span v-if="major == 1">专业为：</span>
            {{ scope.data.majorMsg[major] }}
          </span>
        </div>
        <div v-show="scope.data.major.length === 0">-</div>
      </div>
      <BaseInfo class="block" slot="table" ref="baseInfo" :info="getBaseInfo"></BaseInfo>
    </detail-message>

    <detail-message :form="workMap" :showEdit="showEdit" :useDefault="false" v-if="isBaseAudit || genre1"
      @handelSave="(component) => handelSaveInfo('experience', component)">
      <el-table slot="useOther" :data="workExperienceList" stripe border style="margin-bottom: 20px;">
        <el-table-column label="时间起（年月）" align="center">
          <template slot-scope="scope">
            {{ scope.row.startTime }}
          </template>
        </el-table-column>
        <el-table-column label="时间止（年月）" align="center">
          <template slot-scope="scope">
            {{ scope.row.endTime == '9999-12' ? '至今' : scope.row.endTime }}
          </template>
        </el-table-column>
        <el-table-column label="工作单位" prop="company" align="center"></el-table-column>
        <el-table-column label="职务" prop="companyPost" align="center"></el-table-column>
      </el-table>
      <WorkExperience class="block" slot="table" ref="experience" :experiences="getExperience"></WorkExperience>
    </detail-message>

    <detail-message :form="certificate" :showEdit="showEdit" v-if="(isBaseAudit || genre1)"
      @handelSave="(component) => handelSaveInfo('qualification', component)">
      <QualificationCertificate class="block color" slot="table" ref="qualification" :info="getQualification">
      </QualificationCertificate>
    </detail-message>

    <detail-message :form="auditExpMap" :showEdit="showEdit" v-if="isBaseAudit || genre1"
      @handelSave="(component) => handelSaveInfo('review', component)">
      <StudyExperience class="block" slot="table" ref="review" :info="getStudyEx"></StudyExperience>
    </detail-message>

    <el-card v-if="type && (isAuthAudit || isBaseAudit) && isAdmin" shadow="never"
      style="border: 0; margin-bottom: 10px;">
      <div slot="header" class="clearfix">
        <span>审核结果</span>
      </div>
      <el-form ref="resultForm" label-width="180px" :model="resultForm" :rules="rules">
        <el-form-item label="审核结果：" prop="result">
          <el-select v-model.trim="resultForm.result" placeholder="请选择" clearable filterable>
            <el-option label="审核通过" value="1"></el-option>
            <el-option label="审核拒绝" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="appSkip === 1 && isAuthAudit && resultForm.result && resultForm.result == '1'"
          label="是否豁免参与实践培训：" prop="skipFlag">
          <el-radio-group v-model="resultForm.skipFlag">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核说明：" prop="remark" :rules="remark">
          <el-input type="textarea" v-model.trim="resultForm.remark" :rows="4" :maxlength="100"
            placeholder="请输入审核说明"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">确定</el-button>
          <el-button @click="back">返回</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <!-- <video controls preload="auto" src="http://*************:8080/profile/upload/2022/01/24/0ec05c610896cc20e37c35dcb6cd151c_bc158273-7b0e-4170-a19f-3f02b18a27c9.mp4"></video> -->
  </div>
</template>

<script>
let that = null;
import store from "@/store";
import request from "@/utils/request";

import BaseInfo from '@/views/reviewerCertification/components/baseInfo'
import WorkExperience from '@/views/reviewerCertification/components/workExperience'
import StudyExperience from '@/views/reviewerCertification/components/studyExperience'
import QualificationCertificate from '@/views/reviewerCertification/components/qualificationCertificate'
import { getToken } from '@/utils/auth'

export default {
  name: "ReviewerDetailMessage",
  components: {
    BaseInfo,
    WorkExperience,
    StudyExperience,
    QualificationCertificate
  },
  data() {
    return {
      url: {
        trainList: "/reviewer/query/cstOffline/training",
        submitReviewInfo: "/reviewer/bing/base/info/submit",
        submitReviewInfo2: "/reviewer/base/info/submit",
      },
      queryData: {
        pageNum1: 1,
        pageSize: 10,
        pageNum2: 1,
        pageSize2: 10,
      },
      total2: 0,
      total1: 0,
      defaultForm: {
        title: "基本信息",
        list: [],
        shadow: "never",
      },
      workMap: {
        title: '工作经历',
        line: 1,
        labelWidth: " auto",
        list: [],
        shadow: "never"
      },
      certificate: {
        title: "资质证书",
        line: 1,
        labelWidth: "auto",
        list: [],
        shadow: "never",
      },
      patentList: [
        // {
        //   title: "专利",
        //   list: [],
        //   shadow: 'never'
        // },
      ],
      auditExpMap: {
        title: '评审经历',
        line: 2,
        labelWidth: "auto",
        list: [],
        shadow: "never",
      },
      appSkip: null,
      appSkipDesc: "",
      resultForm: {
        result: null,
        remark: "",
        skipFlag: null,
      },
      rules: {
        result: [
          { required: true, message: "请选择审核结果", trigger: "change" },
        ],
        // remark: [
        //   { required: true, message: "请输入审核说明", trigger: "blur" },
        // ],
        skipFlag: [
          {
            required: true,
            message: "请选择是否豁免参与实践培训",
            trigger: "change",
          },
        ],
      },
      type: 0,
      accountId: "",
      reviewer: {},
      workExperienceList: [],
      trainingPrograms: [],
      trainingRecords: [],
      dataSource: [],
      queryData: {
        pageSize: 10,
        pageNum: 1,
        createId: this.$route.query.userName || this.$store.state.user.name,
      },
      total: 0,
      infoStatus: -1,
      authStatus: -1,
      englishWritOralOptions: [],
      certificateTypes: [],
      auditExpList: [],
      majorSupplementOptions: [],
      englishOralOptions: [],

      showDefault: {
        baseInfo: true,
      },

      fileDetails: {},

      auditInfo: {},
    };
  },
  watch: {
    $route: (to, from) => {
      if (to.path != "/auditManagement/reviewerDetailMessage") return;
      if (!this && !that) return;
      if (to.query.accountId != that.accountId || that.accountId == "") {
        that.getDetail();
      }
    },
  },
  computed: {
    showEdit() {
      return this.isBaseAudit
    },
    isSenior() {
      return this.$store.getters.roles.includes("senior-assessor");
    },

    isBaseAudit() {
      return this.infoStatus === 0;
    },

    isAuthAudit() {
      return this.authStatus === 0 && !this.isBaseAudit;
    },

    genre1() {
      return this.$route.query.genre == 1;
    },

    genre2() {
      return this.$route.query.genre == 2;
    },

    isArray() {
      return (value) => Array.isArray(value);
    },

    isAdmin() {
      return (
        this.$store.getters.roles.includes("admin") ||
        this.$store.getters.roles.includes("common-admin")
      );
    },

    english() {
      return (value) => {
        let en = this.englishWritOralOptions.find(
          ({ dictValue }) => dictValue == value
        );
        return en ? en.dictLabel : "";
      };
    },

    englishOral() {
      return (value) => {
        let en = this.englishOralOptions.find(
          ({ dictValue }) => dictValue == value
        );
        return en ? en.dictLabel : "";
      };
    },

    certificateText() {
      return value => {
        return (this.certificateTypes.find(({ dictValue }) => dictValue == value) || {}).dictLabel
      }
    },

    getMajorName() {
      return major => {
        if (major == 5) return ''
        return (this.majorSupplementOptions.find(({ dictValue }) => dictValue == major) || {}).dictLabel
      }
    },

    remark() {
      return [
        {
          required: this.resultForm.result == 2,
          message: "请输入审核说明",
          trigger: "blur"
        }
      ]
    },

    getInfo() {
      return type => {
        if (type === 'baseInfo') {
          return this.reviewer.reviewerBaseInfo
        }
        return {}
      }
    },

    getBaseInfo() {
      let { majorField = '', headPortrait, majorDirection, ...baseInfo } = this.reviewer?.reviewerBaseInfo || {}

      let majorFields = (majorField || '').split(',');
      let majorDirectionMap = {}
      if (majorDirection) {
        majorDirectionMap = JSON.parse(majorDirection)
      }
      return {
        headPortrait,
        majorField,
        majorFields,
        majorDirectionMap,
        ...JSON.parse(JSON.stringify(baseInfo))
      }
    },

    getExperience() {
      let workExperienceList = JSON.parse(JSON.stringify(this.workExperienceList))
      return workExperienceList
    },

    getStudyEx() {
      let checkList = (this.trainingPrograms || []).map(({ abilityDictValue }) => Number(abilityDictValue))
      let organization_zero_label = (this.trainingPrograms || []).find(({ abilityDictValue }) => abilityDictValue == 0)?.abilityName

      let hosRevDetail = JSON.parse(this.reviewer?.reviewerBaseInfo?.hosRevDetail || '[]');
      let reviewerTypeCheckList = hosRevDetail.map(({ reviewerTypeValue }) => reviewerTypeValue - 0)
      let reviewer_zero_hos_result = hosRevDetail.find(({ reviewerTypeValue }) => reviewerTypeValue == 0)?.hosRevOther || ''
      for (const key in this.auditInfo) {
        if (Object.hasOwnProperty.call(this.auditInfo, key)) {
          if ((key.includes('Num') || key.includes('Year')) && !this.auditInfo[key]) {
            this.$set(this.auditInfo, key, undefined)
          }
        }
      }
      return {
        reviewerTypeCheckList,
        organization_zero_label,
        reviewer_zero_hos_result,
        checkList,
        ...this.auditInfo
      }
    },

    getQualification() {
      return {
        list: this.reviewer.certificateList || []
      }
    }
  },
  created() {
    that = this;
    this.type = this.$route.query.type;
    this.getDetail();

    this.getDicts("knowledge_level").then((response) => {
      this.englishWritOralOptions = response.data;
    });

    this.getDicts("certificate_type").then((response) => {
      this.certificateTypes = response.data;
    });

    this.getDicts("reviewer_major").then((response) => {
      this.majorSupplementOptions = response.data;
    });

    this.getDicts("speaking_level").then((response) => {
      this.englishOralOptions = response.data;
    });

  },
  methods: {
    testQuery() {
      request({
        url: "/system/sheet/list",
        method: "get",
        params: {
          ...this.queryData,
          state: 1,
        },
      }).then((res) => {
        this.dataSource = res.rows || [];
        this.total1 = res.total;
      });
    },

    getDetail() {
      this.accountId =
        this.$route.query.accountId || this.$store.getters.userId;
      request({
        url: "/reviewer/query/reviewer/detail",
        method: "post",
        data: { commonId: this.accountId },
      }).then((res) => {
        this.reviewer = res;
        if (this.reviewer.reviewerBaseInfo) {
          this.appSkip = this.reviewer.reviewerBaseInfo.appSkip;
          this.appSkipDesc = this.reviewer.reviewerBaseInfo.appSkipDesc || "";
          this.infoStatus = this.reviewer.reviewerBaseInfo.infoStatus;
          this.authStatus = this.reviewer.reviewerBaseInfo.authStatus;
          this.testQuery(this.reviewer.reviewerBaseInfo.userName);
        }
        this.workExperienceList = res.workExperienceList || [];
        this.trainingPrograms = res.certificateAbilityList || [];
        this.makeForm(res)
        this.makeForm2(res)
        this.$set(this, 'fileDetails', res.fileDetails)
        this.certificate.list = (res.certificateList || []).map(
          (item, index) => {
            let files = res.fileDetails[item.certificateId];
            let file = files && files[0] ? files[0] : {};
            return {
              label: item.certificate,
              value: item.fileName,
              id: item.certificateId,
              url: file.url,
              type: "file",
            };
          }
        );
      });

      this.trainQuery();
    },

    trainQuery() {
      request({
        url: this.url.trainList + `?participatesTraining=0`,
        method: "post",
        params: this.queryData,
        data: {
          participatesTraining: 0,
          accountId: this.$route.query.accountId || this.$store.getters.userId,
        },
      })
        .then((res) => {
          if (res.code == 200) {
            this.trainingRecords = res.rows;
            this.total2 = res.total;
          }
        })
        .catch((error) => {
          // console.log(error);
        });
    },

    makeForm2(res) {
      let { reviewerBaseInfo } = res;
      let {
        hosRevIs,
        learnExperience,
        hosRevDetail,
        organization,
        reviewHospitalIs,
        reviewHospitalCoach,
        coachHospitalNum,
        trainReviewerIs,
        trainNum,
        trainOrganization,
        otherExperience,
        reviewHospitalYear,
        reviewHospitalNum,
        reviewMajor,
        trainWarning,
      } = reviewerBaseInfo;

      this.auditInfo = {
        hosRevIs,
        learnExperience,
        hosRevDetail,
        organization,
        reviewHospitalIs,
        reviewHospitalCoach,
        coachHospitalNum,
        trainReviewerIs,
        trainNum,
        trainOrganization,
        otherExperience,
        reviewHospitalYear,
        reviewHospitalNum,
        reviewMajor,
        trainWarning
      }

      this.auditExpList = [];

      this.auditExpList.push({
        label: "是否参加过国际认证/医院等级评审评审员培训",
        value: learnExperience == 1 ? "是" : "否",
        lineHeight: '19px'
      });

      this.auditExpList.push({
        label: "是否是医院评审评价评审员",
        value: hosRevIs == 1 ? "是" : "否",
      });

      if (learnExperience == 1) {
        this.auditExpList.push({
          label: "参加过哪个标准的培训",
          // labelWidth: '200px',
          itemStyle: {
            alignItems: 'flex-start'
          },
          style: {
            whiteSpace: 'pre-wrap',
            lineHeight: ' 36px',
            minWidth: '400px',
          },
          value: this.trainingPrograms.map(pro => {
            return pro.abilityName
          }).join(',' + `\n`),
        });
      }

      if (hosRevIs == 1) {
        let hosRevDetailMap = JSON.parse(hosRevDetail || '{}')
        this.auditExpList.push({
          label: "具体为",
          // labelWidth: '200px',
          itemStyle: {
            alignItems: 'flex-start'
          },
          style: {
            whiteSpace: 'pre-wrap',
            lineHeight: ' 36px',
            minWidth: '400px',
          },
          value: hosRevDetailMap.map(({ hosRevOther }) => hosRevOther).join('，' + `\n\r`),
        });
      }

      this.auditExpList.push({
        label: "是否评审过医院",
        value: reviewHospitalIs == 1 ? "是" : "否",
      });

      if (reviewHospitalIs == 1) {
        // this.auditExpList.push({
        //   label: "医院评审jj",
        //   value: `有${reviewHospitalYear}年评审经验，评审过${reviewHospitalNum}家医院,专业方向为：${reviewMajor}`
        //     })
        this.auditExpList.push(
          ...[
            {
              label: "评审年限",
              // labelWidth: '200px',
              value: reviewHospitalYear ? `${reviewHospitalYear}年` : '-',
            },
            {
              label: "评审次数",
              value: reviewHospitalNum || reviewHospitalNum === 0 ? `${reviewHospitalNum}次` : '-',
            },
            {
              label: "专业方向",
              // labelWidth: '200px',
              value: reviewMajor || '-',
            },
          ]
        );
      }

      this.auditExpList.push({
        label: "是否对医院进行标准/评审辅导",
        value: reviewHospitalCoach == 1 ? "是" : "否",
      });

      if (reviewHospitalCoach == 1) {
        this.auditExpList.push({
          label: "辅导场次",
          // labelWidth: '200px',
          value: coachHospitalNum || coachHospitalNum === 0 ? `${coachHospitalNum}次` : '-',
        });
      }

      this.auditExpList.push({
        label: "是否对评审员进行过培训",
        value: trainReviewerIs == 1 ? "是" : "否",
      });

      if (trainReviewerIs == 1) {
        this.auditExpList.push(
          ...[
            {
              label: "培训场次",
              // labelWidth: '200px',
              value: trainNum || trainNum === 0 ? `${trainNum}次` : '-',
            },
            {
              label: "举办的培训单位/组织",
              value: trainOrganization,
            },
          ]
        );
      }

      this.auditExpList.push(...[{
        label: "其他相关经历",
        // labelWidth: '200px',
        value: otherExperience || '-',
      }, {
        label: "对培训、工作安排及生活照顾需要中心特别注意的事项",
        span: 24,
        value: trainWarning || '-',
      }]);
      this.auditExpMap.clz = 'wordBreak'
      this.auditExpMap.list = this.auditExpList.map(item => {
        item.type = "text";
        return item
      });

    },

    makeForm(res) {
      let fileDetails = res.fileDetails;

      this.defaultForm.list = [
        {
          label: "姓名",
          value: res.reviewerBaseInfo.reviewerName + (res.reviewerBaseInfo.firstBatch == 1 ? '(首批评审员)' : ''),
          type: "text",
          slot: "",
        },
        {
          label: "用户名",
          value: res.reviewerBaseInfo.userName,
          type: "text",
          slot: "",
        },
        {
          label: "性别",
          value:
            res.reviewerBaseInfo.reviewerGender === 0
              ? "男"
              : res.reviewerBaseInfo.reviewerGender === 1
                ? "女"
                : "未知",
          type: "text",
          slot: "",
        },
        {
          label: "出生年月",
          value: res.reviewerBaseInfo.reviewerBirthday,
          type: "text",
          slot: "",
        },
        {
          label: "证件类型",
          value: this.certificateText(res.reviewerBaseInfo.certificateType),
          type: "text",
          slot: "",
        },
        {
          label: "证件号码",
          value: res.reviewerBaseInfo.certificateNumber,
          type: "text",
          slot: "",
        },
        {
          label: "手机号",
          value: res.reviewerBaseInfo.reviewerMobile,
          type: "text",
          slot: "",
        },
        {
          label: "邮箱",
          value: res.reviewerBaseInfo.reviewerEmail,
          type: "text",
          slot: "",
        },
        {
          label: "工作单位",
          value: res.reviewerBaseInfo.company,
          type: "text",
          slot: "",
        },
        {
          label: "现任职务",
          value: res.reviewerBaseInfo.companyPost,
          type: "text",
          slot: "",
        },
        {
          label: "英语水平",
          value: `口语${this.englishOral(
            res.reviewerBaseInfo.englishOral
          )} ， 写作${this.english(res.reviewerBaseInfo.englishWrit)}`,
          type: "text",
          slot: "",
        },
        {
          label: "现居住地",
          value: res.reviewerBaseInfo.liveAddress,
          type: "text",
          slot: "",
          // span: 24,
        },
        {
          hidden: this.isSenior,
          label: "专业领域",
          value: {
            major: res.reviewerBaseInfo.majorField ? (res.reviewerBaseInfo.majorField || '').split(',') : [],
            majorMsg: JSON.parse(res.reviewerBaseInfo.majorDirection || '{}')
          },
          type: "text",
          slot: "majorField",
        },
        {
          label: "个人简介",
          value: res.reviewerBaseInfo.profile || '--',
          type: "text",
          slot: "",
          span: 24
        },
      ];

      if (res.reviewerBaseInfo.infoStatus == 1 && res.reviewerBaseInfo.submitStatus == 1) {
        let list = [
          {
            label: "审核状态",
            value: res.reviewerBaseInfo.authStatus == 0 ? '待评估' : res.reviewerBaseInfo.authStatus == 1 ? '审核通过' : '审核拒绝',
            type: "text",
            style: {
              color: res.reviewerBaseInfo.authStatus == 0 ? '#E6A23C' : res.reviewerBaseInfo.authStatus == 1 ? '#67C23A' : '#F56C6C'
            }
          },
          {
            label: "审核说明",
            value: res.reviewerBaseInfo.authDesc || '-',
            type: "text",
          },
        ]

        this.defaultForm.list.unshift(...list)
      }

      if (res.reviewerBaseInfo.headPortrait) {
        this.defaultForm.list.push({
          label: '头像',
          value: fileDetails[res.reviewerBaseInfo.headPortrait][0]?.url,
          type: 'image',
          span: 24
        })
      }
    },

    onSubmit() {
      if (this.isBaseAudit) {
        this.onBaseSubmit();
      } else if (this.isAuthAudit) {
        this.onAuthSubmit();
      }
    },

    onBaseSubmit() {
      this.$refs.resultForm.validate((valid) => {
        if (valid) {
          request({
            url: "/reviewer/base/info/submit",
            method: "post",
            data: {
              reviewerBaseInfo: {
                accountId: this.reviewer.reviewerBaseInfo.accountId,
                submitStatus: 3,
                infoStatus: this.resultForm.result,
                remark: this.resultForm.remark,
                authStatus: this.resultForm.result == 1 ? '0' : '2',
              },
              role: "101",
              audId: store.getters.userId,
            },
          }).then((res) => {
            res.code == 200 && this.back();
          });
        } else {
          return false;
        }
      });
    },

    onAuthSubmit() {
      this.$refs.resultForm.validate((valid) => {
        if (valid) {
          request({
            url: "/aut/record/info/submitAutRecord",
            method: "post",
            data: {
              accountId: this.reviewer.reviewerBaseInfo.accountId,
              role: "101",
              audId: store.getters.userId,
              audDesc: this.resultForm.remark,
              audResult: this.resultForm.result,
              skipFlag: this.resultForm.skipFlag,
            },
          }).then((res) => {
            res.code == 200 && this.back();
          });
        } else {
          return false;
        }
      });
    },

    async handelSaveInfo(type, component) {
      try {
        let ref = this.$refs[type]
        if (!ref) return;
        let value = await ref.getValue();
        if (!value) return;
        let params = null
        if (type === 'baseInfo') {
          const { fields, ...data } = value;
          this.reviewerBaseInfo = {
            accountId: this.reviewer.reviewerBaseInfo.accountId,
            ...data,
          };
          params = {
            reviewerBaseInfo: this.reviewerBaseInfo,
            fields,
          }
        } else if (type === 'experience') {
          let list = [...value];
          params = {
            reviewerBaseInfo: this.reviewer.reviewerBaseInfo,
            workExperienceList: list,
          }
        } else if (type == 'review') {
          const { organizationList, hosRevDetail, data } = value;
          let certificateAbilityList = [...organizationList];
          let reviewerBaseInfo = {
            ...this.reviewer.reviewerBaseInfo,
            ...data,
            hosRevDetail: JSON.stringify(hosRevDetail),
          };
          params = {
            certificateAbilityList,
            reviewerBaseInfo: reviewerBaseInfo,
          }
        }
        else if (type == 'qualification') {
          let certificateList = [...value];
          params = {
            reviewerBaseInfo: this.reviewer.reviewerBaseInfo,
            certificateList,
          }
        }

        if (!params) return;

        await this.saveToReviewerInfo(params);
        // console.log(component);
        if (component) {
          component.hide()
        }
        this.getDetail();
      } catch (error) {
        // console.log('handelSaveInfo', error);
      }
    },

    saveToReviewerInfo(data) {
      return request({
        url: getToken() ? this.url.submitReviewInfo2 : this.url.submitReviewInfo,
        method: "post",
        data,
      })
    },

    back() {
      this.$store.dispatch("tagsView/delView", this.$route) &&
        this.$router.push("/auditManagement/reviewerAudit");
    },
  },
};
</script>

<style lang='scss' scoped>
::v-deep .review-experience {
  &.el-form {
    .el-form-item {
      margin-bottom: 10px;
      margin-right: 0;

      .el-form-item__content {
        width: calc(100% - 240px);
        line-height: 24px;
        margin-top: 7px;

        span {
          word-break: break-all;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.block .el-form-item {
  display: block !important;
}

.color.block {
  padding: 0;
}

.color .el-button--primary span {
  color: white !important;
}

.work-experience.block {
  padding: 0;
  margin-bottom: 20px;
}
</style>
