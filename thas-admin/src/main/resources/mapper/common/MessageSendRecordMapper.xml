<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.MessageSendRecordMapper">

    <resultMap type="MessageSendRecord" id="MessageSendRecordResult">
        <result property="id"    column="id"    />
        <result property="content"    column="content"    />
        <result property="sendType"    column="send_type"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMessageSendRecordVo">
        select id, content, send_type, status, create_by, create_time, update_by, update_time from message_send_record
    </sql>

    <select id="selectMessageSendRecordList" parameterType="MessageSendRecord" resultMap="MessageSendRecordResult">
        <include refid="selectMessageSendRecordVo"/>
        <where>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="sendType != null  and sendType != ''"> and send_type = #{sendType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectMessageSendRecordById" parameterType="Long" resultMap="MessageSendRecordResult">
        <include refid="selectMessageSendRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertMessageSendRecord" parameterType="MessageSendRecord"  useGeneratedKeys="true" keyProperty="id">
        insert into message_send_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="sendType != null and sendType != ''">send_type,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="sendType != null and sendType != ''">#{sendType},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMessageSendRecord" parameterType="MessageSendRecord">
        update message_send_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="sendType != null and sendType != ''">send_type = #{sendType},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMessageSendRecordById" parameterType="Long">
        delete from message_send_record where id = #{id}
    </delete>

    <delete id="deleteMessageSendRecordByIds" parameterType="String">
        delete from message_send_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
