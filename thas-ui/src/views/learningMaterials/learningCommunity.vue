<template>
  <div v-loading="loading">
    <el-card class="learning-community">
      <div class="page-title">
        <span>学习社区</span>
      </div>
      <div class="learning-publish">
        <el-input v-model.trim="textarea" type="textarea" :rows="4" maxlength="20000" placeholder="请输入文章内容"></el-input>
        <div class="img-list">
          <div v-for="(item, index) in fileList" :key="index" class="img-item">
            <el-image style="width: 100px; height: 100px" :src="item.response.data.url" :preview-src-list="[item.response.data.url]" fit="cover"></el-image>
            <div class="close-btn" @click="removeImg(item)">
              <i class="el-icon-close"></i>
            </div>
          </div>
        </div>
        <div style="text-align: right; display: flex; justify-content: space-between; padding: 20px;">
          <el-upload :action="action" :headers="headers" :data="{ type: '1' }" :disabled="fileList.length >= 9" multiple :limit="9" accept=".JPG,.JPEG,.PNG" :file-list="fileList" :before-upload="handleBeforeUpload" :on-success="handleSuccess" :show-file-list="false">
            <el-button circle icon="el-icon-picture" :disabled="fileList.length >= 9"></el-button>
          </el-upload>
          <el-button type="primary" icon="el-icon-document" @click="publish" :disabled="!textarea">发布文章</el-button>
        </div>
      </div>
      <div v-for="(item, index) in messageList" :key="index" class="message-item">
        <div class="item-title">
          <div class="left">
            <el-avatar size="medium" icon="el-icon-user-solid"></el-avatar>
            <div>
              <div class="name">{{item.createBy}}</div>
              <div class="create-time" style="color: #999999;">{{item.createTime}}</div>
            </div>
          </div>
        </div>
        <div class="item-content">
          <div class="article">
            <pre class="text">{{ item.content }}</pre>
            <div class="img-list">
              <div v-for="(urlItem, i) in item.coverUrl" :key="i" class="img-item">
                <div class="img-box">
                  <el-image style="width: 100%; height: 100%; position: absolute;" :src="urlItem.url" :preview-src-list="[urlItem.url]" fit="cover"></el-image>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="options">
          <div class="comment">
            <i class="el-icon-chat-dot-round" @click="item.showCommentInput = !item.showCommentInput"></i><span>{{ item.replyNum }}</span>
          </div>
          <div class="like">
            <svg v-if="item.doLike" fill="#ff8b2a" style="cursor: pointer;" xmlns="http://www.w3.org/2000/svg" class="svg-icon-path-icon fill" viewBox="0 0 32 32" width="18" height="18" @click="like(item)">
              <path d="M10.663 28.441v-15.996c0.54-0.195 0.93-0.362 1.17-0.499 0.672-0.385 1.242-0.909 1.71-1.572 0.71-1.006 0.842-1.47 1.613-4.287 0.051-0.185 0.152-0.438 0.306-0.759 0.708-1.187 1.717-1.78 3.025-1.777 1.264 0 2.275 0.592 3.034 1.777 0.189 0.327 0.323 0.692 0.401 1.096s0.079 0.867 0 1.39l-1.399 4.883c-0.025 0.078-0.025 0.134 0 0.17 0.027 0.039 0.066 0.058 0.117 0.058h4.251l-0.005 0.010h0.554c1.65 0 3 1.3 3 2.95 0 0.25-0.050 0.5-0.1 0.75l-1.496 9.555c-0.3 1.3-1.5 2.25-2.9 2.25h-13.282zM8.885 28.441h-3.556c-0.982 0-1.778-0.796-1.778-1.778v-12.444c0-0.982 0.796-1.778 1.778-1.778h3.556v16z"></path>
            </svg>
            <span v-if="item.doLike" style="color: #ff8b2a;">{{item.likeNum}}</span>
            <svg v-if="!item.doLike" style="cursor: pointer;" xmlns="http://www.w3.org/2000/svg" class="svg-icon-path-icon fill" viewBox="0 0 32 32" width="18" height="18" @click="like(item)">
              <path d="M10.663 12.445c0.54-0.195 0.93-0.362 1.17-0.499 0.672-0.385 1.242-0.909 1.71-1.572 0.71-1.006 0.842-1.47 1.613-4.287 0.051-0.185 0.152-0.438 0.306-0.759 0.708-1.187 1.717-1.78 3.025-1.777 1.264 0 2.275 0.592 3.034 1.777 0.189 0.327 0.323 0.692 0.401 1.096s0.079 0.867 0 1.39l-1.399 4.883c-0.025 0.078-0.025 0.134 0 0.17 0.027 0.039 0.066 0.058 0.117 0.058h4.251l-0.005 0.010h0.554c1.65 0 3 1.3 3 2.95 0 0.25-0.050 0.5-0.1 0.75l-1.496 9.555c-0.3 1.3-1.5 2.25-2.9 2.25h-18.615c-0.982 0-1.778-0.796-1.778-1.778v-12.444c0-0.982 0.796-1.778 1.778-1.778l5.333 0.004zM10.663 14.409v12.254h13.261c0.45 0 0.85-0.3 0.95-0.75 1.193-6.368 1.789-9.694 1.789-9.977 0-0.55-0.45-1-1-1h-1.725l-0.009 0.018h-3.238c-0.714 0-1.28-0.245-1.699-0.736-0.628-0.736-0.59-1.383-0.362-2.204 0.521-1.87 0.962-3.489 1.323-4.857-0.010-0.96-0.451-1.5-1.323-1.622s-1.448 0.358-1.728 1.44l-0.745 2.583c-0.853 2.302-2.52 3.855-5.002 4.66l-0.492 0.19zM5.33 26.663h3.556v-12.444h-3.556v12.444z"></path>
            </svg>
            <span v-if="!item.doLike">{{item.likeNum}}</span>
          </div>
        </div>
        <div v-show="item.showCommentInput" class="send-comment">
          <el-avatar size="medium" src=""></el-avatar>
          <el-input :maxlength="500" v-model.trim="item.comment" type="textarea" style="flex: 1;" :autosize="{ minRows: 5 }" placeholder="发布你的评论"></el-input>
        </div>
        <div v-show="item.showCommentInput" class="send-comment-btn">
          <el-button type="primary" @click="sendComment(item)">评论</el-button>
        </div>
        <div class="comment-list">
          <div v-for="(commentItem) in item.list" :key="commentItem.id" class="comment-item">
            <div class="top">
              <el-avatar size="small" src=""></el-avatar>
              <div>
                <span class="user-name">{{ commentItem.createBy }}</span>
                <span class="comment-time">{{ commentItem.createTime }}</span>
              </div>
            </div>
            <div class="bottom">{{ commentItem.content }}</div>
          </div>
        </div>
      </div>
      <el-empty v-if="!messageList || !messageList.length" :image-size="200"></el-empty>
      <div style="margin-top: 20px; text-align: center;">
        <el-pagination background layout="prev, pager, next" :total="total" @current-change="handleCurrentChange"></el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import request from "@/utils/request";
export default {
  name: "LearningCommunity",
  data() {
    return {
      queryData: {
        pageSize: 10,
        pageNum: 1,
      },
      messageList: [],
      total: 0,
      comment: "",
      loading: false,
      textarea: "",
      fileList: [],
      action: `${
        localStorage.baseUrl || process.env.VUE_APP_BASE_API
      }/common/uploadFile`,
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      accept: ".JPG,.JPEG,.PNG",
    };
  },
  created() {
    this.query();
  },
  methods: {
    query() {
      this.loading = true;
      request({
        url: "/system/community/list",
        method: "get",
        params: this.queryData,
      })
        .then((res) => {
          this.loading = false;
          if (res.code != 200) return;
          for (let index = 0; index < res.rows.length; index++) {
            res.rows[index].showCommentInput = false;
            res.rows[index].comment = "";
          }
          this.messageList = res.rows;
          this.total = res.total;
        })
        .catch((error) => {
          // console.log(error);
          this.loading = false;
        });
    },
    // 评论
    sendComment(item) {
      if (!item.comment) {
        return;
      }
      request({
        url: "/system/community",
        method: "post",
        data: {
          pid: item.id,
          content: item.comment,
        },
      }).then((res) => {
        if (res.code != 200) return;
        item.comment = "";
        item.showCommentInput = !item.showCommentInput;
        this.query();
      });
    },
    like(item) {
      request({
        url: "/system/community",
        method: "get",
        params: {
          id: item.id,
          type: item.doLike == 0 ? 1 : 0,
        },
      }).then((res) => {
        if (res.code != 200) return;
        this.query();
      });
    },
    handleCurrentChange(val) {
      this.queryData.pageNum = val;
      this.query();
    },
    toPublish() {
      this.$router.push({ path: "/learningMaterials/publishArticle" });
    },
    toDetail(item) {
      this.$router.push({
        path: "/learningMaterials/articleDetail",
        query: {
          id: item.id,
        },
      });
    },
    handleSuccess(response, file, fileList) {
      this.fileList = fileList;
    },
    removeImg(file) {
      for (let index = 0; index < this.fileList.length; index++) {
        if (
          file.response.data.fileId == this.fileList[index].response.data.fileId
        ) {
          this.fileList.splice(index, 1);
          break;
        }
      }
    },
    publish() {
      if (!this.textarea) {
        return;
      }
      let cover = "";
      for (let index = 0; index < this.fileList.length; index++) {
        cover = cover + this.fileList[index].response.data.fileId + ",";
      }
      cover.slice(0, cover.length - 1);
      request({
        url: "/system/community",
        method: "post",
        data: { cover, content: this.textarea },
      }).then((res) => {
        if (res.code != 200) return;
        this.textarea = "";
        this.fileList = [];
        this.query();
      });
    },
    handleBeforeUpload(file) {
      const list = this.accept
        .split(",")
        .map((accept) => accept.toLocaleUpperCase());
      const suffix = "." + file.name.split(".").pop().toLocaleUpperCase();
      if (!list.includes(suffix)) {
        this.$message({
          type: "warning",
          message: "请上传指定格式的文件！",
        });
        return false;
      }
      if (file.size / 1024 / 1024 > 50) {
        this.$message({
          type: "warning",
          message: "上传的文件大小不得超过50M",
        });
        return false;
      }
    },
  },
};
</script>

<style lang='scss' scoped>
.learning-community {
  margin-bottom: 20px;
  ::v-deep .el-dialog {
    .el-dialog__header {
      background-color: #3c81f5;
      padding-top: 10px;
      .el-dialog__title {
        color: white;
      }
      .el-dialog__headerbtn {
        top: 15px;
        .el-dialog__close {
          color: white;
        }
      }
    }
    .el-dialog__body {
      padding-bottom: 1px;
      & > span {
        color: black;
      }
    }
  }
  .page-title {
    // display: flex;
    // justify-content: space-between;
    // align-items: center;
    border-bottom: 1px solid #d2d4d6;
    font-size: 18px;
    font-weight: bolder;
    line-height: 50px;
  }
  .message-item {
    margin-top: 20px;
    border-bottom: 1px solid #d2d4d6;
    .item-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      .left {
        display: flex;
        align-items: center;
        font-weight: bold;
        .el-avatar {
          background: #014099;
        }
        .name {
          flex: 1;
          margin-left: 10px;
          margin-bottom: 5px;
          font-size: 16px;
        }
        .create-time {
          margin-left: 10px;
          font-size: 12px;
        }
      }
      .right {
        display: flex;
        align-items: center;
        color: #999999;
        cursor: pointer;
      }
    }
    .item-content {
      margin-left: 46px;
      .article {
        margin-left: 10px;
        font-weight: bolder;
        font-size: 16px;
        .title {
          margin-bottom: 10px;
          font-size: 18px;
          line-height: 30px;
        }
        .text {
          line-height: 30px;
          white-space: pre-wrap;
          word-break: break-word;
        }
        .img-list {
          max-width: 800px;
          display: flex;
          flex-wrap: wrap;
          margin-top: 20px;
          margin-bottom: 20px;
          .img-item {
            width: 250px;
            display: flex;
            border-radius: 5px;
            margin-right: 10px;
            margin-top: 10px;
            overflow: hidden;
            position: relative;
            .img-box {
              width: 100%;
              height: 0px;
              padding-bottom: 100%;
            }
          }
        }
      }
    }
    .options {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-bottom: 20px;
      margin-left: 60px;
      .comment {
        margin-right: 130px;
      }
    }
    .send-comment {
      display: flex;
      margin-bottom: 20px;
      margin-left: 60px;
      .el-avatar {
        margin-right: 15px;
      }
    }
    .send-comment-btn {
      text-align: right;
    }
    .comment-list {
      margin-left: 60px;
      .comment-item {
        .top {
          display: flex;
          align-items: center;
          .user-name {
            margin-left: 10px;
            margin-right: 10px;
            font-size: 16px;
            font-weight: bold;
          }
          .comment-time {
            font-size: 14px;
            color: #999999;
          }
          .right {
            color: #999999;
            cursor: pointer;
          }
        }
        .bottom {
          margin-top: 20px;
          margin-left: 46px;
          margin-bottom: 20px;
          font-size: 14px;
          font-weight: bold;
        }
      }
    }
  }
}
.learning-publish {
  margin-top: 20px;
  .img-list {
    display: flex;
    .img-item {
      display: flex;
      border-radius: 5px;
      margin-right: 10px;
      margin-top: 10px;
      overflow: hidden;
      position: relative;
      .close-btn {
        width: 16px;
        height: 16px;
        position: absolute;
        top: 5px;
        right: 5px;
        color: #fff;
        cursor: pointer;
        background-color: rgb(204 204 204 / 50%);
        border-radius: 50%;
      }
    }
  }
}
</style>
