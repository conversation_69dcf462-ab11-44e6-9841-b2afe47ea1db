<template>
  <div v-loading="loading" class="reviewer-audit">
    <el-row :gutter="24">
      <el-col :span="24" :xs="24">
        <el-form ref="elForm" :model="queryParams" size="small" label-width="80px" inline>
          <el-form-item label="姓名" prop="reviewerName">
            <el-input v-model.trim="queryParams.reviewerName" placeholder="请输入姓名" clearable :style="{ width: '100%' }" :maxlength="20">
            </el-input>
          </el-form-item>

          <el-form-item label="工作单位" prop="company">
            <el-input v-model.trim="queryParams.company" placeholder="请输入工作单位" clearable :style="{ width: '100%' }" :maxlength="20">
            </el-input>
          </el-form-item>

          <el-form-item label="现任职务" prop="companyPost">
            <el-input v-model.trim="queryParams.companyPost" placeholder="请输入现任职务" clearable :style="{ width: '100%' }" :maxlength="20">
            </el-input>
          </el-form-item>

          <el-form-item label="审核状态" prop="authStatus">
            <el-select v-model.trim="queryParams.authStatus" clearable filterable>
              <el-option v-for="(option, index) in authStatusOptions" :key="index" :value="option.dictValue" :label="option.dictLabel"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label=" ">
            <el-button type="primary" size="small" @click="query(1)">
              查询
            </el-button>
            <el-button type="primary" size="small" @click="resetForm">
              重 置
            </el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

    <el-table :data="dataSource" border>
      <el-table-column label="操作" width="100">
        <template slot-scope="scope">
          <el-button type="text" @click="toDetail(scope.row, 1)">转为评审员</el-button>
          <el-button type="text" @click="toDetail(scope.row, 1)" v-if="!scope.row.authStatus">审核</el-button>
          <el-button type="text" @click="toDetail(scope.row)" v-else>查看详情</el-button>
          <el-button type="text" v-if="scope.row.authStatus == 1" @click="allotField(scope.row)">分配分组</el-button>
        </template>
      </el-table-column>
      <el-table-column label="姓名" prop="reviewerName"> </el-table-column>
      <el-table-column label="工作单位" prop="company"> </el-table-column>
      <el-table-column label="现任职务" width="130" prop="companyPost">
      </el-table-column>
      <el-table-column label="现居住地" prop="liveAddress"> </el-table-column>
      <el-table-column label="专业领域">
        <template slot-scope="scope">
          <div v-if="
              scope.row.reviewerFieldInfoVOList &&
              scope.row.reviewerFieldInfoVOList.length
            ">
            <span v-for="(info, index) in scope.row.reviewerFieldInfoVOList" :key="showId(info,index)" style="margin-right: 10px">{{ showName(info) }}</span>
          </div>
          <span v-else> -- </span>
        </template>
      </el-table-column>
      <el-table-column label="审核人" prop="authPersonName" width="130">
      </el-table-column>
      <el-table-column label="审核状态" prop="authStatus" width="100">
        <template slot-scope="scope">
          <span v-if="!scope.row.authStatus">待审核</span>
          <span v-if="scope.row.authStatus == 1" style="color: green">审核通过</span>
          <span v-if="scope.row.authStatus == 2" style="color: red">审核拒绝</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="query" />

    <el-dialog title="分配分组" :visible.sync="fieldDialogVisible" width="60%">
      <div>
        <p>所属分组：</p>
        <el-checkbox-group v-model.trim="checkList">
          <el-checkbox v-for="(item, index) in fieldCodeOptions" :key="index" :label="item.id">{{ item.groupName }}</el-checkbox>
        </el-checkbox-group>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="fieldDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitField">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import request from "@/utils/request";
let that;
export default {
  name: "ReviewerAudit",
  components: {},
  props: [],
  data() {
    return {
      queryParams: {
        reviewerName: undefined,
        company: undefined,
        companyPost: undefined,
        authStatus: undefined,
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      url: {
        list: "/reviewer/query/reviewer/list",
      },
      dataSource: [],
      authStatusOptions: [],
      allReady: false,
      fieldDialogVisible: false,
      checkList: [],
      fieldCodeOptions: [],
      currentRow: {},
      loading: false
    };
  },
  computed: {
    showName() {
      return (info) => {
        return info ? info.fieldName : ''
      }
    },
    showId() {
      return (info, index) => {
        return info ? info.id : index * Math.ceil(Math.random() * 100)
      }
    }
  },
  watch: {
    $route: (to) => {
      if (!that || !that.allReady) return;
      if (to.path != "/auditManagement/reviewerAudit") return;
      that.query();
      that.getDomainList();
    },
  },
  created() { },
  mounted() {
    this.initDicts();
    this.query();
    this.getDomainList();
    that = this;
    this.allReady = true;
  },
  methods: {
    initDicts() {
      this.getDicts("reviewer_auth_status").then((res) => {
        this.authStatusOptions = res.data;
      });
    },

    getDomainList() {
      request({
        url: "/system/domain/list?state=1",
        method: "get",
      }).then((res) => {
        res.code == 200 && (this.fieldCodeOptions = res.rows);
      });
    },

    submitForm() {
      this.$refs["elForm"].validate((valid) => {
        if (!valid) return;
        // TODO 提交表单
      });
    },
    resetForm() {
      this.$refs["elForm"].resetFields();
      this.queryParams.pageNum = 1
      this.query()
    },

    query(arg) {
      if (arg && !isNaN(arg)) {
        this.queryParams.pageNum = arg;
      }
      this.loading = true
      request({
        url:
          this.url.list +
          `?pageNum=${this.queryParams.pageNum}&pageSize=${this.queryParams.pageSize}`,
        method: "post",
        data: this.queryParams,
      }).then((res) => {
        this.loading = false
        if (res.code == 200) {
          let fields = [
            "reviewerName",
            "company",
            "companyPost",
            "authPersonName",
          ];
          this.dataSource = res.rows.map((row) => {
            for (const key in row) {
              if (Object.hasOwnProperty.call(row, key)) {
                if (!row[key] && row[key] != 0 && fields.includes(key)) {
                  row[key] = "--";
                }
              }
            }
            return row;
          });
          this.total = res.total;
        }
      }).catch((error) => {
        // console.log(error)
        this.loading = false
      })
    },
    toDetail(data, type) {
      this.$router.push({
        path: "reviewerDetailMessage",
        query: { accountId: data.accountId, type },
      });
    },

    allotField(row) {
      this.currentRow = row;
      this.$set(
        this,
        "checkList",
        row.reviewerFieldInfoVOList.map((field) => field.id)
      );
      this.fieldDialogVisible = true;
    },

    submitField() {
      if (this.checkList.length == 0) {
        this.$message({
          type: "warning",
          message: "请选择分组!",
        });
        return;
      }
      let fields = this.checkList.filter(id => this.fieldCodeOptions.some(field => field.id == id))
      const data = {
        fields,
        reviewerBaseInfo: {
          accountId: this.currentRow.accountId,
        },
      };
      request({
        url: "/reviewer/base/info/submit",
        method: "post",
        data,
      })
        .then((res) => {
          if (res.code == 200) {
            this.query();
            this.getDomainList();
            this.fieldDialogVisible = false;
          }
        })
        .catch((error) => {
          // console.log(error);
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.reviewer-audit {
  padding: 24px 10px;
  background-color: #fff;
}
</style>

