<!--  -->
<template>
  <el-radio-group v-model.trim="inputValue" @change="change">
    <el-radio :label="1">低</el-radio>
    <el-radio :label="2">中</el-radio>
    <el-radio :label="3">高</el-radio>
  </el-radio-group>
</template>

<script>
export default {
  name: 'Risk',
  components: {},
  props: ['value', 'validateName'],
  watch: {
    value: {
      immediate: true,
      deep: true,
      handler() {
        this.inputValue = this.value
      },
    }
  },
  data() {
    return {
      inputValue: ''
    };
  },

  computed: {},

  created() { },

  mounted() { },

  methods: {
    change() {
      this.$emit('input', this.inputValue);
      this.$emit('validate', this.validateName, this.inputValue);
    }
  }
}

</script>
<style lang='scss' scoped>
</style>