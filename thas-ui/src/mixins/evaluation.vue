<!--  -->
<template>
  <div></div>
</template>

<script>
import request from "@/utils/request";

// 组件
import ClauseItem from "@/components/ClauseItem";
import EvaluationSelf from "@/components/EvaluationForm/evaluationSelf.vue";
import EvaluationReview from "@/components/EvaluationForm/evaluationReview.vue";
import EvaluationModify from "@/components/EvaluationForm/evaluationModify.vue";
import EvaluationExamine from "@/components/EvaluationForm/evaluationExamine.vue";
import EvaluationQuery from "@/components/EvaluationForm/evaluationQuery.vue";
import EvaluationCheck from "@/components/EvaluationForm/evaluationCheck.vue";
import EvaluationAudit from "@/components/EvaluationForm/evaluationAudit.vue";
import EvaluationVerify from "@/components/EvaluationForm/evaluationVerify.vue";
import EvaluationConfirm from "@/components/EvaluationForm/evaluationConfirm.vue";
import EvaluationSlot from '@/components/EvaluationForm/evaluationSlot.vue'
import EvaluationSuggest from '@/components/EvaluationForm/evaluationSuggest.vue'
import ModifyTable from "@/views/hospitalSelfEvaluation/components/modifyTable.vue";
import { Loading } from 'element-ui';



export default {
  name: "EvaluationMixin",
  components: {
    ClauseItem,
    EvaluationAudit,
    EvaluationCheck,
    EvaluationConfirm,
    EvaluationExamine,
    EvaluationModify,
    EvaluationQuery,
    EvaluationReview,
    EvaluationSelf,
    EvaluationVerify,
    EvaluationSlot,
    EvaluationSuggest,
    ModifyTable,
  },
  props: {},
  watch: {
    $route(to) {
      if (to && to.query && to.query.autCode) {
        let autCode = to.query.autCode;
        if (this && this.relation && this.relation.autCode) {
          let autCode2 = this.relation.autCode;
          if (autCode2 != autCode) {
            this.loading = true;
            this.distributed = false;
            this.$nextTick(() => {
              !this.specialInit && this.init(false);
              this.specialInit && this.init2(true, this.$route.query.autCode, this.$route.query.type);
            });
          }
        }
      }
    },

    fixedHeader() {
      this.autCardHeightFun()
    }
  },
  data() {
    return {
      loading: true,
      distributed: false,
      standardLoadedKey: false,
      headerSlotKeys: 0,
      loadingKeys: 0,
      selectedKeys: 0,
      relation: {},
      autCode: "",
      versionId: "",
      autSaAudStatus: '',
      detail: {},
      saMap: {}, // 自评列表
      artMap: {}, // 条小结
      frMap: {}, // 形式审查列表
      frvMap: {}, // 审查审核列表
      srMap: {}, // 现场评审列表
      nSrMap: {},
      stMap: {}, // 小组提小结
      btMap: {}, // 大主题小结
      groupMap: {}, // 组小结
      srvMap: {}, // 现场评审审核列表
      frrMap: {}, // 审查组长 审查评审报告列表
      lwmMap: {}, // 评审组长修改
      farMap: {}, // 事实准确性确认
      fvmSKMap: {}, // 评审员第一次拒绝修改
      fwmMap: {}, // 评审员第一次修改
      fvmMap: {}, // 评审组长第一次复查
      trMap: {}, // 验证评审员验证
      tvmSKMap: {},// 评审员第二次拒绝修改
      twmMap: {}, // 评审员第二次修改
      tvmMap: {},// 评审组长第二次复查
      srmMap: {},
      srmRjMap: {},
      termMap: {}, // 条小结是否完成
      frjMap: {},
      fmRjMap: {},
      trjMap: {},
      lastMap: {},
      artTermMap: {},
      clauseList: [],
      clauseIds: [],
      distributeClauseIds: [],
      fitClauseIds: [],
      allFitClauseIds: [],
      lightHigh: 0,
      articleId: null,
      wholeClauseList: [],
      pageNum: 1,
      limit: 6,
      specialInit: false,
      domainClauseIds: [],
      labelWidth: '120px',
      autCardFix: false,
      showConvenientBtn: true,
      clauseItemHeight: '0px',
      autCardHeight: '0px',
      defaultCardHeight: 0,

      clauseItemKeys: 0,

      submitLoading: null,

      isLeader: false
    };
  },

  computed: {
    total() {
      return this.clauseList.length;
    },
    showClauseList() {
      let startIndex = (this.pageNum - 1) * this.limit;
      let endIndex = startIndex + this.limit;
      return this.clauseList.slice(startIndex, endIndex);
    },
    wholeClauseIds() {
      return this.wholeClauseList.map(({ clauseId }) => Number(clauseId));
    },

    fixedHeader() {
      return this.$store.state.settings.fixedHeader
    },

    isThisPage() {
      return (index) => {
        return index <= this.pageNum * 6 && index > (this.pageNum - 1) * 6;
      };
    },

    srtMap() {
      return {
        ...this.stMap,
        ...this.btMap,
        ...this.groupMap
      };
    },

    specialIds() {
      let statuses = [
        this.AutSaAudCurrentStatusEnum.WAIT_REVIEW_REVIEW_REPORT,
        this.AutSaAudCurrentStatusEnum.REVIEW_REVIEW_REPORT_PROC,
        this.AutSaAudCurrentStatusEnum.REVIEW_REVIEW_REPORT_SUM,
        this.AutSaAudCurrentStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC,
      ]
      return statuses.includes(this.autSaAudStatus)
    },

    autSr() {
      return (clauseId, submitType) => {
        let aut = this.lastMap[clauseId];
        let sr_types = [
          this.AutSaAudCurrentStatusEnum.SR_CLAUSE,
          this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_W_M,
          this.AutSaAudCurrentStatusEnum.TR_CLAUSE_W_M,
          this.AutSaAudCurrentStatusEnum.SR_REPORT_M,
        ]
        if (!submitType || !sr_types.includes(submitType) || submitType == this.AutSaAudCurrentStatusEnum.SR_REPORT_M) {
          aut = this.srmMap[clauseId] || this.twmMap[clauseId] || this.fwmMap[clauseId] || this.srMap[clauseId]
        } else {
          if (submitType == this.AutSaAudCurrentStatusEnum.TR_CLAUSE_W_M) {
            aut = this.twmMap[clauseId] || this.fwmMap[clauseId]
          } else if (submitType == this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_W_M) {
            aut = this.fwmMap[clauseId]
          }
          if (!aut || submitType == this.AutSaAudCurrentStatusEnum.SR_CLAUSE) {
            aut = this.srMap[clauseId]
          }
        }

        console.log(aut);

        return aut
      }
    }
  },


  created() {
    this.bus.$on("autSubmit", (event, { data, clauseId, submitType, status }) => {
      // // console.log(event, data, clauseId, submitType, status);
      if (status != this.autSaAudStatus) throw "状态错误";
      this.onComplete(event, data, clauseId, submitType);
    });

    this.bus.$on("heightChange", () => {
      this.autCardBodyHeight();
    });

    this.bus.$on("autScroll", (event) => {
      // // console.log(event);
      let target = event.target || event.scrElement;
      let scrollTop = target.scrollTop || 0
      let autCards = target.getElementsByClassName('autScrollCard');
      let autCard = autCards && autCards[0] ? autCards[0] : null;
      if (!autCard) return;
      // // console.log(scrollTop, autCard.offsetTop, autCard.offsetTop - scrollTop);
      if (autCard.offsetTop - scrollTop < 90) {
        this.autCardFix = true;
      } else {
        this.autCardFix = false;
      }
    })
  },

  destroyed() {
    this.bus.$off("autSubmit");
    this.bus.$off("autScroll");
    this.bus.$off("heightChange");
  },

  methods: {
    autCardBodyHeight(fresh) {
      this.clauseItemHeight = `calc(100% - 200px)`
      if (this.$refs.standardCard) {
        let el = this.$refs.standardCard.$el;
        let clientHeight = el.clientHeight || 200;
        this.clauseItemHeight = `calc(100% - ${clientHeight + 50}px)`
      }
      fresh && this.selectedKeys++;
    },

    autCardHeightFun() {
      this.autCardHeight = '0px';
      if (this.$refs.auditEvaluation) {
        let el = this.$refs.auditEvaluation;
        let parent = el.parentNode;
        let parentHeight = parent.offsetHeight;
        if (!this.defaultCardHeight) {
          this.defaultCardHeight = parentHeight;
        }
        let elOffsetTop = el.offsetTop
        let height = this.defaultCardHeight - elOffsetTop - 70
        let tagsViewHeight = this.$store.state.settings.tagsView ? 0 : 33
        this.autCardHeight = height + 89 + 'px'
      } else {
        this.autCardHeight = '0px'
      }
    },

    standardLoaded(clauseList = []) {
      this.wholeClauseList = clauseList
      this.standardLoadedKey = true;
      this.standardLoadedBack();
    },

    standardLoadedBack() {
      this.$nextTick(() => {
        this.autCardHeightFun()
        this.autCardBodyHeight(true)
      })
    },

    selectChange(placeholder, clauseList) {
      this.pageNum = 1;
      this.$set(this, "clauseList", clauseList);
      // this.autCardHeightFun();
      this.autCardBodyHeight(true);
    },

    queryList(autCode, pageType, userId) {
      return request({
        url: "/aut/sa/aud/queryList",
        method: 'post',
        data: {
          autCode,
          accountId: userId || this.$store.getters.userId + "",
          pageType
        }
      })
    },

    relationQuery(noThrow) {
      // if(!this.$route.query.autCode) return Promise.reject();
      return request({
        url: "/aut/sa/relation/query",
        method: "post",
        noThrow,
        data: {
          accountId: this.$store.getters.userId,
          autCode: this.$route.query.autCode,
        },
      });
    },


    // 获取评审评价数据详情
    queryDetail(reviewerId) {
      return request({
        url: "/aut/sa/aud/queryDetail",
        method: "post",
        data: {
          autCode: this.$route.query.autCode || this.relation.autCode,
          accountId: reviewerId || this.$store.getters.userId,
          pageType: this.$route.query.pageType
        },
      });
    },

    async init(force = true) {
      try {
        let { data } = await this.relationQuery(!force);
        const { autSaRelation, distributeClauseIdList, fitClauseIds, allFitClauseIds } = data;
        this.clauseIds = distributeClauseIdList || []
        this.distributeClauseIds = distributeClauseIdList || []
        this.fitClauseIds = fitClauseIds || []
        this.allFitClauseIds = allFitClauseIds || []
        this.relation = autSaRelation;
        // this.relation = data;
        this.autCode = this.relation.autCode;
        this.versionId = this.relation.autCsId;
        await this.init_detail(force)
      } catch (error) {
        console.log(error);
        this.handleError();
      } finally {
        this.loading = false;
        this.distributed = true;
      }
    },

    async init2(force = true, autCode, pageType, reviewerId, flag) {
      try {
        let { rows } = await this.queryList(autCode, pageType, reviewerId);
        let autSaRelation = rows[0];
        this.relation = autSaRelation;
        this.autCode = this.relation.autCode;
        this.versionId = this.relation.autCsId;
        if (flag) {
          this.isLeader = this.relation.isLeader == 1;
        };
        await this.init_detail2(force, reviewerId)

      } catch (error) {
        console.log('init2 失败', error);
        this.handleError();
      } finally {
        this.loading = false;
        this.distributed = true;
      }
    },

    async init_detail(force) {
      let res = await this.queryDetail();
      this.detail = res.data;
      this.autSaAudStatus = this.detail.autSaAudStatus;
      force && this.clearClauseList()
      this.analyzeData(force);
    },

    async init_detail2(force, reviewerId) {
      let res = await this.queryDetail(reviewerId);
      this.detail = res.data;
      this.autSaAudStatus = this.detail.autSaAudStatus;

      let { distributeClauseIdList, fitClauseIds, allFitClauseIds } = this.relation
      this.$set(this, 'clauseIds', distributeClauseIdList || []);
      this.$set(this, 'distributeClauseIds', distributeClauseIdList || []);
      this.fitClauseIds = fitClauseIds || []
      this.allFitClauseIds = allFitClauseIds || []

      force && this.clearClauseList()
      this.analyzeData(force);
    },

    handleError() { },

    analyzeData(force) {
      this.analyzeSumData();
      this.analyzeAutData();
      force && this.loadingKeys++
      this.headerSlotKeys++;
      this.initFinalBack();
      this.loading = false;
    },

    // 解析detail返回的数据中，关于总结的部分 autDesc是一个json字符串
    analyzeSumData() {
      // if (this.detail && this.detail.seniorReviewerReviewSum) {
      // }
      // if (this.detail && this.detail.autSaAudSum) {
      // }
    },

    // 解析评审评价各个类型的款项评价数据
    analyzeAutData() {
      if (!this.detail || !this.detail.autSaAudListMap) return;
      let asaListMap = this.detail.autSaAudListMap;
      this.evaluationSelf(asaListMap);
      this.evaluationExamine(asaListMap);
      this.evaluationReview(asaListMap);
      this.evaluationCheck(asaListMap);
      this.evaluationAudit(asaListMap);
      this.evaluationQuery(asaListMap);
      this.evaluationModify(asaListMap);
      this.evaluationVerify(asaListMap);
      this.evaluationSrReportM(asaListMap)
      this.evaluationFarReject(asaListMap)
      this.evaluationFarMReject(asaListMap)
      this.evaluationTrReject(asaListMap)
      this.evaluationLatest();

      this.clauseItemKeys++
    },

    getListMapByType(type, listMap) {
      return listMap[type] || [];
    },

    getRejectedListMapByType(type) {
      const listMap = this.detail.rejectAutSaAudListMap || {};
      return listMap[type] || [];
    },

    getFileList(fileIds) {
      const filesMap = this.detail.fileDetailMap;
      let fileList = [];
      fileIds.forEach(
        (fileId) =>
          filesMap[fileId] &&
          filesMap[fileId][0] &&
          fileList.push(filesMap[fileId][0])
      );
      return fileList;
    },

    // 医院自评和条小结
    evaluationSelf(listMap) {
      let type1 = this.AutSaAudCurrentStatusEnum.SA_CLAUSE,
        type2 = this.AutSaAudCurrentStatusEnum.SA_ARTICLE_SUMMARY;
      const saList = this.getListMapByType(type1, listMap);
      const saRjList = this.getRejectedListMapByType(type1);
      const artList = this.getListMapByType(type2, listMap);

      // 先解析自评
      saList.forEach((data) => {
        let { clauseId, fileIds } = data;
        let fileIdes = fileIds.split(",").filter((id) => !!id);
        this.$set(this.saMap, clauseId, {
          ...data,
          fileList: this.getFileList(fileIdes),
        });
      });

      // 自评驳回
      saRjList.forEach((data) => {
        let { clauseId, fileIds } = data;
        if (this.saMap[clauseId]) return;
        let fileIdes = fileIds.split(",").filter((id) => !!id);
        if (this.saMap[clauseId]) return;
        this.$set(this.saMap, clauseId, {
          ...data,
          fileList: this.getFileList(fileIdes),
          rejected: true,
        });
      });
    },

    // 审查员形式审查（初查）
    evaluationExamine(listMap) {
      let type = this.AutSaAudCurrentStatusEnum.FR_CLAUSE;
      const frList = this.getListMapByType(type, listMap);
      const frRjList = this.getRejectedListMapByType(type);

      // 先解析形式
      frList.forEach((data) => {
        let { clauseId } = data;
        this.$set(this.frMap, clauseId, {
          ...data,
        });
      });
      // 审查驳回
      frRjList.forEach((data) => {
        let { clauseId } = data;
        if (this.frMap[clauseId]) return;
        this.$set(this.frMap, clauseId, {
          ...data,
          rejected: true,
        });
      });
    },

    // 评审人现场评审（初查）
    evaluationReview(listMap) {
      let type = this.AutSaAudCurrentStatusEnum.SR_CLAUSE;
      const srList = this.getListMapByType(type, listMap);
      const srRjList = this.getRejectedListMapByType(type);

      // 先解析评价
      srList.forEach((data) => {
        let { clauseId } = data;
        this.$set(this.srMap, clauseId, {
          ...data,
        });
      });
      // 评价驳回
      srRjList.forEach((data) => {
        let { clauseId } = data;
        if (this.srMap[clauseId]) return;
        this.$set(this.srMap, clauseId, {
          ...data,
          rejected: true,
        });
      });

      // 小结
      let type2 = this.AutSaAudCurrentStatusEnum.SR_ST_SUMMARY; // 无用
      let type3 = this.AutSaAudCurrentStatusEnum.SR_BT_SUMMARY;
      let type4 = this.AutSaAudCurrentStatusEnum.SR_GROUP_SUMMARY;

      const stList = this.getListMapByType(type2, listMap);
      const btList = this.getListMapByType(type3, listMap);
      const groupList = this.getListMapByType(type4, listMap);

      stList.forEach((data) => {
        let { clauseId } = data;
        this.$set(this.stMap, clauseId, {
          ...data,
        });
      });

      btList.forEach((data) => {
        let { clauseId } = data;
        this.$set(this.btMap, clauseId, {
          ...data,
        });
      });

      groupList.forEach((data) => {
        let { clauseId } = data;
        this.$set(this.groupMap, clauseId, {
          ...data,
        });
      });
    },

    // 审查和评价审核（复查）
    evaluationCheck(listMap) {
      let type1 = this.AutSaAudCurrentStatusEnum.FR_V_CLAUSE,
        type2 = this.AutSaAudCurrentStatusEnum.SR_V_CLAUSE;
      const frvList = this.getListMapByType(type1, listMap);
      const frvRjList = this.getRejectedListMapByType(type1);
      const srvList = this.getListMapByType(type2, listMap);
      const srvRjList = this.getRejectedListMapByType(type2);

      // 审查审核
      frvList.forEach((data) => {
        let { clauseId } = data;
        this.$set(this.frvMap, clauseId, {
          ...data,
        });
      });
      // 审查审核驳回
      let rejectedList = [];
      frvRjList.forEach((data) => {
        let { clauseId } = data;
        if (this.frvMap[clauseId]) return;
        rejectedList.push(Number(clauseId));
        this.$set(this.frvMap, clauseId, {
          ...data,
          rejected: true,
        });
      });

      if (rejectedList.length) {
        this.$set(this, 'distributeClauseIds', rejectedList);
        this.$set(this, 'clauseIds', rejectedList);
        this.loadingKeys++;
      }

      // 评价审核
      srvList.forEach((data) => {
        let { clauseId } = data;
        this.$set(this.srvMap, clauseId, {
          ...data,
        });
      });
      // 评价审核驳回
      srvRjList.forEach((data) => {
        let { clauseId } = data;
        if (this.srvMap[clauseId]) return;
        this.$set(this.srvMap, clauseId, {
          ...data,
          rejected: true,
        });
      });
    },

    // 审查组长审查
    evaluationAudit(listMap) {
      let type = this.AutSaAudCurrentStatusEnum.FR_REPORT_R_CLAUSE;
      const frrList = this.getListMapByType(type, listMap);

      // 解析审查
      frrList.forEach((data) => {
        let { clauseId } = data;
        this.$set(this.frrMap, clauseId, {
          ...data,
        });
      });
    },

    // 医院确认
    evaluationQuery(listMap) {
      let type = this.AutSaAudCurrentStatusEnum.FAR_CLAUSE;
      const fraList = this.getListMapByType(type, listMap);

      // 解析确认
      fraList.forEach((data) => {
        let { clauseId } = data;
        this.$set(this.farMap, clauseId, {
          ...data,
        });
      });
    },

    // 第一、二、三次修改
    evaluationModify(listMap) {
      let type1 = this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_M,
        type2 = this.AutSaAudCurrentStatusEnum.TR_CLAUSE_M,
        type3 = this.AutSaAudCurrentStatusEnum.SR_CLAUSE_M;

      const fwmList = this.getListMapByType(type1, listMap);
      const twmList = this.getListMapByType(type2, listMap);
      const lwmList = this.getListMapByType(type3, listMap);

      const lwmRjList = this.getRejectedListMapByType(type3);

      // 解析评审
      lwmList.forEach((data) => {
        let { clauseId } = data;
        this.$set(this.lwmMap, clauseId, {
          ...data,
        });
      });

      lwmRjList.forEach((data) => {
        let { clauseId } = data;
        this.$set(this.lwmMap, clauseId, {
          rejected: true,
          ...data,
        });
      });

      // 解析确认
      fwmList.forEach((data) => {
        let { clauseId } = data;
        this.$set(this.fwmMap, clauseId, {
          ...data,
        });
      });

      // 解析验证
      twmList.forEach((data) => {
        let { clauseId } = data;
        this.$set(this.twmMap, clauseId, {
          ...data,
        });
      });

      let type4 = this.AutSaAudCurrentStatusEnum.FAR_V_CLAUSE_M,
        type5 = this.AutSaAudCurrentStatusEnum.TR_V_CLAUSE_M,
        type6 = this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_M_SKIP,
        type7 = this.AutSaAudCurrentStatusEnum.TR_CLAUSE_M_SKIP;
      const fvmList = this.getListMapByType(type4, listMap);
      const tvmList = this.getListMapByType(type5, listMap);
      const fvmRjList = this.getRejectedListMapByType(type4);
      const tvmRjList = this.getRejectedListMapByType(type5);
      const fvmSkList = this.getListMapByType(type6, listMap);
      const tvmSkList = this.getListMapByType(type7, listMap);


      // 事实修改审核
      fvmList.forEach((data) => {
        let { clauseId } = data;
        this.$set(this.fvmMap, clauseId, {
          ...data,
        });
      });

      fvmRjList.forEach(data => {
        let { clauseId } = data;
        if (this.fvmMap[clauseId]) return;
        this.$set(this.fvmMap, clauseId, {
          ...data,
          rejected: true,
        });
      })

      fvmSkList.forEach(data => {
        let { clauseId } = data;
        if (this.fvmSKMap[clauseId]) return;
        this.$set(this.fvmSKMap, clauseId, {
          ...data,
        });
      })


      // 验证修改审核
      tvmList.forEach((data) => {
        let { clauseId } = data;
        this.$set(this.tvmMap, clauseId, {
          ...data,
        });
      });


      tvmRjList.forEach(data => {
        let { clauseId } = data;
        if (this.tvmMap[clauseId]) return;
        this.$set(this.tvmMap, clauseId, {
          ...data,
          rejected: true,
        });
      })

      tvmSkList.forEach(data => {
        let { clauseId } = data;
        if (this.tvmSKMap[clauseId]) return;
        this.$set(this.tvmSKMap, clauseId, {
          ...data,
        });
      })
    },

    // 验证评审员验证
    evaluationVerify(listMap) {
      let type = this.AutSaAudCurrentStatusEnum.TR_CLAUSE;
      const trList = this.getListMapByType(type, listMap);

      // 解析验证
      trList.forEach((data) => {
        let { clauseId } = data;
        this.$set(this.trMap, clauseId, {
          ...data,
        });
      });
    },


    evaluationSrReportM(listMap) {
      let type = this.AutSaAudCurrentStatusEnum.SR_REPORT_M;
      const srmList = this.getListMapByType(type, listMap);
      const srmRjList = this.getRejectedListMapByType(type);
      srmList.forEach((data) => {
        let { clauseId } = data;
        this.$set(this.srmMap, clauseId, {
          ...data,
        });
      });
      srmRjList.forEach((data) => {
        let { clauseId } = data;
        this.$set(this.srmRjMap, clauseId, {
          ...data,
        });
      });
    },

    evaluationFarReject(listMap) {
      let type = this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_RJ_E;
      const frjList = this.getListMapByType(type, listMap);
      frjList.forEach((data) => {
        let { clauseId } = data;
        this.$set(this.frjMap, clauseId, {
          ...data,
        });
      });
    },

    evaluationFarMReject(listMap) {
      let type = this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_RJ_E_M;
      const fmRjList = this.getListMapByType(type, listMap);
      fmRjList.forEach((data) => {
        let { clauseId } = data;
        this.$set(this.fmRjMap, clauseId, {
          ...data,
        });
      });
    },

    evaluationTrReject(listMap) {
      let type = this.AutSaAudCurrentStatusEnum.TR_CLAUSE_RJ_E;
      const trjList = this.getListMapByType(type, listMap);
      trjList.forEach((data) => {
        let { clauseId } = data;
        this.$set(this.trjMap, clauseId, {
          ...data,
        });
      });
    },

    evaluationLatest() {
      let list = this.detail.latestAutSaAudList || []
      list.forEach(data => {
        let id = data.clauseId;
        this.$set(this.lastMap, id, {
          ...data,
        });
        // if (id == 1) {
        //   // console.log(this.srMap[id]);
        // }
      })
    },

    async onComplete(event, data, clauseId, submitType) {
      let formData = {
        autCode: this.autCode,
        accountId: `${this.$store.getters.userId}`,
        submitType,
        autSaAudLists: [{
          ...data,
          clauseId
        }],
      }

      try {
        await this.submit(formData);
        this.bus.$emit(event, {
          clauseId, submitType, success: true
        });
        if (this.specialInit) {
          let types = [
            this.AutSaAudCurrentStatusEnum.TR_CLAUSE_W_M,
            this.AutSaAudCurrentStatusEnum.FAR_V_CLAUSE_M,
            this.AutSaAudCurrentStatusEnum.TR_V_CLAUSE_M,
            this.AutSaAudCurrentStatusEnum.SR_CLAUSE_M,
          ]
          if (types.includes(this.$route.query.type)) {
            this.init2(false, this.$route.query.autCode, this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_W_M);
          } else {
            if (this.$route.query.type === this.AutSaAudCurrentStatusEnum.SR_CLAUSE) {
              this.init_detail2(false)
            } else {
              this.init2(false, this.$route.query.autCode, this.$route.query.type);
            }
          }
        } else {
          this.init(false);
        }
      } catch (error) {
        this.bus.$emit(event, {
          clauseId, submitType, success: false,
        });
      }
    },

    async submit(data) {
      if (this.submitLoading) {
        return false;
      }
      this.submitLoading = this.$loading({
        lock: true,
        text: "提交中...",
        background: "rgba(0, 0, 0, 0.7)",
      });
      try {
        let result = await request({
          url: "/aut/sa/aud/submitAutSaAud",
          method: "post",
          data,
        });
        return result.code === 200
      } catch (error) {
        console.log('error: ', error);
        return false
      } finally {
        if (this.submitLoading) {
          this.submitLoading.close()
        }
        this.submitLoading = null
      }
    },

    initFinalBack() {

    },

    handleClauseFilter(index, clauseIds = []) {
      if (index === this.lightHigh) {
        this.lightHigh = 0;
        this.$set(this, 'clauseIds', this.distributeClauseIds);
      } else if (this.clauseIds.toString() === clauseIds.toString()) {
        this.lightHigh = 0;
        this.$set(this, 'clauseIds', this.distributeClauseIds);
      } else {
        this.lightHigh = index;
        this.$set(this, 'clauseIds', clauseIds);
      }
      this.clearClauseList();
    },

    clearClauseList() {
      this.$set(this, "clauseList", []);
      this.loadingKeys++;
      // this.autCardHeightFun();
      this.autCardBodyHeight(true);
    },

    async makeData(submitType, standardType = 'clause', fileIds = '', ids = [], test = false, funType) {
      // if (!this.makeDataLoading) {
      //   this.makeDataLoading = Loading.service({
      //     lock: true,
      //     text: '提交中...',
      //     spinner: 'el-icon-loading',
      //   });
      // }
      const data = {
        autCode: this.relation.autCode,
        accountId: this.$store.getters.userId + "",
        submitType
      }
      const versionId = this.relation.autCsId || this.$store.getters.standardBaseVersionId;
      const list = this.$store.getters.standardTypeItemByVersionId(versionId, standardType, "list") || [];
      const map = this.$store.getters.standardTypeItemByVersionId(versionId, standardType, "map") || {};

      let idList = [];
      if (this.distributeClauseIds.length) {
        let hasIds = ids.filter(id => this.distributeClauseIds.includes(id));
        idList = this.distributeClauseIds.filter(id => !hasIds.includes(id));
      } else {
        idList = list.map(({ clauseId }) => `${clauseId}`).filter(id => !ids.includes(id))
      }
      let submitList = this.getListMapByType(submitType, this.detail.autSaAudListMap);
      let submitIds = submitList.map(({ clauseId }) => clauseId);


      data.autSaAudLists = idList.filter(id => !this.allFitClauseIds.includes(id)).filter((id, index) => {
        return !submitIds.includes(id);
      }).map((id) => ({
        autDesc: this.AutSaAudCurrentStatusEnum.SR_CLAUSE == submitType ? '{"autAdvantage":"-"}' : "通过",
        autResult: '1',
        clauseId: id,
        fileIds: map[id].isStar == 1 ? fileIds : ''
      }));

      try {
        let res = await this.submit(data);
        // 报错就不提示
        if (!res) return;
        if (test) {
          this.$message("批量提交成功-仅测试!");
        } else {
          this.$message({
            type: 'success',
            message: "提交成功"
          });
        }

        if (funType === 'submitAllAudS') {
          let obj = {
            path: "/fAssessor/sAssessorIndex",
            query: {
              autCode: this.$route.query.autCode,
              type: this.$route.query.type
            },
          }
          this.$tab.closeOpenPage(obj);
          return;
        }

        !this.specialInit && this.init(true);
        this.specialInit && this.init2(true, this.$route.query.autCode, this.$route.query.type);
      } catch (error) {
        if (error.code == 1000003) {
          !this.specialInit && this.init(true);
          this.specialInit && this.init2(true, this.$route.query.autCode, this.$route.query.type);
        }
      }
      // finally {
      //   if (this.makeDataLoading) {
      //     this.makeDataLoading.close()
      //   }
      //   this.makeDataLoading = null
      // }
    }
  },
};
</script>

<style lang="scss">
::v-deep .el-form {
  padding: 5px 0;
}

.clearfix {
  h4 {
    margin: 0;
    // float: left;
    display: inline;
  }

  .header-right {
    float: right;
    font-size: 12px;

    span {
      cursor: pointer;

      &.lightHigh {
        color: #409eff;
      }

      &>i {
        visibility: hidden;
      }
    }

    span::after {
      content: " | ";
    }

    span+span {
      i {
        visibility: visible;
        font-style: inherit;
      }
    }

    span:last-child::after {
      content: "";
    }
  }
}

.audit-evaluation {
  background-color: transparent !important;

  &>div {
    min-height: inherit;
  }
}

.autScrollCard {
  .el-card__body {
    height: calc(100% - 41px);

    .evaluation-content {
      margin-top: 11px;
      overflow-y: scroll;
    }
  }
}
</style>
