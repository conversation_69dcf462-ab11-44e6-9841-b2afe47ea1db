<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.ReviewerFieldInfoMapper">

    <resultMap type="com.thas.web.domain.ReviewerFieldInfo" id="ReviewerFieldInfoResult">
        <result property="id"    column="id"    />
        <result property="fieldCode"    column="field_code"    />
        <result property="accountId"    column="account_id"    />
        <result property="status"    column="status"    />
        <result property="creator"    column="creator"    />
        <result property="createTime"    column="create_time"    />
        <result property="updater"    column="updater"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectReviewerFieldInfoVo">
        select id, field_code, account_id, status, creator, create_time, updater, update_time from reviewer_field_info
    </sql>

    <select id="selectReviewerFieldInfoList" parameterType="ReviewerFieldInfo" resultMap="ReviewerFieldInfoResult">
        <include refid="selectReviewerFieldInfoVo"/>
        <where>
            <if test="fieldCode != null  and fieldCode != ''"> and field_code = #{fieldCode}</if>
            <if test="accountId != null  and accountId != ''"> and account_id = #{accountId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="creator != null  and creator != ''"> and creator = #{creator}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
        </where>
    </select>

    <select id="selectReviewerFieldInfoByAccountId" parameterType="String" resultType="com.thas.web.dto.ReviewerFieldInfoVO">
        SELECT
            cd.id,
            cd.domain_name fieldName,
            rfi.field_code fieldCode
        FROM
            reviewer_field_info rfi
                LEFT JOIN cst_domain cd ON rfi.field_code = cd.id
        WHERE
            rfi.account_id = #{accountId} and rfi.status = 1
    </select>

    <select id="selectReviewerFieldInfoByAccountIdList" parameterType="list"
            resultType="com.thas.web.dto.ReviewerFieldInfoVO">
        SELECT
        cd.id,
        cd.domain_name fieldName,
        rfi.account_id accountId
        FROM
        reviewer_field_info rfi
        LEFT JOIN cst_domain cd ON rfi.field_code = cd.id
        WHERE
        rfi.account_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and rfi.status = 1
    </select>


    <select id="selectReviewerFieldInfoById" parameterType="Long" resultMap="ReviewerFieldInfoResult">
        <include refid="selectReviewerFieldInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertReviewerFieldInfo" parameterType="ReviewerFieldInfo" useGeneratedKeys="true" keyProperty="id">
        insert into reviewer_field_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fieldCode != null and fieldCode != ''">field_code,</if>
            <if test="accountId != null and accountId != ''">account_id,</if>
            <if test="status != null">status,</if>
            <if test="creator != null and creator != ''">creator,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updater != null and updater != ''">updater,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fieldCode != null and fieldCode != ''">#{fieldCode},</if>
            <if test="accountId != null and accountId != ''">#{accountId},</if>
            <if test="status != null">#{status},</if>
            <if test="creator != null and creator != ''">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updater != null and updater != ''">#{updater},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <insert id="batchInsertReviewerFieldInfo" parameterType="ReviewerFieldInfo">
        insert into reviewer_field_info (field_code, account_id) values
        <foreach collection="list" item="reviewerFieldInfo" separator="," close=";">
            (#{reviewerFieldInfo.fieldCode}, #{reviewerFieldInfo.accountId})
        </foreach>
    </insert>

    <update id="updateReviewerFieldInfo" parameterType="ReviewerFieldInfo">
        update reviewer_field_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="fieldCode != null and fieldCode != ''">field_code = #{fieldCode},</if>
            <if test="accountId != null and accountId != ''">account_id = #{accountId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="creator != null and creator != ''">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateStatusByAccountId">
        UPDATE reviewer_field_info
        SET STATUS = #{status}
        WHERE
            account_id = #{accountId}
          AND STATUS = 1
    </update>

    <delete id="deleteReviewerFieldInfoById" parameterType="Long">
        delete from reviewer_field_info where id = #{id}
    </delete>

    <delete id="deleteReviewerFieldInfoByIds" parameterType="String">
        delete from reviewer_field_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateStatusByFieldCode" parameterType="String">
        UPDATE reviewer_field_info
        SET STATUS = #{status}
        WHERE
            field_code = #{fieldCode}
          AND STATUS = 1
    </update>

    <update id="updateAccountId" parameterType="String">
        update reviewer_field_info set account_id = #{userId} where account_id = #{accountId} and status = 1
    </update>
</mapper>
