<template>
  <div class="review-progress">
    <el-card class="box-card" shadow="never" v-if="detail">
      <div slot="header" class="clearfix">
        <h4>评审进度</h4>
      </div>
      <el-steps :active="active" finish-status="success">
        <el-step title="评审计划分配"></el-step>
        <el-step title="医院自评"></el-step>
        <el-step title="形式审查"></el-step>
        <el-step title="现场评审"></el-step>
        <el-step title="审查组长审查报告"></el-step>
        <el-step title="评审组长修改"></el-step>
        <el-step title="事实准确性确认"></el-step>
        <el-step title="审查组长确认"></el-step>
        <el-step title="评审员第一次修改"></el-step>
        <el-step title="验证评审员验证"></el-step>
        <el-step title="评审员第二次修改"></el-step>
        <el-step title="认证审核委员会确认"></el-step>
        <el-step title="评审评价结束"></el-step>
      </el-steps>

      <div class="review-progress-content">
        <div>
          <!-- 0 进行中  1完成  2拒绝 -->
          <i :class="[`active${status}`, statusIcon]"></i>
          <span>
            <h4>{{ title }}</h4>
            <span>{{ desc }}</span>
          </span>
        </div>

        <el-form label-width="180px" :key="key" style="width: 380px; margin: 60px auto 0 auto">

        </el-form>
      </div>

      <!-- <p>
        {{statuesList[index]}}

        {{index}}
      </p>
      <el-button @click="--index">上一步</el-button>
      <el-input v-model="index"></el-input>
      <el-button @click="++index">下一步</el-button> -->
    </el-card>
  </div>
</template>

<script>
import request from "@/utils/request";
import { formatDate } from "@/utils/index";

export default {
  name: "EvaluationProgress",
  data() {
    return {
      active: 0,
      status: 1,
      icons: [""],
      url: {
        detail: "/aut/sa/aud/selectCurrentStageSaAud",
      },
      aStatus: 0,
      detail: null,
      key: 0,
      title: "",
      desc: "",
      times: [],
      oriTimes: [],
      index: -1,
    };
  },

  watch: {
    index() {
      this.showTitle(this.statuesList[this.index])
    }
  },

  components: {},

  computed: {
    statusIcon() {
      let icon = "el-icon-success";
      if (this.status === 2) {
        icon = "el-icon-warning";
      }
      return icon;
    },
    statusActive() {
      return `${this.active}${this.status}`;
    },

    hasTime() {
      return (
        this.detail != null &&
        this.detail.statusDate != null &&
        Object.keys(this.detail.statusDate).length
      );
    },

    statuesList() {
      return [
        "WAIT_TERM_SA",
        "TERM_SA_PROC",
        "WAIT_CONFIRM_SA_SUM",
        "WAIT_TERM_SA_SUM",
        "TERM_SA_SUM",
        "WAIT_AUD_FIRST_TRIAL",
        "AUD_FIRST_TRIAL_INQUIRE_PROC",
        "WAIT_CONFIRM_FIRST_TRIAL",
        "WAIT_AUD_FIRST_TRIAL_SUM",
        "AUD_FIRST_TRIAL_SUM_REJECT",
        "ADMIN_ALLOT_ASSESSOR",
        "ASSESSOR_WRITE_SHEET",
        "ADMIN_CONFIRM_ASSESSOR",
        "HOSPITAL_AUDIT_ASSESSOR",
        "AUD_FIRST_TRIAL_SUM_PASS",
        "AUD_SECOND_TRIAL_INQUIRE_PROC",
        "WAIT_AUD_SECOND_TRIAL_REVIEW",
        "AUD_SECOND_TRIAL_REVIEW_PROC",
        "AUD_SECOND_TRIAL_REVIEW_REJECT",
        "WAIT_AUD_SECOND_TRIAL_SUM",
        "AUD_SECOND_TRIAL_SUM_REJECT",
        "AUD_SECOND_TRIAL_SUM_CONDITIONAL_PASS",
        "AUD_PASS",
        "WAIT_REVIEW_REVIEW_REPORT",
        "REVIEW_REVIEW_REPORT_PROC",
        "REVIEW_REVIEW_REPORT_SUM",
        "REVIEWER_MODIFICATION_REVIEW",
        "REVIEWER_MODIFICATION_REVIEW_CONFIRM",
        "WAIT_FACTUAL_ACCURACY_REVIEW",
        "FACTUAL_ACCURACY_REVIEW_PROC",
        "FACTUAL_ACCURACY_REVIEW_SUM",
        "CONFIRM_REVIEW_FACTUAL",
        "CHECK_MODIFIED_TERMS_FIRST",
        "MODIFIED_TERMS_FIRST_PROC",
        "MODIFIED_TERMS_TRIAL_FIRST_PROC",
        "MODIFICATION_TERMS_SUM",
        "MODIFICATION_TERMS_CONFIRM",
        "WAIT_SENIOR_REVIEWER_REVIEW",
        "SENIOR_REVIEWER_REVIEW_PROC",
        "SENIOR_REVIEWER_REVIEW_SUM",
        "CHECK_MODIFIED_TERMS_SECOND",
        "REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW",
        "REVIEWER_MODIFICATION_TRIAL_SENIOR_REVIEWER_REVIEW",
        "REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW_SUM",
        "WAIT_AUD_REPORT_SUBMIT",
        "AUD_REPORT_CONFIRM",
        "REVIEW_PROCESS_FINISH",
        "WAIT_REPORT",
        "REPORT_FINISH"
      ]
    },

    time() {
      this.key++;
      return (type) =>
        this.hasTime ? this.formatDate(this.detail.statusDate[type]) : "";
    },

    showTime() {
      return (index) => {
        let times = this.oriTimes;
        if (!times) return "4周";
        let time = times.find((item) => item.stageValue == index);
        if (!time) return "4周";
        return time.cycle.split(",").join(" ~ ");
      };
    },
  },

  mounted() {
    this.getDetail();
  },

  methods: {
    formatDate: formatDate,
    getDetail() {
      request({
        url: this.url.detail,
        method: "get",
      }).then((res) => {
        if (res.code == 200) {
          this.aStatus = res.data.nowStageStatus;
          this.detail = res.data;
          this.oriTimes = res.data.hospitalReviewCycles;
          this.showTitle();
        }
      }).catch(error => {
        // // console.log(error);
        this.detail = {}
        this.showTitle();
      });
    },

    showTitle(bStatus) {
      this.status = 0;
      // let status = this.AutSaAudCurrentStatusEnum[bStatus] || this.aStatus
      let status = this.aStatus
      this.title = this.AutSaAudCurrentStatusEnum[status] || '待管理员分配评审安排与流程';
      if (status == -1) {
        return;
      } else if (status == -2) {
        this.title = '待医院端审核评审安排与流程'
        return;
      }
      // if (status == -1 || this.index == -1) {
      //   return;
      // } else if (status == -2 || this.index == -2) {
      //   this.title = '待医院端审核评审安排与流程'
      //   return;
      // }


      this.active = 0;
      if (!status) return;
      let statusEnums = [
        this.AutSaAudCurrentStatusEnum.ADMIN_ALLOT_ASSESSOR,
        this.AutSaAudCurrentStatusEnum.ASSESSOR_WRITE_SHEET,
        this.AutSaAudCurrentStatusEnum.ADMIN_CONFIRM_ASSESSOR,
        this.AutSaAudCurrentStatusEnum.HOSPITAL_AUDIT_ASSESSOR,
      ]
      if (statusEnums.includes(status)) return;

      this.active = 1;
      statusEnums = [
        this.AutSaAudCurrentStatusEnum.WAIT_TERM_SA,
        this.AutSaAudCurrentStatusEnum.TERM_SA_PROC,
        this.AutSaAudCurrentStatusEnum.WAIT_CONFIRM_SA_SUM,
        this.AutSaAudCurrentStatusEnum.WAIT_TERM_SA_SUM,
        this.AutSaAudCurrentStatusEnum.TERM_SA_SUM,
        this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_SUM_REJECT,
      ]
      if (statusEnums.includes(status)) {
        if (this.AutSaAudCurrentStatusEnum.WAIT_AUD_FIRST_TRIAL == status) {
          this.status = 1
        }
        return
      };

      this.active = 2;
      statusEnums = [
        this.AutSaAudCurrentStatusEnum.WAIT_AUD_FIRST_TRIAL,
        this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_INQUIRE_PROC,
        this.AutSaAudCurrentStatusEnum.WAIT_CONFIRM_FIRST_TRIAL,
        this.AutSaAudCurrentStatusEnum.WAIT_AUD_FIRST_TRIAL_SUM,
      ]
      if (statusEnums.includes(status)) return;


      this.active = 3
      statusEnums = [
        this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_SUM_PASS,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_INQUIRE_PROC,
        this.AutSaAudCurrentStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_REJECT,
        this.AutSaAudCurrentStatusEnum.WAIT_AUD_SECOND_TRIAL_SUM,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_SUM_REJECT,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_SUM_CONDITIONAL_PASS,
        this.AutSaAudCurrentStatusEnum.AUD_PASS,
      ]
      if (this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_SUM_REJECT == status ||
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_SUM_CONDITIONAL_PASS == status) {
        this.status = 2
      }
      if (statusEnums.includes(status)) return;

      statusEnums = [
        this.AutSaAudCurrentStatusEnum.WAIT_REVIEW_REVIEW_REPORT,
      ];

      this.active = 4
      statusEnums = [
        this.AutSaAudCurrentStatusEnum.WAIT_REVIEW_REVIEW_REPORT,
        this.AutSaAudCurrentStatusEnum.REVIEW_REVIEW_REPORT_PROC,
        this.AutSaAudCurrentStatusEnum.REVIEW_REVIEW_REPORT_SUM,
      ]
      if (this.AutSaAudCurrentStatusEnum.WAIT_REVIEW_REVIEW_REPORT == status) {
        this.status = 1
      }
      if (statusEnums.includes(status)) return;

      this.active = 5
      statusEnums = [
        this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_REVIEW,
        this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_REVIEW_CONFIRM,
      ]
      if (statusEnums.includes(status)) return;

      this.active = 6
      statusEnums = [
        this.AutSaAudCurrentStatusEnum.WAIT_FACTUAL_ACCURACY_REVIEW,
        this.AutSaAudCurrentStatusEnum.FACTUAL_ACCURACY_REVIEW_PROC,
        this.AutSaAudCurrentStatusEnum.FACTUAL_ACCURACY_REVIEW_SUM,
      ]
      if (statusEnums.includes(status)) return;

      this.active = 7
      statusEnums = [
        this.AutSaAudCurrentStatusEnum.CONFIRM_REVIEW_FACTUAL,
      ]
      if (statusEnums.includes(status)) return;

      this.active = 8
      statusEnums = [
        this.AutSaAudCurrentStatusEnum.CHECK_MODIFIED_TERMS_FIRST,
        this.AutSaAudCurrentStatusEnum.MODIFIED_TERMS_FIRST_PROC,
        this.AutSaAudCurrentStatusEnum.MODIFIED_TERMS_TRIAL_FIRST_PROC,
        this.AutSaAudCurrentStatusEnum.MODIFICATION_TERMS_SUM,
        this.AutSaAudCurrentStatusEnum.MODIFICATION_TERMS_CONFIRM,
      ]
      if (statusEnums.includes(status)) return;

      this.active = 9
      statusEnums = [
        this.AutSaAudCurrentStatusEnum.WAIT_SENIOR_REVIEWER_REVIEW,
        this.AutSaAudCurrentStatusEnum.SENIOR_REVIEWER_REVIEW_PROC,
        this.AutSaAudCurrentStatusEnum.SENIOR_REVIEWER_REVIEW_SUM,
      ]
      if (statusEnums.includes(status)) return;


      this.active = 10
      statusEnums = [
        this.AutSaAudCurrentStatusEnum.CHECK_MODIFIED_TERMS_SECOND,
        this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW,
        this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_TRIAL_SENIOR_REVIEWER_REVIEW,
        this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW_SUM,
      ]
      if (statusEnums.includes(status)) return;


      this.active = 11
      statusEnums = [
        this.AutSaAudCurrentStatusEnum.WAIT_AUD_REPORT_SUBMIT,
        this.AutSaAudCurrentStatusEnum.AUD_REPORT_CONFIRM,
        this.AutSaAudCurrentStatusEnum.REPORT_FINISH,
        this.AutSaAudCurrentStatusEnum.WAIT_REPORT,
      ]
      if (statusEnums.includes(status)) return;


      this.active = 12
      statusEnums = [
        this.AutSaAudCurrentStatusEnum.REVIEW_PROCESS_FINISH,
      ]
      if (statusEnums.includes(status)) {
        this.status = 1
        return;
      };


      this.active = 13
      this.title = '评审流程已结束'
    },
  },
};
</script>
<style lang='scss' scoped>
.review-progress {
  min-height: inherit;
  background-color: #fff;
  ::v-deep .el-card {
    border: 0;
    .el-card__header {
      padding-bottom: 15px;
      .clearfix h4 {
        float: left;
        font-weight: bold;
        margin: 0;
      }
    }
  }

  ::v-deep .el-steps {
    width: 98%;
    margin: 40px auto;
    margin-top: 20px;
    .el-step__icon {
      width: 40px;
      height: 40px;
    }
    .el-step__title {
      font-size: 14px;
      line-height: 16px;
      margin-top: 5px;
    }
    .el-step__head.is-process {
      color: #357cf4;
      border-color: #357cf4;
      & + .el-step__main .el-step__title {
        color: #357cf4;
      }
    }
    .el-step__head.is-success {
      color: #357cf4;
      border-color: #357cf4;
      & + .el-step__main .el-step__title {
        color: #357cf4;
      }
      .el-icon-check {
        font-weight: bold;
        font-size: 20px;
        color: white;
      }
      .el-step__icon.is-text {
        background-color: #357cf4;
      }
    }
    .el-step.is-horizontal .el-step__line {
      top: 19px;
    }
  }
  .review-progress-content {
    text-align: center;
    [class^="el-icon-"],
    [class*=" el-icon-"] {
      font-size: 96px;
    }
    .active0 {
      color: #ffce50;
      transform: rotate(136deg);
    }
    .active1 {
      color: green;
    }
    .active2 {
      color: #f52e3a;
    }

    h4 {
      font-weight: bold;
      font-size: 20px;
    }
  }

  ::v-deep .el-form {
    margin-top: 20px;
    .el-form-item {
      margin-bottom: 0;
      border: 1px solid grey;
      .el-form-item__content {
        border-left: 1px solid grey;
      }
    }

    .el-form-item + .el-form-item {
      border-top: 0;
    }
  }
}
</style>