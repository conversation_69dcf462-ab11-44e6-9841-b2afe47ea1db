package com.thas.web.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.druid.util.MapComparator;
import com.thas.common.constant.Constants;
import com.thas.common.enums.ServiceExceptionEnum;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.StringUtils;
import com.thas.web.domain.*;
import com.thas.web.dto.QueryBaseConditionDTO;
import com.thas.web.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PdfGenParamServiceImpl implements PdfGenParamService {

    @Autowired
    private IHospitalReviewerService hospitalReviewerService;

    @Autowired
    private IHospitalBaseInfoService hospitalBaseInfoService;

    @Autowired
    private IHospitalReviewCycleService hospitalReviewCycleService;

    @Autowired
    private IReviewerBaseInfoService reviewerBaseInfoService;

    @Autowired
    private ICstDomainService cstDomainService;

    @Override
    public Map<String, Object> hosRewPlanParam(String applyNo) {
        Map<String, Object> hosRewPlanParamMap = new HashMap<>();
        // 查询医院名称
        QueryBaseConditionDTO conditionDTO = new QueryBaseConditionDTO();
        conditionDTO.setCommonId(applyNo);
        HospitalBaseInfo hospitalBaseInfo = hospitalBaseInfoService.selectHospitalByApplyNo(conditionDTO);
        if (Objects.isNull(hospitalBaseInfo)) {
            throw new ServiceException(ServiceExceptionEnum.HOSPITAL_ERROR_1000015);
        }
        hosRewPlanParamMap.put("hospitalName", hospitalBaseInfo.getHospitalName());
        // 通过applyNo评审员列表
        List<Map<String, Object>> reviewerUserInfoList = hospitalReviewerService.selectReviewerUserInfoByApplyNo(applyNo);
        for (Map<String, Object> reviewerUserInfo : reviewerUserInfoList) {
            byte[] bs = (byte[]) reviewerUserInfo.get("fieldIdList");
            reviewerUserInfo.put("fieldIdList", new String(bs));
            reviewerUserInfo.put("name", new String((byte[]) reviewerUserInfo.get("name")));
        }
        // 通过applyNo评审周期
        List<HospitalReviewCycle> hospitalReviewCycleList
                = hospitalReviewCycleService.selectHospitalReviewCycleByApplyNo(applyNo);
        // 获取 现场评审 时间
        HospitalReviewCycle siteReviewCycle = hospitalReviewCycleList.stream()
                .filter(hospitalReviewCycle -> "4".equals(hospitalReviewCycle.getStageValue()))
                .findFirst()
                .orElse(new HospitalReviewCycle());
        if (CharSequenceUtil.isEmpty(siteReviewCycle.getCycle())) {
            throw new ServiceException(ServiceExceptionEnum.ERROR_REVIEW_CYCLE_1000000);
        }
        // 组装现场评审时间
        String cycle = siteReviewCycle.getCycle();
        String[] cycleDates = cycle.split(",");
        StringBuilder siteCycle = new StringBuilder();
        String start = DateUtil.format(DateUtil.parse(cycleDates[0]), DatePattern.CHINESE_DATE_PATTERN);
        String end = DateUtil.format(DateUtil.parse(cycleDates[1]), DatePattern.CHINESE_DATE_PATTERN);
        siteCycle.append(start).append("-").append(end);
        hosRewPlanParamMap.put("siteCycle", siteCycle.toString());

        //查询领域表，获取分组名称，根据评审员分组Id匹配分组名
        CstDomain cstDomain = new CstDomain();
        cstDomain.setState(Constants.INT_ONE);
        List<CstDomain> cstDomains = cstDomainService.selectCstDomainList(cstDomain);
        if (CollectionUtils.isEmpty(cstDomains)) {
            throw new ServiceException("查询领域数据为空，请联系管理员");
        }
        Map<String, String> groupInfoMap = cstDomains.stream().collect(Collectors.toMap(o->o.getId().toString(), CstDomain::getGroupName));

        // 组装评审组长
        Map<String, Object> reviewerLeader = reviewerUserInfoList.stream()
                .filter(userInfo -> MapUtil.getInt(userInfo, "leaderIs") == 1)
                .findFirst()
                .orElse(new HashMap<>());
        if (MapUtil.isEmpty(reviewerLeader)) {
            throw new ServiceException(ServiceExceptionEnum.REVIEWER_ERROR_1000007);
        }

        //如果组长分配多组的情况，按分组正序排序
        List<Map<String, Object>> reviewerLeaderList = packReviewerGroupNameList(reviewerLeader,groupInfoMap);
        hosRewPlanParamMap.put("reviewerLeaderList", reviewerLeaderList);

        reviewerUserInfoList.remove(reviewerLeader);

        // 组装资深评审员
        Map<String, Object> seniorReviewer = reviewerUserInfoList.stream()
                .filter(userInfo -> Constants.HospitalConstants.SENIOR_REVIEW.equals(MapUtil.getStr(userInfo, "fieldIdList")))
                .findFirst()
                .orElse(new HashMap<>());
        if (MapUtil.isNotEmpty(seniorReviewer)) {
            reviewerUserInfoList.remove(seniorReviewer);
        }

        // 组装评审学员
        List<Map<String, Object>> traineesReviewList = reviewerUserInfoList.stream()
                .filter(userInfo -> Constants.HospitalConstants.TRAINEES_REVIEW.equals(MapUtil.getStr(userInfo, "fieldIdList")))
                .collect(Collectors.toList());
        hosRewPlanParamMap.put("traineesReviewList", traineesReviewList);

        // 组装普通评审员
        List<Map<String, Object>> ordinaryReviewList = new ArrayList<>();
        reviewerUserInfoList.stream()
                .filter(userInfo -> !Constants.HospitalConstants.SENIOR_REVIEW.equals(MapUtil.getStr(userInfo, "fieldIdList"))
                        && !Constants.HospitalConstants.TRAINEES_REVIEW.equals(MapUtil.getStr(userInfo, "fieldIdList")))
                .forEach(reviewer -> {
                    List<Map<String, Object>> reviewerList = packReviewerGroupNameList(reviewer,groupInfoMap);
                    ordinaryReviewList.addAll(reviewerList);
                });
        ordinaryReviewList.sort(new MapComparator("fieldId", false));

        hosRewPlanParamMap.put("ordinaryReviewList", ordinaryReviewList);

        // 组装现场评审时间前二个月时间点
        DateTime beforeCycleDate = DateUtil.offsetMonth(DateUtil.parseDate(cycleDates[0]), -2);
        String beforeCycleDateStr = DateUtil.format(beforeCycleDate, DatePattern.CHINESE_DATE_PATTERN);
        hosRewPlanParamMap.put("beforeCycleDateStr", beforeCycleDateStr);

        // 组装医院对评审员 学员是否有拒绝情况
        // 判断普通评审员是否是拒绝
        List<Map<String, Object>> rejectReviewerList = ordinaryReviewList.stream()
                .filter(userInfo -> MapUtil.getInt(userInfo, "reviewerStatus") == 2)
                .collect(Collectors.toList());

        // 判断组长是否为拒绝
        if (MapUtil.getInt(reviewerLeader, "reviewerStatus") == 2) {
            rejectReviewerList.add(reviewerLeader);
        }

        hosRewPlanParamMap.put("rejectReviewerList", rejectReviewerList);

        // 判断学员是否有拒绝的
        List<Map<String, Object>> rejectTraineesReviewList = traineesReviewList.stream()
                .filter(userInfo -> MapUtil.getInt(userInfo, "reviewerStatus") == 2)
                .collect(Collectors.toList());
        hosRewPlanParamMap.put("rejectTraineesReviewList", rejectTraineesReviewList);

        // 拼接
        StringBuilder rejectRec = new StringBuilder();
        if (CollUtil.isNotEmpty(rejectReviewerList)) {
            for (Map<String, Object> rejectReviewer : rejectReviewerList) {
                rejectRec.append(rejectReviewer.get("name")).append("、");
            }
        }
        if (CollUtil.isNotEmpty(rejectTraineesReviewList)) {
            for (Map<String, Object> rejectTraineesReview : rejectTraineesReviewList) {
                rejectRec.append(rejectTraineesReview.get("name")).append("、");
            }
        }
        if (rejectRec.length() > 0) {
            rejectRec.deleteCharAt(rejectRec.length() - 1);
            hosRewPlanParamMap.put("rejectRecStr", rejectRec.toString());
        }
        return hosRewPlanParamMap;
    }

    /**
     * 封装评审分组名称
     *
     * @param reviewerMap 评审员信息（分配的分组id，组长状态，评审状态，评审员用户Id,昵称）
     * @param groupInfoMap 分组信息Map<分组Id,分组名>
     * @return
     */
    private List<Map<String, Object>> packReviewerGroupNameList(Map<String, Object> reviewerMap, Map<String, String> groupInfoMap) {
        String fieldIdList = reviewerMap.get("fieldIdList").toString();
        if (StringUtils.isBlank(fieldIdList)) {
            log.error("评审员分组信息fieldIdList为空，数据为{}", reviewerMap);
            throw new ServiceException("评审员分组信息为空，请联系管理员");
        }
        List<Map<String, Object>> reviewerList = Arrays.stream(fieldIdList.split(",")).sorted()
                .map(fieldId -> {
                            String groupName = groupInfoMap.get(fieldId);
                            if (StringUtils.isBlank(groupName)) {
                                log.error("评审员分组信息：{}，领域分组配置：{}", reviewerMap, groupInfoMap);
                                throw new ServiceException("评审员获取对应分组名称为空，请联系管理员");
                            }
                            Map<String, Object> reviewerLeaderCopy = new HashMap<>(reviewerMap);
                            reviewerLeaderCopy.put("fieldId", fieldId);
                            reviewerLeaderCopy.put("groupName", groupName);
                            return reviewerLeaderCopy;
                        }
                ).collect(Collectors.toList());

        return reviewerList;
    }

    @Override
    public Map<String, Object> reviewInterestParam(String applyNO, String accountId) {
        Map<String, Object> reviewInterestParam = new HashMap<>();
        // 查询对应的评审员基础数据
        ReviewerBaseInfo reviewerBaseInfo = reviewerBaseInfoService.selectReviewerBaseInfoByAccountId(accountId);
        HospitalReviewer hospitalReviewer = hospitalReviewerService.selectHosRevByAlnAndAct(applyNO, accountId);
        reviewInterestParam.put("reviewerName", reviewerBaseInfo.getReviewerName());
        reviewInterestParam.put("reviewerMobile", reviewerBaseInfo.getReviewerMobile());
        reviewInterestParam.put("companyPost", reviewerBaseInfo.getCompanyPost());
        reviewInterestParam.put("company", reviewerBaseInfo.getCompany());
        reviewInterestParam.put("hasInterest", hospitalReviewer.getHasInterest());
        reviewInterestParam.put("interestDesc", hospitalReviewer.getInterestDesc());
        return reviewInterestParam;
    }
}
