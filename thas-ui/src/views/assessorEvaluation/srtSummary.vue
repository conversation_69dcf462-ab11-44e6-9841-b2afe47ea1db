
<!--  -->
<template>
  <div class='srt_summary'>
    <el-tabs v-model="domainActive" @tab-click="handleClick">
      <el-tab-pane v-for="(first, index1) in groupList" :key="index1" :label="first.groupDetail" :name="first.groupId">
        <div style="margin-bottom:20px;">
          <span :style="{ verticalAlign: !(initReadonly(first) || first.readonly) ? 'top' : 'inherit' }">组小结：</span>
          <span v-if="initReadonly(first) || first.readonly">{{ first.summary }}</span>
          <el-input v-else v-model.trim="first.summary" type="textarea" :rows="4"
            :class="{ inputError: validateAll(first.groupId) }" style="display:inline-block;width:80%;" :maxlength="1000"
           ></el-input>
          <!--  -->
          <template v-if="canModify">
            <el-button v-if="!initReadonly(first) || (!first.readonly && first.readonly != undefined)" type="primary"
              size="mini" @click="save(first)" style="margin-left:10px;vertical-align: top;"
              :disabled='loadingMap[first.groupId]'>提交</el-button>
            <el-button size="mini" v-else @click="modify1(index1, first)" style="margin-left:10px;">修改</el-button>
          </template>
          <span v-if="validateAll(first.groupId)" class="empty-error" style="margin-left:60px;">请输入小结</span>
        </div>
        <el-table v-if="topicMap[index1]" :data="topicMap[index1]" row-key="groupId" border default-expand-all
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
          <el-table-column label="主题">
            <template slot-scope="scope">
              <span>{{ scope.row.groupDetail }}</span>
            </template>
          </el-table-column>
          <el-table-column label="小结" width="480">
            <template slot-scope="scope">
              <span v-if="initReadonly2(scope.row) || scope.row.readonly">{{ scope.row.summary }}</span>
              <el-input v-else v-model.trim="scope.row.summary" type="textarea" :rows="2"
                :class="{ inputError: validateAll(scope.row.groupId) }" @input="summaryInput(scope.row)" :maxlength="500"
               ></el-input>
              <span v-if="validateAll(scope.row.groupId)" class="empty-error">请输入小结</span>
            </template>
          </el-table-column>
          <el-table-column v-if="canModify" label="操作" width="90">
            <template slot-scope="scope">
              <el-button v-if="!initReadonly2(scope.row) || (!scope.row.readonly && scope.row.readonly != undefined)"
                type="primary" size="mini" @click="save(scope.row)"
                :disabled="loadingMap[scope.row.groupId]">提交</el-button>
              <el-button v-else @click="modify2(index1, scope.row)" size="mini">修改</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <el-button v-if="showBtn" type="primary" style="margin-top:20px;" @click="submitOther">提交所有未提交(测试)</el-button>
  </div>
</template>

<script>
export default {
  name: 'SrtSummary',
  components: {},
  props: {
    domainGroups: {
      type: Array,
      default: () => [],
    },
    fitClauseIds: {
      type: Array,
      default: () => [],
    },
    flags: {
      type: Array,
      default: () => [],
    },
    srtMap: {
      type: Object,
      default: () => { },
    },
    showBtn: {
      type: Boolean,
      default: () => false,
    },
    canModify: {
      type: Boolean,
      default: () => false
    },
    clauseMoveInfo: {
      type: Array,
      default: () => []
    },
    distributeClauseIds: {
      type: Array,
      default: () => [],
    }
  },
  watch: {
    domainGroups: {
      immediate: true,
      deep: true,
      handler() {
        let domainIds = this.domainGroupsFilters3.map(({ groupId }) => groupId) || [];
        if (!domainIds.includes(this.domainActive)) {
          if (!this.domainGroupsFilters3[0]) return;
          this.domainActive = this.domainGroupsFilters3[0].groupId;
        }
      },
    },
  },
  data() {
    return {
      domainActive: 0,
      children: [],
      value1: '',
      value2: '',
      row: {},
      submitLoading: false,
      contentMap: {},
      validateList: [],
      loadingMap: {},
      groupList: [],
      topicMap: {},
    };
  },

  computed: {
    initReadonly() {
      return row => {
        let { groupId } = row;
        let data = this.srtMap[groupId];
        if (data && row.readonly == undefined) {
          this.$set(row, 'summary', data.autDesc)
          this.$set(this.loadingMap, groupId, false);
          return true
        }

        this.contentMap[groupId] = {
          groupId,
          autDesc: data ? data.autDesc : row.summary
        }

        if (row.readonly === true) return true;

        return false;
      }
    },
    initReadonly2() {
      return row => {
        let { groupId } = row;
        let data = this.srtMap[groupId];
        if (data && row.readonly == undefined) {
          this.$set(row, 'summary', data.autDesc)
          this.$set(this.loadingMap, groupId, false);
          return true
        }

        this.contentMap[groupId] = {
          groupId,
          autDesc: data ? data.autDesc : row.summary
        }

        if (row.readonly === true) return true;

        return false;
      }
    },
    validate() {
      return groupId => {
        let data = this.contentMap[groupId];
        let flag = data ? data.autDesc : true;
        if (flag) {
          if (this.validateList.includes(groupId)) {
            this.validateList.splice(this.validateList.indexOf(groupId), 1);
          }
        } else {
          !this.validateList.includes(groupId) && this.validateList.push(groupId);
        }
        return flag
      }
    },
    validateAll() {
      return groupId => {
        return this.validateList.includes(groupId);
      }
    },
    domainGroupsFilters() {
      let list = this.domainGroups.filter((data, index) => this.flags[index]);
      return list;
    },

    domainGroupsFilters2() {
      return (index) => {
        let list = this.domainGroups.filter((data, index) => this.flags[index]);
        let list2 = []
        // 分组列表
        list.forEach((group, idx) => {
          // 分组
          if (idx === index) {
            // 大主题列表
            list2 = group.children;
            let final = list2.filter(topic => {
              // 大主题
              // 小主题列表
              let list3 = topic.children
              let flag3 = list3.map(sup => {
                // 小主题
                // 款项列表
                let list4 = sup.children
                let flag4 = list4.map(({ groupDetail }) => groupDetail).every(id => this.fitClauseIds.includes(id))
                return !flag4
              })
              return flag3.includes(true);
            })
            list2 = final.map(item => {
              let { children, hasChildren, ...data } = item
              return {
                ...data,
                hasChildren: false
              }
            })
          }
        })
        return list2;
      }
    },

    domainGroupsFilters3() {
      let list = this.dealWithAllClauseId2Topic();
      let ll = list.map(group => {
        let { children, ...data } = group;
        let move = this.clauseMoveInfo.find(info => info.groupId == group.groupId) || {}
        let { outputClauseIds = [] } = move
        let kids = children.filter(topic => {
          let clauseIds = topic.clauseIds
          let allFit = clauseIds.every(clauseId => this.fitClauseIds.map(id => Number(id)).includes(Number(clauseId)))
          let allMove = clauseIds.every(clauseId => outputClauseIds.map(id => Number(id)).includes(Number(clauseId)))
          let allIsHim = clauseIds.some(clauseId => this.distributeClauseIds.map(id => Number(id)).includes(Number(clauseId)))
          return !allFit && !allMove && allIsHim
        }).map(topic => {
          return JSON.parse(JSON.stringify(topic))
        })
        return {
          children: kids,
          hasChildren: kids.length != 0,
          ...data
        }
      })
      return ll.filter(child => child.children.length)
    }
  },

  created() { },

  mounted() {
    this.init()
    // let list = this.dealWithAllClauseId2Topic();
    // let list = this.domainGroupsFilters3;
    // console.log(list);
  },

  methods: {
    init() {
      this.groupList = this.domainGroupsFilters3
      this.groupList.forEach((group, index) => {
        this.$set(this.topicMap, index, group.hasChildren ? group.children : [])
      })
    },

    handleClick() { },

    modify1(index, row) {
      let data = this.domainGroupsFilters3[index];
      if (!data) return;
      this.$set(data, 'readonly', false)
      this.$set(row, 'readonly', false)
    },

    modify2(index, row) {
      let data = (this.topicMap[index] || []).find(({ groupId }) => groupId == row.groupId);
      if (!data) return;
      this.$set(data, 'readonly', false)
      this.$set(row, 'readonly', false)
    },

    change() {
      let list = this.domainGroupsFilters3[this.domainActive];
      let domain = list.find(({ groupId }) => groupId == this.value1);
      this.children = domain ? domain.children : [];
    },

    load(tree, treeNode, resolve) {

    },

    summaryInput(row) {

    },

    readonly(readonly) {
      // this.loadingMap[this.row.groupId] = false;
      // this.submitLoading = false;
      // if (this.row) {
      //   this.$set(this.row, 'readonly', readonly)
      // }
    },

    submitOther() {
      let list = [];
      this.recursion(this.domainGroupsFilters3, list, [2, 3, 4]);
      let dataList = [];

      list.forEach((list, index) => {
        let submitType = ''
        if (index == 0) {
          submitType = this.AutSaAudCurrentStatusEnum.SR_GROUP_SUMMARY
        } else if (index == 1) {
          submitType = this.AutSaAudCurrentStatusEnum.SR_BT_SUMMARY
        }
        if (!dataList[index]) dataList[index] = [];
        list.forEach(id => {
          dataList[index].push({
            clauseId: id,
            autResult: 1,
            autDesc: index == 0 ? '自动填充分组小结' : '自动填充大主题小结',
            submitType
          })
        })
      })

      console.log(dataList);
      this.$emit('srtSubmit', dataList)
    },

    recursion(recursionList, bufferList, typeOut) {
      if (!Array.isArray(recursionList) || !recursionList.length) return bufferList;
      recursionList.forEach(data => {
        if (typeOut.includes(data.type)) return;
        if (!data.readonly) {
          if (!bufferList[data.type]) bufferList[data.type] = [];
          bufferList[data.type].push(data.groupId);
        }
        return this.recursion(data.children, bufferList, typeOut)
      })
      return bufferList;
    },

    dealWithAllClauseId2Topic() {
      let groups = this.domainGroups;
      let newGroups = []
      groups.forEach(group => {
        let { children, ...data } = group
        let newG = Object.assign({ children: [] }, data)
        newGroups.push(newG)
        let topics = children;
        topics.forEach(topic => {
          let { children, ...data } = topic
          let newT = Object.assign({ clauseIds: [] }, data)
          newG.children.push(newT)
          let subs = children;
          subs.forEach(sub => {
            newT.clauseIds.push(...sub.children.map(clause => Number(clause.groupDetail)))
          })
        })
      })
      return newGroups
    },

    save(row) {
      this.row = row;
      let { groupId, type, summary } = row;
      if (!this.validate(groupId)) return;
      if (!summary) return;
      // this.submitLoading = true;
      // this.loadingMap[groupId] = true;
      this.$set(this.loadingMap, groupId, true)
      let submitType = ''
      if (type == 0) {
        submitType = this.AutSaAudCurrentStatusEnum.SR_GROUP_SUMMARY
      } else if (type == 1) {
        submitType = this.AutSaAudCurrentStatusEnum.SR_BT_SUMMARY
      }
      else if (type == 2) {
        submitType = this.AutSaAudCurrentStatusEnum.SR_ST_SUMMARY
      }

      this.$emit('srtSubmit', {
        clauseId: groupId,
        autResult: 1,
        autDesc: summary
      }, submitType)
    },

    reInit() {
      if (!this.row) return;
      this.$set(this.row, 'readonly', true);
      this.$set(this.loadingMap, this.row.groupId, false)
    }
  }
}

</script>
<style lang='scss' >
.srt_summary {
  height: 100%;

  .el-tabs {
    height: calc(100% - 10px);

    .el-tabs__content {
      overflow-y: scroll;
      height: calc(95% - 40px);

      &::-webkit-scrollbar {
        display: none;
        /*隐藏滚动条*/
      }

      scrollbar-color: transparent transparent;
      scrollbar-track-color: transparent;
      -ms-scrollbar-track-color: transparent;
      // height: 100%;
    }
  }

  .empty-error {
    font-size: 12px;
    color: red;
    display: block;
  }

  .inputError {
    .el-input__inner {
      border-color: red;
    }
  }
}
</style>
