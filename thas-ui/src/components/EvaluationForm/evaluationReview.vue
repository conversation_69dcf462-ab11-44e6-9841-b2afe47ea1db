<!--  -->
<template>
  <div class="evaluation-review evaluation" v-loading="submitLoading"  >
    <template v-if="'sr_report_m' != (aut || {}).submitType">
      <el-button v-if="entryModify && !readonly" class="objection" size="small" type="text"
        @click="edit = true">修改评价内容</el-button>
      <el-button v-if="exitModify && !readonly" class="objection" size="small" type="text"
        @click="edit = false">放弃修改</el-button>
    </template>
    <el-form ref="evaluationForm" :model="formData" :label-width="labelWidth" :rules="rules">
      <el-form-item v-if="isLeader && aut" class="form-item readonly" :label="updateBy">
        {{ aut.updateBy || aut.createBy }}
      </el-form-item>
      <el-form-item class="form-item" :class="{ readonly: !modifying && (!exitModify || !isFinal) }"
        :label="reviewResultLabel" prop="autResult">
        <div style="display: flex; justify-content: space-between;">
          <div>
            <el-select v-if="modifying || (exitModify && isFinal)" v-model="formData.autResult" clearable filterable placeholder="请选择评价结果"
              @change="resultChange">
              <el-option v-for="(option, index) in reviewResultOptions" :key="index" :value="option.dictValue"
                :label="option.dictLabel"></el-option>
            </el-select>
            <dict-span :noTag='true' v-else :key="checkSummaryOptions.length + Number(formData.autResult)"
              :value="formData.autResult" :options="reviewResultOptions" type="default"></dict-span>
          </div>
          <el-button v-show="!modifying && !readonly && status === '030202' && ($store.getters.userId == aut.accountId)" type="text" @click="toggleModify">编辑</el-button>
        </div>
      </el-form-item>

      <template v-if="formData.beforeAutDesc && !modifying && !edit && rightStatus">
        <el-form-item v-if="formData.autResult == 1 || formData.autResult == 2 || formData.autResult == 3" class="form-item beforeDesc"
          :label="`亮点：`">
          <span>{{ formData.beforeAutDescMap.autAdvantage || '--' }}</span>
        </el-form-item>
        <el-form-item v-if="formData.autResult == 4 || formData.autResult == 5" class="form-item beforeDesc"
          :label="`不足：`">
          <span>{{ formData.beforeAutDescMap.autEvaluate || '--' }}</span>
        </el-form-item>
        <el-form-item v-if="formData.autResult == 4 || formData.autResult == 5" class="form-item beforeDesc"
          :label="`整改建议：`">
          <span>{{ formData.beforeAutDescMap.autProposal || '--' }}</span>
        </el-form-item>
        <el-form-item v-if="formData.autResult" class="form-item beforeDesc" :label="`改进机会：`">
          <span>{{ formData.beforeAutDescMap.autImprove || '--' }}</span>
        </el-form-item>
        <!-- <el-form-item v-if="!formData.autResult || formData.autResult == 6" class="form-item beforeDesc" :label="`评价描述：`">
          <span>{{ formData.beforeAutDescMap.autDesc || '--' }}</span>
        </el-form-item> -->

        <el-divider content-position="left">评审组长修正的内容：</el-divider>

        <!-- <el-form-item class="form-item readonly" label="评审组长">
          {{ aut.updateBy }}
              </el-form-item> -->
        <!-- <el-form-item class="form-item beforeDesc" label="评审组长修正的内容：">
              </el-form-item> -->
      </template>

      <el-form-item v-if="formData.autResult == 1 || formData.autResult == 2 || formData.autResult == 3" class="form-item"
        :class="{ readonly: !modifying && (!exitModify || !isFinal) }" :label="`${correction}亮点：`" prop="autAdvantage">
        <el-input v-if="modifying || exitModify" type="textarea" v-model="formData.autAdvantage" placeholder="请填写亮点"
          :rows="4" :maxlength="descMaxLength"></el-input>
        <span v-else>{{ formData.autAdvantage || '--' }}</span>
      </el-form-item>
      <el-form-item v-if="formData.autResult == 4 || formData.autResult == 5" class="form-item"
        :class="{ readonly: !modifying && (!exitModify || !isFinal) }" :label="`${correction}不足：`" prop="autEvaluate">
        <el-input v-if="modifying || exitModify" type="textarea" v-model="formData.autEvaluate" placeholder="请填写不足"
          :rows="4" :maxlength="descMaxLength"></el-input>
        <span v-else>{{ formData.autEvaluate || '--' }}</span>
      </el-form-item>
      <el-form-item v-if="formData.autResult == 4 || formData.autResult == 5" class="form-item"
        :class="{ readonly: !modifying && (!exitModify || !isFinal) }" :label="`${correction}整改建议：`" prop="autProposal">
        <el-input v-if="modifying || exitModify" type="textarea" v-model="formData.autProposal" placeholder="请填写整改建议"
          :rows="4" :maxlength="descMaxLength"></el-input>
        <span v-else>{{ formData.autProposal || '--' }}</span>
      </el-form-item>
      <el-form-item class="form-item"
        :class="{ readonly: !modifying && (!exitModify || !isFinal) }" :label="`${correction}改进机会：`" prop="autImprove">
        <el-input v-if="modifying || exitModify" type="textarea" v-model="formData.autImprove" placeholder="请填写改进机会"
          :rows="4" :maxlength="descMaxLength"></el-input>
        <span v-else>{{ formData.autImprove || '--' }}</span>
      </el-form-item>
      <!-- <el-form-item v-if="!formData.autResult || formData.autResult == 6" class="form-item"
        :class="{ readonly: !modifying && (!exitModify || !isFinal) }" :label="`${correction}评价描述：`" prop="autDesc">
        <el-input v-if="modifying || exitModify" type="textarea" v-model="formData.autDesc" placeholder="请填写评价描述"
          :rows="4" :maxlength="descMaxLength"></el-input>
        <span v-else>{{ formData.autDesc || '--' }}</span>
      </el-form-item> -->

      <el-form-item label="风险：" v-if="showRiskWithResult">
        <el-popover placement="top" width="400" trigger="click">
          <el-image :src="require('../../assets/images/able.png')"></el-image>
          <el-button type="text" slot="reference" icon="el-icon-question" style="float: left"></el-button>
        </el-popover>
        <div class="risk-content" style="float: left">
          <el-form-item class="risk" label="可能性：" prop="riskPossibility" :rules="rules.riskPossibility"
            label-width="80px">
            <el-radio-group v-model.trim="formData.riskPossibility" :disabled='!modifying && !exitModify'>
              <el-radio :label="1">低</el-radio>
              <el-radio :label="2">中</el-radio>
              <el-radio :label="3">高</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item class="risk" label="影响：" prop="riskImpact" :rules="rules.riskImpact" label-width="80px">
            <el-radio-group v-model.trim="formData.riskImpact" :disabled='!modifying && !exitModify'>
              <el-radio :label="1">低</el-radio>
              <el-radio :label="2">中</el-radio>
              <el-radio :label="3">高</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item class="risk" label="总分：">
            {{ totalRangeText }}
          </el-form-item>
        </div>
      </el-form-item>

      <el-form-item class="form-item" label=" " v-if="modifying || exitModify">
        <el-button type="primary" @click="submit">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import mixin from "./mixin";

export default {
  name: "EvaluationReview",
  mixins: [mixin],
  components: {},
  props: {
    isLeader: {
      type: Boolean,
      default: () => false
    },

    modifier: {
      type: String,
      default: () => ''
    },
  },
  watch: {
    edit() {
      if (!this.edit) {
        this.reply();
      }
    }
  },

  data() {
    return {
      autDescValidate: false,
      edit: false,
      submitForm: null
    };
  },

  computed: {
    // 是否是复查（审核）
    isCheck() {
      let list =
        [
          this.AutSaAudCurrentStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW,
          this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC,
        ] || [];
      return list.includes(this.status);
    },

    psModify() {
      let statuses = [
        this.AutSaAudCurrentStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC,
        this.AutSaAudCurrentStatusEnum.WAIT_REPORT
      ];

      return statuses.includes(this.status) &&
        (this.$route.query.type == this.AutSaAudCurrentStatusEnum.SR_V_CLAUSE ||
          this.type == this.AutSaAudCurrentStatusEnum.SR_REPORT_M)
        && this.isLeader;
    },

    entryModify() {
      return this.psModify && !this.edit;
    },

    exitModify() {
      return this.psModify && this.edit;
    },

    isFinal() {
      return this.type == this.AutSaAudCurrentStatusEnum.SR_REPORT_M
    },

    showRiskWithResult() {
      return (this.formData.autResult == 5 || this.formData.autResult == 4) && this.clause.isStar == 1;
    },

    showBuZhu() {
      return this.formData.autResult == 5 || this.formData.autResult == 4;
    },

    labelLabel() {
      if (this.formData.autResult == 1 || this.formData.autResult == 2) {
        return '亮点：'
      } else if (this.formData.autResult == 3) {
        return '改进机会：'
      } else if (this.formData.autResult == 4 || this.formData.autResult == 5) {
        return '整改建议：'
      } else {
        return '评价描述：'
      }
    },

    correction() {
      return '';
      // return this.formData.beforeAutDesc && this.formData.beforeAutDescMap && !this.edit ? '评审组长修正-' : ''
    },

    labelDesc() {
      if (this.formData.autResult == 1 || this.formData.autResult == 2) {
        return '请填写亮点'
      } else if (this.formData.autResult == 3 || this.formData.autResult == 4 || this.formData.autResult == 5) {
        return '请填写改进机会'
      } else {
        return '请填写评价描述'
      }
    },

    descRule() {
      let flag = false;
      if (this.formData.autResult == 1 || this.formData.autResult == 2) {
        flag = true;
      }
      return {
        required: flag,
        message: this.labelDesc,
        trigger: 'blur'
      }
    },
    totalRange() {
      return (
        Number(this.formData.riskImpact) * Number(this.formData.riskPossibility)
      );
    },
    totalRangeText() {
      let riskMap = {
        0: '无风险',
        1: '低风险',
        2: '低风险',
        3: '中风险',
        4: '中风险',
        6: '高风险',
        9: '严重风险',
      }
      return `${this.totalRange} (${riskMap[this.totalRange]})`;
    },

    updateBy() {
      return this.modifier ? this.modifier + "：" : '评审员：'
    },

    reviewResultLabel() {
      // if (this.aut?.submitType === "sr_report_m") {
      //   return "审查组长评价结果："
      // }
      if (this.modifier) {
        return this.modifier + '评价结果：'
      }
      return this.isLeader ? "组员评价结果：" : "评价结果：";
    },

    rightStatus() {
      let list = [
        this.AutSaAudCurrentStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_REJECT,
      ]
      return list.includes(this.status)
    }
  },

  created() {
    this.autResultMessage = "请选择评价结果";
    this.autDescMessage = "请填写评价描述";
  },

  mounted() {
  },

  methods: {
    submit() {
      const evaluationForm = this.$refs.evaluationForm;
      if (evaluationForm && evaluationForm.validate) {
        evaluationForm.validate((valid) => {
          if (!valid) return;
          this.submitForm = JSON.parse(JSON.stringify(this.formData))
          let { autAdvantage, autEvaluate, autProposal, autImprove, autDesc } = this.submitForm
          this.submitForm.autDesc = JSON.stringify({
              autAdvantage, autEvaluate, autProposal, autImprove, autDesc:autDesc
            })
          this.submitEmit();
        });
      }
    },

    clearDesc() {
      // this.formData.reviewerRespond = '';
      this.formData.autDesc = ''
      this.formData.autAdvantage = ''
      this.formData.autEvaluate = ''
      this.formData.autProposal = ''
      this.formData.autImprove = ''
      this.formData.riskPossibility = ''
      this.formData.riskImpact = ''
    },

    componentCallBack() {
      if (this.type === this.AutSaAudCurrentStatusEnum.SR_REPORT_M && this.aut.submitType == this.AutSaAudCurrentStatusEnum.SR_CLAUSE) {
        this.initAutFormData(this.aut_backup, this.aut);
      }
    },
    // 切换可编辑状态
    toggleModify() {
      this.modifying = true
    }
  },
};
</script>

<style lang="scss">
.evaluation-review {
  .el-divider--horizontal {
    width: calc(100% - 180px);
    margin: 15px auto;
  }

  .el-divider__text {
    background-color: rgba(250, 250, 250);
  }

  &.evaluation .form-item.beforeDesc {
    margin-bottom: 2px;
  }
}
</style>
