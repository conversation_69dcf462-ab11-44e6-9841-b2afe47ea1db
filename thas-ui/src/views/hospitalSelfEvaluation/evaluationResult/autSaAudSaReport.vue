<!--  -->
<template>
  <div class='autSaAudSaReport'>
    <el-card class="box-card" :class="{noPadding: !toggleShow}" shadow="never">
      <div slot="header" class="clearfix">
        <span>
          <h4 style="font-weight:500;float:left;margin:0;">{{title || '自评结果表'}}</h4>
          <el-tooltip class="item" effect="dark" content="如果不满意当前自评结果，可以重新自评；如果无需修改，请确认自评结果！" placement="right">
            <i v-if="!flag" class="el-icon-info" style="color:#1890ff;text-indent:5px;"></i>
          </el-tooltip>
        </span>
        <span style="float:right;color:#1890ff;font-size: 12px;cursor:pointer;" @click="toggleShow = !toggleShow">
          <i :class="{'el-icon-arrow-up': toggleShow, 'el-icon-arrow-down': !toggleShow,}"></i>
          {{toggleShow ? '收起': '展开'}}
        </span>
      </div>

      <h4 :style="{marginTop:toggleShow ? '0' : '' , color: autSaAudResult==1 ? 'green' : 'red' }">{{sums[autSaAudResult]}}</h4>

      <el-table v-show="toggleShow" ref="clauseTable" :data="dataSource" border stripe :span-method="arraySpanMethod">
        <el-table-column prop="title" label=" " align="center" width="200">
          <template slot-scope="{row}">
            <p :class="[row.className]">{{row.title}}</p>
          </template>
        </el-table-column>
        <el-table-column prop="chapter" label=" " width="200" align="center">
          <template slot-scope="{row}">
            <p>{{row.chapter}}</p>
          </template>
        </el-table-column>
        <el-table-column prop="result" label="评价结果">
          <template slot-scope="{row}">
            <p :class="[row.className, row.listClass]">{{row.result}}</p>
          </template>
        </el-table-column>
        <el-table-column prop="count" label="款数">
          <template slot-scope="{row}">
            <p :class="[row.className]">{{row.count}}</p>
          </template>
        </el-table-column>
        <el-table-column prop="rate" label="达标占比">
          <template slot-scope="{row}">
            <p :class="[row.className]">{{row.rate}}</p>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import request from "@/utils/request";

export default {
  name: 'AutSaAudSaReport',
  components: {},
  props: {
    report: {
      type: Object,
      default: () => { },
    },
    flag: {
      type: Boolean,
      default: () => true
    },
    title: {
      type: String,
      default: () => ''
    },
    toggle: {
      type: [Boolean, null],
      default: () => null
    }
  },
  watch: {},
  data() {
    return {
      results: ['', '优秀', '良好', '达标', '部分达标', '不达标', '不适用', '达标及以上', '部分达标及以上'],
      chapters: ['', '第一章', '第二章', '第三章'],
      sums: ['', '通过认证', '有条件通过认证', '不通过认证',],
      autSaAudResult: 0,
      dataSource: [],
      basicSource: [],
      nonBasicSource: [],
      toggleShow: false,
    };
  },

  computed: {},

  created() {
    if (this.toggle === true || this.toggle === false) {
      this.toggleShow = this.toggle
    };
  },

  mounted() {
    if (this.report) {
      let { autSaAudReportListVos, autSaAudResult } = this.report;
      this.autSaAudResult = autSaAudResult;
      if (Array.isArray(autSaAudReportListVos)) {
        autSaAudReportListVos.forEach(report => {
          if (report.isBasic == 1) {
            this.nonBasicClauseInfo(report)
          } else {
            this.basicClauseInfo(report)
          }
        })
      }
      this.$set(this, 'dataSource', [...this.basicSource, ...this.nonBasicSource])
      this.$nextTick(() => this.changeTableHead());
    } else {
      this.query();
    }
  },

  methods: {
    query() {
      let loading = this.$loading();
      request({
        url: '/aut/sa/aud/queryList',
        method: "post",
        data: this.$route.query,
      }).then((res) => {
        // console.log(res);
        let data = res.rows[0];
        if (data && data.autSaAudReport) {
          let { autSaAudReportListVos, autSaAudResult } = data.autSaAudReport
          this.autSaAudResult = autSaAudResult;
          if (Array.isArray(autSaAudReportListVos)) {
            autSaAudReportListVos.forEach(report => {
              if (report.isBasic == 1) {
                this.nonBasicClauseInfo(report)
              } else {
                this.basicClauseInfo(report)
              }
            })
          }
          this.$set(this, 'dataSource', [...this.basicSource, ...this.nonBasicSource])
          this.$nextTick(() => this.changeTableHead());

        }
      }).finally(() => {
        // this.loading = false;
        loading.close()
      });
    },
    changeTableHead() {
      let headCells =
        this.$refs.clauseTable.$el.getElementsByClassName("el-table__header")[0]
          .rows[0].cells;
      // 将达标项长度为2
      headCells[2].colSpan = 2;
      // 隐藏章节条款项
      headCells[1].style.display = "none";
    },

    basicClauseInfo(report) {
      let data = {
        title: `基本款\n(共${report.sumCount}款)`,
        chapter: '',
        result: '',
        count: '',
        rate: ''
      }
      let reportList = report.reportList;
      reportList.sort((a, b) => {
        return a.autSaAudResult - b.autSaAudResult
      })
      let list = reportList.map((report, index) => {
        let { autSaAudResult, count, rate } = report;
        let value = {
          ...data
        }
        value.result = this.results[autSaAudResult];
        value.count = count;
        value.rate = `${rate}%`;
        value.className = (autSaAudResult == 7 || autSaAudResult == 8) ? 'weight' : '';
        value.listClass = autSaAudResult == 6 ? 'maladaptive' : ''
        return value
      }).filter((report, index) => index < 7)
      this.basicSource = list;
    },
    nonBasicClauseInfo(report) {
      let list = this.nonBasicInfo(report);
      let aList = report.chapterReportList.map(report => this.nonBasicInfo(report, true)).reduce((list, data) => {
        list.push(...data);
        return list;
      }, []);
      this.$set(this, 'nonBasicSource', [...list, ...aList])
    },

    nonBasicInfo(report, showChapter = false) {
      let data = {
        title: `非基本款\n(共${report.sumCount}款)`,
        chapter: '',
        result: '',
        count: '',
        rate: ''
      }
      let reportList = report.reportList;
      let chapterId = report.chapterId;
      let sumCount = report.sumCount
      reportList.sort((a, b) => {
        return a.autSaAudResult - b.autSaAudResult
      })
      let list = reportList.map((report, index) => {
        let { autSaAudResult, count, rate } = report;
        let value = {
          ...data
        }
        value.result = this.results[autSaAudResult];
        value.count = `${count}款`;
        value.rate = `${rate}%`;
        value.className = (autSaAudResult == 7 || autSaAudResult == 8) ? 'weight' : '';
        value.listClass = autSaAudResult == 6 ? 'maladaptive' : ''
        if (showChapter) value.chapter = `${this.chapters[chapterId]}\n(共${sumCount}款 \n非基本款)`;
        return value
      })
      return list;
    },
    arraySpanMethod({ rowIndex, columnIndex }) {
      if (columnIndex == 0) {
        if (rowIndex === 0) {
          return [7, 1]
        } else if (rowIndex === 7) {
          return [38, 1]
        }
        return [0, 0]
      } else if (columnIndex === 1) {
        if (rowIndex === 15) {
          return [8, 1]
        } else if (rowIndex === 23) {
          return [8, 1]
        } else if (rowIndex === 31) {
          return [8, 1]
        }
        return [0, 0]
      }
      if (rowIndex >= 0 && rowIndex <= 14) {
        if (columnIndex === 1) {
          return [1, 0]
        }
        if (columnIndex === 2) {
          return [1, 2]
        }
      }
    },
    toModify() {
      this.toggleShow = false;
    },
  }
}

</script>
<style lang='scss' scoped>
.box-card {
  margin-bottom: 10px;
}
.noPadding {
  ::v-deep .el-card__body {
    padding-bottom: 0;
    padding-top: 0;
  }
}
.weight {
  font-weight: 600;
}
.maladaptive {
  color: #CC9900;
}
</style>
<style lang="scss">
.autSaAudSaReport {
  ::v-deep .el-table .el-table__body-wrapper td {
    padding: 0;
  }
  p {
    margin: 0;
    padding: 0;
  }
  .el-table--medium .el-table__cell {
    padding: 5px 0;
  }
}
</style>