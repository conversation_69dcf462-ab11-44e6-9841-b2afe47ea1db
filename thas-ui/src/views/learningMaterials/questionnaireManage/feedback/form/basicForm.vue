<template>
  <div>
    <component :is="component" :ref="component" :key="type" :options="options" :edit="false" :reviewResultType="type"></component>
  </div>
</template>

<script>
import theory from './theory.vue';
import faculty from './faculty.vue';
import hospital from './hospital.vue';
import practice from './practice.vue';
import reviewer from './reviewer.vue';

export default {
  name: '',
  props: {
    type: {
      type: [String, Number],
      default: () => 0
    },
    edit: {
      type: Boolean,
      default: () => false
    },
    options: {
      type: Object,
      default: () => { }
    },
  },
  data() {
    return {
      componentMap: {
        5: 'theory',
        8: 'practice',
        6: 'hospital',
        7: 'faculty',
        9: 'reviewer',
      }
    };
  },

  components: {
    theory,
    faculty,
    hospital,
    practice,
    reviewer,
  },

  computed: {
    component() {
      return this.componentMap[this.type]
    }
  },

  mounted() { 
    // console.log(this.options);
  },

  methods: {
    async getValue() {
      return this.$refs[this.component].getValue()
    }
  }
}

</script>
<style lang='scss' scoped>
</style>