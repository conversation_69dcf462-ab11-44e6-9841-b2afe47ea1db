package com.thas.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 医疗结构初审员/评审员管理列表入参
 */
@Data
@ApiModel(value = "ReviewManageDTO")
public class ReviewManageDTO {

    @ApiModelProperty("医疗机构名称")
    private String hospitalName;

    @NotBlank(message = "查询类型不能为空")
    @ApiModelProperty(value = "类型 1初审员 2评审员")
    private Integer personForm;

}
