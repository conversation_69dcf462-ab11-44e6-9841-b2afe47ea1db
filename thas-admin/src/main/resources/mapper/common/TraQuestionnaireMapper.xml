<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.TraQuestionnaireMapper">

    <resultMap type="com.thas.web.domain.vo.TraQuestionnaireVO" id="TraQuestionnaireResult">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="type" column="type"/>
        <result property="description" column="description"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="answerNum" column="answerNum"/>
        <result property="doFinish" column="doFinish"/>
    </resultMap>

    <sql id="selectTraQuestionnaireVo">
        select *
        from tra_questionnaire
    </sql>

    <select id="selectTraQuestionnaireList" parameterType="com.thas.web.domain.dto.TraQuestionnaireDTO"
            resultMap="TraQuestionnaireResult">
        SELECT * from v_questionnaire
        <where>
            <if test="title != null and title != ''">and title = #{title}</if>
            <if test="type != null">and type = #{type}</if>
            <if test="createId != null">and create_id = #{createId}</if>
            <if test="status != null">and status = #{status}</if>
            <if test="doFinish != null">and doFinish = #{doFinish}</if>
            <if test="typeList != null and typeList.size > 0 ">
                and type in
                <foreach item="item" index="index" collection="typeList" open="(" separator="," close=")">
                     #{item}
                </foreach>
            </if>

        </where>
        ORDER BY update_time DESC
    </select>

    <select id="selectTraQuestionnaireById" parameterType="Long" resultMap="TraQuestionnaireResult">
        <include refid="selectTraQuestionnaireVo"/>
        where id = #{id}
    </select>

    <insert id="insertTraQuestionnaire" parameterType="TraQuestionnaire" useGeneratedKeys="true" keyProperty="id">
        insert into tra_questionnaire
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="type != null">type,</if>
            <if test="description != null">description,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createId != null">create_id,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="type != null">#{type},</if>
            <if test="description != null">#{description},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateTraQuestionnaire" parameterType="TraQuestionnaire">
        update tra_questionnaire
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="type != null">type = #{type},</if>
            <if test="description != null">description = #{description},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateStatus">
        update tra_questionnaire
        set status = #{status}
        where id = #{id}
    </update>

    <delete id="deleteTraQuestionnaireById" parameterType="Long">
        delete
        from tra_questionnaire
        where id = #{id}
    </delete>

    <delete id="deleteTraQuestionnaireByIds" parameterType="String">
        delete from tra_questionnaire where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>