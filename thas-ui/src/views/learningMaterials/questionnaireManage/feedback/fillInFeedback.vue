<template>
  <div class='fill-in-feedback'>
    <div class="content">
      <h1 style="text-align:center;padding-top:20px;">{{detail.title}}</h1>

      <p>{{detail.description}}</p>
      <BasicForm :key="keys" ref="basicForm" :type="type" :options="options" :edit="true"></BasicForm>

      <el-form ref="form" size="small" :model="{list}">
        <div class="question" v-for="(data, index) in list" :key="index">
          <h3>{{toChinese(index + 1)}}、{{data.question}}</h3>

          <div v-for="(question, idx) in data.questionList" :key="idx">
            <h4 v-show="data.questionList.length > 1">({{toChinese(idx + 1)}})、{{question.subtitle}}</h4>
            <span :class="`list.${index}.questionList.${idx}`"></span>
            <div v-if="question.type == 1">
              <div v-if="question.circulation">
                <el-tabs v-model="select[`${index}${idx}`]" type="border-card" style="box-shadow:none;">
                  <el-tab-pane v-for="(user) in circulation(question.cycleType)" :key="user.accountId" :label="user.name" :name="`${user.accountId}`">
                    <el-table :data="question.questionList" border :span-method="arraySpanMethod">
                      <el-table-column align="center" label="序号" type="index" width="50"></el-table-column>
                      <el-table-column :label="question.questionTitle.theme" prop="label" width="220">
                        <template slot-scope="{row}">
                          <pre style="white-space: pre-line;margin:0;">{{row.label}}</pre>
                        </template>
                      </el-table-column>
                      <el-table-column :label="question.questionTitle.score">
                        <template slot-scope="{row, $index}">
                          <el-form-item v-if="row.type == 1 || row.type == 3" :prop="valueProp(true, index, idx, $index, user.accountId)" :rules="[{required:true, message:'请勾选认可情况', trigger:['change', 'blur']}]">
                            <el-radio-group v-model="row.value[user.accountId]">
                              <el-radio v-for="score in row.type == 1 ? [5, 4, 3, 2, 1] : [5, 4, 3, 2, 1, 0]" :key="score" :label="score">{{score}}</el-radio>
                            </el-radio-group>
                          </el-form-item>
                          <el-form-item v-if="row.type == 2" :prop="commentProp(true, index, idx, $index, user.accountId)" :rules="[{required:true, message:'请输入', trigger:['change', 'blur']}]">
                            <el-input v-model="row.comment[user.accountId]" type="textarea" :rows="3" show-word-list :maxlength="250"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column :label="question.questionTitle.remark">
                        <template slot-scope="{row, $index}">
                          <el-form-item v-if="row.type == 1 || row.type == 3" :prop="commentProp(true, index, idx, $index, user.accountId)" :rules="commentRules(question.circulation ?  row.value[user.accountId] : row.value)">
                            <el-input v-model="row.comment[user.accountId]" type="textarea" :maxlength="250" show-word-list :rows="3" placeholder="请输入具体原因">
                            </el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-tab-pane>
                </el-tabs>
              </div>
              <el-table v-else :data="question.questionList" border :span-method="arraySpanMethod">
                <el-table-column align="center" label="序号" type="index" width="50"></el-table-column>
                <el-table-column :label="question.questionTitle.theme" prop="label" width="220">
                  <template slot-scope="{row}">
                    <pre style="white-space: pre-line;margin:0;">{{row.label}}</pre>
                  </template>
                </el-table-column>
                <el-table-column :label="question.questionTitle.score">
                  <template slot-scope="{row, $index}">
                    <el-form-item v-if="row.type == 1 || row.type == 3" :prop="valueProp(false, index, idx, $index)" :rules="[{required:true, message:'请勾选认可情况', trigger:['change', 'blur']}]">
                      <el-radio-group v-model="row.value">
                        <el-radio v-for="score in row.type == 1 ? [5, 4, 3, 2, 1] : [5, 4, 3, 2, 1, 0]" :key="score" :label="score">{{score}}</el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item v-if="row.type == 2" :prop="commentProp(false, index, idx, $index)" :rules="[{required:true, message:'请输入', trigger:['change', 'blur']}]">
                      <el-input v-model="row.comment" type="textarea" :rows="3" show-word-list :maxlength="250"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column :label="question.questionTitle.remark">
                  <template slot-scope="{row, $index}">
                    <el-form-item v-if="row.type == 1 || row.type == 3" :prop="commentProp(false, index, idx, $index)" :rules="commentRules(question.circulation ?  row.value[select] : row.value)">
                      <el-input v-model="row.comment" type="textarea" :rows="3" placeholder="请输入具体原因" show-word-list :maxlength="250"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <el-form-item v-else :prop="answerProp(index, idx)" :rules="[{required:true, message:'请输入', trigger:['change', 'blur']}]">
              <el-input v-model="question.answer" type="textarea" :rows="3" show-word-list :maxlength="250"></el-input>
            </el-form-item>

            <p class="remark">{{question.remark}}</p>
          </div>
        </div>
      </el-form>

      <el-button class="submitBtn" type="primary" @click="submit">提交</el-button>
    </div>
  </div>
</template>

<script>
import request from "@/utils/request"
import BasicForm from './form/basicForm.vue'
import { toChinese } from '@/utils/index.js'
export default {
  name: 'fillInFeedback',
  components: {
    BasicForm
  },
  data() {
    return {
      id: '',
      backList: {},
      detail: {},
      list: [],
      form: {},
      type: 0,
      select: {},
      origin: [],
      circulationList: [],
      applyNo: '',
      autCode: '',
      applyName: '',
      sceneReviewTime: [],
      recordId: '',
      show: false,
      basicData: {
        courseEndTime: '',
        courseFormat: '',
        courseStartTime: '',
        email: '',
        needId: '',
        needName: '',
        phoneNumber: '',
        sex: '',
        trainerUserInfo: '',
        virtualName: '',
      },
      keys: 0
    };
  },

  computed: {
    commentRules() {
      return score => {
        return [{
          required: score == 1 || score == 2,
          message: '请输入具体原因',
          trigger: ['change', 'blur']
        }]
      }
    },

    valueProp() {
      return (circulation, index, idx, $index, accountId) => {
        if (circulation) {
          return `list.${index}.questionList.${idx}.questionList.${$index}.value.${accountId}`
        } else {
          return `list.${index}.questionList.${idx}.questionList.${$index}.value`
        }
      }
    },

    commentProp() {
      return (circulation, index, idx, $index, accountId) => {
        if (circulation) {
          return `list.${index}.questionList.${idx}.questionList.${$index}.comment.${accountId}`
        } else {
          return `list.${index}.questionList.${idx}.questionList.${$index}.comment`
        }
      }
    },

    answerProp() {
      return (index, idx) => {
        return `list.${index}.questionList.${idx}.answer`
      }
    },

    circulation() {
      return type => {
        if (type == 'reviewer') {
          return this.reviewerList
        } else if (type == 'student') {
          return this.traineesList
        } else {
          return []
        }
      }
    },

    options() {
      return {
        applyNo: this.applyNo,
        autCode: this.autCode,
        applyName: this.applyName,
        sceneReviewTime: this.sceneReviewTime,
        reviewerList: this.reviewerList,
        traineesList: this.traineesList,
        courseEndTime: this.basicData.courseEndTime,
        courseFormat: this.basicData.courseFormat,
        courseStartTime: this.basicData.courseStartTime,
        email: this.basicData.email,
        needId: this.basicData.needId,
        needName: this.basicData.needName,
        phoneNumber: this.basicData.phoneNumber,
        sex: this.basicData.sex,
        // reviewerList: this.basicData.trainerUserInfo,
        virtualName: this.basicData.virtualName,
      }
    }
  },

  async mounted() {
    let path = this.$route.path || '';
    if (path.includes('fillInFeedback')) {
      this.id = path.substring(path.lastIndexOf('/') + 1);
      this.type = this.$route.query.type
      let res = await this.getFeedBackList(this.id, this.type);
      this.backList = res.data;
      if (this.backList.notFilledQuestionnaireTotal) {
        this.$message(`一共有 ${this.backList.notFilledQuestionnaireTotal} 份反馈问卷未填写`)
        // console.log(`一共有 ${this.backList.notFilledQuestionnaireTotal} 份反馈问卷未填写`);
      }

      this.reviewerList = res.data.reviewerInfoVo?.reviewerList || []
      this.traineesList = res.data.reviewerInfoVo?.traineesList || []
      this.recordId = res.data.recordId || ''
      this.applyNo = res.data.applyNo;
      this.autCode = res.data.autCode;
      this.applyName = res.data.applyName;
      this.basicData.courseEndTime = res.data.courseEndTime
      this.basicData.courseFormat = res.data.courseFormat
      this.basicData.courseStartTime = res.data.courseStartTime
      this.basicData.email = res.data.email
      this.basicData.needId = res.data.needId
      this.basicData.needName = res.data.needName
      this.basicData.phoneNumber = res.data.phoneNumber
      this.basicData.trainerUserInfo = res.data.reviewerInfoVo?.reviewerList || []
      this.basicData.virtualName = res.data.virtualName
      this.sceneReviewTime = (res.data.sceneReviewTime || '').split(',') || []
      this.show = true;
      this.getPaperDetail()
      this.keys++;
    }
  },

  methods: {
    toChinese,

    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (row.type == 2) {
        if (columnIndex == 2) {
          return [1, 2];
        } else if (columnIndex == 3) {
          return [0, 0];
        }
      }
    },

    getFeedBackList(questionnaireId, feedBackType) {
      return request({
        url: "/tra/questionnaire/feed/back/list",
        method: "get",
        params: {
          questionnaireId,
          feedBackType
        }
      })
    },

    getPaperDetail() {
      request({
        url: "/system/questionnaire/" + this.id,
        method: "get",
      }).then((res) => {
        this.detail = res.data
        let { data, description, title, type, id } = res.data
        let list = data.map((que, i) => {
          let questionList = JSON.parse(que.questionOptions || '[]')
          questionList.forEach((ques, idx) => {
            ques.answer = ''
            if (ques.circulation) {
              ques.questionList.forEach((quest) => {
                quest.value = {}
                quest.comment = {}
                this.circulation(ques.cycleType).forEach((user, idxx) => {
                  if (idxx === 0) {
                    this.select[`${i}${idx}`] = user.accountId + ''
                  }
                  quest.value[user.accountId] = ''
                  quest.comment[user.accountId] = ''
                })
              })
            }
          })

          return {
            ...que,
            answer: '',
            questionList,
          }
        });
        this.form = {
          description,
          title,
          type,
          id
        }

        this.origin = list;
        this.list = list;
      });
    },

    async submit() {

      // this.tipMessage();
      // return;

      let basicForm = await this.$refs.basicForm.getValue();
      if (!basicForm) return;

      this.$refs.form.validate((flag, msg) => {
        if (!flag) {
          try {
            // console.log(flag, msg);
            let key = Object.keys(msg)[0];
            let word = key.split('.').pop();
            let user = this.circulationList.find(({ accountId }) => word == accountId);
            if (user) {
              this.$message({
                type: 'warning',
                message: `${user.name}相关问卷题目尚未填写完毕`
              })
            }

            let keys = key.split('.');
            let newKeys = keys.slice(0, 4);
            let key2 = newKeys.join('.');
            (document.getElementsByClassName(key2)[0]).scrollIntoView({
              behavior: 'smooth',
              block: 'center',
              inline: 'nearest'
            })
          } catch (error) {
            // console.log(error);
          }
          return
        };

        let feedData = this.list.map(({ id, questionList }) => {
          return {
            id,
            questionList: questionList.map(({ answer, type, questionList }) => {
              return {
                type,
                answer: type == 2 ? answer : undefined,
                questionList: type == 1 ? questionList : undefined,
              }
            })
          }
        })

        let assessorLeader = [];
        if (Array.isArray(basicForm.reviewerList)) {
          assessorLeader = basicForm.reviewerList.filter(({ userId, accountId }) => userId || accountId).map(({ userId, accountId }) => userId || accountId)
        }
        let jsonData = {
          basicForm,
          feedData,
          reviewerList: this.reviewerList || [],
          traineesList: this.traineesList || [],
        }

        let data = {
          recordId: this.recordId,
          applyName: this.applyName,
          applyNo: this.applyNo,
          autCode: this.autCode,
          feedBackType: this.type,
          questionnaireId: this.id,
          userId: this.$store.getters.userId,
          assessorLeaderIds: assessorLeader,
          jsonData: JSON.stringify(jsonData),
        }

        request({
          url: '/tra/questionnaire/feed/back/add',
          method: 'post',
          data,
        }).then(() => {
          this.tipMessage();
          this.$store.dispatch("tagsView/delView", this.$route);
          this.$router.push({ path: "/questionnaireList" });
        })
      })
    },

    tipMessage() {
      if (!isNaN(this.backList.notFilledQuestionnaireTotal)) {
        let str = "";
        if (this.backList.notFilledQuestionnaireTotal == 1) {
          str = `您已填完全部的《${this.detail.title}》。`
        } else {
          str = `您尚有${this.backList.notFilledQuestionnaireTotal - 1}份《${this.detail.title}》未填写完毕，请尽快填写。`
        }
        this.$message({
          type: 'success',
          message: `提交成功, ${str}`
        })
      } else {
        this.$message({
          type: 'success',
          message: `提交成功`
        })
      }

    },

    circulationChange() {
      this.$refs.form.clearValidate();
    },
  }
}

</script>

<style lang='scss'>
.fill-in-feedback {
  margin-bottom: 20px;

  h3,
  h4 {
    font-weight: bold;
  }
  .content {
    width: 95%;
    margin: 0 auto;
    .el-form {
      // padding-top: 20px;
      .el-form-item {
        margin-bottom: 0;

        .el-form-item__error {
          position: relative;
        }
      }
    }
    .question {
      padding-top: 10px;
      // h4 {
      //   margin-bottom: 5px;
      // }
    }
  }
  .submitBtn {
    margin-top: 32px;
    margin-bottom: 20px;
  }

  ::v-deep .el-tabs--border-card > .el-tabs__content {
    padding: 0;
  }

  .el-radio {
    margin: 0;
    flex: 1;
  }

  .el-radio-group {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
}
</style>
