package com.thas.framework.web.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.thas.common.constant.Constants;
import com.thas.common.core.domain.entity.SysUser;
import com.thas.common.core.domain.model.LoginUser;
import com.thas.common.core.redis.RedisCache;
import com.thas.common.exception.ServiceException;
import com.thas.common.exception.user.CaptchaException;
import com.thas.common.exception.user.CaptchaExpireException;
import com.thas.common.exception.user.UserPasswordNotMatchException;
import com.thas.common.utils.DateUtils;
import com.thas.common.utils.MessageUtils;
import com.thas.common.utils.ServletUtils;
import com.thas.common.utils.StringUtils;
import com.thas.common.utils.ip.IpUtils;
import com.thas.framework.manager.AsyncManager;
import com.thas.framework.manager.factory.AsyncFactory;
import com.thas.framework.utils.IPUtil;
import com.thas.system.domain.SysUserOnline;
import com.thas.system.service.ISysConfigService;
import com.thas.system.service.ISysUserOnlineService;
import com.thas.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 登录校验方法
 * 
 * <AUTHOR>
 */
@Component
public class SysLoginService
{
    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private RedisCache redisCache;
    
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private ISysUserOnlineService userOnlineService;

    /**
     * 登录验证
     * 
     * @param username 用户名
     * @param password 密码
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public String login(String username, String password, String code, String uuid) {
        boolean captchaOnOff = configService.selectCaptchaOnOff();
        // 验证码开关
        if (captchaOnOff) {
            validateCaptcha(username, code, uuid);
        }
        // 用户验证
        Authentication authentication = null;
        try {
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager
                    .authenticate(new UsernamePasswordAuthenticationToken(username, password));
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                // 密码错次数记录
                signPasswordError(username);
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    private void signPasswordError(String username) {
        // 校验一下配置开关
        String flag = configService.selectConfigByKey("password.error.switch");
        if (!Constants.FLAG_VALUE_Y.equals(flag)) {
            return;
        }

        // 获取当前用户之前错误的次数
        String passwordErrorNumKey = Constants.PASSWORD_ERROR_ + username;
        Integer errorNum = redisCache.getCacheObject(passwordErrorNumKey);
        if (errorNum == null) {
            errorNum = 0;
        }
        if (errorNum < Constants.PASSWORD_ERROR_NUM) {
            // 如果还没有超过配置增加一次
            redisCache.setCacheObject(passwordErrorNumKey, errorNum + 1);
        } else {
            String passwordLockKey = Constants.PASSWORD_LOCK_ + username;
            // 如果已经达到了配置次数 则锁定账户 设置过期时间为配置时间
            redisCache.setCacheObject(passwordLockKey, passwordLockKey, Constants.PASSWORD_LOCK_NUM, TimeUnit.MINUTES);
        }
    }

    /**
     * 校验验证码
     * 
     * @param username 用户名
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid)
    {
        String verifyKey = Constants.CAPTCHA_CODE_KEY + uuid;
        String captcha = redisCache.getCacheObject(verifyKey);
        redisCache.deleteObject(verifyKey);
        if (captcha == null)
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
            throw new CaptchaExpireException();
        }
        if (!code.equalsIgnoreCase(captcha))
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
            throw new CaptchaException();
        }
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId)
    {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(IpUtils.getIpAddr(ServletUtils.getRequest()));
        sysUser.setLoginDate(DateUtils.getNowDate());
        userService.updateUserProfile(sysUser);
    }

    /**
     * 判断当前登录用户名判断是否需要检验ip
     */
    public void adminCheckIp(String username, HttpServletRequest request) {
        // 获取开关 判断开关是否为开
        String checkIpSwitch = configService.selectConfigByKey("admin.login.check.ip.switch");
        if (!Constants.FLAG_VALUE_Y.equals(checkIpSwitch)) {
            return;
        }
        // 如果开关为开，则获取到对应的需要验证的username列表
        String usernames = configService.selectConfigByKey("admin.username.list");
        // 如果需要校验ip的username列表为空，则直接不校验
        if (CharSequenceUtil.isEmpty(usernames)) {
            return;
        }
        List<String> usernameList = Arrays.asList(usernames.split(","));
        // 如果没有匹配上则不需要检验
        if (!usernameList.contains(username)) {
            return;
        }
        // 获取到对应的ip列表
        String targetIps = configService.selectConfigByKey("admin.target.ip.list");
        // 如果为空或者包含*则直接校验通过
        if (CharSequenceUtil.isEmpty(targetIps) || targetIps.contains("*")) {
            return;
        }
        List<String> targetIpList = Arrays.asList(targetIps.split(","));
        String ip = IPUtil.getIpAddr(request);
        if (StringUtils.isEmpty(ip)) {
            return;
        }
        // 获取ip的第一段。
        String ipPre = ip.split("\\.")[0];
        if (!targetIpList.contains(ipPre)) {
            throw new ServiceException("当前用户需要内网登录!");
        }
    }

    /**
     * 获取当前登录用户信息
     * */
    public List<SysUserOnline> sysUserOnlineList(String userName) {
        Collection<String> keys = redisCache.keys(Constants.LOGIN_TOKEN_KEY + "*");
        List<SysUserOnline> userOnlineList = new ArrayList<>();
        for (String key : keys) {
            LoginUser user = redisCache.getCacheObject(key);
            if (StringUtils.isNotEmpty(userName) && StringUtils.isNotNull(user.getUser())) {
                if (StringUtils.equals(userName, user.getUsername())) {
                    userOnlineList.add(userOnlineService.selectOnlineByUserName(userName, user));
                }
            }

        }
        return userOnlineList;
    }

    /**
     * 退出当前登录用户
     * */
    public void forceLogout(List<SysUserOnline> sysUserOnlines) {
        if (CollectionUtil.isNotEmpty(sysUserOnlines)) {
            List<String> tokenIds = sysUserOnlines.stream().filter(o -> StringUtils.isNotEmpty(o.getTokenId())).
                    map(o -> Constants.LOGIN_TOKEN_KEY + o.getTokenId()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(tokenIds)) {
                redisCache.deleteObject(tokenIds);
            }
        }
    }

}
