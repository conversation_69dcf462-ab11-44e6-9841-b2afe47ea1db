<!--  -->
<template>
  <div class="audit-evaluation" v-loading="loading">
    <div v-if="distributed">
      <!-- 医院名称 -->
      <el-card class="box-card" shadow="always" style="margin-bottom: 10px; margin-left: -5px">
        <h1 style="margin: 0">{{ detail.hospitalName }}</h1>
      </el-card>

      <el-card v-if="showSummary" class="box-card" shadow="never" style="margin-bottom: 10px; margin-left: -5px">
        <div slot="header" class="clearfix">
          <h4>事实准确性确认结果</h4>
        </div>
        <el-form ref="summaryForm" :model="summaryForm" label-width="100px">
          <el-form-item label="需修改项：" v-if="modifyClause.length">
            <modify-table :small="true" :data="modifyClause" :versionId='relation.autCsId' :showAll='false' split>
              <el-table-column label="评价结果" width="90">
                <template slot-scope="scope">
                  <span>{{ modifyAutResult(scope.row.clauseId) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="评审员评价">
                <template slot-scope="scope">
                  <span style="white-space: pre-line;">{{ modifyAutDesc(scope.row.clauseId) }}</span>
                </template>
              </el-table-column>
              <el-table-column>
                <div slot="header">
                  医院反馈：<br>
                  事实准确性审查报告中的错误/需要注意/澄清的地方：
                </div>
                <template slot-scope="scope">
                  <span style="">{{ modifyAutQuestion(scope.row.clauseId) }}</span>
                </template>
              </el-table-column>
            </modify-table>
          </el-form-item>
          <el-form-item label="备注：" prop="autDesc">
            <el-input type="textarea" v-model="summaryForm.autDesc" :rows="3" :maxlength="1000"
             ></el-input>
          </el-form-item>
          <el-form-item label="下载：" prop="downloaded"
            :rules="[{ required: true, message: '请下载事实准确性查询表', trigger: ['blur'] }]">
            <el-button v-loading='downloadLoading' @click="downloadFarReport">下载事实准确性查询表</el-button>
          </el-form-item>
          <el-form-item label="上传：" prop="fileIds"
            :rules="[{ required: true, message: '请上传已盖章的事实准确性查询表', trigger: ['blur'] }]">
            <!-- <el-button :disabled='!summaryForm.downloaded'>上传</el-button> -->
            <upload :accept='`.PDF`' :disabled="!summaryForm.downloaded" single class="evaluation-upload" :files="files" :data="{type: '1', downLoadFileName: `${detail.hospitalName}-事实准确性确认表.pdf`}" @uploadSuccess="onSuccess" @fileRemove="onError" :text="`上传签字盖章后的事实准确性查询表`"></upload>
          </el-form-item>

          <el-form-item label=" ">
            <el-button :disabled="!summaryForm.fileIds" type="primary" @click="submitSummary"
              v-loading='termSubmitLoading'>提交事实准确性确认结果</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- <el-card  v-show="!showSummary" class="box-card" shadow="never" style="margin-bottom: 10px; margin-left: -5px"></el-card> -->

      <!-- 评价  -->
      <el-card class="box-card" shadow="never" v-show="!showSummary">
        <div slot="header" class="clearfix">
          <div style="float:left;">
            <h4>事实准确性确认</h4>
          </div>
          <div class="header-right" v-if="standardLoadedKey" :key="headerSlotKeys">
            <el-button v-if="showComplete" type="primary" size="mini" @click="auditComplete"
              v-loading='termSubmitLoading'>事实准确性确认完成</el-button>

            <el-button type="primary" size="mini" @click="downloadReport" style="margin-left: 20px;">下载评审报告</el-button>
          </div>
        </div>
        <!-- 款项选择器 -->
        <standard-card ref="standardCard" :key="`base${loadingKeys}`" :clauseIds="clauseIds" :fitClauseIds="fitClauseIds"
          :versionId="relation.autCsId" @selectChange="selectChange" @loaded="standardLoaded">
        </standard-card>

        <!-- 款项 -->
        <el-form :key="loadingKeys" label-width="100px">
          <clause-item v-for="(clause, index) in clauseList" v-show="isThisPage(index + 1)" :clause="clause"
            :key="clause.clauseId">
            <evaluation-review :readonly="true" :type="AutSaAudCurrentStatusEnum.SR_CLAUSE" :clause="clause"
              :aut="srMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-review>
            <evaluation-audit v-if="frrMap[clause.clauseId]" :readonly="true"
              :type="AutSaAudCurrentStatusEnum.FR_REPORT_R_CLAUSE" :clause="clause" :aut="frrMap[clause.clauseId]"
              :status="autSaAudStatus"></evaluation-audit>
            <evaluation-confirm :readonly="!!farMap[clause.clauseId]" :type="AutSaAudCurrentStatusEnum.FAR_CLAUSE"
              :clause="clause" :aut="farMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-confirm>
            <evaluation-query v-if="frjMap[clause.clauseId]" :type="AutSaAudCurrentStatusEnum.FAR_CLAUSE_RJ_E"
              :clause="clause" :aut="frjMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-query>
          </clause-item>
          <pagination v-show="total > limit" :total="total" :page.sync="pageNum" :limit.sync="limit"
            :pageSizes="[limit]" />
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
import { downloadPDF, download } from "@/utils/request";
import evaluationMixin from "@/mixins/evaluation.vue";
import AutSaAudSaReport from './evaluationResult/autSaAudSaReport.vue'
import SummaryEdit from './summaryEdit/index.vue'
import SummaryShow from './summaryShow/index2.vue'

// 组件
export default {
  name: "HAccuracy",
  mixins: [evaluationMixin],
  components: {
    AutSaAudSaReport,
    SummaryEdit,
    SummaryShow
  },
  props: {},
  watch: {},
  data() {
    return {
      show: false,
      loading: true,
      relation: {},
      detail: {},
      termSubmitLoading: false,
      summaryForm: {
        autDesc: '',
        autResult: '1',
        fileIds: '',
        downloaded: '',
      },
      downloadLoading: false,
      files: [],
      reviewResultOptions: [],

      downloadDesc: '',
    };
  },

  computed: {
    showSummary() {
      return this.autSaAudStatus == this.AutSaAudCurrentStatusEnum.FACTUAL_ACCURACY_REVIEW_SUM
    },
    modifyClause() {
      return Object.values(this.farMap).filter(({ autResult }) => autResult == 2).map(({ clauseId }) => Number(clauseId));
    },
    showComplete() {
      return this.autSaAudStatus == this.AutSaAudCurrentStatusEnum.WAIT_FACTUAL_ACCURACY_REVIEW
        || this.autSaAudStatus == this.AutSaAudCurrentStatusEnum.FACTUAL_ACCURACY_REVIEW_PROC
    },
    modifyAutResult() {
      return (clauseId) => {
        let result = this.srMap[clauseId].autResult;
        let option = this.reviewResultOptions.find(({ dictValue }) => dictValue == result);
        return option ? option.dictLabel : '--'
      }
    },

    modifyAutDesc() {
      return (clauseId) => {
        let aut = this.srMap[clauseId];
        return this.descSubdivision(aut)
      }
    },

    descSubdivision() {
      return aut => {
        try {
          let autResult = aut.autResult;
          let { autAdvantage, autEvaluate, autProposal, autImprove, autDesc } = JSON.parse(aut.autDesc)
          if (autResult == 0) {
            return '拒绝原因：' + (autDesc || '--')
          }
          if (autResult == 1 || autResult == 2) {
            return '评价描述：' + (autAdvantage || '--')
          } else if (autResult == 3) {
            return '评价描述：' + (autImprove || '--')
          } else {
            if (autResult != 6) {
              let a = autEvaluate ? `不足：${autEvaluate}  \n` : '--'
              let b = autProposal ? `整改建议：${autProposal}  \n` : '--'
              let c = autImprove ? `改进机会：${autImprove}  \n` : '--'
              if (autEvaluate || autProposal || autImprove) {
                return [a, b, c].join(' ')
              }
            }
            return '评价描述：' + (autDesc || '--');
          }
        } catch (error) {
          return '评价描述：' + (aut.autDesc || '--');
        }
      }
    },

    modifyAutQuestion() {
      return (clauseId) => {
        return this.farMap[clauseId].autDesc;
      }
    }
  },

  mounted() {
    this.init();
    this.getDicts("review_result").then((response) => {
      this.reviewResultOptions = response.data;
    });
  },

  methods: {
    initFinalBack() {
      if (Object.keys(this.frjMap).length) {
        this.clauseIds = Object.values(this.frjMap).map(({ clauseId }) => clauseId)
      }
    },
    empty(value) {
      let type = typeof value;
      if (type === 'object') {
        if (Array.isArray(value)) {
          return value.length === 0
        } else {
          return Object.keys(value).length === 0;
        }
      } else if (type === 'string') {
        return value.trim() === ""
      } else if (type === 'number') {
        return Number.isNaN(value);
      }
      return true;
    },

    downloadFarReport() {
      this.downloadLoading = false
      let autDesc = this.summaryForm.autDesc || ''
      this.downloadDesc = autDesc
      downloadPDF({
        filename: `${this.detail.hospitalName}-事实准确性确认表.pdf`,
        autCode: this.relation.autCode,
        ftlTemplateCode: 'far_report_hos',
        checkStatus: "N",
        autDesc
      }, (flag) => {
        if (flag) {
          this.summaryForm.downloaded = 1;
          this.$refs.summaryForm.clearValidate('downloaded');
        }
        this.downloadLoading = false;
      });
    },


    submitSummary() {
      this.$refs.summaryForm.validate(async valid => {
        if (!valid) return;
        this.termSubmitLoading = true;
        let data = {
          autCode: this.relation.autCode,
          accountId: this.$store.getters.userId + "",
          submitType: this.AutSaAudCurrentStatusEnum.FAR_SUMMARY,
          autSaAudLists: [this.summaryForm]
        }
        try {
          let res = await this.submit(data);
          // 报错就不提示
          if (!res) return;
          let ids = this.files.map(file => file.fileId)
          if (ids.length) {
            // 医院上传
            this.shareCreate(ids, this.$store.getters.userId, 'hospital_review');
          }

          this.$message({
            type: 'success',
            message: '提交成功'
          });
          this.handleError();
        } finally {
          this.termSubmitLoading = false;
          this.init(false);
        }
      })
    },

    async auditComplete() {
      this.termSubmitLoading = true;
      let data = {
        autCode: this.relation.autCode,
        accountId: this.$store.getters.userId + "",
        submitType: this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_CONFIRM,
        autSaAudLists: []
      }
      try {
        let res = await this.submit(data);
        // 报错就不提示
        if (!res) return;
        this.$message({
          type: 'success',
          message: '确认完成'
        });
      } finally {
        this.termSubmitLoading = false;
        this.init(false);
      }
    },

    onSuccess(res, file) {
      file.fileId = res.data.fileId
      this.$set(this.files, 0, file)
      this.setFileIDs();
    },

    onError() {
      if (this.files[0]) {
        this.files.splice(0, 1);
        this.setFileIDs();
      }
    },

    setFileIDs() {
      let fileIds = (
        this.files.map((file) => file.fileId) || []
      ).join(",")

      this.$set(this.summaryForm, 'fileIds', fileIds)
      this.$refs.summaryForm.validateField("fileIds");
    },


    handleError() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push({ path: "/" });
    },

    downloadReport() {
      let fileId = this.detail.reviewReportFileId
      if (!fileId) {
        return this.$message({
          type:'warning',
          message:'评审报告不存在！'
        });
      }
      let fileList = this.getFileList([fileId]) || [];
      let file = fileList[0]

      if (file) {
        download(
          "/common/downloadFile",
          { fileId: Number(file.fileId) },
          file.fileName
        );
      }


    }
  },
};
</script>
<style lang="scss" scoped>
.box-card+.box-card {
  margin-top: 15px;
}

.articleSum {
  margin-top: 15px;
}
</style>

<style lang="scss"></style>

