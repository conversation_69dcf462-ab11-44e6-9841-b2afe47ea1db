<template>
  <el-card v-loading="loading" class="notice-template">
    <el-row :gutter="24">
      <el-col :span="24" :xs="24">
        <el-form ref="queryForm" :model="queryData" size="small" label-width="80px" inline>
          <el-form-item label="模板名称" prop="name">
            <el-input :maxlength="50" v-model.trim="queryData.name" placeholder="请输入模板名称" clearable :style="{ width: '100%' }">
            </el-input>
          </el-form-item>
          <el-form-item label="模板类型" prop="type">
            <el-select v-model.trim="queryData.type" placeholder="请选择模板类型" clearable filterable>
              <el-option v-for="typeItem in noticeTemplateTypeOptions" :key="typeItem.dictValue" :value="typeItem.dictValue" :label="typeItem.dictLabel"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model.trim="queryData.status" placeholder="请选择状态" clearable filterable>
              <el-option v-for="statusItem in noticeTemplateStatusOptions" :key="statusItem.dictValue" :value="statusItem.dictValue" :label="statusItem.dictLabel">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="search">
              查询
            </el-button>
            <el-button type="primary" size="small" @click="resetForm">
              重 置
            </el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="showAddTemp">添加</el-button>
        <el-button type="primary" icon="el-icon-plus" size="small" @click="issuedNotice">发布通知</el-button>
      </el-col>
    </el-row>
    <el-row :gutter="24" style="margin-top: 24px">
      <el-col :span="24">
        <el-table :data="dataSource" border>
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="edit(scope.row)">编辑</el-button>
              <el-button type="text" @click="del(scope.row)">删除</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" label="序号" type="index" :index="indexMethod"></el-table-column>
          <el-table-column align="center" label="模板名称" prop="name"></el-table-column>
          <el-table-column align="center" label="模板内容" prop="content"></el-table-column>
          <el-table-column align="center" label="模板类型" prop="type">
            <template slot-scope="scope">
              {{ tempType(`${scope.row.type}`) }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="状态" prop="status">
            <template slot-scope="scope">
              {{ tempStatus(`${scope.row.status}`) }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="创建时间" prop="createTime"></el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query" />
      </el-col>
    </el-row>
    <el-dialog :title="title" :visible.sync="dialogVisible" width="60%" :close-on-click-modal="false" @close="close">
      <el-form ref="addForm" :model="addForm" :rules="addFormRules" label-width="120px">
        <el-form-item label="模板名称" prop="name">
          <el-input v-if="!isEdit" :maxlength="50" v-model.trim="addForm.name"></el-input>
          <div v-else>{{ addForm.name }}</div>
        </el-form-item>
        <el-form-item label="模板内容" prop="content">
          <el-input :maxlength="250" v-model.trim="addForm.content" type="textarea"></el-input>
        </el-form-item>
        <el-form-item label="模板类型" prop="type">
          <el-select v-model.trim="addForm.type" placeholder="请选择模板类型" clearable filterable>
            <el-option v-for="typeItem in noticeTemplateTypeOptions" :key="typeItem.dictValue" :value="typeItem.dictValue" :label="typeItem.dictLabel"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model.trim="addForm.status">
            <el-radio v-for="statusItem in noticeTemplateStatusOptions" :key="statusItem.dictValue" :label="statusItem.dictValue">{{ statusItem.dictLabel }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="发布通知" :visible.sync="publishDialogVisible" width="60%" :close-on-click-modal="false" @close="close2">
      <el-form ref="publishForm" :model="publishForm" :rules="publishFormRules" label-width="120px">
        <el-form-item label="选择模板" prop="messageTemplateId">
          <el-select v-model.trim="publishForm.messageTemplateId" placeholder="请选择模板" @change="messageTemplateIdChange" clearable filterable>
            <el-option v-for="tempItem in noticeTemplateOptions" :key="tempItem.value" :value="tempItem.value" :label="tempItem.label"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="通知内容" prop="content">
          <el-input :maxlength="250" v-model.trim="publishForm.content" type="textarea" :disabled="!!publishForm.messageTemplateId"></el-input>
        </el-form-item>
        <el-form-item label="发送方式" prop="sendType">
          <el-checkbox-group v-model="publishForm.sendType">
            <el-checkbox label="0" name="type">系统通知</el-checkbox>
            <el-checkbox label="1" name="type">电子邮件</el-checkbox>
            <el-checkbox label="2" name="type">手机短信</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="选择通知的用户" prop="userVoList">
          <el-cascader v-model.trim="publishForm.userVoList" :options="options" :props="props" collapse-tags clearable></el-cascader>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="publishDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitPublish">确 定</el-button>
      </span>
    </el-dialog>
  </el-card>
</template>

<script>
import request from "@/utils/request";
export default {
  name: "NoticeTemplate",
  components: {},
  data() {
    return {
      dataSource: [],
      queryData: {
        name: "",
        type: null,
        status: null,
        pageSize: 10,
        pageNum: 1,
      },
      total: 0,
      noticeTemplateTypeOptions: [],
      noticeTemplateStatusOptions: [],
      noticeTemplateTypeOptions: [],
      dialogVisible: false,
      title: "添加通知模板",
      addForm: {
        id: "",
        name: "",
        content: "",
        type: null,
        status: null,
      },
      addFormRules: {
        name: [{ required: true, message: "请输入模板名称", trigger: "blur" }],
        content: [
          { required: true, message: "请输入模板内容", trigger: "blur" },
        ],
        type: [{ required: true, message: "请选择模板类型", trigger: "blur" }],
        status: [{ required: true, message: "请选择状态", trigger: "change" }],
      },
      publishForm: {
        messageTemplateId: "",
        content: "",
        sendType: [],
        userVoList: [],
      },
      publishFormRules: {
        content: [
          {
            required: true,
            message: "请选择模板或输入通知内容",
            trigger: "blur",
          },
        ],
        sendType: [
          {
            type: "array",
            required: true,
            message: "请至少选择一个发送方式",
            trigger: "change",
          },
        ],
        userVoList: [
          {
            type: "array",
            required: true,
            message: "请至少选择一个用户",
            trigger: "change",
          },
        ],
      },
      props: { multiple: true },
      options: {},
      isEdit: false,
      publishDialogVisible: false,
      noticeTemplateOptions: [],
      loading: false,
    };
  },
  computed: {
    tempStatus() {
      return (value) => {
        const status = this.noticeTemplateStatusOptions.find(
          (status) => status.dictValue == value
        );
        return status ? status.dictLabel : value;
      };
    },
    tempType() {
      return (value) => {
        const type = this.noticeTemplateTypeOptions.find(
          (type) => type.dictValue == value
        );
        return type ? type.dictLabel : value;
      };
    },
  },
  created() {
    this.initDicts();
    this.query();
    this.getOptions();
  },
  methods: {
    initDicts() {
      this.getDicts("notice_template_status").then((response) => {
        this.noticeTemplateStatusOptions = response.data;
      });
      this.getDicts("notice_template_type").then((response) => {
        this.noticeTemplateTypeOptions = response.data;
      });
    },
    search() {
      this.currentPage = 1;
      this.query();
    },
    query() {
      this.loading = true;
      request({
        url: "/system/messageTemplate/list",
        method: "get",
        params: this.queryData,
      })
        .then((res) => {
          this.loading = false;
          if (res.code != 200) return;
          this.dataSource = res.rows;
          this.total = res.total;
        })
        .catch((error) => {
          // console.log(error);
          this.loading = false;
        });
    },
    resetForm() {
      this.$refs["queryForm"].resetFields();
      this.queryData.pageNum = 1;
      this.query();
    },
    showAddTemp() {
      this.isEdit = false;
      this.title = "添加通知模板";
      this.dialogVisible = true;
    },
    edit(data) {
      const { id, name, content, type, sendType, status } = data;
      this.isEdit = true;
      this.addForm.id = id;
      this.addForm.name = name;
      this.addForm.content = content;
      this.addForm.type = type;
      this.addForm.status = status;
      this.title = "编辑通知模板";
      this.dialogVisible = true;
    },
    del(data) {
      this.$confirm("此操作将永久删除该模板, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          request({
            url: "/system/messageTemplate/" + data.id,
            method: "delete",
          })
            .then((res) => {
              if (res.code != 200) {
                this.$message({
                  type: "error",
                  message: res.message || "系统异常，请稍后再试。",
                });
                return;
              }
              this.$message({
                type: "success",
                message: "删除成功！",
              });
              this.dialogVisible = false;
              this.query();
            })
            .catch((error) => {
              this.$message({
                type: "error",
                message: "系统异常，请稍后再试。",
              });
            });
        })
        .catch(() => {});
    },
    submit() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          request({
            url: "/system/messageTemplate",
            method: this.isEdit ? "put" : "post",
            data: this.addForm,
          })
            .then((res) => {
              if (res.code != 200) {
                this.$message({
                  type: "error",
                  message: res.message || "系统异常，请稍后再试。",
                });
                return;
              }
              this.$message({
                type: "success",
                message: (this.isEdit ? "编辑" : "添加") + "通知模板成功！",
              });
              this.dialogVisible = false;
              this.query();
            })
            .catch((error) => {
              // console.log(error)
            });
        } else {
          return false;
        }
      });
    },
    close() {
      this.$refs.addForm.resetFields();
      this.addForm.id = "";
      this.addForm.name = "";
      this.addForm.content = "";
      this.addForm.type = null;
      this.addForm.status = null;
    },
    close2() {
      this.$refs.publishForm.resetFields();
      this.publishForm.messageTemplateId = "";
      this.publishForm.content = "";
      this.publishForm.sendType = [];
      this.publishForm.userVoList = [];
    },
    getOptions() {
      request({
        url: "/system/user/selectUserListGroupByRole",
        method: "post",
      }).then((res) => {
        if (res.code != 200) return;
        let arr = [];
        for (let index = 0; index < res.data.length; index++) {
          arr.push({
            label: res.data[index].roleName,
            value: res.data[index].roleId,
            children: [],
          });
          for (let i = 0; i < res.data[index].sysUserList.length; i++) {
            arr[index].children.push({
              label: res.data[index].sysUserList[i].userName,
              value:
                res.data[index].sysUserList[i].userId +
                ",,," +
                res.data[index].sysUserList[i].email,
            });
          }
        }
        this.options = arr;
      });
    },
    issuedNotice() {
      request({
        url: "/system/messageTemplate/list",
        method: "get",
        params: { status: '1' }
      }).then((res) => {
        if (res.code != 200) return;
        this.noticeTemplateOptions = res.rows.map((item) => {
          return {
            label: item.name,
            value: item.id,
            content: item.content,
          };
        });
        this.noticeTemplateOptions.unshift({
          label: "请选择模板",
          value: null,
          content: "",
        });
      });
      this.publishDialogVisible = true;
    },
    submitPublish() {
      this.$refs.publishForm.validate((valid) => {
        if (valid) {
          let _data = {
            content: this.publishForm.content,
            status: this.publishForm.status,
            userVoList: this.publishForm.userVoList.map((item) => {
              return {
                email: item[1].split(",,,")[1],
                userId: +item[1].split(",,,")[0],
              };
            }),
          };
          if (this.publishForm.messageTemplateId) {
            _data.messageTemplateId = this.publishForm.messageTemplateId;
          }
          _data.sendType = this.publishForm.sendType.join(",");
          request({
            url: "/system/messageSendRecord/sendMessageToUser",
            method: "post",
            data: _data,
          }).then((res) => {
            if (res.code != 200) {
              this.$message({
                type: "error",
                message: res.message || "系统异常，请稍后再试。",
              });
              return;
            }
            this.$message({
              type: "success",
              message: "发布通知成功！",
            });
            this.publishDialogVisible = false;
          });
        } else {
          return false;
        }
      });
    },
    messageTemplateIdChange(value) {
      this.$refs.publishForm.clearValidate();
      for (let index = 0; index < this.noticeTemplateOptions.length; index++) {
        if (this.noticeTemplateOptions[index].value === value) {
          this.publishForm.content = this.noticeTemplateOptions[index].content;
          break;
        }
      }
    },
    indexMethod(index) {
      return this.queryData.pageSize * (this.queryData.pageNum - 1) + index + 1;
    }
  },
};
</script>

<style lang="scss" scoped>
.notice-template {
  padding: 24px;
  margin-bottom: 10px;
  ::v-deep .el-dialog {
    .el-dialog__header {
      background-color: #3c81f5;
      padding-top: 10px;
      .el-dialog__title {
        color: white;
      }
      .el-dialog__headerbtn {
        top: 15px;
        .el-dialog__close {
          color: white;
        }
      }
    }
    .el-dialog__body {
      padding-bottom: 0;
      & > span {
        color: black;
      }
    }
  }
}
</style>
