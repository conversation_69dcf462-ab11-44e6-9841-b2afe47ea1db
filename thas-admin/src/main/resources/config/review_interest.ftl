<!DOCTYPE html>
<html
  lang="en"
  xmlns:o="urn:schemas-microsoft-com:office:office"
  xmlns:w="urn:schemas-microsoft-com:office:word"
  xmlns:dt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882"
  xmlns="http://www.w3.org/TR/REC-html40"
>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="ProgId" content="Word.Document" />
    <meta name="Generator" content="Microsoft Word 14" />
    <meta name="Originator" content="Microsoft Word 14" />
    <title></title>
    <style>
      @font-face {
        font-family: "SimSun";
      }

      @page {
        @top-left {
          content: element(header);
        }

        size: A4;
      }

      body {
        font-family: "SimSun";
      }
    </style>

    <!-- 事实准确性查询表 表单 -->
    <style>
      /* Fact accuracy query form */
      .faqf {
        width: calc(90% - 2px);
        margin: 0 auto;
        border: 1px solid black;
      }

      .faqf > p {
        margin: 0;
        border-bottom: 1px solid black;
      }

      .faqf > p:first-child {
        text-align: center;
        padding: 7px 0;
        /* height: 36px; */
        /* vertical-align: middle; */
      }

      .faqf > p:last-child {
        border-bottom: 0;
        /* border-left: 1px solid #000; */
      }

      .faqf > p > span {
        display: inline-block;
        text-align: center;
        font-size: 14px;
        vertical-align: middle;
      }

      .faqf > p > span.form-label {
        /* height: 36px; */
        padding: 8px 0;
        border-right: 1px solid black;
        width: 120px;
      }

      .faqf > p:nth-child(3) > span.form-label {
        padding: 18px 0;
      }

      .faqf > p:nth-child(5) > span.form-label {
        padding: 0;
        line-height: 18px;
      }

      .faqf > p.form-inline {
        width: 49%;
        display: inline-block;
        border-bottom: 0;
      }

      .faqf > p > span.form-content {
        /* height: 36px; */
        width: calc(100% - 130px);
        padding: 8px 0;
        /* border: 1px solid black; */
      }
    </style>

    <!-- 事实准确性查询表 表格 -->
    <style>
      /* Fact accuracy query table */
      .faqt {
        width: calc(90% - 2px);
        margin: 0 auto;
        /* margin-top: 15px; */
        border: 1px solid black;
        border-top: 0;
      }

      .faqt > .grid {
        display: grid;
        grid-template-columns: auto auto auto;
      }

      .faqt > .table {
        width: 100%;
      }

      .faqt > .table > .thead {
        width: 100%;
        border-bottom: 1px solid black;
      }

      .faqt > .table > .thead > div {
        display: inline-block;
        /* width: calc(25% - 4px); */
        width: 24%;
        border-right: 1px solid black;
        /* text-align: center; */
      }

      .faqt > .table > .thead > div > span {
        margin-left: 5px;
      }

      .faqt > .table > .thead > div:last-child {
        border-right: 0;
      }

      /* .faqt > .table > .tbody {
      } */

      .faqt > .table > .tbody > div {
        width: 100%;
        border-bottom: 1px solid black;
        display: flex;
      }

      .faqt > .table > .tbody > div:last-child {
        border-bottom: 0;
      }

      .faqt > .table > .tbody > div > div {
        display: inline-block;
        width: 24%;
        max-width: 24%;
        border-right: 1px solid black;
        text-align: center;
      }

      .faqt > .table > .tbody > div > div:last-child {
        border-right: 0;
      }
    </style>

    <!-- 事实准确性查询表 表格2 -->
    <style>
      .table2 {
        width: 100%;
      }

      .table2 table {
        width: 100%;
        table-layout: fixed;
      }

      .table2 > table th {
        text-align: left;
        border: 1px solid black;
        border-right: 0;
        font-weight: normal;
      }

      .table2 > table th,
      .table2 > table td {
        word-break: break-all;
        word-wrap: break-word;
      }

      .table2 > table tr td:first-child {
        width: 100px;
      }

      .table2 > table th,
      .table2 > table td {
        padding: 5px 0;
        padding-left: 2px;
      }

      .table2 > table th:first-child {
        border-right: 0;
        border-left: 0;
      }

      .table2 > table tr:first-child th {
        border-top: 0;
      }

      .table2 > table td {
        text-align: left;
        border-left: 1px solid black;
        border-bottom: 1px solid #000;
      }

      .table2 > table td:first-child {
        border-right: 0;
        border-left: 0;
      }

      .table2 > table tr:last-child td {
        border-bottom: 0;
      }
    </style>

    <!-- 事实准确性查询表  盖章-->
    <style>
      /* Fact accuracy query sign */
      .faqs {
        width: calc(90% - 2px);
        margin: 0 auto;
        /* margin-top: 15px; */
        border: 1px solid black;
        border-top: 0;
      }

      .faqs > div {
        margin: 0;
        font-size: 14px;
        border-bottom: 1px solid #000;
        /* height: 130px; */
      }

      .faqs > div:last-child {
        border-bottom: 0;
      }

      .faqs > div > div {
        display: inline-block;
        /* height: 100%; */
        vertical-align: middle;
      }

      .faqs > div > div.form-label {
        width: 60px;
        letter-spacing: 3px;
        border-right: 1px solid #000;
        padding: 35px 5px;
      }

      .faqs > div > div.form-content {
        width: calc(100% - 87px);
      }

      .faqs > div > div.form-content > span {
        display: block;
        margin: 0 auto;
        width: 50%;
      }
    </style>

    <style>
      table {
        width: 80%;
        margin: 0 auto;
        table-layout: fixed;
        word-wrap: break-word;
        word-break: break-all;
      }

      table td {
        text-align: center;
      }

      .checkBox {
        display: inline-block;
        width: 12px;
        height: 12px;
        border: 1px solid black;
        margin-left: 20px;
        position: relative;
        margin-right: 2px;
      }

      .right::after {
        content: "√";
        font-weight: bolder;
        position: absolute;
        top: -8px;
        right: -5px;
        font-size: 24px;
        color: green;
        transform: rotate(10deg);
      }
    </style>
  </head>

  <body>
    <div>
      <h3>附件5： 评审员保密承诺与利益冲突申报表</h3>
      <table border="1" cellspacing="0" cellpadding="0">
        <tr>
          <td>评审员姓名</td>
          <td>${reviewerName}</td>
          <td>职务</td>
          <td>${companyPost}</td>
        </tr>
        <tr>
          <td>工作单位</td>
          <td>${company}</td>
          <td>联系电话</td>
          <td>${reviewerMobile}</td>
        </tr>
        <tr>
          <td>拟评审医院</td>
          <td colspan="3">${hospitalName}</td>
        </tr>
        <tr>
          <td>保密承诺与利<br />益冲突申报</td>
          <td colspan="3" style="text-align: left; padding: 0 10px">
            <h5 style="margin-bottom: 10px">1. 保密承诺</h5>
            本人承诺：在为深圳市卫健医院评审评价中心提供相关服务期间，严格遵守国家保密法和中心相关保密要求，保守在工作过程中接触或掌握的需要保密的信息，保证不因自身利益或任何第三方的利益而泄露任何保密信息或利用保密信息获利。
            <div style="padding:10px 0;">本人承诺所涉指的保密信息包括但不限于：</div>
            一、中心所有非公开的规章制度、管理流程以及内控文件资料。<br />
            二、中心所有未准公开的相关受评医院患者信息、自评报告、员工资料、内部报告、各种数据和相关资料。
            <div>
            上述保密信息的载体包括但不限于：医院评审评价平台系统、纸质文件、视频、音频、计算机软件以及中心保密资料的任何载体等。</div>
			      <div style="padding:10px 0;">保密义务的期限为无限期保密，直至中心宣布解密或者秘密信息实际上已经公开。</div>
            <div>如因本人责任造成涉密信息泄露，将承担相应的责任。</div>
            <h5 style="margin-bottom: 5px">
              2. <span style="color: red;">是否存在</span>任何利益冲突（请勾选√）
            </h5>
            
            <#if hasInterest==0>
            <span class="checkBox "></span>是
            <span class="checkBox right"></span>否
            <#else>
            <span class="checkBox right"></span>是
            <span class="checkBox"></span>否
            </#if>
            <br />
            <br />
            如是，请具体描述利益冲突情况 <span style="font-style: italic;">（例如：有直接利害关系，直系亲属或有直接经济往来者；评审前三年内以个人身份受邀到该院进行讲学、培训或担任顾问、兼职等）</span>：
            <br />
            <p style="text-decoration: underline;">${interestDesc}</p>
            
           <div style="padding:0 0 15px 0;"> 评审员（签字）：</div>
           <div style="padding-bottom:15px;"> 日期：</div>
          </td>
        </tr>
        <tr>
          <td>
			      中心标准与<br />
            评价管理部<br />
            审批意见
          </td>
          <td colspan="3">
             <br />
            <br />
            签字/盖章： 
            <br />
             <br />
            <br />
          日期：  
          <br />
            <br />
          </td>
        </tr>
        <tr>
          <td>
			      中心主任审<br />
            批意见
          </td>
          <td colspan="3">
            <br />
            <br />
            签字/盖章：
             <br />
            <br />
            <br />
            日期：
            <br />
            <br />
          </td>
        </tr>
        <tr>
          <td>
            <div style="padding:10px 0;">归档日期</div>
          </td>
          <td></td>
          <td>
            归档人
          </td>
          <td></td>
        </tr>
      </table>
    </div>
  </body>
</html>
