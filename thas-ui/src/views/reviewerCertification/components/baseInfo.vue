<template>
  <div class="reviewer-application-base-info">
    <el-row :gutter="15">
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="108px">
        <el-col :span="18" style="padding:0;">
          <el-row :gutter="15" style="margin:0;">
            <el-col :span="12">
              <el-form-item label="姓名" prop="reviewerName">
                <el-input v-model.trim="formData.reviewerName" placeholder="请输入姓名" :style="{ width: '100%' }"
                  :maxlength="50"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="性别" prop="reviewerGender">
                <el-select v-model.trim="formData.reviewerGender" placeholder="请输入性别" clearable
                  :style="{ width: '100%' }">
                  <el-option v-for="(item, index) in genderOptions" :key="index" :label="item.dictLabel" :value="isNaN(item.dictValue)
                      ? item.dictValue
                      : Number(item.dictValue)
                    " :disabled="item.disabled"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="证件类型" prop="certificateType">
                <el-select v-model.trim="formData.certificateType" placeholder="请输入证件类型" clearable
                  :style="{ width: '100%' }">
                  <el-option v-for="(item, index) in certificateTypeOptions" :key="index" :label="item.dictLabel" :value="isNaN(item.dictValue)
                      ? item.dictValue
                      : Number(item.dictValue)
                    " :disabled="item.disabled"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="证件号码" prop="certificateNumber">
                <el-input v-model.trim="formData.certificateNumber" placeholder="请输入证件号码" clearable
                  :style="{ width: '100%' }">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="出生年月" prop="reviewerBirthday">
                <el-date-picker v-model.trim="formData.reviewerBirthday" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                  :style="{ width: '100%' }" placeholder="请选择出生年月" clearable
                  :picker-options="pickerOptions"></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6">
          <el-row>
            <el-col :span="24" style="height: 174px;">
              <el-form-item label="" class="headPortrait" prop="headPortrait" label-width="0">
                <el-upload class="avatar-uploader" :action="action" accept=".JPG,.JPEG,.PNG" :data="{ type: '1' }"
                  :show-file-list="false" :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload">
                  <img v-if="headPortraitImage" :src="headPortraitImage" class="avatar">
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="9">
          <el-form-item label="联系手机号" prop="reviewerMobile">
            <el-input v-model.trim="formData.reviewerMobile" placeholder="请输入联系手机号码" clearable
              :style="{ width: '100%' }">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="9">
          <el-form-item label="联系邮箱" prop="reviewerEmail">
            <el-input v-model.trim="formData.reviewerEmail" placeholder="请输入联系邮箱" :maxlength="128" clearable
              :style="{ width: '100%' }">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="现居住地" prop="liveAddress">
            <el-input v-model.trim="formData.liveAddress" placeholder="请输入现居住地" clearable :style="{ width: '100%' }"
              :maxlength="50">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="工作单位" prop="company">
            <el-input v-model.trim="formData.company" placeholder="请输入工作单位" clearable :style="{ width: '100%' }"
              :maxlength="50">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="现任职务" prop="companyPost">
            <el-input v-model.trim="formData.companyPost" placeholder="请输入现任职务" clearable :style="{ width: '100%' }"
              :maxlength="50">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="英语水平" class="english" required>
            <el-form-item prop="englishWrit" style="margin-bottom: 22px">
              写作:
              <el-radio-group v-model.trim="formData.englishWrit" size="medium">
                <el-radio v-for="(item, index) in englishWritOralOptions" :key="index" :label="isNaN(item.dictValue)
                    ? item.dictValue
                    : Number(item.dictValue)
                  ">{{ item.dictLabel }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item prop="englishOral">
              口语:
              <el-radio-group v-model.trim="formData.englishOral" size="medium">
                <el-radio v-for="(item, index) in englishOralOptions" :key="index" :label="isNaN(item.dictValue)
                    ? item.dictValue
                    : Number(item.dictValue)
                  ">{{ item.dictLabel }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="!isSenior">
          <el-form-item label="专业领域" prop="majorFields">
            <el-checkbox-group v-model="formData.majorFields" size="medium" @change="majorChange">
              <div v-for="(item, index) in fieldCodeOptions" :key="index">
                <el-checkbox :label="item.dictValue">
                  {{ item.dictLabel }}
                </el-checkbox>
                <div v-if="(item.dictValue == 1 || item.dictValue == 5) && formData.majorFields.includes(item.dictValue)"
                  style="position:relative;display:inline-block; width: 80%;">
                  <div v-if="item.dictValue == 1" style="font-size:13px;display:inline-block;">，专业： </div>
                  <el-input size="small" v-model="formData.majorDirectionMap[item.dictValue]" :maxlength="50"
                    style="display:inline-block; width: 30%;" :class="{ red: showMajorCheck(item.dictValue) }"
                    @input="(value) => majorInput(value, item.dictValue)"></el-input>
                  <span v-if="showMajorCheck(item.dictValue)" style="position:absolute;top:25px;font-size:13px;color:red;"
                    :style="{ left: item.dictValue == 1 ? '50px' : 0 }">{{ item.dictValue == 1 ? '请填写专业' : '请填写其他'
                    }}</span>
                </div>
              </div>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="height:inherit;">
          <el-form-item label="个人简介" prop="profile">
            <el-input v-model.trim="formData.profile" type="textarea" :rows="3" placeholder="请输入个人简介" clearable
              :style="{ width: '100%' }" :maxlength="500">
            </el-input>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>
<script>

import { getFileRaw, checkEmail, checkContactsPhone } from '@/utils/request'
import { deepClone } from "@/utils/index";
export default {
  name: "BaseInfo",
  components: {},
  props: {
    info: {
      type: Object,
      default: () => { },
    },
  },
  data() {
    return {
      formData: {
        reviewerName: "",
        reviewerBirthday: "",
        reviewerGender: undefined,
        certificateType: undefined,
        certificateNumber: undefined,
        reviewerMobile: undefined,
        reviewerEmail: undefined,
        liveAddress: undefined,
        company: undefined,
        companyPost: undefined,
        otherPost: undefined,
        englishWrit: undefined,
        englishOral: undefined,
        majorFields: [],
        majorField: [],
        majorDirectionMap: {},
        majorDirection: '',
        fields: [],
        profile: '',
        headPortrait: '',
        practiceLicenseNo: undefined,
      },
      majorCheck1: false,
      majorCheck2: false,
      headPortraitImage: '',
      rules: {
        reviewerName: [
          {
            required: true,
            message: "请输入姓名",
            trigger: ["blur", "change"],
          },
        ],
        reviewerBirthday: [
          {
            required: true,
            message: "请选择出生年月",
            trigger: "change",
          },
        ],
        reviewerGender: [
          {
            required: true,
            message: "请输入性别",
            trigger: ["blur", "change"],
          },
        ],
        certificateType: [
          {
            required: true,
            message: "请输入证件类型",
            trigger: "change",
          },
        ],
        certificateNumber: [
          {
            required: true,
            validator: (rule, value, callback) =>
              this.certificatesValid(value, callback),
            trigger: ["blur", "change"],

          },
        ],
        reviewerMobile: [
          {
            required: true,
            // message: "请输入手机号码",
            validator: (rule, value, callback) => this.mobileValid(rule, value, callback),
            trigger: ["change"],
          },
          // {

          //   // pattern:
          //   //   /^((13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8})$/,
          //   message: "请输入正确的手机号码",
          // },
        ],
        reviewerEmail: [
          {
            required: true,
            // message: "请输入联系邮箱",
            validator: (rule, value, callback) => this.emailValid(rule, value, callback),
            trigger: ["blur"],
          },
          // {
          //   // pattern: /^\w+@[a-zA-Z0-9]+((\.[a-z0-9A-Z]{1,})+)$/,

          //   message: "请输入正确的联系邮箱",
          // },
        ],
        liveAddress: [
          {
            required: true,
            message: "请输入现居住地",
            trigger: ["blur", "change"],
          },
        ],
        company: [
          {
            required: true,
            message: "请输入工作单位",
            trigger: ["blur", "change"],
          },
        ],
        companyPost: [
          {
            required: true,
            message: "请输入现任职务",
            trigger: ["blur", "change"],
          },
        ],
        otherPost: [
          {
            required: true,
            message: "请输入其他社会职务",
            trigger: ["blur", "change"],
          },
        ],
        englishWrit: [
          {
            required: true,
            message: "请选择写作水平",
            trigger: ["blur", "change"],
          },
        ],
        englishOral: [
          {
            required: true,
            message: "请选择口语水平",
            trigger: ["blur", "change"],
          },
        ],
        majorField: [
          {
            required: true,
            type: "array",
            message: "请至少选择一项专业领域",
            trigger: ["blur", "change"],
          },
        ],
        profile: [{
          required: true,
          message: "请填写个人简介",
          trigger: ["blur", "change"],
        }],
        headPortrait: [{
          required: true,
          message: "请上传个人头像",
          trigger: ["blur", "change"],
        }]
      },
      certificateTypeOptions: [],
      genderOptions: [],
      englishWritOralOptions: [],
      englishOralOptions: [],
      fieldCodeOptions: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      regMap: {},
    };
  },
  computed: {
    action() {
      return `${localStorage.baseUrl || process.env.VUE_APP_BASE_API}/binTang/common/uploadFile`
    },
    isSenior() {
      return this.$store.getters.roles.includes("senior-assessor");
    },

    showMajorCheck() {
      return dictValue => {
        if (dictValue == 1) {
          return this.majorCheck1
        } else {
          return this.majorCheck2
        }
      }
    }
  },

  watch: {
    info: {
      immediate: true,
      deep: true,
      handler(val) {
        let deepInfo = deepClone(val) || {};
        if (deepInfo && Object.keys(deepInfo).length) {
          for (const key in deepInfo) {
            if (Object.hasOwnProperty.call(deepInfo, key)) {
              if (key === 'fileDetails') {
                this.getFileByFileId2Raw(deepInfo['headPortrait'], deepInfo[key])
              } if (key == 'headPortrait' && !deepInfo['fileDetails']) {
                this.getFileByFileId2Raw(deepInfo[key])
                this.$set(this.formData, key, deepInfo[key]);
              } else {
                this.$set(this.formData, key, deepInfo[key]);
              }
            }
          }
        }
      },
    },
  },
  created() { },
  mounted() {
    this.initDicts();
    this.getDomainList();
  },
  methods: {
    initDicts() {
      this.getDicts("sys_user_sex").then((response) => {
        this.genderOptions = response.data;
      });
      this.getDicts("certificate_type").then((response) => {
        this.certificateTypeOptions = response.data;
      });
      this.getDicts("knowledge_level").then((response) => {
        this.englishWritOralOptions = response.data;
      });

      this.getDicts("speaking_level").then((response) => {
        this.englishOralOptions = response.data;
      });

      this.getBingConfigKey("reg.email").then(response => {
        this.regMap['regEmail'] = response.msg.trim();
      });

      this.getBingConfigKey("reg.mobile").then(response => {
        this.regMap['regMobile'] = response.msg.trim();
      });

      this.getBingConfigKey("reg.idCard").then(response => {
        this.regMap['1'] = response.msg.trim();
      });
      this.getBingConfigKey("reg.passport").then(response => {
        this.regMap['2'] = response.msg.trim();
      });
      this.getBingConfigKey("reg.reentry").then(response => {
        this.regMap['3'] = response.msg.trim();
      });
      this.getBingConfigKey("reg.hkCard").then(response => {
        this.regMap['4'] = response.msg;
      });
      this.getBingConfigKey("reg.moCard").then(response => {
        this.regMap['5'] = response.msg.trim();
      });
      this.getBingConfigKey("reg.twCard").then(response => {
        this.regMap['6'] = response.msg.trim();
      });
    },

    getValue() {
      return new Promise(async resolve => {
        const el = await this.$refs["elForm"].validate();
        if (Object.hasOwnProperty.call(this.formData.majorDirectionMap, '1') && !this.formData.majorDirectionMap['1']) {
          this.majorCheck1 = true;
          resolve(false)
        }
        if (Object.hasOwnProperty.call(this.formData.majorDirectionMap, '5') && !this.formData.majorDirectionMap['5']) {
          this.majorCheck2 = true;
          resolve(false)
        }
        this.formData.majorField = this.formData.majorFields.filter(item => !!item).join(',')
        this.formData.majorDirection = JSON.stringify(this.formData.majorDirectionMap)
        if (el) return resolve(this.formData);
        resolve(false)
      })
    },

    getDraft() {
      return this.formData;
    },

    majorChange(value) {
      if (value.includes('1')) {
        if (!this.formData.majorDirectionMap[1]) {
          this.$set(this.formData.majorDirectionMap, 1, '')
        }
      } else {
        delete this.formData.majorDirectionMap[1]
      }

      if (value.includes('5')) {
        if (!this.formData.majorDirectionMap[5]) {
          this.$set(this.formData.majorDirectionMap, 5, '')
        }
      } else {
        delete this.formData.majorDirectionMap[5]
      }
    },

    majorInput(value, dictValue) {
      if (dictValue == 1) {
        this.majorCheck1 = !value
      } else {
        this.majorCheck2 = !value
      }
    },

    emailValid(rule, value, callback) {
      if (!value) callback('请输入联系邮箱')
      let regString = this.regMap['regEmail'];
      if (!regString) callback();
      let reg = new RegExp(regString);
      if (!reg.test(value)) callback('请输入正确的联系邮箱')
      checkEmail(value, 'assessor', this.info.certificateNumber).then(res => {
        if (res.code != 200) {
          callback(res.message || res.msg)
        } else {
          callback()
        }
      })
    },

    mobileValid(rule, value, callback) {
      if (!value) callback('请输入联系手机号')
      let regString = this.regMap['regMobile'];
      if (!regString) callback();
      let reg = new RegExp(regString);
      if (!reg.test(value)) callback('请输入正确的联系手机号')
      checkContactsPhone(value, 'assessor', this.info.certificateNumber).then(res => {
        if (res.code != 200) {
          callback(res.message || res.msg)
        } else {
          callback()
        }
      })
    },

    certificatesValid(value, callback) {
      if (!value) callback('请输入证件号码')
      if (!this.formData["certificateType"]) {
        if (value) return callback();
        return callback('请输入证件号码')
      }
      let regString = this.regMap[this.formData["certificateType"]]
      if (regString) {
        if (!value) callback('请输入证件号码')
        let cer = this.certificateTypeOptions.find(({ dictValue }) => dictValue == this.formData["certificateType"]);
        let reg = new RegExp(regString);
        if (!reg.test(value)) callback('请输入正确的证件号码')
      }

      if (this.formData["certificateType"] == 1) {
        // this.formData.reviewerBirthday
        const birthday = value.substr(6, 8);
        let year = birthday.substr(0, 4);
        let month = birthday.substr(4, 2);
        let day = birthday.substr(6, 2);
        this.$set(this.formData, "reviewerBirthday", `${year}-${month}-${day}`);
      }
      callback();
    },

    getDomainList() {
      this.getDicts("reviewer_major").then((response) => {
        this.fieldCodeOptions = response.data;
      });
    },

    handleAvatarSuccess(res, file) {
      if (res.code == 200) {
        this.headPortraitImage = URL.createObjectURL(file.raw);
        this.formData.headPortrait = res.data.fileId
      } else {
        this.$message.error(res.message)
      }
    },

    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 10;

      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG 或者 PNG 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 10MB!');
      }
      return isJPG && isLt2M;
    },


    getFileByFileId2Raw(fileId, fileDetails) {
      if (!fileDetails) {
        getFileRaw({ fileId }, blob => {
          if (!blob) return
          this.headPortraitImage = URL.createObjectURL(blob);
        })
        return
      };
      let files = fileDetails[fileId]
      if (files && files[0]) {
        this.headPortraitImage = files[0]?.url
      }
    }
  },
};
</script>
<style lang="scss" scoped>
.reviewer-application-base-info {
  padding: 24px;

  ::v-deep .el-col-12 {
    height: 58px;
  }

  ::v-deep .el-checkbox+.el-checkbox {
    margin-top: 15px;
    // .el-checkbox__label {
    //   line-height: 22px;
    // }
  }

  ::v-deep .red .el-input__inner {
    border-color: red;
  }

  //   ::v-deep .english.el-form-item {
  //       .el-form-item >.
  //   }

  .avatar-uploader {
    display: flex;
    justify-content: center;
  }

  .avatar-uploader ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    margin: 0 auto;
  }

  .avatar-uploader ::v-deep .el-upload:hover {
    border-color: #409eff;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }

  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }

  .headPortrait ::v-deep .el-form-item__error {
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
