<!--  -->
<template>
  <div class='test-case'>
    <el-dialog title="测试%" :visible.sync="dialogVisible" width="70%" destroy-on-close>
      <div>
        <el-divider content-position="left">基本款： 58款</el-divider>
        <div style="display: flex;
            justify-content:  space-between;
            flex-wrap: wrap;">
          <div>优秀
            <el-input-number v-model.trim="basic.a" placeholder="优秀"></el-input-number>
          </div>
          <div>良好
            <el-input-number v-model.trim="basic.b" placeholder="良好"></el-input-number>
          </div>
          <div>达标
            <el-input-number v-model.trim="basic.c" placeholder="达标"></el-input-number>
          </div>
          <div>部分达标
            <el-input-number v-model.trim="basic.d" placeholder="部分达标"></el-input-number>
          </div>
          <div>不达标
            <el-input-number v-model.trim="basic.e" placeholder="不达标"></el-input-number>
          </div>
          <div>不适用
            <el-input-number v-model.trim="basic.f" placeholder="不适用"></el-input-number>
          </div>
          <div style="width:200px">
            已填基本款总数：{{basicTotal}}
          </div>
        </div>
        <el-divider content-position="left">基本款风险</el-divider>
        <div v-if="basic.e">
          <el-radio-group v-for="(item,index) in basic.e" :key='index' v-model.trim="basicRisk[index]" style="margin-left:0px;margin-top:15px">
            <el-radio :label="0" disabled style="margin-right:0;margin-left:30px">{{item}}</el-radio>
            <el-radio :label="2" style="margin-right:15px">低</el-radio>
            <el-radio :label="4" style="margin-right:15px">中</el-radio>
            <el-radio :label="6" style="margin-right:15px">高</el-radio>
            <el-radio :label="9" style="margin-right:15px">严重</el-radio>
          </el-radio-group>
        </div>

        <el-divider content-position="left">非基本款：128款</el-divider>
        <div style="display: flex;
            justify-content: space-between;
            flex-wrap: wrap;">
          <div>优秀
            <el-input-number v-model.trim="nonBasic.a" placeholder="优秀"></el-input-number>
          </div>
          <div>良好
            <el-input-number v-model.trim="nonBasic.b" placeholder="良好"></el-input-number>
          </div>
          <div>达标
            <el-input-number v-model.trim="nonBasic.c" placeholder="达标"></el-input-number>
          </div>
          <div>部分达标
            <el-input-number v-model.trim="nonBasic.d" placeholder="部分达标"></el-input-number>
          </div>
          <div>不达标
            <el-input-number v-model.trim="nonBasic.e" placeholder="不达标"></el-input-number>
          </div>
          <div>不适用
            <el-input-number v-model.trim="nonBasic.f" placeholder="不适用"></el-input-number>
          </div>
          <div>
            已填非基本款总数：{{nonBasicTotal}}
          </div>
        </div>
        <el-divider content-position="left">非基本款风险</el-divider>
        <div v-if="nonBasic.e">
          <el-radio-group v-for="(item,index) in nonBasic.e" :key='index' v-model.trim="nonBasicRisk[index]" style="margin-left:0px;margin-top:15px">
            <el-radio :label="0" disabled style="margin-right:0;margin-left:30px">{{item}}</el-radio>
            <el-radio :label="2" style="margin-right:15px">低</el-radio>
            <el-radio :label="4" style="margin-right:15px">中</el-radio>
            <el-radio :label="6" style="margin-right:15px">高</el-radio>
            <el-radio :label="9" style="margin-right:15px">严重</el-radio>
          </el-radio-group>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogClick">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: '',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: () => false
    },
    clauseList: {
      type: Array,
      default: () => []
    }
  },
  watch: {},
  data() {
    return {
      basic: {
        a: 0, b: 0, c: 0, d: 0, e: 0, f: 0,
      },
      nonBasic: {
        a: 0, b: 0, c: 0, d: 0, e: 0, f: 0,
      },
      basicRisk: [],
      nonBasicRisk: [],
    };
  },

  computed: {},

  created() { },

  mounted() { },

  methods: {
    async dialogClick() {
      if (this.basicTotal > 58) {
        return this.$message({
          type: 'warning',
          message: '基本款的总数是58,不能超过'
        })
      }
      if (this.nonBasicTotal > 128) {
        return this.$message({
          type: 'warning',
          message: '非基本款的总数是128,不能超过'
        })
      }
      if (this.basicRisk.length != this.basic.e) {
        return this.$message({
          type: 'warning',
          message: '基本款有风险没有选择'
        })
      }
      if (this.nonBasicRisk.length != this.nonBasic.e) {
        return this.$message({
          type: 'warning',
          message: '非基本款有风险没有选择'
        })
      }
      let list = [],
        defaultClauseList = this.clauseList || this.$store.getters.standardTypeItemByVersionId(
          this.relation.autCsId,
          "clause",
          "list"
        );

      // 如果存在所属 就使用并完善所属款项 id => clause
      if (this.clauseList.length) {
        list = this.clauseList.map(id => defaultClauseList.find(clause => clause.clauseId == id))
      } else {
        list = defaultClauseList;
      }

      const basic = list.filter(li => li.isStar == 1).map(li => li.clauseId);
      const nonBasic = list.filter(li => li.isStar == 0).map(li => li.clauseId);

      let ba = basic.splice(0, this.basic.a),
        bb = basic.splice(0, this.basic.b),
        bc = basic.splice(0, this.basic.c),
        bd = basic.splice(0, this.basic.d),
        be = basic.splice(0, this.basic.e),
        bf = basic.splice(0, this.basic.f);

      let dba = ba.map(a => {
        return {
          autDesc: `第${a}款 自评总结`,
          autResult: "1",
          clauseId: a,
        }
      }),
        dbb = bb.map(b => {
          return {
            autDesc: `第${b}款 自评总结`,
            autResult: "2",
            clauseId: b,
          }
        }),
        dbc = bc.map(b => {
          return {
            autDesc: `第${b}款 自评总结`,
            autResult: "3",
            clauseId: b,
          }
        }),
        dbd = bd.map(b => {
          return {
            autDesc: `第${b}款 自评总结`,
            autResult: "4",
            clauseId: b,
          }
        }),
        dbe = be.map((b, index) => {
          return {
            autDesc: `第${b}款 自评总结`,
            autResult: "5",
            clauseId: b,
            riskPossibility: this.basicRisk[index] / 2,
            riskImpact: this.basicRisk[index] / 2,
          }
        }),
        dbf = bf.map(b => {
          return {
            autDesc: `第${b}款 自评总结`,
            autResult: "6",
            clauseId: b,
          }
        });

      let na = nonBasic.splice(0, this.nonBasic.a),
        nb = nonBasic.splice(0, this.nonBasic.b),
        nc = nonBasic.splice(0, this.nonBasic.c),
        nd = nonBasic.splice(0, this.nonBasic.d),
        ne = nonBasic.splice(0, this.nonBasic.e),
        nf = nonBasic.splice(0, this.nonBasic.f);

      let dna = na.map(a => {
        return {
          autDesc: `第${a}款 自评总结`,
          autResult: "1",
          clauseId: a,
        }
      }),
        dnb = nb.map(b => {
          return {
            autDesc: `第${b}款 自评总结`,
            autResult: "2",
            clauseId: b,
          }
        }),
        dnc = nc.map(b => {
          return {
            autDesc: `第${b}款 自评总结`,
            autResult: "3",
            clauseId: b,
          }
        }),
        dnd = nd.map(b => {
          return {
            autDesc: `第${b}款 自评总结`,
            autResult: "4",
            clauseId: b,
          }
        }),
        dne = ne.map((b, index) => {
          return {
            autDesc: `第${b}款 自评总结`,
            autResult: "5",
            clauseId: b,
            riskPossibility: this.nonBasicRisk[index] / 2,
            riskImpact: this.nonBasicRisk[index] / 2,
          }
        }),
        dnf = nf.map(b => {
          return {
            autDesc: `第${b}款 自评总结`,
            autResult: "6",
            clauseId: b,
          }
        });

      let dataList = [...dba, ...dbb, ...dbc, ...dbd, ...dbe, ...dbf,
      ...dna, ...dnb, ...dnc, ...dnd, ...dne, ...dnf];

      let autSaAudLists = dataList.sort((a, b) => a.clauseId - b.clauseId);
      let data = {
        autCode: this.relation.autCode,
        accountId: store.getters.userId + "",
        type: this.AutSaAudSubmitTypeEnum.AUD_SECOND_TRIAL_INQUIRE_PROC,
        autSaAudLists
      };
      try {
        const res = await this.submitAutSaAud(data);
        this.$message("批量提交成功-仅测试提示");
        this.formateDetail();
      } catch (error) { } finally {
        this.$emit('closeTestCase')
      }
    },
  }
}

</script>
<style lang='scss' scoped>
</style>