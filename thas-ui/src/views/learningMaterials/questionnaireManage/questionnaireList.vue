<template>
  <el-card v-loading="loading" class="questionnaire-list">
    <el-row :gutter="24">
      <el-col :span="24" :xs="24">
        <el-form ref="queryForm" :model="queryData" size="small" label-width="80px" inline>
          <el-form-item label="问卷名称" prop="title">
            <el-input v-model.trim="queryData.title" placeholder="请输入问卷名称" clearable :style="{ width: '100%' }" maxlength="128"></el-input>
          </el-form-item>
          <el-form-item label="问卷类型" prop="type">
            <el-select v-model.trim="queryData.type" placeholder="请选择问卷类型" clearable>
              <el-option v-for="classifyItem in classifyOption" :key="classifyItem.dictValue" :value="classifyItem.dictValue" :label="classifyItem.dictLabel"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="状态" prop="doFinish">
            <el-select v-model.trim="queryData.doFinish" placeholder="请选择状态" clearable>
              <el-option :value="1" label="已提交"></el-option>
              <el-option :value="0" label="未提交"></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item>
            <el-button type="primary" size="small" @click="search">
              查询
            </el-button>
            <el-button type="primary" size="small" @click="resetForm">
              重 置
            </el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-row :gutter="24" style="margin-top: 24px">
      <el-col :span="24">
        <el-table :data="dataSource" border>
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="fillIn(scope.row)">填写问卷</el-button>
              <el-button v-if="scope.row.feedBackFlag != 0" type="text" @click="toDetail(scope.row)">答卷详情</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" label="问卷名称" prop="title"></el-table-column>
          <el-table-column align="center" label="问卷类型" prop="type">
            <template slot-scope="scope">
              {{ type(`${scope.row.type}`) }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="创建时间" prop="createTime"></el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query" />
      </el-col>
    </el-row>

    <el-dialog title="答卷详情" :visible.sync="dialogVisible" width="80%" :close-on-click-modal="false">
      <div v-if="!isNormal(current_type)">
        <span>反馈答卷：</span>
        <el-select v-model="feedBackIndex" style="width:320px;" clearable filterable>
          <el-option v-for="(feedback, index) in feedBackList" :key="index" :label="showFeedBackLabel(feedback, current_type)" :value="index"></el-option>
        </el-select>
      </div>
      <el-divider></el-divider>
      <div v-if="current || feedBackList.length" style="height:500px;overflow-y:scroll;">
        <div v-if="isNormal(current_type)">
          <div v-for="(item, index) in current.data" :key="index" class="item">
            <div class="title">
              <span v-if="item.hasRequired" style="color: red;">*&nbsp;&nbsp;</span>
              <span>{{ item.question }}</span>
              <span v-if="item.type == 2" style="margin-left: 10px; color: #99a1c1;">[多选题]</span>
            </div>
            <div v-if="item.type == 2" class="content">
              <div v-for="(answerItem, i) in JSON.parse(item.answer)" :key="i" style="margin-bottom: 5px; color: #666666; font-size: 14px;">{{ answerItem }}</div>
            </div>
            <div v-else class="content">
              <span style="color: #666666; font-size: 14px">{{ item.answer }}</span>
            </div>
          </div>
        </div>
        <FeedbackDetail v-else :key="feedBackIndex" :detail="feedBackList[feedBackIndex]" :title="current.title" :description="current.description"></FeedbackDetail>
      </div>
      <el-divider></el-divider>
      <div slot="footer" class="dialog-footer" style="display: flex; justify-content: space-between;">
        <div class="left-btns">
          <el-button @click="dialogVisible = false">返回</el-button>
          <!-- <el-button @click="delAnswer">删除</el-button> -->
        </div>
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import request from "@/utils/request";
import FeedbackDetail from './feedback/feedbackDetail.vue'
export default {
  name: "QuestionnaireList",
  components: {
    FeedbackDetail
  },
  data() {
    return {
      dataSource: [],
      queryData: {
        title: "",
        type: null,
        doFinish: null,
        pageSize: 10,
        pageNum: 1,
      },
      total: 0,
      questionnaireStatusOptions: [],
      questionnaireClassifyOptions: [],
      feedbackClassifyOptions: [],
      dialogVisible: false,
      current: null,
      loading: false,
      current_type: 0,
      feedBackIndex: 0,
      feedBackList: [],
    };
  },
  computed: {
    type() {
      return (value) => {
        let list = [...this.feedbackClassifyOptions, ...this.questionnaireClassifyOptions]
        const type = list.find(
          (type) => type.dictValue == value
        );
        return type ? type.dictLabel : value;
      };
    },

    isNormal() {
      return type => {
        if (!type) return null;
        return this.questionnaireClassifyOptions.some(({ dictValue }) => dictValue == type);
      }
    },

    classifyOption() {
      let list = []
      let roles = this.$store.getters.roles;
      if (roles.includes('hospital')) {
        list = this.feedbackClassifyOptions.filter(({ dictValue }) => dictValue == 6)
      } else if (roles.includes('assessor')) {
        list = this.feedbackClassifyOptions.filter(({ dictValue }) => dictValue != 6)
      } else if (roles.includes('trainees_assessor')) {
        list = this.feedbackClassifyOptions.filter(({ dictValue }) => dictValue == 5 || dictValue == 8)
      } else {
        list = this.feedbackClassifyOptions
      }
      return [...this.questionnaireClassifyOptions, ...list]
    },

    showFeedBackLabel() {
      return (feedBack, type) => {
        let str = ''
        if (type == 5) {
          return feedBack.createTime
        } else {
          str = `${feedBack.applyName || ''} (${feedBack.createTime})`
        }
        return str;
      }
    },
  },
  created() {
    this.initDicts();
    this.query();
  },
  methods: {
    initDicts() {
      this.getDicts("questionnaire_classify").then((response) => {
        this.questionnaireClassifyOptions = response.data;
      });
      this.getDicts("feedback_classify").then((response) => {
        this.feedbackClassifyOptions = response.data;
      });
    },
    search() {
      this.queryData.pageNum = 1;
      this.query();
    },
    query() {
      this.loading = true;
      request({
        url: "/system/questionnaire/list",
        method: "get",
        params: this.queryData,
      })
        .then((res) => {
          this.loading = false;
          if (res.code != 200) return;
          this.dataSource = res.rows;
          this.total = res.total;
        })
        .catch((error) => {
          // console.log(error);
          this.loading = false;
        });
    },
    resetForm() {
      this.$refs["queryForm"].resetFields();
      this.queryData.pageNum = 1;
      this.query();
    },

    fillIn(data) {
      if (this.isNormal(data.type)) {
        request({
          url: "/system/answer/detail",
          method: "get",
          params: { questionnaireId: data.id, answerId: "" },
        }).then((res) => {
          if (res.code != 200) return;
          if (res.data && res.data.doFinish != 0) {
            this.$message({
              type: "warning",
              message: "已填写过此问卷！",
            });
          } else {
            this.$router.push({
              path: "/learningMaterials/fillInQuestionnaire",
              query: { id: data.id },
            });
          }
        });
      } else {
        request({
          url: "/tra/questionnaire/feed/back/list",
          method: "get",
          params: {
            questionnaireId: data.id,
            feedBackType: data.type
          }
        }).then(() => {
          this.$router.push({
            path: "/learningMaterials/fillInFeedback/" + data.id + '?type=' + data.type,
          });
        })
      }

    },
    toDetail(data) {
      if (this.isNormal(data.type)) {
        request({
          url: "/system/answer/detail",
          method: "get",
          params: { questionnaireId: data.id, answerId: "" },
        }).then((res) => {
          if (res.code != 200) return;
          this.current = res.data;
          this.current_type = data.type;
          this.current.questionnaireId = data.id;
          this.dialogVisible = true;
        });
      } else {
        request({
          url: "/tra/questionnaire/feed/back/details",
          method: "get",
          params: { questionnaireId: data.id, feedBackType: data.type },
        }).then(res => {
          this.current_type = data.type
          this.feedBackList = res.data
          this.dialogVisible = true;
          this.current = data
        })
      }

    },

    delAnswer() {
      this.$confirm("此操作将删除该答卷, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          request({
            url: "/system/answer/" + this.current.id,
            method: "delete",
          }).then((res) => {
            if (res.code != 200) return;
            this.$message({
              type: "success",
              message: "删除成功!",
            });
            this.query();
          });
          this.dialogVisible = false;
        })
        .catch(() => { });
    },
  },
};
</script>

<style lang="scss" scoped>
.questionnaire-list {
  // padding: 24px;
  ::v-deep .el-dialog {
    .el-dialog__header {
      background-color: #3c81f5;
      padding-top: 10px;
      .el-dialog__title {
        color: white;
      }
      .el-dialog__headerbtn {
        top: 15px;
        .el-dialog__close {
          color: white;
        }
      }
    }
    .el-dialog__body {
      padding-bottom: 0;
      & > span {
        color: black;
      }
      .item {
        padding: 20px;
        border-bottom: 2px dashed #ebeced;
        .title {
          font-size: 16px;
          font-weight: bold;
          color: #000;
          text-align: left;
        }
        .content {
          padding: 10px 0;
        }
      }
    }

    .el-divider {
      margin-bottom: 10px;
    }
  }
}
</style>
