<template>
  <el-card v-loading="loading" class="my-paper">
    <el-row :gutter="24" style="margin-top: 24px">
      <el-col :span="24">
        <el-table :data="dataSource" border>
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="toDetail(scope.row)">答卷详情</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" label="答卷名称" prop="examPaperName"></el-table-column>
          <el-table-column align="center" label="答卷归属课程" prop="learnResourceName"></el-table-column>
          <el-table-column align="center" label="得分" prop="score"></el-table-column>
          <el-table-column align="center" label="状态" prop="status">
            <template slot-scope="scope">
              {{ status(`${scope.row.status}`) }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="提交时间" prop="createTime"></el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query" />
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
let that
import request from '@/utils/request'
export default {
  name: "MyPaper",
  data() {
    return {
      dataSource: [],
      queryData: {
        createId: this.$store.state.user.name,
        pageNum: 1,
        pageSize: 10,
        examPaperId: ''
      },
      total: 0,
      answerStatusOptions: [],
      loading: false
    }
  },
  computed: {
    status() {
      return (value) => {
        const status = this.answerStatusOptions.find(
          (status) => status.dictValue == value
        )
        return status ? status.dictLabel : value
      }
    }
  },
  watch: {
    $route: (to) => {
      if (!that) return
      that.query()
    },
  },
  created() {
    this.initDicts()
    this.query()
  },
  mounted() {
    that = this
  },
  methods: {
    initDicts() {
      this.getDicts("answer_status").then((response) => {
        this.answerStatusOptions = response.data
      })
    },
    query() {
      this.loading = true
      this.queryData.examPaperId = this.$route.query.examPaperId || ''
      request({
        url: '/system/sheet/list',
        method: "get",
        params: this.queryData,
      }).then((res) => {
        this.loading = false
        if (res.code != 200) return
        this.dataSource = res.rows
        this.total = res.total
      }).catch((error) => {
        // console.log(error)
        this.loading = false
      })
    },
    toDetail(data) {
      this.$router.push({
        path: '/learningMaterials/answerDetail',
        query: {
          id: data.id,
          from: this.$route.path
        }
      })
    }
  }
}
</script>

<style lang='scss' scoped>
.my-paper {
  padding: 24px;
  ::v-deep .el-dialog {
    .el-dialog__header {
      background-color: #3c81f5;
      padding-top: 10px;
      .el-dialog__title {
        color: white;
      }
      .el-dialog__headerbtn {
        top: 15px;
        .el-dialog__close {
          color: white;
        }
      }
    }
    .el-dialog__body {
      padding-bottom: 0;
      & > span {
        color: black;
      }
    }
  }
}
</style>