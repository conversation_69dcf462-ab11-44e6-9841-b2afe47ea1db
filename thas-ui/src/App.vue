<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  name: "App",
  metaInfo() {
    return {
      title:
        this.$store.state.settings.dynamicTitle &&
        this.$store.state.settings.title,
      titleTemplate: (title) => {
        return title
          ? `${title}-${process.env.VUE_APP_TITLE}`
          : process.env.VUE_APP_TITLE;
      },
    };
  },
  mounted() {
    //将所有字段全部保存一次：
    let dictFields = [
      'answer_status',
      'agree_or_not',
      'appraisal_progress_status',
      'appraisal_type',
      'auth_status',
      'aut_sa_aud_status_enum',
      'business_nature',
      'certificate_type',
      'check_summary',
      'clinical_service',
      'course_form',
      'enable_disable',
      'feedback_classify',
      'fileSceneType',
      'hold_certification',
      'hospital_class',
      'hospital_level',
      'hospital_type',
      'inspector_first_progress_status',
      'inspector_second_progress_status',
      'knowledge_level',
      'managing_affiliation',
      'medical_service',
      'notice_template_status',
      'notice_template_type',
      'offline_training_status',
      'ownership_form',
      'paper_status',
      'provide_ornot',
      'questionnaire_classify',
      'questionnaire_status',
      'questionnaire_type',
      'question_type',
      'reviewer_auth_status',
      'reviewer_hold_certificate',
      'reviewer_major',
      'reviewer_type',
      'review_cycle',
      'review_experience',
      'review_result',
      'review_summary',
      'route_for_admin',
      'speaking_level',
      'study_category',
      'study_resource_status',
      'study_type',
      'sys_common_status',
      'sys_job_group',
      'sys_job_status',
      'sys_mess_temp_type',
      'sys_normal_disable',
      'sys_notice_status',
      'sys_notice_type',
      'sys_oper_type',
      'sys_show_hide',
      'sys_user_sex',
      'sys_yes_no',
      'teaching_category',
      'true_false',
      'version_status',
      'yes_no',
    ];
    this.getDictList(dictFields);
  },
};
</script>
