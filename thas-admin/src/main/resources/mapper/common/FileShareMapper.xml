<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.FileShareMapper">

    <resultMap type="FileShare" id="FileShareResult">
        <result property="roleKey" column="role_key"/>
        <result property="fileId" column="file_id"/>
        <result property="upLoadTime" column="up_load_time"/>
        <result property="upLoaderId" column="up_loader_id"/>
        <result property="upLoaderName" column="up_loader_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectFileShareVo">
        select role_key, file_id, up_load_time, up_loader_id, up_loader_name, create_time, update_time
        from file_share
    </sql>

    <select id="selectFileShareList" parameterType="FileShare" resultMap="FileShareResult">
        <include refid="selectFileShareVo"/>
        <where>
            <if test="upLoadTime != null ">and up_load_time = #{upLoadTime}</if>
            <if test="upLoaderId != null ">and up_loader_id = #{upLoaderId}</if>
            <if test="upLoaderName != null  and upLoaderName != ''">and up_loader_name like concat('%', #{upLoaderName},
                '%')
            </if>
        </where>
    </select>

    <select id="selectFileShareListByFileId" parameterType="list" resultMap="FileShareResult">
        <include refid="selectFileShareVo"/>
        where file_id in
        <foreach collection="list" index="index" item="fileId" open="(" separator="," close=")">
            #{fileId}
        </foreach>
    </select>

    <select id="qryFileShare" parameterType="com.thas.web.dto.QryFileShareRequest"
            resultType="map">
        SELECT
            fs.role_key roleKey,
            fs.up_loader_id upLoaderId,
            fs.up_loader_name upLoaderName,
            fs.role_key roleKey,
            fs.source source,
            fs.reviewer_name reviewerName,
            fs.hospital_name hospitalName,
            ufi.create_time upLoadTime,
            ufi.id fileId,
            ufi.origin fileName,
            ufi.path url,
            ufi.platform platForm
        FROM
            file_share fs
        LEFT JOIN upload_file_info ufi ON fs.file_id = ufi.id
        <where>
            <if test="curRoleKey != null and curRoleKey != ''">fs.role_key = #{curRoleKey}</if>
            <if test="source != null and source.size > 0"> and fs.source in
                <foreach collection="source" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ownerId != null and ownerId != ''">and (fs.owner_id = #{ownerId} or fs.source = 'sourceUpload')</if>
            <if test="uploaderName != null and uploaderName != ''">and fs.up_loader_name like concat('%', #{uploaderName}, '%')</if>
            <if test="fileName != null and fileName != ''"> and ufi.origin like concat('%', #{fileName}, '%')</if>
            <if test="reviewerName != null and reviewerName != ''">and fs.reviewer_name like concat('%', #{reviewerName}, '%')</if>
            <if test="hospitalName != null and hospitalName != ''">and fs.hospital_name like concat('%', #{hospitalName}, '%')</if>
        </where>
        order by ufi.create_time desc
    </select>

    <select id="selectFileShareByRoleKey" parameterType="String" resultMap="FileShareResult">
        <include refid="selectFileShareVo"/>
        where role_key = #{roleKey}
    </select>

    <insert id="insertFileShare" parameterType="FileShare">
        insert into file_share
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roleKey != null">role_key,</if>
            <if test="fileId != null">file_id,</if>
            <if test="upLoadTime != null">up_load_time,</if>
            <if test="upLoaderId != null">up_loader_id,</if>
            <if test="upLoaderName != null and upLoaderName != ''">up_loader_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roleKey != null">#{roleKey},</if>
            <if test="fileId != null">#{fileId},</if>
            <if test="upLoadTime != null">#{upLoadTime},</if>
            <if test="upLoaderId != null">#{upLoaderId},</if>
            <if test="upLoaderName != null and upLoaderName != ''">#{upLoaderName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <insert id="batchInsertFileShare" parameterType="list">
<!--        insert into file_share (role_key, file_id, up_load_time, up_loader_id, up_loader_name, owner_id, `source`)-->
<!--        values-->
<!--        <foreach collection="list" index="index" item="fileShare" separator=",">-->
<!--            (#{fileShare.roleKey}, #{fileShare.fileId}, #{fileShare.upLoadTime}, #{fileShare.upLoaderId},-->
<!--            #{fileShare.upLoaderName}, #{fileShare.ownerId}, #{fileShare.source})-->
<!--        </foreach>-->
        <foreach collection="list" item="fileShare" index="index" open="" close="" separator=";">
            INSERT INTO file_share (role_key, file_id, up_load_time, up_loader_id, up_loader_name, owner_id, `source`,reviewer_id,reviewer_name,apply_no,hospital_name)
            values (
            #{fileShare.roleKey},
            #{fileShare.fileId},
            #{fileShare.upLoadTime},
            #{fileShare.upLoaderId},
            #{fileShare.upLoaderName},
            #{fileShare.ownerId},
            #{fileShare.source},
            ifnull(#{fileShare.reviewerId, jdbcType=VARCHAR}, ''),
            ifnull(#{fileShare.reviewerName, jdbcType=VARCHAR},''),
            ifnull(#{fileShare.applyNo, jdbcType=VARCHAR},''),
            ifnull(#{fileShare.hospitalName, jdbcType=VARCHAR},'')
            )
        </foreach>
    </insert>

    <update id="updateFileShare" parameterType="FileShare">
        update file_share
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileId != null">file_id = #{fileId},</if>
            <if test="upLoadTime != null">up_load_time = #{upLoadTime},</if>
            <if test="upLoaderId != null">up_loader_id = #{upLoaderId},</if>
            <if test="upLoaderName != null and upLoaderName != ''">up_loader_name = #{upLoaderName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where role_key = #{roleKey}
    </update>

    <update id="updateByOwnerId" parameterType="com.thas.web.dto.UpdFileShareTempDataDto">
        update file_share
        <trim prefix="SET" suffixOverrides=",">
            <if test="roleKey != null and roleKey != ''">role_key = #{roleKey},</if>
            <if test="userId != null">up_loader_id = #{userId},</if>
            <if test="nickName != null and nickName != ''">up_loader_name = #{nickName},</if>
            <if test="ownerId != null and ownerId != ''">owner_id = #{ownerId},</if>
        </trim>
        where owner_id = #{tempCode}
    </update>

    <update id="updateByOwnerIdAndRoleKey" parameterType="com.thas.web.dto.UpdFileShareTempDataDto">
        update file_share
        <trim prefix="SET" suffixOverrides=",">
            <if test="roleKey != null and roleKey != ''">role_key = #{roleKey},</if>
            <if test="userId != null">up_loader_id = #{userId},</if>
            <if test="nickName != null and nickName != ''">up_loader_name = #{nickName},</if>
            <if test="ownerId != null and ownerId != ''">owner_id = #{ownerId},</if>
        </trim>
        where owner_id = #{tempCode}
        and role_key = #{oldRoleKey}
    </update>

    <delete id="deleteFileShareByRoleKey" parameterType="String">
        delete
        from file_share
        where role_key = #{roleKey}
    </delete>

    <delete id="deleteFileShareByRoleKeys" parameterType="String">
        delete from file_share where role_key in
        <foreach item="roleKey" collection="array" open="(" separator="," close=")">
            #{roleKey}
        </foreach>
    </delete>

    <delete id="delFileShareByFileIdList" parameterType="com.thas.web.dto.DelFileShareRequest">
        delete from file_share where file_id in
        <foreach collection="fileIdList" item="fileId" open="(" close=")" separator=",">
            #{fileId}
        </foreach>
        <if test="ownerId != null and ownerId != ''">
            and owner_id = #{ownerId}
        </if>
    </delete>
</mapper>