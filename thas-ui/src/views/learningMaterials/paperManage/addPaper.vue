<template>
  <div class="add-paper">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>{{$route.query.id ? "编辑考卷" : "添加考卷"}}</span>
        <div style="float: right; padding: 3px 0">
          <span>考试总分：</span>
          <span>{{score}}</span>
          <span style="padding-right: 10px; border-right: 2px solid #ccc;">分</span>
          <span style="padding-left: 10px;">试卷题目数：</span>
          <span>{{questionList.length}}</span>
        </div>
      </div>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="考卷名称" prop="paperName">
          <el-input v-model.trim="form.paperName" placeholder="请输入考卷名称" maxlength="128"></el-input>
        </el-form-item>
        <el-form-item label="归属课程" prop="learnResourceId">
          <!-- <el-autocomplete v-model.trim="form.learnResourceId" :fetch-suggestions="querySearchAsync" placeholder="请选择课程名称" @select="handleSelect"></el-autocomplete> -->
          <el-select v-model.trim="form.learnResourceId" clearable placeholder="请选择归属课程" @change="handleSelect" popper-class="resource-popper">
            <el-option v-for="(item, index) in learnResourceOptions" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="考卷描述" prop="paperDiscription">
          <el-input type="textarea" v-model.trim="form.paperDiscription" placeholder="请输入考卷描述" maxlength="128"></el-input>
        </el-form-item>
        <el-form-item label="及格分数" prop="passScore">
          <el-input :maxlength="50" v-model.trim="form.passScore" type="number" min="0" placeholder="请输入及格分数"></el-input>
        </el-form-item>
      </el-form>
      <div>
        <div v-for="(item, index) in questionList" :key="index" style="display: flex; justify-content: space-between; border-top: 2px solid #e9eaeb; margin-left: 100px;">
          <div v-if="item.isEdit">
            <edit-question :question-info="item" :index="index" @add="editSubject" @cancel="cancel"></edit-question>
          </div>
          <div v-else>
            <el-form size="medium" label-width="0px">
              <el-form-item label="">
                <div style="font-size: 16px; font-weight: bold;">
                  <span style="word-break: break-all;">{{item.question}}</span>
                  <span style="margin-left: 30px; color: #00ae22;">（分值：{{item.score}}分）</span>
                </div>
                <el-radio-group v-if="item.questionType == 1">
                  <el-radio v-for="(question, i) in item.singleAnswerOptions" :key="i" :label="question.label">
                    {{question.label + (item.singleAnswer == question.label ? " (正确答案)" : "")}}
                  </el-radio>
                </el-radio-group>
                <el-checkbox-group v-else-if="item.questionType == 2" value="null">
                  <el-checkbox v-for="(question, i) in item.multipleAnswerOptions" :key="i" :label="question.label">
                    {{question.label + (item.multipleAnswer.includes(question.label) ? " (正确答案)" : "")}}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-form>
          </div>
          <div>
            <el-button v-show="!item.isEdit" type="text" icon="el-icon-edit" @click="edit(index)">编辑</el-button>
            <el-button type="text" style="color: #f00;" icon="el-icon-delete" @click="del(index)">删除</el-button>
          </div>
        </div>
      </div>
      <edit-question v-show="showAddForm" ref="editQuestionForm" @add="addSubject" @cancel="cancel"></edit-question>
      <el-button v-show="!showAddForm" icon="el-icon-plus" @click="showAddForm = true">添加题目</el-button>
      <div style="margin-top: 50px;">
        <el-button type="primary" @click="save">保存</el-button>
        <el-button @click="back">返回</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import EditQuestion from '@/components/TextQuestion/EditQuestion.vue'
import request from "@/utils/request"
export default {
  name: 'addPaper',
  components: {
    EditQuestion
  },
  data() {
    return {
      form: {
        paperName: '',
        learnResourceId: null,
        paperDiscription: '',
        passScore: ''
      },
      learnResourceId: null,
      rules: {
        paperName: [{ required: true, message: '请输入考卷名称', trigger: 'blur' }],
        learnResourceId: [{ required: true, message: '请选择课程名称', trigger: 'change' }],
        paperDiscription: [{ required: true, message: '请输入考卷描述', trigger: 'blur' }]
      },
      questionList: [],
      showAddForm: false,
      learnResourceOptions: [],
      timeout: null
    }
  },
  computed: {
    score() {
      return this.questionList.reduce((prev, cur) => {
        return prev + parseInt(cur.score)
      }, 0)
    }
  },
  created() {
    if (this.$route.query.id) {
      this.getPaperDetail()
    }
    this.querySearchAsync()
  },
  methods: {
    getPaperDetail() {
      request({
        url: '/system/paper/edit/' + this.$route.query.id,
        method: 'get'
      }).then((res) => {
        if (res.code != 200) {
          this.$message({
            type: 'error',
            message: res.message || '系统错误，请稍后再试。'
          })
        } else {
          this.form.paperName = res.data.paperName
          this.form.learnResourceId = res.data.learnResourceTitle
          this.learnResourceId = res.data.learnResourceId
          this.form.paperDiscription = res.data.paperDiscription
          this.form.passScore = res.data.passScore
          for (let index = 0; index < res.data.details.length; index++) {
            let item = {
              score: res.data.details[index].score,
              questionType: res.data.details[index].type,
              question: res.data.details[index].examQuestion,
              id: res.data.details[index].id
            }
            if (res.data.details[index].type == 1) {
              item.singleAnswer = res.data.details[index].correctAnswer
              item.singleAnswerOptions = JSON.parse(res.data.details[index].questionOptions)
            } else if (res.data.details[index].type == 2) {
              item.multipleAnswer = res.data.details[index].correctAnswer
              item.multipleAnswerOptions = JSON.parse(res.data.details[index].questionOptions)
            }
            this.questionList.push(item)
          }
        }
      })
    },
    addSubject(data) {
      if (data) {
        for (let i = 0; i < this.questionList.length; i++) {
          if (this.questionList[i].question === data.question) {
            return this.$message({
              type: 'warning',
              message: '题目 ' + data.question + ' 已存在，请勿重复添加！'
            })
          }
        }
        if (data.questionType == 1) {
          for (let index = 0; index < data.singleAnswerOptions.length; index++) {
            data.singleAnswerOptions[index].isEdit = false
          }
        } else if (data.questionType == 2) {
          for (let index = 0; index < data.multipleAnswerOptions.length; index++) {
            data.multipleAnswerOptions[index].isEdit = false
          }
        }
        this.questionList.push(JSON.parse(JSON.stringify(data)))
        this.$refs.editQuestionForm.resetForm()
        this.showAddForm = false
      }
    },
    editSubject(data) {
      for (let i = 0; i < this.questionList.length; i++) {
        if (this.questionList[i].question === data.question && data.index != i) {
          return this.$message({
            type: 'warning',
            message: '题目 ' + data.question + ' 已存在，请勿重复添加！'
          })
        }
      }
      this.questionList.splice(data.index, 1, data)
    },
    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (!this.questionList.length) {
            this.$message({
              type: 'error',
              message: '请添加题目！'
            })
            return
          }
          if (this.score < this.form.passScore) {
            this.$message({
              type: 'warning',
              message: '及格分数不能大于总分哦~'
            })
            return
          }
          let params = {}
          let details = []
          for (let index = 0; index < this.questionList.length; index++) {
            let item = {}
            item.score = this.questionList[index].score
            item.type = this.questionList[index].questionType
            item.examQuestion = this.questionList[index].question
            if (this.questionList[index].id || this.questionList[index].id === 0) {
              item.id = this.questionList[index].id
            }
            if (this.questionList[index].questionType == 1) {
              item.correctAnswer = this.questionList[index].singleAnswer
              item.questionOptions = JSON.stringify(this.questionList[index].singleAnswerOptions)
            } else if (this.questionList[index].questionType == 2) {
              if (Array.isArray(this.questionList[index].multipleAnswer)) {
                item.correctAnswer = JSON.stringify(this.questionList[index].multipleAnswer)
              } else {
                item.correctAnswer = this.questionList[index].multipleAnswer
              }
              item.questionOptions = JSON.stringify(this.questionList[index].multipleAnswerOptions)
            }
            details.push(item)
          }
          params = {
            ...this.form,
            learnResourceId: this.learnResourceId,
            details
          }
          if (this.$route.query.id || this.$route.query === 0) {
            params.id = this.$route.query.id
          }
          request({
            url: '/system/paper',
            method: this.$route.query.id || this.$route.query.id === 0 ? 'put' : 'post',
            data: params
          }).then((res) => {
            if (res.code != 200) {
              this.$message({
                type: 'error',
                message: res.message || '系统错误，请稍后再试。'
              })
            } else {
              this.$message({
                type: 'success',
                message: this.$route.query.id || this.$route.query.id === 0 ? '编辑考卷成功！' : '添加考卷成功！'
              })
              this.back()
            }
          })
        } else {
          return false
        }
      })
    },
    cancel(index) {
      if (index || index === 0) {
        this.questionList[index].isEdit = false
        const obj = JSON.parse(JSON.stringify(this.questionList[index]))
        this.questionList.splice(index, 1, obj)
      } else {
        this.showAddForm = false
      }
    },
    edit(index) {
      this.questionList[index].isEdit = true
      const obj = JSON.parse(JSON.stringify(this.questionList[index]))
      this.questionList.splice(index, 1, obj)
    },
    del(index) {
      this.questionList.splice(index, 1)
    },
    back() {
      this.$store.dispatch("tagsView/delView", this.$route) &&
        this.$router.push("/learningMaterials/paperManage/paperList")
    },
    querySearchAsync(queryString, cb) {
      request({
        url: '/system/resource/list',
        method: 'get',
        params: {
          title: queryString
        }
      }).then((res) => {
        if (res.code != 200) return
        this.learnResourceOptions = []
        for (let index = 0; index < res.rows.length; index++) {
          this.learnResourceOptions.push({
            label: res.rows[index].title,
            value: res.rows[index].id
          })
        }
        // clearTimeout(this.timeout)
        // this.timeout = setTimeout(() => {
        //   let list = []
        //   for (let index = 0; index < res.rows.length; index++) {
        //     list.push({
        //       value: res.rows[index].title,
        //       id: res.rows[index].id
        //     })
        //   }
        //   cb(list)
        // }, 500)
      })
    },
    handleSelect(item) {
      this.learnResourceId = item
    }
  }
}
</script>

<style lang="scss" scoped>
.add-paper {
  .el-radio {
    display: block;
    padding: 10px 0 0 0;
    line-height: 36px;
    &:nth-child(1) {
      padding: 0;
    }
    ::v-deep .el-radio__label {
      display: inline-block;
      word-break: break-all;
      white-space: normal;
      vertical-align: top;
    }
  }
  .el-checkbox {
    padding: 10px 0 0 0;
    display: block;
    &:nth-child(1) {
      padding: 0;
    }
    ::v-deep .el-checkbox__label {
      display: inline-block;
      word-break: break-all;
      white-space: normal;
      vertical-align: top;
      line-height: 36px;
    }
  }
}
</style>
<style lang="scss">
.resource-popper {
  max-width: 400px !important;
  .el-select-dropdown__item {
    span {
      text-overflow: ellipsis;
    }
  }
  .el-scrollbar__bar {
    opacity: 1;
  }
}
</style>