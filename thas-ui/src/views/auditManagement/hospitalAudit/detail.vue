<template>
  <div class="hospital_detail_message">
    <dict-span style="display: none" :dictType="`provide_ornot`" :value="0"></dict-span>
    <detail-message :form="baseInfo">
      <span slot="authStatus" slot-scope="scope">
        <i class="el-icon-warning" v-if="scope.data == 1" style="color: red"> 待审核</i>
        <i class="el-icon-success" v-else-if="scope.data == 2" style="color: green">
          审核通过</i>
        <i class="el-icon-error" v-else style="color: red"> 审核拒绝</i>
      </span>
    </detail-message>
    <!-- 具体型心 -->
    <detail-message :form="detailInfo"></detail-message>
    <detail-message :form="qualificationInfo"></detail-message>
    <detail-message :form="reviewInfo">
      <span slot="prevReviewResult" slot-scope="scope">
        <dict-span :dictType="`hospital_level`" :value="scope.data[0]" style="float: left"></dict-span>
        <dict-span :dictType="`hospital_class`" style="float: left" :value="scope.data[1]"></dict-span>
      </span>
      <span slot="certificate" slot-scope="scope">
        <div v-for="(cer, index) in scope.data" :key="index">
          <span v-if="cer.abilityDictValue == 0">{{ cer.abilityName }}</span>
          <span v-else-if="cer.abilityDictValue == 'fail'">{{ `${cer.expireDate}参加${cer.abilityName}国际认证项目，未通过` }}</span>
          <span v-else>{{ `${cer.abilityName} ，到期时间：${cer.expireDate}`}}</span>
        </div>
      </span>
    </detail-message>
    <!--  -->
    <div style="padding: 0 0 10px 0">
      <el-card class="box-card services" style="border: 0" shadow="never">
        <div slot="header" class="clearfix">
          <span>医院科室服务</span>
        </div>
        <el-table :data="services" border stripe>
          <el-table-column prop="departmentName" label="科室"></el-table-column>
          <el-table-column prop="outpatientService" label="医技服务">
            <template slot-scope="scope">
              <dict-span v-if="scope.row.departmentDictType == 'medical_service'" :dictType="`provide_ornot`"
                :value="scope.row.outpatientService"></dict-span>
              <span v-else> / </span>
            </template>
          </el-table-column>
          <el-table-column prop="outpatientService" label="门诊服务">
            <template slot-scope="scope">
              <span v-if="scope.row.departmentDictType == 'medical_service'">
                /
              </span>
              <dict-span v-else :dictType="`provide_ornot`" :value="scope.row.outpatientService"></dict-span>
            </template>
          </el-table-column>
          <el-table-column prop="inpatientService" label="住院服务">
            <template slot-scope="scope">
              <span v-if="scope.row.departmentDictType == 'medical_service'">
                /
              </span>
              <dict-span v-else :dictType="`provide_ornot`" :value="scope.row.inpatientService"></dict-span>
            </template>
          </el-table-column>
          <el-table-column prop="departmentDesc" label="备注"></el-table-column>
        </el-table>
      </el-card>
    </div>
    <!--  -->

    <!-- <detail-message :form="authInfo"> </detail-message> -->
    <el-card shadow="never" style="border: 0; margin-bottom: 10px">
      <div slot="header" class="clearfix">
        <span>医院申请表</span>
      </div>
      <el-link type="primary" @click="downloadSignFile">{{ signFile.fileName }}<i
          class="el-icon-download el-icon--right"></i>
      </el-link>
    </el-card>

    <el-card v-if="type" shadow="never" style="border: 0; margin-bottom: 10px">
      <div slot="header" class="clearfix">
        <span>审核结果</span>
      </div>
      <el-form ref="resultForm" label-width="205px" :model="resultForm" :rules="rules">
        <el-form-item label="审核结果：" prop="result">
          <el-select v-model.trim="resultForm.result" clearable filterable placeholder="请选择">
            <el-option label="审核通过" value="2"></el-option>
            <el-option label="审核拒绝" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="resultForm.result == 3" label="审核说明：" prop="remark" :rules="remark">
          <el-input type="textarea" v-model.trim="resultForm.remark" placeholder="请输入审核说明" style="width: 260px"
            :maxlength="50"></el-input>
        </el-form-item>
        <el-form-item class="signFileId" label="上传“中心”审批后的申请表：" prop="signFileId" :rules="signFileId">
          <upload :accept="`.PDF`" :action="`${uploadUrl}/common/uploadFile`" :files="files" :single="true"
          :data="{type: '1', downLoadFileName: `${hospitalName}-认证评审申请表.pdf`}" @uploadSuccess="uploadSuccess" @fileRemove="fileRemove"></upload>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">确定</el-button>
          <el-button @click="back">返回</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import request, { download } from "@/utils/request";
import store from "@/store";
import DictSpan from "@/components/DetailMessage/dictSpan.vue";
let that = null;
export default {
  name: "HospitalDetailMessage",
  components: { DictSpan },
  data() {
    return {
      defaultForm: {
        title: "测试标题",
        list: [],
      },
      url: {
        detail: "/hospital/query/hospital/detail",
      },
      baseInfo: {
        title: "医院基本信息",
        list: [],
        shadow: "never",
      },
      authInfo: {
        title: "被授权人信息",
        list: [],
        shadow: "never",
      },
      detailInfo: {
        title: "医院详细信息",
        list: [],
        shadow: "never",
      },
      qualificationInfo: {
        title: "医院资质信息",
        list: [],
        shadow: "never",
      },
      reviewInfo: {
        title: "医院评审信息",
        list: [],
        shadow: "never",
      },
      resultForm: {
        result: null,
        remark: "",
        signFileId: "",
      },
      rules: {
        result: [
          { required: true, message: "请选择审核结果", trigger: "change" },
        ],
      },
      fileDetails: {},
      files: [],
      baseData: {},
      services: [],
      type: 0,
      applyNo: "",
      hospitalName: ""
    };
  },

  watch: {
    $route: (to, from) => {
      if (to.path != "/auditManagement/hospitalDetailMessage") return;
      if (!this && !that) return;
      if (to.query.applyNo != that.applyNo || that.applyNo == "") {
        that.getDetail();
        that.type = that.$route.query.type;
      }
    },
  },

  computed: {
    uploadUrl() {
      return localStorage.baseUrl || process.env.VUE_APP_BASE_API;
    },
    remark() {
      return [
        {
          required: this.resultForm.result == 3,
          message: "请输入审核说明",
          trigger: "blur",
        },
      ];
    },
    signFileId() {
      return [
        {
          required: true,
          message: "请上传平台盖章后的医院申请表",
          trigger: "blur",
        },
      ];
    },
    fileType() {
      return (fileList) => {
        if (fileList && fileList.length) {
          if (fileList[0]) {
            return fileList[0].fileName.split(".").pop().toLowerCase() == "pdf"
              ? "file"
              : "image";
          }
        }
        return "file";
      };
    },

    isFile() {
      return (fileList) => {
        // console.log(fileList);
        return this.fileType(fileList) == "file";
      };
    },

    signFile() {
      let signFileId = this.baseData?.hospitalBaseInfo?.signFileId;
      let signFile = this.fileDetails[signFileId];
      if (!signFile) return {};
      return signFile[0];
    },
  },

  created() {
    that = this;
  },

  mounted() {
    this.getDetail();
    this.type = this.$route.query.type;
  },
  methods: {
    getDetail() {
      const { applyNo } = this.$route.query;
      this.applyNo = applyNo;
      request({
        url: this.url.detail,
        method: "post",
        data: {
          commonId: applyNo,
        },
      }).then((res) => {
        this.baseData = res;
        this.processData(res);
      });
    },

    downloadSignFile() {
      download(
        "/common/downloadFile",
        { fileId: this.signFile.fileId },
        this.signFile.downLoadFileName
      );
    },

    processData(data) {
      const {
        hospitalBaseInfo,
        hospitalAuthContact,
        hospitalDepartmentList,
        hospitalLegalPerson,
        certificateAbilityList,
        fileDetails,
      } = data;
      let arr1 = [];
      let arr2 = [];
      this.fileDetails = fileDetails;
      for (let index = 0; index < hospitalDepartmentList.length; index++) {
        if (
          hospitalDepartmentList[index].departmentDictType ===
          "clinical_service"
        ) {
          arr1.push(hospitalDepartmentList[index]);
        } else if (
          hospitalDepartmentList[index].departmentDictType === "medical_service"
        ) {
          arr2.push(hospitalDepartmentList[index]);
        }
      }
      this.services = [
        ...arr1.concat(arr2).map((dep) => {
          if (!dep.departmentDesc) {
            dep.departmentDesc = "--";
          }
          return dep;
        }),
      ];

      this.hospitalName = hospitalBaseInfo.hospitalName;
      // 基本信息
      const baseInfo = {
        authStatus: hospitalBaseInfo.authStatus,
        authDesc: hospitalBaseInfo.authDesc,
        hospitalName: hospitalBaseInfo.hospitalName,
        practiceLicenseNo: hospitalBaseInfo.practiceLicenseNo,
        businessLicenseId: hospitalBaseInfo.businessLicenseId,
        signFileId: hospitalBaseInfo.signFileId,
        certificateType: hospitalLegalPerson.certificateType,
        certificateNumber: hospitalLegalPerson.certificateNumber,
        legalPersonName: hospitalLegalPerson.legalPersonName,
        legalPersonPhoto: hospitalLegalPerson.legalPersonPhoto,
        legalPersonMobile: hospitalLegalPerson.legalPersonMobile,
      };

      const detailInfo = {
        businessTime: hospitalBaseInfo.businessTime,
        postalOde: hospitalBaseInfo.postalOde,
        hospitalPhone: hospitalBaseInfo.hospitalPhone,
        hospitalFax: hospitalBaseInfo.hospitalFax,
        hospitalOfficialWebsite: hospitalBaseInfo.hospitalOfficialWebsite,
        hospitalContacts: hospitalBaseInfo.hospitalContacts,
        hospitalExtPhone: hospitalBaseInfo.hospitalExtPhone,
        contactsEmail: hospitalBaseInfo.contactsEmail,
        postalAddress: hospitalBaseInfo.postalAddress,
        competentAuthorityName: hospitalBaseInfo.competentAuthorityName,
        clinicalDepartmentNum: hospitalBaseInfo.clinicalDepartmentNum,
        inpatientAreaNum: hospitalBaseInfo.inpatientAreaNum,
        medicalTechnologyNum: hospitalBaseInfo.medicalTechnologyNum,
        areaCovered: hospitalBaseInfo.areaCovered,
        areaArchitecture: hospitalBaseInfo.areaArchitecture,
        areaBusinessArchitecture: hospitalBaseInfo.areaBusinessArchitecture,
        preparationBed: hospitalBaseInfo.preparationBed,
        actualBed: hospitalBaseInfo.actualBed,
        bedUtilization: hospitalBaseInfo.bedUtilization,
        onDutyNum: hospitalBaseInfo.onDutyNum,
        healthTechnologyNum: hospitalBaseInfo.healthTechnologyNum,
        contactsPhone: hospitalBaseInfo.contactsPhone,
        hospitalAddress: hospitalBaseInfo.hospitalAddress,
      };

      const reviewInfo = {
        first: hospitalBaseInfo.first,
        beforeReviewerConclusionLevel:
          hospitalBaseInfo.beforeReviewerConclusionLevel,
        beforeReviewerConclusionGrade:
          hospitalBaseInfo.beforeReviewerConclusionGrade,
        beforeReviewerDate: hospitalBaseInfo.beforeReviewerDate,
        expectReviewDate: hospitalBaseInfo.expectReviewDate,
      };

      this.reviewInfo.list =
        reviewInfo.first == 2
          ? [
            {
              label: "评审信息",
              value: reviewInfo.first,
              type: "dict",
              dict: "review_experience",
            },
            {
              label: "上次等级评审时间",
              value: reviewInfo.beforeReviewerDate,
              type: "text",
            },
            {
              label: "上次评审结论",
              value: [
                reviewInfo.beforeReviewerConclusionGrade,
                reviewInfo.beforeReviewerConclusionLevel,
              ],
              slot: "prevReviewResult",
              span: 24,
            },
            {
              label: "期望评审日期",
              value: reviewInfo.expectReviewDate || "--",
              type: "text",
            },
          ]
          : [
            {
              label: "评审信息",
              value: reviewInfo.first,
              type: "dict",
              dict: "review_experience",
            },
            {
              label: "期望评审日期",
              value: reviewInfo.expectReviewDate || "--",
              type: "text",
            },
          ];
      if (reviewInfo.first == 2) {
        this.reviewInfo.list.push({
          label: "相关认证",
          value: certificateAbilityList,
          slot: "certificate",
          span: 24,
        });
      }

      const qualificationInfo = {
        hospitalType: hospitalBaseInfo.hospitalType,
        formOwnership: hospitalBaseInfo.formOwnership,
        natureOperation: hospitalBaseInfo.natureOperation,
        managementAffiliation: hospitalBaseInfo.managementAffiliation,
        hospitalLevel: hospitalBaseInfo.hospitalLevel,
        teachingCategory: hospitalBaseInfo.teachingCategory,
        teachingCategoryOther: hospitalBaseInfo.teachingCategoryOther,
        formOwnershipOther: hospitalBaseInfo.formOwnershipOther,
        managementAffiliationOther: hospitalBaseInfo.managementAffiliationOther,
      };

      this.qualificationInfo.list = [
        {
          label: "医疗机构类别",
          value: qualificationInfo.hospitalType,
          type: "dict",
          dict: "hospital_type",
        },
        {
          label: "所有制形式",
          value: qualificationInfo.formOwnership,
          type: "dict",
          dict: "ownership_form",
          otherValue:
            qualificationInfo.formOwnership == 6
              ? qualificationInfo.formOwnershipOther
              : "",
        },
        {
          label: "经营性质",
          value: qualificationInfo.natureOperation,
          type: "dict",
          dict: "business_nature",
        },
        {
          label: "管理隶属关系",
          value: qualificationInfo.managementAffiliation,
          type: "dict",
          dict: "managing_affiliation",
          otherValue:
            qualificationInfo.managementAffiliation == 5
              ? qualificationInfo.managementAffiliationOther
              : "",
        },
        {
          label: "医院目前级别",
          value: qualificationInfo.hospitalLevel,
          type: "dict",
          dict: "hospital_level",
        },
        {
          label: "教学类别",
          value: qualificationInfo.teachingCategory,
          type: "dict",
          dict: "teaching_category",
          otherValue:
            qualificationInfo.teachingCategory == 4
              ? qualificationInfo.teachingCategoryOther
              : "",
        },
      ];

      // const authInfo = {
      //   authContactEmail: hospitalAuthContact.authContactEmail,
      //   authContactMobile: hospitalAuthContact.authContactMobile,
      //   authContactName: hospitalAuthContact.authContactName,
      //   authContactPhoto: hospitalAuthContact.authContactPhoto,
      //   certificateNumber: hospitalAuthContact.certificateNumber,
      //   certificateType: hospitalAuthContact.certificateType,
      //   contactAddress: hospitalAuthContact.contactAddress,
      //   hospitalCertificateAuth: hospitalAuthContact.hospitalCertificateAuth,
      // };

      //
      // const authContactPhoto = authInfo.authContactPhoto.split(",") || [];
      // const acPhotos = authContactPhoto.map((photo) => {
      //   return {
      //     url: this.isFile(fileDetails[photo])
      //       ? fileDetails[photo][0].fileName
      //       : fileDetails[photo][0].url,
      //     id: fileDetails[photo][0].id,
      //   };
      // });
      // this.authInfo.list = [
      //   { label: "姓名", value: authInfo.authContactName, type: "text" },
      //   { label: "手机号", value: authInfo.authContactMobile, type: "text" },
      //   {
      //     label: "证件类型",
      //     value: authInfo.certificateType,
      //     type: "dict",
      //     dict: "certificate_type",
      //   },
      //   { label: "邮箱", value: authInfo.authContactEmail, type: "text" },
      //   { label: "证件号码", value: authInfo.certificateNumber, type: "text" },
      //   { label: "联系地址", value: authInfo.contactAddress, type: "text" },
      //   {
      //     label: "证件照",
      //     value: acPhotos,
      //     type: "images",
      //   },
      //   {
      //     label: "医院授权书",
      //     value: this.isFile(fileDetails[authInfo.hospitalCertificateAuth])
      //       ? fileDetails[authInfo.hospitalCertificateAuth][0].fileName
      //       : fileDetails[authInfo.hospitalCertificateAuth][0].url,
      //     type: this.fileType(fileDetails[authInfo.hospitalCertificateAuth]),
      //     id: fileDetails[authInfo.hospitalCertificateAuth][0].fileId,
      //   },
      // ];

      const legalPersonPhoto = baseInfo.legalPersonPhoto.split(",") || [];
      const lpPhotos = legalPersonPhoto.map((photo) => {
        return {
          url: this.isFile(fileDetails[photo])
            ? fileDetails[photo][0].fileName
            : fileDetails[photo][0].url,
          id: fileDetails[photo][0].id,
        };
      });
      const signFile = fileDetails[baseInfo.signFileId][0];
      this.baseInfo.list = [
        {
          label: "医院审核状态",
          value: baseInfo.authStatus,
          slot: "authStatus",
          span: !baseInfo.authDesc ? 24 : 12,
        },
        {
          label: "审核说明",
          value: baseInfo.authDesc,
          hidden: !baseInfo.authDesc,
          type: "text",
        },
        {
          label: "医院名称",
          value: baseInfo.hospitalName,
          type: "text",
        },
        {
          label: "法定代表人",
          value: baseInfo.legalPersonName,
          type: "text",
        },
        {
          label: "医疗机构执业许可证登记号（如适用）",
          value: baseInfo.practiceLicenseNo,
          type: "text",
          clz: "long",
        },
        {
          label: "法人证件类型",
          value: baseInfo.certificateType,
          type: "dict",
          dict: "certificate_type",
        },

        {
          label: "法人证件号",
          value: baseInfo.certificateNumber,
          type: "text",
        },
        {
          label: "法人手机号",
          value: baseInfo.legalPersonMobile,
          type: "text",
          // label: "盖章确认文件",
          // id: signFile.fileId,
          // value: signFile.fileName,
          // type: "file",
        },

        {
          label: "法人证件照",
          value: lpPhotos,
          type: "images",
        },

        {
          label: "工商营业执照",
          value: this.isFile(fileDetails[baseInfo.businessLicenseId])
            ? fileDetails[baseInfo.businessLicenseId][0].fileName
            : fileDetails[baseInfo.businessLicenseId][0].url,
          type: this.fileType(fileDetails[baseInfo.businessLicenseId]),
          id: fileDetails[baseInfo.businessLicenseId][0].fileId,
          url: this.isFile(fileDetails[baseInfo.businessLicenseId])
            ? fileDetails[baseInfo.businessLicenseId][0].url : ''
        },
      ];

      this.detailInfo.list = [
        { label: "开业/院日期", value: detailInfo.businessTime, type: "text" },
        { label: "邮政编码", value: detailInfo.postalOde, type: "text" },
        { label: "地址", value: detailInfo.hospitalAddress, type: "text" },
        { label: "座机", value: `${detailInfo.hospitalPhone}${detailInfo.hospitalExtPhone ? ('-' + detailInfo.hospitalExtPhone) : ''}`, type: "text" },
        { label: "传真", value: detailInfo.hospitalFax, type: "text" },
        {
          label: "医院网址",
          value: detailInfo.hospitalOfficialWebsite,
          type: "text",
        },
        {
          label: "联系人姓名",
          value: detailInfo.hospitalContacts,
          type: "text",
        },
        { label: "联系手机号", value: detailInfo.contactsPhone, type: "text" },
        { label: "联系邮箱", value: detailInfo.contactsEmail, type: "text" },
        { label: "通讯地址", value: detailInfo.postalAddress, type: "text" },
        {
          label: "主管单位名称",
          value: detailInfo.competentAuthorityName,
          type: "text",
        },
        {
          label: "临床科室总数",
          value: detailInfo.clinicalDepartmentNum,
          type: "text",
          unit: "个",
        },
        {
          label: "病区总数",
          value: detailInfo.inpatientAreaNum,
          type: "text",
          unit: "个",
        },
        {
          label: "医技科室总数",
          value: detailInfo.medicalTechnologyNum,
          type: "text",
          unit: "个",
        },
        {
          label: "占地面积",
          value: detailInfo.areaCovered,
          type: "text",
          unit: "m²",
        },
        {
          label: "总建筑面积",
          value: detailInfo.areaArchitecture,
          type: "text",
          unit: "m²",
        },
        {
          label: "业务用房建筑面积",
          value: detailInfo.areaBusinessArchitecture,
          type: "text",
          unit: "m²",
        },
        {
          label: "编制床位",
          value: detailInfo.preparationBed,
          type: "text",
          unit: "个",
        },
        {
          label: "实际开放总床位数",
          value: detailInfo.actualBed,
          type: "text",
          unit: "个",
        },
        {
          label: "病床使用率",
          value: detailInfo.bedUtilization,
          type: "text",
          unit: "%",
        },
        {
          label: "全院在岗人员",
          value: detailInfo.onDutyNum,
          type: "text",
          unit: "人",
        },
        {
          label: "卫生技术人员",
          value: detailInfo.healthTechnologyNum,
          type: "text",
          unit: "人",
        },
      ];
    },

    async getImageUrlById(id) {
      return request({
        url: "",
        method: "post",
        data: {},
      });
    },

    onSubmit() {
      this.$refs.resultForm.validate((valid) => {
        if (valid) {
          request({
            url: "/aut/record/info/submitAutRecord",
            method: "post",
            data: {
              accountId: this.baseData.hospitalBaseInfo.applyNo,
              role: "100",
              audId: store.getters.userId,
              audDesc: this.resultForm.remark,
              audResult: this.resultForm.result,
              signFileId: this.resultForm.signFileId
            },
          }).then((res) => {
            res.code == 200 &&
              this.$message({
                type: "success",
                message: "提交成功",
              }) &&
              this.back();
          });
        } else {
          return false;
        }
      });
    },
    back() {
      this.$store.dispatch("tagsView/delView", this.$route) &&
        this.$router.push("/auditManagement/hospitalAudit");
    },

    fileRemove(file, length) {
      if (length == 0) {
        this.$set(this.resultForm, "signFileId", "");
        this.$refs.resultForm.validateField("signFileId");
      }
    },

    uploadSuccess(response, file) {
      if (response.code != 200) {
        return this.$message.error(response.msg || response.message);
      }
      this.$set(this.resultForm, "signFileId", `${response.data.fileId}`);
      this.files.length = 0;
      this.files.push(file);
      this.$refs.resultForm.validateField("signFileId");
    },
  },
};
</script>
<style lang="scss" scoped>
.hospital_detail_message {
  padding: 8px;

  ::v-deep .services.el-card {
    .el-card__header {
      padding-bottom: 15px;
    }
  }

  ::v-deep .el-col-12 {
    min-height: 60px;
  }
}
</style>
<style lang="scss">
//   .signFileId label.el-form-item__label{
//     line-height:18px;
//   }
// </style>
