# 认证自评审核查询功能迁移计划

## 1. 迁移文件清单

### Controller层
- `AutSaAudController.java` - 主控制器

### Service层
- `BaseProcessService.java` - 基础流程服务接口
- `BaseProcessServiceImpl.java` - 基础流程服务实现
- `CommonProcessServiceImpl.java` - 通用流程服务实现
- `ValidationReviewer01ProcessServiceImpl.java` - 验证评审员流程服务
- `IAutSaAudService.java` - 认证自评审核服务接口
- `AutSaAudServiceImpl.java` - 认证自评审核服务实现
- `IAutSaRelationService.java` - 认证自评关联服务接口
- `AutSaRelationServiceImpl.java` - 认证自评关联服务实现

### Domain/DTO层
- `AutSaAudQueryDTO.java` - 查询请求DTO
- `AutSaAudList.java` - 列表响应对象
- `AutSaAud.java` - 认证自评审核实体
- `AutSaRelation.java` - 认证自评关联实体
- `AutSaAudReport.java` - 评审报告实体
- `GroupProgressInfo.java` - 小组进度信息
- `AutSaAudVo.java` - 认证自评审核VO
- `FileInfoVO.java` - 文件信息VO
- `HospitalReviewerVo.java` - 医院评审员VO

### Mapper层
- `AutSaAudMapper.java` - 认证自评审核Mapper
- `AutSaRelationMapper.java` - 认证自评关联Mapper
- `CommonProcessMapper.java` - 通用流程Mapper
- `AutSaAudMapper.xml` - 认证自评审核SQL映射
- `AutSaRelationMapper.xml` - 认证自评关联SQL映射
- `CommonProcessMapper.xml` - 通用流程SQL映射

### 枚举类
- `AutSaAudRoleEnum.java` - 认证自评审核角色枚举
- `AutSaAudStatusEnum.java` - 认证自评审核状态枚举
- `AutSaAudResultEnum.java` - 认证自评审核结果枚举

## 2. 数据库表迁移

### 核心业务表
```sql
-- 认证自评关联表
CREATE TABLE aut_sa_relation (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    aut_code varchar(64) NOT NULL COMMENT '自评编码',
    aut_cs_id varchar(64) NOT NULL COMMENT '自评认证标准id',
    hospital_apply_no varchar(64) NOT NULL COMMENT '医院编号',
    aut_status varchar(64) NOT NULL COMMENT '认证状态',
    status tinyint NOT NULL DEFAULT 1 COMMENT '是否有效',
    create_id int(11) NOT NULL DEFAULT 0,
    create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_id int(11) NOT NULL DEFAULT 0,
    update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    evaluate_flag tinyint DEFAULT 0 COMMENT '评估标识',
    PRIMARY KEY (id),
    UNIQUE KEY uk_aut_code (aut_code),
    KEY idx_hospital_apply_no (hospital_apply_no)
);

-- 认证自评审核表
CREATE TABLE aut_sa_aud (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    aut_code varchar(64) NOT NULL COMMENT '自评编码',
    submit_type varchar(64) NOT NULL COMMENT '提交类型',
    clause_id varchar(64) COMMENT '条款id',
    account_id varchar(64) NOT NULL COMMENT '账户id',
    aut_result varchar(64) NOT NULL COMMENT '自评打分/审核结果',
    aut_desc BLOB COMMENT '自评描述/审核描述',
    file_ids varchar(1024) COMMENT '材料文件Id列表',
    submit_date datetime COMMENT '提交时间',
    status tinyint NOT NULL DEFAULT 1 COMMENT '审核状态',
    risk_impact int COMMENT '风险影响',
    risk_possibility int COMMENT '风险可能性',
    reviewer_respond text COMMENT '验证评审员回应',
    is_share_update tinyint DEFAULT 0 COMMENT '共享评审是否需修改',
    proposer_ids varchar(255) COMMENT '共享需修改提出人Id',
    share_desc text COMMENT '共享修改描述',
    before_aut_desc BLOB COMMENT '修改前的自评描述',
    before_aut_result varchar(64) COMMENT '修改前的评审结果',
    create_id int(11) NOT NULL DEFAULT 0,
    create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_id int(11) NOT NULL DEFAULT 0,
    update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_aut_code (aut_code),
    KEY idx_account_id (account_id),
    KEY idx_clause_id (clause_id)
);
```

### 配置表
```sql
-- 状态配置表
CREATE TABLE asa_status_config (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    current_status varchar(64) NOT NULL COMMENT '当前状态',
    status_desc varchar(255) COMMENT '状态描述',
    next_status_config text COMMENT '下一状态配置',
    service_name varchar(255) COMMENT '服务名称',
    process_method varchar(255) COMMENT '处理方法',
    check_method varchar(255) COMMENT '校验方法',
    submit_type varchar(64) COMMENT '提交类型',
    cycle_stage varchar(64) COMMENT '周期阶段',
    PRIMARY KEY (id),
    UNIQUE KEY uk_current_status (current_status)
);

-- 业务配置表
CREATE TABLE aut_sa_aud_business_config (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    item_type varchar(64) NOT NULL COMMENT '配置项类型',
    item_key varchar(64) NOT NULL COMMENT '配置项键',
    item_arr1 text COMMENT '配置项数组1',
    item_arr2 text COMMENT '配置项数组2',
    item_desc varchar(255) COMMENT '配置项描述',
    PRIMARY KEY (id),
    KEY idx_item_type_key (item_type, item_key)
);
```

## 3. 迁移步骤

### 步骤1：环境准备
1. 确认二期项目的包结构和命名规范
2. 确认数据库连接和表结构
3. 确认依赖的基础框架是否已迁移

### 步骤2：代码迁移
1. 按照依赖关系顺序迁移：枚举类 → Domain → Mapper → Service → Controller
2. 调整包名和导入路径
3. 检查和调整配置文件

### 步骤3：数据库迁移
1. 执行DDL脚本创建表结构
2. 迁移基础配置数据
3. 验证表结构和索引

### 步骤4：测试验证
1. 单元测试
2. 集成测试
3. 接口测试

## 4. 注意事项

### 依赖检查
- 确认BaseController、TableDataInfo等基础类已迁移
- 确认SecurityUtils、SpringContextUtil等工具类已迁移
- 确认SysUser、SysUserService等用户相关服务已迁移

### 配置调整
- 调整application.yml中的数据库配置
- 调整MyBatis的mapper扫描路径
- 调整Spring的组件扫描路径

### 数据一致性
- 确认用户表结构一致
- 确认医院信息表结构一致
- 确认文件服务接口一致

## 5. 风险评估

### 高风险项
- 复杂的业务逻辑依赖多个服务
- 状态机流程配置复杂
- 权限校验逻辑复杂

### 缓解措施
- 分阶段迁移，先迁移查询功能
- 充分的单元测试和集成测试
- 保留原系统作为对比验证
