<template>
  <div v-loading="loading" class="reviewer-audit">
    <el-row :gutter="24">
      <el-col :span="24" :xs="24">
        <el-form ref="elForm" :model="queryParams" size="small" label-width="80px" inline>
          <el-form-item label="姓名" prop="reviewerName">
            <el-input v-model.trim="queryParams.reviewerName" placeholder="请输入姓名" clearable :style="{ width: '100%' }" :maxlength="20">
            </el-input>
          </el-form-item>

          <el-form-item label="角色" prop="roleName">
            <el-select v-model.trim="queryParams.roleName" clearable filterable>
              <el-option label="评审学员" value="评审学员"></el-option>
              <el-option label="评审员" value="评审员"></el-option>
              <el-option label="验证评审员" value="验证评审员"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="工作单位" prop="company">
            <el-input v-model.trim="queryParams.company" placeholder="请输入工作单位" clearable :style="{ width: '100%' }" :maxlength="20">
            </el-input>
          </el-form-item>

          <el-form-item label="现任职务" prop="companyPost">
            <el-input v-model.trim="queryParams.companyPost" placeholder="请输入现任职务" clearable :style="{ width: '100%' }" :maxlength="20">
            </el-input>
          </el-form-item>

          <el-form-item label="审核状态" prop="authStatus">
            <el-select v-model.trim="queryParams.authStatus" clearable filterable>
              <el-option v-for="(option, index) in authStatusOptions" :key="index" :value="option.dictValue" :label="option.dictLabel"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label=" ">
            <el-button type="primary" size="small" @click="query(1)">
              查询
            </el-button>
            <el-button type="primary" size="small" @click="resetForm">
              重 置
            </el-button>
            <el-button type="primary" size="small" @click="exportExcl">
              导 出
            </el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

    <el-table :data="dataSource" border @select="handleSelectionChange" @select-all="handleSelectionChange" height="630">
      <el-table-column align="center" type="selection" :selectable="selectable"> </el-table-column>
      <el-table-column align="center" label="操作" width="130">
        <template slot-scope="scope">
          <el-button size="small" type="text" v-if="scope.row.roleId == 107 && !isReject(scope.row)" @click="toTheoretical(scope.row)">理论培训评估结果</el-button>
          <el-button size="small" type="text" v-if="scope.row.roleId == 107 && !isReject(scope.row)" @click="toPractice(scope.row)">带教培训评估结果</el-button>
          <el-button size="small" type="text" v-if="(scope.row.roleId == 101 || scope.row.roleId == 106) && scope.row.authStatus == 1" @click="roleTurn(scope.row)">{{ scope.row.roleId == 106 ? '转为评审员': '转为验证评审员'}}</el-button>
          <el-button size="small" type="text" v-if="!scope.row.authStatus && scope.row.infoStatus != 1" @click="toAudit(scope.row, 1)" style="display:block;width:100%">审核</el-button>
          <el-button size="small" type="text" v-else @click="toDetail(scope.row)" style="display:block;width:100%">查看详情</el-button>
          <el-button size="small" type="text" v-if="showGroup(scope.row)" @click="allotField(scope.row)" style="display:block;width:100%">分配分组</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="姓名" prop="reviewerName" min-width="100">
        <template slot-scope="{row}">
          {{row.reviewerName}}{{row.firstBatch == 1? '(首批评审员)': ''}}
        </template>
      </el-table-column>
      <el-table-column align="center" label="角色" prop="roleName">
        <template slot-scope="{row}">
          {{row.roleName || '--'}}
        </template>
      </el-table-column>
      <el-table-column align="center" label="工作单位" prop="company"> </el-table-column>
      <el-table-column align="center" label="现任职务" width="130" prop="companyPost">
      </el-table-column>
      <el-table-column align="center" label="现居住地" prop="liveAddress"> </el-table-column>
      <el-table-column align="center" label="专业领域">
        <template slot-scope="scope">
          <div v-if="scope.row.majorField">
            {{majorFields(scope.row.majorField)}}
            <!-- <span v-for="(info, index) in " :key="index" style="margin-right: 10px">{{ showName(info) }}</span> -->
          </div>
          <span v-else> -- </span>
        </template>
      </el-table-column>
      <el-table-column label="所属分组" align="center">
        <template slot-scope="scope">
          <div v-if="
              scope.row.roleId == '101' &&
              scope.row.reviewerFieldInfoVOList &&
              scope.row.reviewerFieldInfoVOList.length
            ">
            <div v-for="(info, index) in scope.row.reviewerFieldInfoVOList" :key="showFieldId(info,index)" style="margin-right: 10px">{{ showFieldName(info) }}</div>
          </div>
          <span v-else> -- </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="审核人" prop="authPersonName" width="130">
      </el-table-column>
      <el-table-column align="center" label="审核状态" prop="authStatus" width="100">
        <template slot-scope="scope">
          <span v-if="!scope.row.authStatus">
            <span v-if="!scope.row.infoStatus">待审核基本信息</span>
            <span v-if="scope.row.infoStatus == 1 && scope.row.submitStatus != 1"><span style="color: green">基本信息审核通过</span>
              <span v-if="!scope.row.userName">，待管理员分配账号</span>
              <!-- <span v-else>，待用户提交审核</span> -->
            </span>
            <span v-if="scope.row.infoStatus == 1 && scope.row.submitStatus == 1">
              <span v-if="scope.row.evaluateResultList == null">待评估</span>
              <span v-else>
                {{evaluateRes(scope.row.evaluateResultList)}}
              </span>

            </span>
            <span v-if="scope.row.infoStatus == 2" style="color: red">审核拒绝</span>
          </span>
          <span v-if="scope.row.authStatus == 1" style="color: green">审核通过</span>
          <span v-if="scope.row.authStatus == 2" style="color: red">审核拒绝</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="query" />

    <el-dialog title="分配分组" :visible.sync="fieldDialogVisible" width="60%">
      <div>
        <p>请勾选需要分配给该评审员的分组：</p>
        <el-checkbox-group v-model.trim="checkList">
          <el-checkbox v-for="(item, index) in fieldCodeOptions" :key="index" :label="item.id">{{ item.groupName }}</el-checkbox>
        </el-checkbox-group>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="fieldDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitField">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import request from "@/utils/request";
import { saveAs } from 'file-saver'
import { Loading } from 'element-ui'
let that;
export default {
  name: "ReviewerAudit",
  components: {},
  props: [],
  data() {
    return {
      queryParams: {
        reviewerName: undefined,
        company: undefined,
        companyPost: undefined,
        authStatus: undefined,
        roleName: undefined,
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      url: {
        list: "/reviewer/query/reviewer/list",
      },
      dataSource: [],
      authStatusOptions: [],
      allReady: false,
      fieldDialogVisible: false,
      checkList: [],
      fieldCodeOptions: [],
      currentRow: {},
      loading: false,
      majorSupplementOptions: [],
      selectList: [],

      theText: {
        0: '其他',
        11: '可参与现场评审带教培训',
        12: '需再培训后参与现场评审带教培训',
      },
      praText: {
        0: '其他',
        22: '需再带教培训',
        23: '不适合当评审员',
      },
    };
  },
  computed: {
    evaluateRes() {
      return list => {
        if (!Array.isArray(list)) return '待评估'
        let pra = list.find(({ reviewResultType }) => reviewResultType == 2);
        let the = list.find(({ reviewResultType }) => reviewResultType == 1);
        if (pra) {
          if (pra.conclusion == 20 || pra.conclusion == 21) return '审核通过'
          if (pra.conclusion == 0) return '其他：' + pra.otherRemark
          return this.praText[pra.conclusion]
        } else if (the) {
          if (the.conclusion == 10) return '审核通过'
          if (the.conclusion == 0) return '其他：' + the.otherRemark
          return this.theText[the.conclusion]
        }
      }
    },
    showName() {
      return (fieldId) => {
        return (this.majorSupplementOptions.find(({ dictValue }) => dictValue == fieldId) || {}).dictLabel || "-"
      }
    },
    showId() {
      return (info, index) => {
        return info ? info.id : index * Math.ceil(Math.random() * 100)
      }
    },
    majorFields() {
      return field => {
        let fields = (field || '').split(',')
        let names = fields.sort().filter(fieldId => fieldId != 5).map(fieldId => this.showName(fieldId))
        return names.join(',') + `${fields.includes('5') ? '等' : ''}`
      }
    },
    isReject() {
      return row => {
        return row.authStatus == 2 || row.infoStatus == 2
      }
    },
    showFieldName() {
      return (info) => {
        return info ? info.fieldName : ''
      }
    },
    showFieldId() {
      return (info, index) => {
        return info ? info.id : index * Math.ceil(Math.random() * 100)
      }
    },

    showGroup() {
      return row => {
        return row.authStatus == 1 && row.roleId == '101'
      }
    }
  },
  watch: {
    $route: (to) => {
      if (!that || !that.allReady) return;
      if (to.path != "/auditManagement/reviewerAudit") return;
      that.query();
      that.getDomainList();
    },
  },
  created() { },

  mounted() {
    this.initDicts();
    this.query();
    this.getDomainList();
    that = this;
    this.allReady = true;


    this.getDicts("reviewer_major").then((response) => {
      this.majorSupplementOptions = response.data;
    });
  },
  methods: {
    selectable(row) {
      return row.roleId == "101" || row.roleName == "评审员"
    },

    handleSelectionChange(val, row) {
      if (!row) {
        this.selectList = (val || []).map(row => row.accountId);
      } else {
        let accountId = row.accountId
        if ((val || []).length == 0) {
          if (this.selectList.includes(accountId)) {
            this.selectList.splice(this.selectList.indexOf(accountId), 1)
          }
        } else {
          if (this.selectList.includes(accountId) && !val.map(({ accountId }) => accountId).includes(accountId)) {
            this.selectList.splice(this.selectList.indexOf(accountId), 1)
          } else if (!this.selectList.includes(accountId)) {
            this.selectList.push(accountId)
          }
        }
      }
    },
    initDicts() {
      this.getDicts("reviewer_auth_status").then((res) => {
        this.authStatusOptions = res.data;
      });
    },

    getDomainList() {
      request({
        url: "/system/domain/list?state=1",
        method: "get",
      }).then((res) => {
        res.code == 200 && (this.fieldCodeOptions = res.rows);
      });
    },

    submitForm() {
      this.$refs["elForm"].validate((valid) => {
        if (!valid) return;
        // TODO 提交表单
      });
    },
    resetForm() {
      this.$refs["elForm"].resetFields();
      this.queryParams.pageNum = 1
      this.query()
    },

    query(arg) {
      this.selectList = []
      if (arg && !isNaN(arg)) {
        this.queryParams.pageNum = arg;
        if (!this.queryParams.roleName) {
          this.queryParams.roleName = undefined
        }
      }
      this.loading = true
      request({
        url:
          this.url.list +
          `?pageNum=${this.queryParams.pageNum}&pageSize=${this.queryParams.pageSize}`,
        method: "post",
        data: this.queryParams,
      }).then((res) => {
        this.loading = false
        if (res.code == 200) {
          let fields = [
            "reviewerName",
            "company",
            "companyPost",
            "authPersonName",
          ];
          this.dataSource = res.rows.map((row) => {
            for (const key in row) {
              if (Object.hasOwnProperty.call(row, key)) {
                if (!row[key] && row[key] != 0 && fields.includes(key)) {
                  row[key] = "--";
                }
              }
            }
            return row;
          });
          this.total = res.total;
        }
      }).catch((error) => {
        // console.log(error)
        this.loading = false
      })
    },


    roleTurn(row) {
      let { roleId, accountId, userName } = row
      request({
        url: '/reviewer/update-reviewer-role',
        method: 'post',
        data: {
          roleId,
          userName,
          reviewerId: accountId
        }
      }).then(() => {
        this.query();
      })
    },

    toAudit(data, type) {
      let query = {
        accountId: data.accountId,
        userName: data.userName || undefined,
        type,
      }
      this.$router.push({
        path: "reviewerInfoReview",
        query,
      });
    },

    toDetail(data, type) {
      let query = {
        accountId: data.accountId,
        userName: data.userName || undefined,
        type,
        genre: 1
      }
      this.$router.push({
        path: "reviewerDetailMessage",
        query,
      });
    },

    allotField(row) {
      this.currentRow = row;
      if (!row.reviewerFieldInfoVOList || !Array.isArray(row.reviewerFieldInfoVOList)) {
        this.$set(row, 'reviewerFieldInfoVOList', [])
      }
      let list = row.reviewerFieldInfoVOList.filter(field => field)
      this.$set(
        this,
        "checkList",
        list.map((field) => field.id)
      );
      this.fieldDialogVisible = true;
    },

    submitField() {
      if (this.checkList.length == 0) {
        this.$message({
          type: "warning",
          message: "请选择分组!",
        });
        return;
      }
      let fields = this.checkList.filter(id => this.fieldCodeOptions.some(field => field.id == id))
      const data = {
        fields,
        reviewerBaseInfo: {
          accountId: this.currentRow.accountId,
        },
      };
      request({
        url: "/reviewer/base/info/submit",
        method: "post",
        data,
      })
        .then((res) => {
          if (res.code == 200) {
            this.query();
            this.getDomainList();
            this.fieldDialogVisible = false;
          }
        })
        .catch((error) => {
          // console.log(error);
        });
    },

    toTheoretical(row) {
      this.$router.push({
        path: "theoreticalEvaluationSheet/" + row.accountId + '?roleName=' + row.roleName,
      });
    },

    toPractice(row) {
      this.$router.push({
        path: "practiceEvaluationSheet/" + row.accountId + '?roleName=' + row.roleName,
      });
    },

    exportExcl() {
      if (this.selectList.length == 0) {
        return this.$message({
          type: 'warning',
          message: '请选择要导出的数据'
        })
      }
      let loading = Loading.service({ text: "正在下载数据，请稍候", spinner: "el-icon-loading", background: "rgba(0, 0, 0, 0.7)", })
      request({
        url: '/reviewer/list/export',
        method: 'post',
        data: {
          accountIdList: this.selectList,
          fileName: '评审员信息.xls',
          sheetName: "评审员信息"
        },
        responseType: "blob",
      }).then(res => {
        const blob = new Blob([res], { type: 'application/vnd.ms-excel' });
        saveAs(blob, '评审员信息.xlsx');
      }).finally(() => {
        loading.close();
      })
    },
  },
};
</script>

<style lang="scss" scoped>
.reviewer-audit {
  padding: 24px 10px;
  background-color: #fff;

  ::v-deep .el-table {
    .el-button + .el-button {
      margin-left: 0;
    }
  }
}
</style>

