<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.CstEvaluationCriterionMapper">

    <resultMap type="CstEvaluationCriterion" id="CstEvaluationCriterionResult">
        <result property="id"    column="id"    />
        <result property="certificationStandardsId"    column="certification_standards_id"    />
        <result property="evaluate"    column="evaluate"    />
        <result property="standard"    column="standard"    />
        <result property="status"    column="status"    />
        <result property="createId"    column="create_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateId"    column="update_id"    />
        <result property="updateTime"    column="update_time"    />
        <result property="versionId"    column="version_id"    />
    </resultMap>

    <sql id="selectCstEvaluationCriterionVo">
        select id, certification_standards_id, evaluate, standard, status, create_id, create_time, update_id, update_time, version_id from cst_evaluation_criterion
    </sql>

    <select id="selectCstEvaluationCriterionList" parameterType="CstEvaluationCriterion" resultMap="CstEvaluationCriterionResult">
        <include refid="selectCstEvaluationCriterionVo"/>
        <where>
            <if test="certificationStandardsId != null "> and certification_standards_id = #{certificationStandardsId}</if>
            <if test="evaluate != null  and evaluate != ''"> and evaluate = #{evaluate}</if>
            <if test="standard != null  and standard != ''"> and standard = #{standard}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createId != null "> and create_id = #{createId}</if>
            <if test="updateId != null "> and update_id = #{updateId}</if>
            <if test="versionId != null "> and version_id = #{versionId}</if>
        </where>
    </select>

    <select id="selectCstEvaluationCriterionById" parameterType="Long" resultMap="CstEvaluationCriterionResult">
        <include refid="selectCstEvaluationCriterionVo"/>
        where id = #{id}
    </select>

    <insert id="insertCstEvaluationCriterion" parameterType="CstEvaluationCriterion" useGeneratedKeys="true" keyProperty="id">
        insert into cst_evaluation_criterion
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="certificationStandardsId != null">certification_standards_id,</if>
            <if test="evaluate != null and evaluate != ''">evaluate,</if>
            <if test="standard != null and standard != ''">standard,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="versionId != null">version_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="certificationStandardsId != null">#{certificationStandardsId},</if>
            <if test="evaluate != null and evaluate != ''">#{evaluate},</if>
            <if test="standard != null and standard != ''">#{standard},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="versionId != null">#{versionId},</if>
         </trim>
    </insert>

    <update id="updateCstEvaluationCriterion" parameterType="CstEvaluationCriterion">
        update cst_evaluation_criterion
        <trim prefix="SET" suffixOverrides=",">
            <if test="certificationStandardsId != null">certification_standards_id = #{certificationStandardsId},</if>
            <if test="evaluate != null and evaluate != ''">evaluate = #{evaluate},</if>
            <if test="standard != null and standard != ''">standard = #{standard},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="versionId != null">version_id = #{versionId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCstEvaluationCriterionById" parameterType="Long">
        delete from cst_evaluation_criterion where id = #{id}
    </delete>

    <delete id="deleteCstEvaluationCriterionByIds" parameterType="String">
        delete from cst_evaluation_criterion where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertListByVersionIdAndClauseId" useGeneratedKeys="true" keyProperty="id">
        insert into cst_evaluation_criterion(certification_standards_id,evaluate,standard,version_id) values
            <foreach collection="list" item="item" separator=",">
                (#{item.certificationStandardsId},#{item.evaluate},#{item.standard},#{item.versionId})
            </foreach>
    </insert>

    <delete id="deleteByVersionIdAndClauseId" >
        delete from cst_evaluation_criterion where certification_standards_id=#{clauseId} and version_id = #{versionId}
    </delete>

    <select id="selectByVersionIdAndClauseId" parameterType="CstEvaluationCriterion" resultMap="CstEvaluationCriterionResult">
        <include refid="selectCstEvaluationCriterionVo"/>
        where version_id = #{versionId}
        and certification_standards_id in
        <foreach collection="clauseIds.split(',')" index="index" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </select>
</mapper>
