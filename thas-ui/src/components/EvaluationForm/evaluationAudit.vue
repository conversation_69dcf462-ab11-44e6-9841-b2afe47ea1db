<!--  -->
<template>
  <div class='evaluation-audit evaluation' v-loading="submitLoading"  >
    <el-button v-if="showForm && !readonly" class="objection" size="small" type="text" @click="showForm = false">取消审核</el-button>
    <el-form ref="evaluationForm" :model="formData" :label-width="labelWidth" :rules="rules">
      <el-form-item v-if="!showForm && !readonly" class="form-item" label=" " style="margin:0;">
        <el-button type="danger" size="mini" @click="showFormChange" style="margin:auto;">有异议</el-button>
      </el-form-item>
      <el-form-item v-if="showForm || readonly" class="form-item readonly" label="审查结果：" prop="autResult">
        <dict-span :noTag='true' :key="checkSummaryOptions.length+ Number(formData.autResult)" :value="formData.autResult" :options="agreeOrNotOptions" type="default"></dict-span>
      </el-form-item>
      <el-form-item v-if="showForm || readonly" class="form-item" label="审查描述：" prop="autDesc">
        <el-input v-if="modifying && !readonly" type="textarea" v-model="formData.autDesc" placeholder="请填写审查描述" :rows="4" :maxlength="descMaxLength"></el-input>
        <span v-else>{{ formData.autDesc || '--' }}</span>
      </el-form-item>
      <el-form-item class="form-item" label=" " v-if="showForm">
        <el-button v-if="modifying" type="primary" @click="submit">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>



<script>
import mixin from "./mixin";

const defaultForm = {
  autResult: '',
  autDesc: ''
}
export default {
  name: 'EvaluationAudit',
  components: {},
  mixins: [mixin],
  props: {},
  watch: {
    showForm() {
      if (!this.showForm) {
        this.reply();
      }
    }
  },
  data() {
    return {
      showForm: false
    };
  },

  computed: {
  },

  created() {
    this.autResultMessage = '请选择审核结果'
    this.autDescMessage = '请填写审核描述'
  },

  mounted() {
    if (!this.aut || this.aut.rejected) {
      this.modifying = true;
    }

    if (this.aut && this.aut.rejected) {
      this.formData.autResult = undefined;
      this.$refs.evaluationForm.clearValidate();
    }
    this.formData.autResult = 2
  },

  methods: {
    submit() {
      const evaluationForm = this.$refs.evaluationForm;
      if (evaluationForm && evaluationForm.validate) {
        evaluationForm.validate(valid => {
          if (!valid) return;
          this.submitEmit()
        })
      }
    },
    showFormChange() {
      this.showForm = true;
      this.bus.$emit('heightChange')
    },

    clearDesc() {
      this.formData.autDesc = '';
    }
  }
}

</script>
<style lang='scss'>
</style>
