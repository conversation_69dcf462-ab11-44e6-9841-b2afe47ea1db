<template>
  <el-card v-loading="loading" class="approvalStandards-version">
    <div class="title">
      <h2>《国际医院评审认证标准（中国）》（2021版）</h2>
      <el-divider></el-divider>
    </div>
    <div class="button-action">
      <el-button type="primary" icon="el-icon-plus" size="small" @click="showImportDialog" v-hasPermi="['approvalStandards:import']">导入</el-button>
      <el-button size="small" type="info" icon="el-icon-refresh" style="background-color: #666666" @click="query">刷新</el-button>
    </div>
    <div class="table">
      <el-table :data="tableData" border style="width: 100%" :key="key">
        <el-table-column label="操作" align="center" width="165">
          <template slot-scope="scope">
            <el-button type="text" @click="toDetail(scope.row.versionId, scope.row.versionName)">查看详情</el-button>
            <el-button type="text" v-hasPermi="['approvalStandards:delete']" v-if="scope.row.status != 1" @click="
                isDelete = false;
                enableDialogVisible = true;
                current = scope.row;
              ">启用</el-button>
            <el-button type="text" v-if="scope.row.status != 1" v-hasPermi="['approvalStandards:delete']" @click="
                isDelete = true;
                enableDialogVisible = true;
                current = scope.row;
              ">删除</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="versionName" label="版本名称" align="center">
        </el-table-column>
        <el-table-column prop="status" label="状态" align="center" width="80">
          <template slot-scope="scope">
            <dict-span dictType="version_status" :value="scope.row.status"></dict-span>
          </template>
        </el-table-column>
        <el-table-column prop="createName" label="导入人" align="center"></el-table-column>
        <el-table-column prop="createTime" label="导入时间" align="center">
        </el-table-column>
        <el-table-column prop="updateTime" label="最后修改时间" align="center">
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query" />
    </div>

    <el-dialog :title="title" :visible.sync="enableDialogVisible" width="40%" :close-on-click-modal="false" destroy-on-close>
      <span v-if="!isDelete">您正在启用认证标准
        <a style="cursor: auto; color: #347af4">{{ current.name }}</a>
        ，启用后，审查人员、评审人员将使用该版本作为评审标准参考</span>
      <span v-else>
        您正在删除认证标准
        <a style="cursor: auto; color: #red">{{ current.name }}</a>
        ，删除后，所有相关的在途评审单将会关闭
      </span>
      <el-form class="enabledForm" ref="enabledForm" :model="formData" label-width="95px" style="margin-top: 24px">
        <el-form-item label="验证密码：" prop="password" :rules="passwordRule">
          <el-input type="password" v-model.trim="formData.password" placeholder="请输入当前管理员账号的密码" style="width: 100%"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="enableDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleClose('enableDialogVisible')">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="导入医院质量国际认证标准" :visible.sync="importDialogVisible" width="40%" :close-on-click-modal="false" :before-close='versionFormClose'>
      <el-form class="enabledForm" ref="importForm" :model="importForm" label-width="95px">
        <el-form-item label="版本名称：" prop="title" :rules="importRules.title">
          <el-input type="text" :maxlength="50" v-model.trim="importForm.title" placeholder="请输入版本名称" style="width: 80%"></el-input>
        </el-form-item>
        <el-form-item label="上传文件：" prop="fileId" :rules="importRules.fileId">
          <upload ref="uploadVersion" :action="``" :files="files" :accept="`.xlsx,.xls,.xlsm`" :headSupple="`请上传Excel文件`" :fileSize="0" :tip="false" :data="{ type: '1' }" single @fileAdd="uploadSuccess" @fileRemove="fileRemove"></upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleClose('importDialogVisible')">确 定</el-button>
      </span>
    </el-dialog>
  </el-card>
</template>

<script>
import request from "@/utils/request";
import DictSpan from "@/components/DetailMessage/dictSpan.vue";

export default {
  name: "approvalStandardsVersion",
  components: { DictSpan },
  data() {
    return {
      url: {
        list: "/system/versioning/list",
      },
      tableData: [],
      enableDialogVisible: false,
      importDialogVisible: false,
      isDelete: false,
      current: {
        name: "",
        enabled: true,
      },
      formData: {
        password: "",
      },
      total: 0,
      queryData: {
        pageSize: 10,
        pageNum: 1,
      },
      importForm: {
        title: "",
        fileId: "",
        fileType: "cst_certification_standards",
        file: null,
      },
      key: "",
      passwordRule: {
        required: true,
        message: "请输入当前管理员密码",
        trigger: ["change", "blur"],
      },
      importRules: {
        title: {
          required: true,
          message: "请输入版本名称",
          trigger: ["change", "blur"],
        },
        fileId: {
          required: true,
          validator: (rule, value, callback) =>
            this.importFileValidate(callback),
        },
      },
      files: [],
      loading: false,
    };
  },
  computed: {
    enabled() {
      return (enabled) => (enabled ? "禁用" : "启用");
    },
    title() {
      return this.current && this.current.enabled != undefined
        ? `${this.enabled(this.current.enabled)}版本`
        : "提示";
    },
  },
  methods: {
    query() {
      this.loading = true;
      let queryData = this.queryData
      request({
        url:
          this.url.list +
          `?pageNum=${queryData.pageNum}&pageSize=${queryData.pageSize}`,
        method: "get",
      })
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.tableData = res.rows;
            this.total = res.total
            this.key = new Date().getTime();
          }
        })
        .catch((error) => {
          // console.log(error);
          this.loading = false;
        });
    },
    importFileValidate(callback) {
      if (this.files.length === 0) callback("请上传Excel文件");
      callback();
    },

    toDetail(versionId, versionName) {
      this.$router.push({
        name: "ApprovalStandardDetail",
        params: {
          versionId,
          versionName,
        },
      });
    },
    versionFormClose(done) {
      this.resetForm("importForm");
      this.$set(this, 'files', [])
      done();
    },
    handleClose(dialogVisible) {
      if (dialogVisible === "enableDialogVisible") {
        this.$refs["enabledForm"].validate((valid) => {
          if (valid) {
            const sm3 = require('sm-crypto').sm3
            const sm3Password = sm3(this.formData.password)
            this[dialogVisible] = false;
            if (this.isDelete) {
              request({
                url: "/system/versioning/deleteAndVerifyPwdById",
                method: "post",
                data: {
                  id: this.current.id,
                  password: sm3Password,
                  delFlag: "1",
                },
              })
                .then((res) => {
                  if (res.code == 200)
                    this.$message({
                      type: "success",
                      message: res.msg,
                    });
                  this.query();
                })
                .finally(() => {
                  this.formData.password = "";
                });
              return;
            }
            request({
              url: "/system/versioning/updateStatusStartById",
              method: "put",
              data: {
                id: this.current.id,
                password: sm3Password,
                status: "1",
                versionId: this.current.versionId + "",
              },
            })
              .then((res) => {
                if (res.code == 200)
                  this.$message({
                    type: "success",
                    message: res.msg,
                  });
                this.resetForm("enabledForm");
                this.query();
              })
              .finally(() => {
                this.formData.password = "";
              });
          }
        });
      } else if (dialogVisible === "importDialogVisible") {
        this.$refs["importForm"].validate((valid) => {
          if (valid) {
            this.$message('医院质量国际认证标准上传中，请稍后！')
            this[dialogVisible] = false;
            const formData = new FormData();
            formData.append("file", this.importForm.file);
            formData.append("fileType", this.importForm.fileType);
            formData.append("title", this.importForm.title);

            request({
              url: "/common/import/excel",
              method: "post",
              data: formData,
            })
              .then((res) => {
                if (res.code == 200) {
                  this.importForm.title = "";
                  this.$set(this, "files", []);
                  this.resetForm("importForm");
                  this.query();
                  this.$message({
                    type:'success',
                    message: '医院质量国际认证标准上传成功！'
                  })
                }
              })
              .catch((error) => {
                // console.log(error);
              });
          }
        });
      }
    },

    showImportDialog() {
      this.importDialogVisible = true;
    },

    fileRemove(file, length, index) {
      this.files.length = 0;
      this.$refs.importForm.validateField("fileId");
    },

    uploadSuccess(file) {
      this.files = [file];
      this.importForm.file = file;
      this.$refs.importForm.validateField("fileId");
      this.$refs.uploadVersion.loading = false
    },
  },

  mounted() {
    this.query();
  },
};
</script>

<style lang="scss" scoped>
.approvalStandards-version {
  // padding: 18px;
  h2 {
    margin-top: 0;
    font-weight: bold;
  }
  ::v-deep .el-divider {
    margin-top: 0;
    margin-bottom: 18px;
  }
  .title {
    margin-bottom: 0;
    text-align: left;
  }
  .button-action {
    margin-top: 24px;
    margin-bottom: 24px;
  }

  ::v-deep .el-dialog {
    &:not(.is-fullscreen) {
      margin-top: 15vh !important;
    }
    .el-dialog__header {
      background-color: #3c81f5;
      padding-top: 10px;
      .el-dialog__title {
        color: white;
      }
      .el-dialog__headerbtn {
        top: 15px;

        .el-dialog__close {
          color: white;
        }
      }
    }
    .el-dialog__body {
      padding-bottom: 0;
      & > span {
        color: black;
      }
    }
  }
  ::v-deep .enabledForm.el-form {
    .el-form-item__label {
      padding-right: 6px;
    }
  }
}
</style>
