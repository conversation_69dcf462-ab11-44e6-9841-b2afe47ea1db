<template>
  <el-card v-loading="loading" class="offline-training">
    <el-row :gutter="24">
      <el-col :span="24" :xs="24">
        <el-form ref="queryForm" :model="queryData" size="small" label-width="80px" inline>
          <el-form-item label="课程名称" prop="subjectName">
            <el-input v-model.trim="queryData.subjectName" maxlength="128" placeholder="请输入课程名称" clearable :style="{ width: '100%' }"></el-input>
          </el-form-item>
          <el-form-item label="课程代码" prop="subjectCode">
            <el-input v-model.trim="queryData.subjectCode" maxlength="128" placeholder="请输入课程代码" clearable :style="{ width: '100%' }"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="search">
              查询
            </el-button>
            <el-button type="primary" size="small" @click="resetForm">
              重 置
            </el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-row :gutter="24" style="margin-top: 24px">
      <el-col :span="24">
        <el-table :data="dataSource" border stripe>
          <el-table-column align="center" label="课程名称" prop="subjectName"></el-table-column>
          <el-table-column align="center" label="课程代码" prop="subjectCode"></el-table-column>
          <el-table-column align="center" label="课程形式" prop="subjectMode">
            <template slot-scope="scope">
              <dict-span :noTag='true' :key="scope.row.subjectMode" :value="scope.row.subjectMode" :options="statusOptions" type="default"></dict-span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="课程时间" prop="subjectTime" width="300"></el-table-column>
          <el-table-column align="center" label="现场评审带教时间" prop="sceneReviewTime" width="300"></el-table-column>
          <el-table-column align="center" label="带教评审员" prop="reviewerNames">
            <template slot-scope="{row}">
              {{ showNames(row) || '--'}}
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query" />
      </el-col>
    </el-row>
    <el-dialog title="培训详情" :visible.sync="dialogVisible" width="60%" :close-on-click-modal="false">
      <div class="detail-item">
        <div class="item-name">培训名称：</div>
        <div class="item-value">{{ current.trainingName }}</div>
      </div>
      <div class="detail-item">
        <div class="item-name">培训类型：</div>
        <div class="item-value">{{ current.trainingType }}</div>
      </div>
      <div class="detail-item">
        <div class="item-name">举办时间：</div>
        <div class="item-value">{{ current.trainingTime }}</div>
      </div>
      <div class="detail-item">
        <div class="item-name">举办地点：</div>
        <div class="item-value">{{ current.trainingLocation }}</div>
      </div>
      <div class="detail-item">
        <div class="item-name">举办方：</div>
        <div class="item-value">{{ current.organizers }}</div>
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import request from "@/utils/request";
export default {
  name: "OfflineTraining",
  data() {
    return {
      dataSource: [],
      queryData: {
        subjectName: '',
        subjectCode: '',
        pageSize: 10,
        pageNum: 1,
      },
      total: 0,
      dialogVisible: false,
      statusOptions: [],
      current: {},
      loading: false
    }
  },
  computed: {
    trainingStatus() {
      return (value) => {
        const status = this.statusOptions.find((status) => status.dictValue == value)
        return status ? status.dictLabel : value
      }
    },

    showNames() {
      return ({teaReviewerId, teaReviewerMap}) => {
        if (!teaReviewerMap || !teaReviewerId) return '--';
        let names = teaReviewerId.split(',').map(id => {
          return teaReviewerMap[id].name
        })
        return names.join('，');
      }
    },
  },
  created() {
    this.initDicts()
    this.query()
  },
  methods: {
    initDicts() {

      this.getDicts('course_form').then(res => {
        this.statusOptions = res.data;
      })
    },
    search() {
      this.queryData.pageNum = 1;
      this.query();
    },
    query() {
      this.loading = true
      request({
        url: '/practice/subject/list',
        method: "get",
        params: {
          accountId: this.$store.state.user.userId,
          ...this.queryData
        },
        params: this.queryData
      }).then((res) => {
        this.loading = false
        if (res.code != 200) return;
        this.dataSource = res.rows;
        this.total = res.total;
      }).catch((error) => {
        // console.log(error)
        this.loading = false
      })
    },
    resetForm() {
      this.$refs["queryForm"].resetFields();
      this.queryData.pageNum = 1
      this.query();
    },
    showTrainingDetail(training) {
      this.current = training
      this.dialogVisible = true
    }
  }
}
</script>

<style lang='scss' scoped>
.offline-training {
  padding: 24px;
  ::v-deep .el-dialog {
    .el-dialog__header {
      background-color: #3c81f5;
      padding-top: 10px;
      .el-dialog__title {
        color: white;
      }
      .el-dialog__headerbtn {
        top: 15px;
        .el-dialog__close {
          color: white;
        }
      }
    }
    .el-dialog__body {
      padding-bottom: 1px;
      & > span {
        color: black;
      }
    }
  }
  .detail-item {
    display: flex;
    margin-bottom: 32px;
    font-size: 14px;
    .item-name {
      width: 100px;
      text-align: right;
      color: #666666d8;
    }
    .item-value {
      font-weight: bold;
      color: #000;
    }
  }
}
</style>
