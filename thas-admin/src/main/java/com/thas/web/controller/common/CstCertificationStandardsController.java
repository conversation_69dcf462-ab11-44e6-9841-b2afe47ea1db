package com.thas.web.controller.common;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.thas.common.utils.poi.ExcelUtil;
import com.thas.web.domain.*;
import com.thas.web.domain.dto.CstCertificationStandardsDetailQueryDTO;
import com.thas.web.domain.dto.SelectStandardsByClauseIdsDTO;
import com.thas.web.domain.vo.ChapterVo;
import com.thas.web.domain.vo.SelectByVersionIdVo;
import com.thas.web.service.ICstCertificationStandardsService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.enums.BusinessType;
import com.thas.common.core.page.TableDataInfo;

/**
 * 认证标准模板Controller
 *
 * <AUTHOR>
 * @date 2022-01-21
 */
@Api(value="CstCertificationStandardsController",tags = "认证标准模板")
@RestController
@RequestMapping("/system/standards")
public class CstCertificationStandardsController extends BaseController
{
    @Autowired
    private ICstCertificationStandardsService cstCertificationStandardsService;


    /**
     * 查询认证标准模板列表
     */
    //@PreAuthorize("@ss.hasPermi('system:standards:list')")
    @GetMapping("/list")
    public TableDataInfo list(CstCertificationStandards cstCertificationStandards)
    {
        startPage();
        List<CstCertificationStandards> list = cstCertificationStandardsService.selectCstCertificationStandardsList(cstCertificationStandards);
        return getDataTable(list);
    }

    /**
     * 导出认证标准模板列表
     */
    //@PreAuthorize("@ss.hasPermi('system:standards:export')")
    @Log(title = "认证标准模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CstCertificationStandards cstCertificationStandards)
    {
        List<CstCertificationStandards> list = cstCertificationStandardsService.selectCstCertificationStandardsList(cstCertificationStandards);
        ExcelUtil<CstCertificationStandards> util = new ExcelUtil<>(CstCertificationStandards.class);
        util.exportExcel(response, list, "认证标准模板数据");
    }

    /**
     * 获取认证标准模板详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:standards:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(cstCertificationStandardsService.selectCstCertificationStandardsById(id));
    }

    /**
     * 新增认证标准模板
     */
    //@PreAuthorize("@ss.hasPermi('system:standards:add')")
    @ApiOperation("新增认证标准模板")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "chapterNo", value = "章编号", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "chapter", value = "章内容", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "sectionNo", value = "节编号", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "section", value = "节内容", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "articleNo", value = "条编号", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "article", value = "条内容", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "clauseNo", value = "款编号", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "clause", value = "款内容", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "isStar", value = "受否带星（0是 1否）", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "detailRulesTitle", value = "细则标题", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "detailRulesDesc", value = "细则内容", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "evidenceMaterial", value = "佐证材料", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "regulationFile", value = "相关法律法规、规范性文件", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "evidenceMaterial", value = "佐证材料", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "internationalReference", value = "国际参考文献", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "status", value = "数据状态（1正常 0停用）", dataType = "String", dataTypeClass = String.class)
    })
    @Log(title = "认证标准模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CstCertificationStandards cstCertificationStandards)
    {
        return toAjax(cstCertificationStandardsService.insertCstCertificationStandards(cstCertificationStandards));
    }

    /**
     * 修改认证标准模板
     */
    //@PreAuthorize("@ss.hasPermi('system:standards:edit')")
    @Log(title = "认证标准模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CstCertificationStandards cstCertificationStandards)
    {
        return toAjax(cstCertificationStandardsService.updateCstCertificationStandards(cstCertificationStandards));
    }

    /**
     * 删除认证标准模板
     */
    //@PreAuthorize("@ss.hasPermi('system:standards:remove')")
    @Log(title = "认证标准模板", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(cstCertificationStandardsService.deleteCstCertificationStandardsByIds(ids));
    }

    /**
     * 获取认证模板
     */
    //@PreAuthorize("@ss.hasPermi('system:standards:query')")
    @ApiOperation("获取认证模板")
    @GetMapping("/selectByVersionId")
    public AjaxResult selectByVersionId(Long versionId)
    {
        SelectByVersionIdVo selectByVersionIdVo = cstCertificationStandardsService.selectByVersionId(versionId);
        return AjaxResult.success(selectByVersionIdVo);
    }

    /**
     * 获取认证模板--条款详情
     */
    //@PreAuthorize("@ss.hasPermi('system:standards:query')")
    @ApiOperation("获取认证模板-条款详情")
    @GetMapping("/selectDetailByVersionIdAndClauseId")
    public AjaxResult selectClauseDetailByVersionId(Long versionId,Long articleId)
    {
        List<ChapterVo> clauseVoList = cstCertificationStandardsService.selectDetailByVersionIdAndClauseId(versionId,articleId);
        return AjaxResult.success(clauseVoList);
    }

    /**
     * 通过版本id,款id查找详情
     */
    //@PreAuthorize("@ss.hasPermi('system:standards:query')")
    @ApiOperation("通过版本id,款id查找详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "clauseId", value = "款id", dataType = "Long", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "versionId", value = "版本id", dataType = "Long", dataTypeClass = Long.class)
    })
    @PostMapping("/selectDetailByVersionIdAndClauseId")
    public AjaxResult selectDetailByVersionIdAndClauseId(@RequestBody CstCertificationStandardsDetailQueryDTO cstCertificationStandardsDetailQueryDTO)
    {
        CstCertificationStandards cstCertificationStandards = cstCertificationStandardsService.selectDetailByVersionIdAndClauseId(cstCertificationStandardsDetailQueryDTO);
        return AjaxResult.success(cstCertificationStandards);
    }

    /**
     * 通过款id和版本id修改认证标准模板
     */
    //@PreAuthorize("@ss.hasPermi('system:standards:edit')")
    @ApiOperation("修改认证标准模板")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "chapterNo", value = "章编号", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "chapter", value = "章内容", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "sectionNo", value = "节编号", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "section", value = "节内容", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "articleNo", value = "条编号", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "article", value = "条内容", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "clauseNo", value = "款编号", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "clause", value = "款内容", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "isStar", value = "受否带星（0是 1否）", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "detailRulesTitle", value = "细则标题", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "detailRulesDesc", value = "细则内容", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "regulationFile", value = "相关法律法规、规范性文件", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "evidenceMaterial", value = "佐证材料", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "internationalReference", value = "国际参考文献", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "status", value = "数据状态（1正常 0停用）", dataType = "String", dataTypeClass = String.class)
    })
    @Log(title = "认证标准模板", businessType = BusinessType.UPDATE)
    @PutMapping("/updateByClauseIdAndVersionId")
    public AjaxResult updateByClauseIdAndVersionId(@RequestBody CstCertificationStandards cstCertificationStandards)
    {
        return toAjax(cstCertificationStandardsService.updateByClauseIdAndVersionId(cstCertificationStandards));
    }


    @ApiOperation("通过clauseId和versionId获取认证模板")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "versionId", value = "版本id", dataType = "Long", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "clauseIds", value = "款id", dataType = "List", dataTypeClass = List.class)
    })
    @PostMapping("/selectByClauseIdsAndVersionId")
    public AjaxResult selectByClauseIdsAndVersionId(@RequestBody SelectStandardsByClauseIdsDTO selectStandardsByClauseIdsDTO)
    {
        List<CstCertificationStandards> cstCertificationStandards = cstCertificationStandardsService.selectByClauseIdsAndVersionId(selectStandardsByClauseIdsDTO);
        return AjaxResult.success(cstCertificationStandards);
    }

}
