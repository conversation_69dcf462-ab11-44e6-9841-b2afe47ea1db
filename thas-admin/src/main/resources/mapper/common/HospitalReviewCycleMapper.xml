<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.HospitalReviewCycleMapper">

    <resultMap type="com.thas.web.domain.HospitalReviewCycle" id="HospitalReviewCycleResult">
        <result property="id" column="id"/>
        <result property="applyNo" column="apply_no"/>
        <result property="stageValue" column="stage_value"/>
        <result property="cycle" column="cycle"/>
        <result property="creator" column="creator"/>
        <result property="createTime" column="create_time"/>
        <result property="updater" column="updater"/>
        <result property="updateTime" column="update_time"/>
        <result property="autCode" column="aut_code"/>
    </resultMap>

    <resultMap type="com.thas.web.dto.SysUserBaseInfo" id="SysUserBaseInfoResult">
        <result property="accountId" column="accountId"/>
        <result property="userName" column="userName"/>
        <result property="name" column="name"/>
        <result property="email" column="email"/>
        <result property="phoneNumber" column="phonenumber"/>
    </resultMap>

    <resultMap type="com.thas.web.dto.HospitalReviewCycleDTO" id="HospitalReviewCycleDTOResult">
        <result property="id" column="id"/>
        <result property="applyNo" column="apply_no"/>
        <result property="stageValue" column="stage_value"/>
        <result property="cycle" column="cycle"/>
        <result property="creator" column="creator"/>
        <result property="createTime" column="create_time"/>
        <result property="updater" column="updater"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="accountId" column="accountId"/>
        <result property="nineCycleTime" column="nineCycleTime"/>
        <result property="cycleStartTime" column="cycleStartTime"/>
        <result property="hospitalName" column="hospitalName"/>
        <association property="sysUserBaseInfo" resultMap="SysUserBaseInfoResult"/>
    </resultMap>


    <sql id="selectHospitalReviewCycleVo">
        select id, apply_no, stage_value, cycle, creator, create_time, updater, update_time, aut_code from hospital_review_cycle
    </sql>

    <sql id="SQL_AES_ENCRYPT">'${@com.thas.web.config.SqlAesEncryptConfig@SQL_AES_ENCRYPT}'</sql>

    <sql id="selectHospitalReviewCycleDTO1">
        SELECT hrc.* ,hpe.pre_exam_id accountId,su.user_name userName,
        AES_DECRYPT(unhex(su.nick_name),<include refid="SQL_AES_ENCRYPT"/>) name
        FROM hospital_review_cycle hrc
        LEFT JOIN hospital_pre_exam hpe ON hrc.apply_no = hpe.apply_no
        LEFT JOIN sys_user su ON su.user_id = hpe.pre_exam_id
        LEFT JOIN aut_sa_relation asr ON hpe.apply_no = asr.hospital_apply_no
    </sql>

    <sql id="selectHospitalReviewCycleDTO2">
        SELECT hrc.* ,hr.reviewer_id accountId ,su.user_name userName,
        AES_DECRYPT(unhex(su.nick_name),<include refid="SQL_AES_ENCRYPT"/>) name,
        hrc.cycle nineCycleTime
        FROM hospital_review_cycle hrc
        LEFT JOIN hospital_reviewer hr ON hrc.apply_no = hr.apply_no
        LEFT JOIN sys_user su ON su.user_id = hr.reviewer_id
        LEFT JOIN aut_sa_relation asr ON hrc.apply_no = asr.hospital_apply_no
    </sql>

    <select id="selectHospitalReviewCycleList" parameterType="HospitalReviewCycle"
            resultMap="HospitalReviewCycleResult">
        <include refid="selectHospitalReviewCycleVo"/>
        from hospital_review_cycle
        <where>
            <if test="applyNo != null  and applyNo != ''">and apply_no = #{applyNo}</if>
            <if test="stageValue != null  and stageValue != ''">and stage_value = #{stageValue}</if>
            <if test="cycle != null  and cycle != ''">and cycle = #{cycle}</if>
            <if test="creator != null  and creator != ''">and creator = #{creator}</if>
            <if test="updater != null  and updater != ''">and updater = #{updater}</if>
        </where>
    </select>

    <select id="selectHospitalReviewCycleById" parameterType="Long" resultMap="HospitalReviewCycleResult">
        <include refid="selectHospitalReviewCycleVo"/>
        where id = #{id}
    </select>


    <select id="selectHospitalReviewCycleByApplyNo" parameterType="String" resultMap="HospitalReviewCycleResult">
        <include refid="selectHospitalReviewCycleVo"/>
        where apply_no = #{applyNo} and status = 1
    </select>

    <insert id="insertHospitalReviewCycle" parameterType="HospitalReviewCycle" useGeneratedKeys="true" keyProperty="id">
        insert into hospital_review_cycle
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">apply_no,</if>
            <if test="autCode != null and autCode != ''">aut_code,</if>
            <if test="stageValue != null and stageValue != ''">stage_value,</if>
            <if test="cycle != null and cycle != ''">cycle,</if>
            <if test="creator != null">creator,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updater != null">updater,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">#{applyNo},</if>
            <if test="autCode != null and autCode != ''">#{autCode},</if>
            <if test="stageValue != null and stageValue != ''">#{stageValue},</if>
            <if test="cycle != null and cycle != ''">#{cycle},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updater != null">#{updater},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateHospitalReviewCycle" parameterType="HospitalReviewCycle">
        update hospital_review_cycle
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">apply_no = #{applyNo},</if>
            <if test="stageValue != null and stageValue != ''">stage_value = #{stageValue},</if>
            <if test="cycle != null and cycle != ''">cycle = #{cycle},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>


    <update id="updateHospitalReviewCycleByApplyNo" parameterType="HospitalReviewCycle">
        update hospital_review_cycle
        <trim prefix="SET" suffixOverrides=",">
            <if test="stageValue != null and stageValue != ''">stage_value = #{stageValue},</if>
            <if test="cycle != null and cycle != ''">cycle = #{cycle},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="autCode != null and autCode != ''">aut_code = #{autCode},</if>
        </trim>
        where apply_no = #{applyNo} and status = 1
    </update>
    <update id="updateHospitalReviewCycleByApplyNoList">
         update hospital_review_cycle set status =2 where apply_no in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
         and status = 1

    </update>

    <delete id="deleteHospitalReviewCycleById" parameterType="Long">
        delete from hospital_review_cycle where id = #{id}
    </delete>

    <delete id="deleteHospitalReviewCycleByIds" parameterType="String">
        delete from hospital_review_cycle where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertHospitalReviewCycleList" parameterType="list">
        insert into hospital_review_cycle(apply_no,stage_value,cycle,creator,create_time,aut_code) values
        <foreach collection="list" separator="," item="item">
            (#{item.applyNo},#{item.stageValue},#{item.cycle},#{item.creator},#{item.createTime},#{item.autCode})
        </foreach>
    </insert>

    <delete id="deleteByApplyNoList" parameterType="list">
        delete from hospital_review_cycle where apply_no in (
        <foreach collection="list" item="item" separator=",">
            #{item}
        </foreach>
        )
        and status = 1
    </delete>

    <select id="selectByApplyNoAndStageValue" parameterType="string" resultMap="HospitalReviewCycleResult">
        <include refid="selectHospitalReviewCycleVo"/>
        where apply_no=#{applyNo} and stage_value = #{cycleStage} and status = 1
    </select>

    <select id="getHospitalReviewCycleByPreExamIds" resultMap="HospitalReviewCycleDTOResult">
        <include refid="selectHospitalReviewCycleDTO1"/>
        WHERE hpe.pre_exam_id IN
        <foreach collection="preExamIds" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        AND hpe.`status`=1
        AND hrc.stage_value = #{stageValue}
        AND hrc.`status` = 1
        AND hpe.apply_no != #{applyNo}
        AND asr.`status` = 1
        AND asr.aut_status != '050202'
    </select>

    <select id="getHospitalReviewCycleByReviewerIds" resultMap="HospitalReviewCycleDTOResult">
        <include refid="selectHospitalReviewCycleDTO2"/>
        WHERE
        hrc.`status` = 1
        AND hr.`status`= 1
        AND asr.`status` = 1
        AND hr.reviewer_id IN
        <foreach collection="reviewerIds" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        AND hrc.stage_value IN
        <foreach collection="stageValueList" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        AND hr.apply_no != #{applyNo}
        AND asr.aut_status != '050202'
    </select>

    <select id="getHospitalReviewCycleByStageValueAndStatus" resultMap="HospitalReviewCycleDTOResult">
        SELECT substring_index(hrc.cycle,',',1) cycleStartTime,
        su.user_id accountId,su.user_name userName,
        AES_DECRYPT(unhex(su.nick_name),<include refid="SQL_AES_ENCRYPT"/>) name,
        AES_DECRYPT(unhex(su.email),<include refid="SQL_AES_ENCRYPT"/>) email,
        AES_DECRYPT(unhex(su.phonenumber),<include refid="SQL_AES_ENCRYPT"/>) phonenumber,
        hbi.hospital_name hospitalName FROM hospital_review_cycle hrc
        LEFT JOIN sys_user su ON hrc.creator = su.user_name
        LEFT JOIN hospital_base_info hbi ON hrc.apply_no = hbi.apply_no
        WHERE
        hrc.stage_value = ${stageValue} AND
        hrc.status = 1 AND
        hrc.apply_no IN(
        SELECT apply_no FROM hospital_planned_distribution WHERE ${field} = '2'
        AND status = 1
        AND cycle_status = 1
        )
    </select>
    <select id="selectHospitalReviewCycleByApplyNos" resultMap="HospitalReviewCycleResult">
        <include refid="selectHospitalReviewCycleVo"/>
        where
        status = 1
        and stage_value = #{cycleStageValue}
        and apply_no IN
        <foreach item="applyNo" collection="list" open="(" separator="," close=")">
            #{applyNo}
        </foreach>

    </select>
    <select id="selectByApplyNoAndStageValue2" resultMap="HospitalReviewCycleResult">
        <include refid="selectHospitalReviewCycleVo"/>
        where
        1=1
        <if test="applyNo != null and applyNo != ''">AND apply_no=#{applyNo}</if>
        and stage_value = #{cycleStage}
        and aut_code = #{autCode}
    </select>

</mapper>
