<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.ImportServiceMapper">

<!--    <insert id="saveBatchCstCertificationStandards" parameterType="java.util.List">-->
<!--        <foreach collection="list" item="item" index="index" open="" close="" separator=";">-->
<!--            INSERT INTO cst_certification_standards (chapter_id,chapter_no, chapter, section_id, section_no, section,-->
<!--            article_id, article_no, article, clause_id, clause_no, clause, is_star, detail_rules_title-->
<!--            , detail_rules_desc, evidence_material, regulation_file, international_reference, status, version_id)-->
<!--            values (-->
<!--            ifnull(#{item.chapterId, jdbcType=VARCHAR}, 0),-->
<!--            ifnull(#{item.chapterNo, jdbcType=VARCHAR}, ""),-->
<!--            ifnull(#{item.chapter, jdbcType=VARCHAR}, ""),-->
<!--            ifnull(#{item.sectionId, jdbcType=VARCHAR}, 0),-->
<!--            ifnull(#{item.sectionNo, jdbcType=VARCHAR}, ""),-->
<!--            ifnull(#{item.section, jdbcType=VARCHAR}, ""),-->
<!--            ifnull(#{item.articleId, jdbcType=VARCHAR}, 0),-->
<!--            ifnull(#{item.articleNo, jdbcType=VARCHAR}, ""),-->
<!--            ifnull(#{item.article, jdbcType=VARCHAR}, ""),-->
<!--            ifnull(#{item.clauseId, jdbcType=VARCHAR}, 0),-->
<!--            ifnull(#{item.clauseNo, jdbcType=VARCHAR}, ""),-->
<!--            ifnull(#{item.clause, jdbcType=VARCHAR}, ""),-->
<!--            ifnull(#{item.isStar, jdbcType=VARCHAR}, '1'),-->
<!--            ifnull(#{item.detailRulesTitle, jdbcType=VARCHAR}, ""),-->
<!--            ifnull(#{item.detailRulesDesc, jdbcType=VARCHAR}, ""),-->
<!--            ifnull(#{item.evidenceMaterial, jdbcType=VARCHAR}, ""),-->
<!--            ifnull(#{item.regulationFile, jdbcType=VARCHAR}, ""),-->
<!--            ifnull(#{item.internationalReference, jdbcType=VARCHAR}, ""),-->
<!--            ifnull(#{item.status, jdbcType=VARCHAR}, '0'),-->
<!--            ifnull(#{item.versionId, jdbcType=VARCHAR}, 0)-->
<!--            )-->
<!--        </foreach>-->
<!--    </insert>-->

    <insert id="saveBatchCstCertificationStandards" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            INSERT INTO cst_certification_standards (chapter_id,chapter_no, chapter, section_id, section_no, section,
            article_id, article_no, article, clause_id, clause_no, clause, is_star,
            detail_rules_desc, evidence_material, regulation_file, international_reference, status, version_id)
            values (
            ifnull(#{item.chapterId, jdbcType=VARCHAR}, 0),
            ifnull(#{item.chapterNo, jdbcType=VARCHAR}, ""),
            ifnull(#{item.chapter, jdbcType=VARCHAR}, ""),
            ifnull(#{item.sectionId, jdbcType=VARCHAR}, 0),
            ifnull(#{item.sectionNo, jdbcType=VARCHAR}, ""),
            ifnull(#{item.section, jdbcType=VARCHAR}, ""),
            ifnull(#{item.articleId, jdbcType=VARCHAR}, 0),
            ifnull(#{item.articleNo, jdbcType=VARCHAR}, ""),
            ifnull(#{item.article, jdbcType=VARCHAR}, ""),
            ifnull(#{item.clauseId, jdbcType=VARCHAR}, 0),
            ifnull(#{item.clauseNo, jdbcType=VARCHAR}, ""),
            ifnull(#{item.clause, jdbcType=VARCHAR}, ""),
            ifnull(#{item.isStar, jdbcType=VARCHAR}, '1'),
            ifnull(#{item.detailRulesDesc, jdbcType=VARCHAR}, ""),
            ifnull(#{item.evidenceMaterial, jdbcType=VARCHAR}, ""),
            ifnull(#{item.regulationFile, jdbcType=VARCHAR}, ""),
            ifnull(#{item.internationalReference, jdbcType=VARCHAR}, ""),
            ifnull(#{item.status, jdbcType=VARCHAR}, '0'),
            ifnull(#{item.versionId, jdbcType=VARCHAR}, 0)
            )
        </foreach>
    </insert>

    <insert id="saveBatchCstEvaluationCriterion" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            INSERT INTO cst_evaluation_criterion (certification_standards_id, evaluate, standard, status, version_id)
            values (
            ifnull(#{item.id, jdbcType=VARCHAR}, 0),
            ifnull(#{item.evaluate, jdbcType=VARCHAR}, ""),
            ifnull(#{item.standard, jdbcType=VARCHAR}, ""),
            ifnull(#{item.status, jdbcType=VARCHAR}, '0'),
            ifnull(#{item.versionId, jdbcType=VARCHAR}, 0)
            )
        </foreach>
    </insert>

    <insert id="saveCstVersioning" parameterType="com.thas.common.core.domain.CstVersioningExcel">
        INSERT INTO cst_versioning (version_id, version_name, status, create_id, update_id)
        values (ifnull(#{versionId, jdbcType=VARCHAR}, 0),
                ifnull(#{versionName, jdbcType=VARCHAR}, ""),
                ifnull(#{status, jdbcType=VARCHAR}, '0'),
                ifnull(#{userId, jdbcType=VARCHAR}, '0'),
                ifnull(#{userId, jdbcType=VARCHAR}, '0'))
    </insert>

    <select id="selectCstVersioningId" resultType="string">
--         SELECT auto_increment
--         FROM information_schema.`TABLES`
--         WHERE TABLE_NAME = 'cst_versioning'
--           and table_schema = (select database())

        SELECT id+1 num FROM cst_versioning ORDER BY create_time DESC LIMIT 1
    </select>
</mapper>
