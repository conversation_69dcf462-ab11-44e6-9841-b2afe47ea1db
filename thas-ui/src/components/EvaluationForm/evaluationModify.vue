<!--  -->
<template>
  <div class="evaluation-modify evaluation" v-loading="submitLoading"  >
    <el-form v-if="!refuse" ref="evaluationForm" :model="formData" :label-width="labelWidth" :rules="rules"
      :key="formKey">
      <template v-if="!isReject">
        <el-form-item class="form-item" :class="{ readonly: !modifying }" :label="reviewResultLabel" prop="autResult">
          <el-select v-if="modifying" v-model="formData.autResult" clearable filterable placeholder="请选择" @change="resultChange">
            <el-option v-for="(option, index) in reviewResultOptions" :key="index" :value="option.dictValue"
              :label="option.dictLabel"></el-option>
          </el-select>
          <dict-span :noTag='true' v-else :key="checkSummaryOptions.length + Number(formData.autResult)"
            :value="formData.autResult" :options="reviewResultOptions" type="default"
            style="display:inline-block;"></dict-span>
          <span v-if="autResultBetweenDiff" class="aut-result-between-diff"> 温馨提示：修改结果低于原评价结果</span>
        </el-form-item>
        <el-form-item v-if="formData.autResult == 1 || formData.autResult == 2 || formData.autResult == 3" class="form-item"
          :class="{ readonly: !modifying && (!exitModify || !isFinal) }" label="亮点：" prop="autAdvantage">
          <el-input v-if="modifying || exitModify" type="textarea" v-model="formData.autAdvantage" placeholder="请填写亮点"
            :rows="4" :maxlength="descMaxLength"></el-input>
          <span v-else>{{ formData.autAdvantage || '--' }}</span>
        </el-form-item>
        <el-form-item v-if="formData.autResult == 4 || formData.autResult == 5" class="form-item"
          :class="{ readonly: !modifying && (!exitModify || !isFinal) }" label="不足：" prop="autEvaluate">
          <el-input v-if="modifying || exitModify" type="textarea" v-model="formData.autEvaluate" placeholder="请填写不足"
            :rows="4" :maxlength="descMaxLength"></el-input>
          <span v-else>{{ formData.autEvaluate || '--' }}</span>
        </el-form-item>
        <el-form-item v-if="formData.autResult == 4 || formData.autResult == 5" class="form-item"
          :class="{ readonly: !modifying && (!exitModify || !isFinal) }" label="整改建议：" prop="autProposal">
          <el-input v-if="modifying || exitModify" type="textarea" v-model="formData.autProposal" placeholder="请填写整改建议"
            :rows="4" :maxlength="descMaxLength"></el-input>
          <span v-else>{{ formData.autProposal || '--' }}</span>
        </el-form-item>
        <el-form-item v-if="formData.autResult" class="form-item" :class="{ readonly: !modifying && (!exitModify || !isFinal) }" label="改进机会："
          prop="autImprove">
          <el-input v-if="modifying || exitModify" type="textarea" v-model="formData.autImprove" placeholder="请填写改进机会"
            :rows="4" :maxlength="descMaxLength"></el-input>
          <span v-else>{{ formData.autImprove || '--' }}</span>
        </el-form-item>
        <!-- <el-form-item v-if="!formData.autResult || formData.autResult == 6" class="form-item"
          :class="{ readonly: !modifying && (!exitModify || !isFinal) }" label="评价描述：" prop="autDesc1">
          <el-input v-if="modifying || exitModify" type="textarea" v-model="formData.autDesc1" placeholder="请填写评价描述"
            :rows="4" :maxlength="descMaxLength"></el-input>
          <span v-else>{{ formData.autDesc1 || '--' }}</span>
        </el-form-item> -->
      </template>
      <template v-else>
        <el-form-item class="form-item" label="评审员修改：" prop="autResult">
          <span style="color:red;"> 拒绝修改</span>
        </el-form-item>
        <el-form-item class="form-item" :class="{ readonly: !modifying && (!exitModify || !isFinal) }" label="拒绝原因："
          prop="autDesc" :rules="rejectRule">
          <el-input v-if="modifying || exitModify" type="textarea" v-model="formData.autDesc" placeholder="请填写拒绝原因"
            :rows="4" :maxlength="descMaxLength"></el-input>
          <span v-else>{{ formData.autDesc || '--' }}</span>
        </el-form-item>
      </template>

      <!-- <el-form-item v-if="showBuZhu" class="form-item" :class="{ readonly:!modifying && !psModify }" label="不足及建议：" prop="reviewerRespond">
        <el-input v-if="modifying || psModify" type="textarea" v-model="formData.reviewerRespond" placeholder="请填写不足及建议" :rows="4" :maxlength="descMaxLength"></el-input>
        <span v-else>{{ formData.reviewerRespond || '--' }}</span>
              </el-form-item> -->
      <!-- <el-form-item class="form-item" :class="{ readonly: !modifying && !psModify  }" :label="labelLabel" prop="autDesc" :rules="descRule">
        <el-input v-if="modifying || psModify" type="textarea" v-model="formData.autDesc" :placeholder="labelDesc" :rows="4" :maxlength="descMaxLength"></el-input>
        <span v-else>{{ formData.autDesc || '--' }}</span>
              </el-form-item> -->

      <el-form-item label="风险：" v-if="showRiskWithResult">
        <el-popover placement="top" width="400" trigger="click">
          <el-image :src="require('../../assets/images/able.png')"></el-image>
          <el-button type="text" slot="reference" icon="el-icon-question" style="float: left"></el-button>
        </el-popover>
        <div class="risk-content" style="float: left">
          <el-form-item class="risk" label="可能性：" prop="riskPossibility" :rules="rules.riskPossibility"
            label-width="80px">
            <el-radio-group v-model.trim="formData.riskPossibility" :disabled='!modifying'>
              <el-radio :label="1">低</el-radio>
              <el-radio :label="2">中</el-radio>
              <el-radio :label="3">高</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item class="risk" label="影响：" prop="riskImpact" :rules="rules.riskImpact" label-width="80px">
            <el-radio-group v-model.trim="formData.riskImpact" :disabled='!modifying'>
              <el-radio :label="1">低</el-radio>
              <el-radio :label="2">中</el-radio>
              <el-radio :label="3">高</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item class="risk" label="总分：">
            {{ totalRangeText }}
          </el-form-item>
        </div>
      </el-form-item>

      <el-form-item class="form-item" label=" " v-if="modifying || psModify">
        <el-button type="primary" @click="submit">提交</el-button>
        <template v-if="rejectType">
          <el-button v-if="!isReject" type="danger" @click="submit2">拒绝修改</el-button>
          <el-button v-else @click="() => { isReject = false; formData.autResult = '' }"
           >取消拒绝</el-button>

        </template>
      </el-form-item>
    </el-form>
    <el-form v-else :label-width="labelWidth">
      <el-form-item label="评价结果："><span style="color:red;">拒绝修改</span></el-form-item>
      <el-form-item label="拒绝原因："><span style="color:red;">{{ formData.autDesc }}</span></el-form-item>
    </el-form>
  </div>
</template>

<script>
import mixin from "./mixin";

export default {
  name: "EvaluationModify",
  mixins: [mixin],
  components: {},
  props: {
    isLeader: {
      type: Boolean,
      default: () => false
    },
    prevAutResult: {
      type: [String, Number],
      default: () => -1
    },
    refuse: {
      type: Boolean,
      default: () => false,
    }
  },
  watch: {},
  data() {
    return {
      autDescValidate: true,
      isReject: false,

      rejectRule: [{
        required: true,
        message: '请填写拒绝原因',
        trigger: ['change', 'blur'],
      }],

      submitForm: null,
    };
  },

  computed: {
    // 是否是复查（审核）
    isCheck() {
      let list =
        [
          this.AutSaAudCurrentStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW,
          this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC,
        ] || [];
      return list.includes(this.status);
    },

    reviewResultLabel() {
      return this.isLeader && this.isCheck ? "组员评价结果：" : "评价结果：";
    },

    autResultBetweenDiff() {
      if (this.prevAutResult === -1) return false;
      if (!this.formData.autResult) return false;
      if (Number(this.formData.autResult) - Number(this.prevAutResult) <= 0) return false;
      return true;
    },

    psModify() {
      let statuses = [
        this.AutSaAudCurrentStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC
      ];
      return statuses.includes(this.status) && this.$route.query.type == this.AutSaAudCurrentStatusEnum.SR_V_CLAUSE;
    },

    exitModify() {
      return this.psModify && this.edit;
    },

    showRiskWithResult() {
      console.log(this.formData.autResult, this.clause.isStar, (this.formData.autResult == 5 || this.formData.autResult == 4) && this.clause.isStar == 1);
      // return this.formData.autResult == 5 && this.clause.isStar == 1;
      return (this.formData.autResult == 5 || this.formData.autResult == 4) && this.clause.isStar == 1;
    },

    labelLabel() {
      if (this.formData.autResult == 1 || this.formData.autResult == 2) {
        return '亮点：'
      } else if (this.formData.autResult == 3) {
        return '改进机会：'
      } else if (this.formData.autResult == 4 || this.formData.autResult == 5) {
        return '整改建议：'
      } else {
        return '评价描述：'
      }
    },

    labelDesc() {
      if (this.formData.autResult == 1 || this.formData.autResult == 2) {
        return '请填写亮点'
      } else if (this.formData.autResult == 3 || this.formData.autResult == 4 || this.formData.autResult == 5) {
        return '请填写改进机会'
      } else {
        return '请填写评价描述'
      }
    },

    showBuZhu() {
      return this.formData.autResult == 5 || this.formData.autResult == 4;
    },

    descRule() {
      let flag = false;
      if (this.formData.autResult == 1 || this.formData.autResult == 2 || this.formData.autResult == 4 || this.formData.autResult == 5) {
        flag = true;
      }
      return {
        required: flag,
        message: this.labelDesc,
        trigger: 'blur'
      }
    },
    totalRange() {
      return (
        Number(this.formData.riskImpact) * Number(this.formData.riskPossibility)
      );
    },
    totalRangeText() {
      let riskMap = {
        0: '无风险',
        1: '低风险',
        2: '低风险',
        3: '中风险',
        4: '中风险',
        6: '高风险',
        9: '严重风险',
      }
      return `${this.totalRange} (${riskMap[this.totalRange]})`;
    },
  },

  created() {
    this.autResultMessage = "请选择评价结果";
    this.autDescMessage = "请填写评价描述";
  },

  mounted() { },

  methods: {
    submit() {
      const evaluationForm = this.$refs.evaluationForm;
      if (evaluationForm && evaluationForm.validate) {
        evaluationForm.validate((valid) => {
          if (!valid) return;
          this.submitForm = JSON.parse(JSON.stringify(this.formData))
          let { autAdvantage, autEvaluate, autProposal, autImprove, autDesc1, autDesc } = this.submitForm
          this.submitForm.autDesc = JSON.stringify({
            autAdvantage, autEvaluate, autProposal, autImprove, autDesc: autDesc1 || autDesc
          })
          if (this.isReject) {
            this.rejectEmit()
          } else {
            this.submitEmit();
          }
        });
      }
    },

    submit2() {
      this.isReject = true;
      this.formData.autResult = '0'
      this.formKey++
    },

    clearDesc() {
      this.formData.autDesc = '';
      this.formData.riskPossibility = ''
      this.formData.riskImpact = ''
    }
  },
};
</script>
<style lang="scss" >
.aut-result-between-diff {
  font-size: 12px;
  color: red;
}
</style>
