<!--  -->
<template>
    <div class='evaluation-audit evaluation' v-loading="submitLoading"  >
        <el-form ref="evaluationForm" :model="formData" :label-width="labelWidth">
            <el-form-item v-if="showInput" class="form-item" prop="suggest" label="修改意见：" :rules="suggestRules">
                <el-input type="textarea" v-model="formData.suggest" :rows="4" :maxlength="200" placeholder="请输入修改意见"
                   ></el-input>
            </el-form-item>
            <el-form-item v-if="showInput" class="form-item">
                <slot name="submit" :data='slotForm' :refs="$refs.evaluationForm"></slot>
                <el-button @click="cancel">取消</el-button>
            </el-form-item>
            <el-form-item v-if="!showInput" class="form-item">
                <el-button type="danger" @click="handleShowSuggest">修改意见</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import mixin from "./mixin";

export default {
    name: 'EvaluationSuggest',
    components: {},
    mixins: [mixin],
    props: {},
    data() {
        return {
            showInput: false,
            formData: {
                suggest: '',
            },
            suggestRules: [
                {
                    required: true,
                    message: '请输入修改意见',
                    trigger: ['change', 'blur']
                }
            ]
        }
    },
    computed: {
        slotForm() {
            return {
                ...this.formData
            }
        }
    },

    methods: {
        handleShowSuggest() {
            this.showInput = true;
        },

        cancel() {
            this.$refs.evaluationForm.clearValidate();
            setTimeout(() => {
                this.showInput = false
            }, 0);
        }
    }
}

</script>
<style lang='scss'></style>
