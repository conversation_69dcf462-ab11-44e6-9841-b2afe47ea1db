<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.TraQuestionnaireAnswerDetailsMapper">

    <resultMap type="TraQuestionnaireAnswerDetails" id="TraQuestionnaireAnswerDetailsResult">
        <result property="id" column="id"/>
        <result property="questionnaireAnswerId" column="questionnaire_answer_id"/>
        <result property="questionnaireDetailsId" column="questionnaire_details_id"/>
        <result property="answer" column="answer"/>
    </resultMap>

    <sql id="selectTraQuestionnaireAnswerDetailsVo">
        select id, questionnaire_answer_id, questionnaire_details_id, answer
        from tra_questionnaire_answer_details
    </sql>

    <select id="selectTraQuestionnaireAnswerDetailsList" parameterType="TraQuestionnaireAnswerDetails"
            resultMap="TraQuestionnaireAnswerDetailsResult">
        <include refid="selectTraQuestionnaireAnswerDetailsVo"/>
        <where>
            <if test="questionnaireAnswerId != null ">and questionnaire_answer_id = #{questionnaireAnswerId}</if>
            <if test="questionnaireDetailsId != null ">and questionnaire_details_id = #{questionnaireDetailsId}</if>
            <if test="answer != null  and answer != ''">and answer = #{answer}</if>
        </where>
    </select>

    <select id="selectTraQuestionnaireAnswerDetailsById" parameterType="Long"
            resultMap="TraQuestionnaireAnswerDetailsResult">
        <include refid="selectTraQuestionnaireAnswerDetailsVo"/>
        where id = #{id}
    </select>
    <select id="getAnswerCountMap" resultType="AnswerCountVO">
        SELECT answer AS options, count(1) as value
        FROM tra_questionnaire_answer_details
        WHERE questionnaire_details_id = #{questionnaireDetailsId}
        GROUP BY answer
    </select>

    <insert id="insertTraQuestionnaireAnswerDetails" parameterType="TraQuestionnaireAnswerDetails">
        insert into tra_questionnaire_answer_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="questionnaireAnswerId != null">questionnaire_answer_id,</if>
            <if test="questionnaireDetailsId != null">questionnaire_details_id,</if>
            <if test="answer != null">answer,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="questionnaireAnswerId != null">#{questionnaireAnswerId},</if>
            <if test="questionnaireDetailsId != null">#{questionnaireDetailsId},</if>
            <if test="answer != null">#{answer},</if>
        </trim>
    </insert>

    <update id="updateTraQuestionnaireAnswerDetails" parameterType="TraQuestionnaireAnswerDetails">
        update tra_questionnaire_answer_details
        <trim prefix="SET" suffixOverrides=",">
            <if test="questionnaireAnswerId != null">questionnaire_answer_id = #{questionnaireAnswerId},</if>
            <if test="questionnaireDetailsId != null">questionnaire_details_id = #{questionnaireDetailsId},</if>
            <if test="answer != null">answer = #{answer},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTraQuestionnaireAnswerDetailsById" parameterType="Long">
        delete
        from tra_questionnaire_answer_details
        where id = #{id}
    </delete>

    <delete id="deleteTraQuestionnaireAnswerDetailsByIds" parameterType="String">
        delete from tra_questionnaire_answer_details where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>