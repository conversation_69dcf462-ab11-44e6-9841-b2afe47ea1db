<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.HospitalDomainGroupMapper">
    
    <resultMap type="HospitalDomainGroup" id="HospitalDomainGroupResult">
        <result property="id"    column="id"    />
        <result property="groupDetail"    column="group_detail"    />
        <result property="parentId"    column="parent_id"    />
        <result property="type"    column="type"    />
        <result property="status"    column="status"    />
        <result property="creator"    column="creator"    />
        <result property="createTime"    column="create_time"    />
        <result property="updater"    column="updater"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectHospitalDomainGroupVo">
        select id, describe, parent_id, type, status, creator, create_time, updater, update_time from hospital_domain_group
    </sql>

    <select id="selectHospitalDomainGroupList" parameterType="HospitalDomainGroup" resultMap="HospitalDomainGroupResult">
        <include refid="selectHospitalDomainGroupVo"/>
        <where>  
            <if test="describe != null  and describe != ''"> and describe = #{describe}</if>
            <if test="parentId != null  and parentId != ''"> and parent_id = #{parentId}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="creator != null  and creator != ''"> and creator = #{creator}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
        </where>
    </select>

    <select id="selectHospitalDomainGroupAll" parameterType="String" resultType="com.thas.web.domain.vo.DomainGroupNode">
        SELECT
            group_id groupId,
            group_detail groupDetail,
            parent_id parentId,
            type
        FROM
            hospital_domain_group
        WHERE status = 1
        <if test="groupIdList != null  and groupIdList != ''">
            and group_id in
            <foreach collection="groupIdList.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    
    <select id="selectHospitalDomainGroupById" parameterType="Long" resultMap="HospitalDomainGroupResult">
        <include refid="selectHospitalDomainGroupVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertHospitalDomainGroup" parameterType="HospitalDomainGroup">
        insert into hospital_domain_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="describe != null and describe != ''">describe,</if>
            <if test="parentId != null and parentId != ''">parent_id,</if>
            <if test="type != null">type,</if>
            <if test="status != null">status,</if>
            <if test="creator != null and creator != ''">creator,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updater != null and updater != ''">updater,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="describe != null and describe != ''">#{describe},</if>
            <if test="parentId != null and parentId != ''">#{parentId},</if>
            <if test="type != null">#{type},</if>
            <if test="status != null">#{status},</if>
            <if test="creator != null and creator != ''">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updater != null and updater != ''">#{updater},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <insert id="batchInsertByDomainGroupNode" parameterType="com.thas.web.domain.vo.DomainGroupNode">
        insert into hospital_domain_group (`group_id`, `group_detail`, `parent_id`, `type`)
        values
        <foreach collection="list" index="index" item="item" separator=",">
             (#{item.groupId}, #{item.groupDetail}, #{item.parentId}, #{item.type})
        </foreach>
    </insert>

    <update id="updateHospitalDomainGroup" parameterType="HospitalDomainGroup">
        update hospital_domain_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="describe != null and describe != ''">describe = #{describe},</if>
            <if test="parentId != null and parentId != ''">parent_id = #{parentId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="status != null">status = #{status},</if>
            <if test="creator != null and creator != ''">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHospitalDomainGroupById" parameterType="Long">
        delete from hospital_domain_group where id = #{id}
    </delete>

    <delete id="deleteHospitalDomainGroupByGroupIds" parameterType="List">
        delete from hospital_domain_group where group_id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>