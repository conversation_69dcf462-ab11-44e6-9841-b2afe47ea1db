<template>
  <div class='reviewer-list'>
    <el-row :gutter="24">
      <el-col :span="24" :xs="24">
        <el-form ref="elForm" :model="queryData" size="small" label-width="80px" inline>
          <el-form-item label="姓名">
            <el-input :maxlength="50" v-model.trim="queryData.name" placeholder="请输入姓名" clearable :style="{ width: '100%' }">
            </el-input>
          </el-form-item>

          <el-form-item label="手机号">
            <el-input :maxlength="50" v-model.trim="queryData.phoneNum" placeholder="请输入手机号" clearable :style="{ width: '100%' }">
            </el-input>
          </el-form-item>

          <el-form-item label="账号状态">
            <el-select v-model.trim="queryData.state" clearable placeholder="请选择账号状态">
              <el-option label="开启" :value="1"> </el-option>
              <el-option label="关闭" :value="2"> </el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" size="small" @click="query">
              查询
            </el-button>
            <el-button type="primary" size="small"> 重 置 </el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

    <el-table :data="dataSource" stripe border></el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query" />

  </div>
</template>

<script>
export default {
  name: 'ReviewerList',
  data() {
    return {
      queryData: {
        name: '',
        phoneNum: '',
        state: '',
        pageNum: 1,
        pageSize: 10,
      },
      dataSource: [{}],
      total: 0
    };
  },

  components: {},

  computed: {},

  mounted() { },

  methods: {
    query() { }
  }
}

</script>
<style lang='scss' scoped>
.reviewer-list {
  padding: 12px;
  background-color: #fff;
}
</style>