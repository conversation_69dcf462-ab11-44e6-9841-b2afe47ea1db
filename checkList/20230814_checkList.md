## 上线checkList

1. 菜单变动（有/无）
   有

2. 字典变动（有/无）
   有

3. 参数配置变动（有/无）
   无

4. 通知模板变动（有/无）
   无

5. 模版文件变动（有/无）
   thas-admin/src/main/resources/config/hos_rew_plan.ftl
   thas-admin/src/main/resources/config/aud_sa_report.ftl
   thas-admin/src/main/resources/config/review_report.ftl

6. SFTP配置文件变动（有/无） 
   无

7. sql（有/无）
   sql/dml/feature-2023v06/dml_20230731_wzg.sql
   sql/dml/feature-2023v06/dml_20230811_wzg.sql
   sql/dml/feature-2023v06/dml_20230814_wzg.sql
   sql/dml/feature-2023v06/dml_20230814_wzg-2.sql
   sql/ddl/feature-2023v06/ddl_20230811_wzg.sql

8. application.yml（有/无）
   # CORS跨域配置白名单
   cors:
     allowedOrigins: http://localhost:1024,http://**************:1020,http://**************:1021,http://**************:1028

   # 外部跳转请求文件资源处理-url前缀 ：http://{fileHost}:{filePort}{prefix}{filePath}
   outFileDownload:
     prefix: /resource/get
     fileHost: **************
     filePort: 1020
     fileUrl: http://**************:1020
     resourceUrl: http://*************:22

   # 加解密算法密钥配置
   privacy:
     crypto:
       sm4Key: e96ecde127fdc277b1a29b2ff003ae78
       aesKey: e96ecde127fdc277b1a29b2ff003ae78
       sm2:
         privateKey: 4adf3b5d584081da413dc27f648e7408d45478567add7853f4de50a9b42782c1
         publicKey: 0422ea057a182ac0b747718fec98065c3132ded0ac46ef67555bb15420d0d0fc0ce96855724e286884bfbcfb4eed64c30ab435336aac137231812f46000d433b25

   # token配置
   token:
    # 令牌自定义标识
     header: Authorization,Cookie

9. application-druid.yml（有/无）
    无   

10. 第三方jar包更新
    <!-- bcprov-jdk15to18 -->
     <dependency>
         <groupId>org.bouncycastle</groupId>
         <artifactId>bcprov-jdk15to18</artifactId>
         <version>1.69</version>
     </dependency>
   
     <dependency>
         <groupId>com.google.guava</groupId>
         <artifactId>guava</artifactId>
         <version>24.1.1-jre</version>
     </dependency>


11. 环境配置
    nginx：无

12. 其他表、配置等需要同步生产的点
    停服，先更新sql，再部署应用，redis需要更新
