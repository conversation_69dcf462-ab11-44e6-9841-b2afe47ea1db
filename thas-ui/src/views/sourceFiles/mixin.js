export const hospitalFileTypeTree = [
    {
        dictLabel: '医院资料',
        dictValue: 'hospital_data',
        // children: [
        //     {
        //         dictLabel: '医院详情',
        //         dictValue: 'hos_info',
        //     },
        //     {
        //         dictLabel: '服务范围',
        //         dictValue: 'hos_scope_service',
        //     },
        //     {
        //         dictLabel: '申请表格',
        //         dictValue: 'hos_application_form',
        //     },
        // ],
    },
    {
        dictLabel: '财务文件',
        dictValue: 'hospital_finance',
        // children: [
        //     {
        //         dictLabel: '发票',
        //         dictValue: 'hos_invoice',
        //     },
        //     {
        //         dictLabel: '付款',
        //         dictValue: 'hos_payment',
        //     }
        // ],
    },
    {
        dictLabel: '评审前准备',
        dictValue: 'hospital_before_review',
        // children: [
        //     {
        //         dictLabel: '时间安排表',
        //         dictValue: 'hos_timetable',
        //     },
        //     {
        //         dictLabel: '评审员文件',
        //         dictValue: 'hos_review_paper',
        //     },
        //     {
        //         dictLabel: '联络性文件',
        //         dictValue: 'hos_liaison_paper',
        //     },
        //     {
        //         dictLabel: '补充信息文件',
        //         dictValue: 'hos_supplementary_paper',
        //     }
        // ],
    },
    {
        dictLabel: '评审',
        dictValue: 'hospital_review',
        // children: [
        //     {
        //         dictLabel: '自评报告和相关支持文件',
        //         dictValue: 'hos_self_evaluation_etc_paper',
        //     },
        //     {
        //         dictLabel: '现场评审时间表',
        //         dictValue: 'hos_review_timetable',
        //     },
        //     {
        //         dictLabel: '评审报告及认证建议初稿',
        //         dictValue: 'hos_review_report_results_draft',
        //     },
        //     {
        //         dictLabel: '用于事实准确性确认报告',
        //         dictValue: 'hos_confirmation_factual_accuracy_report',
        //     },
        //     {
        //         dictLabel: '验证审核报告',
        //         dictValue: 'hos_certification_audit_report',
        //     },
        // ],
    },
    {
        dictLabel: '认证授予',
        dictValue: 'hospital_grant_certification',
        // children: [
        //     {
        //         dictLabel: '最终评审报告',
        //         dictValue: 'hos_final_review_report',
        //     },
        //     {
        //         dictLabel: '认证证书',
        //         dictValue: 'hos_certification_certificate',
        //     },
        //     {
        //         dictLabel: '通知',
        //         dictValue: 'hos_notification',
        //     },
        //     {
        //         dictLabel: '整改建议/进度报告',
        //         dictValue: 'hos_corrective_recommendations',
        //     },
        // ],
    },
    {
        dictLabel: '持续监控',
        dictValue: 'hospital_monitoring',
        // children: [
        //     {
        //         dictLabel: '行动计划',
        //         dictValue: 'hos_action_plan',
        //     },
        //     {
        //         dictLabel: '年度进度报告',
        //         dictValue: 'hos_annual_progress_report',
        //     },
        //     {
        //         dictLabel: '沟通事项',
        //         dictValue: 'hos_matters_communication',
        //     },
        // ],
    },
    {
        dictLabel: '评估',
        dictValue: 'hospital_assessment',
        // children: [
        //     {
        //         dictLabel: '医院评价',
        //         dictValue: 'hos_appraise',
        //     },
        //     {
        //         dictLabel: '评审员评价',
        //         dictValue: 'hos_reviewer_appraise',
        //     },
        // ],
    }
]

export const reviewerFileTypeTree = [
    {
        dictLabel: '评审员遴选与聘任',
        dictValue: 'reviewer_selection_appointment',
        // children: [
        //     {
        //         dictLabel: '申请表和简历',
        //         dictValue: 'rev_application_form_resume'
        //     },
        //     {
        //         dictLabel: '遴选过程和结果',
        //         dictValue: 'rev_selection_process_results'
        //     },
        // ],
    },
    {
        dictLabel: '合同和财务',
        dictValue: 'reviewer_contract_finance',
        // children: [
        //     {
        //         dictLabel: '合同签署',
        //         dictValue: 'rev_contract'
        //     },
        //     {
        //         dictLabel: '行为守则',
        //         dictValue: 'rev_code_conduct'
        //     },
        //     {
        //         dictLabel: '付款信息及记录',
        //         dictValue: 'rev_payment_record'
        //     },
        // ],
    },
    {
        dictLabel: '初始培训',
        dictValue: 'reviewer_initial_training',
        // children: [
        //     {
        //         dictLabel: '培训及考核记录',
        //         dictValue: 'rev_training_assessment_records'
        //     },
        //     {
        //         dictLabel: '准入证书',
        //         dictValue: 'rev_certificate_access'
        //     },
        // ],
    },
    {
        dictLabel: '持续培训',
        dictValue: 'reviewer_continuous_training',
        // children: [
        //     {
        //         dictLabel: '年度培训及考核记录',
        //         dictValue: 'rev_annual_training_assessment_records'
        //     },
        // ],
    },
    {
        dictLabel: '评审活动',
        dictValue: 'reviewer_evaluation_party',
        // children: [
        //     {
        //         dictLabel: '所参与评审时间表的副本',
        //         dictValue: 'rev_copy_participating_review'
        //     },
        //     {
        //         dictLabel: '提供申请和回复内容的电子邮件',
        //         dictValue: 'rev_copy_participating_review'
        //     },
        // ],
    },
    {
        dictLabel: '评价',
        dictValue: 'reviewer_evaluation',
        // children: [],
    },
    {
      dictLabel: '评审',
      dictValue: 'hospital_review',
      // children: [],
    }
]

export const inspectorFileTypeTree = [
    {
        dictLabel: '选拔招聘',
        dictValue: 'inspector_selection_appointment',
        // children: [],
    },
    {
        dictLabel: '合同',
        dictValue: 'inspector_contract',
        // children: [],
    },
    {
        dictLabel: '入职培训',
        dictValue: 'inspector_induction_training',
        // children: [],
    },
    {
        dictLabel: '考核与员工发展',
        dictValue: 'inspector_assessment_staff_development',
        // children: [],
    },
    {
        dictLabel: '财务',
        dictValue: 'inspector_finance',
        // // children: [],
    },
    {
        dictLabel: '职业健康与安全',
        dictValue: 'inspector_health_security',
        // // children: [],
    }
]

export const typeList = () => {
    if (final.length) return final;
    listMap2List();
    return final
}

export const getLabelByType = (type) => {
    if (type === 'sourceUpload') {
        return '平台资源'
    }
    return typeList().find(({ dictValue }) => type == dictValue)?.dictLabel || '-'
}

const final = []

const listMap2List = () => {
    let list = [...hospitalFileTypeTree, ...reviewerFileTypeTree, ...inspectorFileTypeTree]
    final.length = 0
    poll(list)
}

const poll = (list = []) => {
    if (!Array.isArray(list) || list.length === 0) return;
    list.forEach(data => {
        let { children, ...other } = data
        if (other) {
            final.push(other)
        }
        if (data.children) {
            poll(data.children)
        }
    })
}

