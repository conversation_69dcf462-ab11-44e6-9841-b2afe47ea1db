<template>
  <div class="review-plan-allot-detail" v-if="showAll">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>医院基本详情</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="toHospitalDetail">详细信息
          <i class="el-icon-arrow-right" />
        </el-button>
      </div>
      <detail-message :form="hospitalForm"></detail-message>
    </el-card>

    <el-card v-if="$route.query.personForm == 2" class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>评审员利益冲突</span>
      </div>
      <el-table size="mini" class="interest-table" ref="interestTable" :data="hosReviewClauseNumInfos" border>
        <el-table-column label="评审员姓名" prop="name" width="150"></el-table-column>
        <el-table-column label="是否存在利益冲突" width="150">
          <template slot-scope="{row}">
            <template v-if="row.interestFileId">
              <span v-if="row.hasInterest == 1">是</span>
              <span v-else>否</span>
            </template>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="利益冲突表">
          <template slot-scope="{row}">
            <div v-if="row.interestFileId || row.adminInterestFileId">
              <div>{{ fileName(row.interestFileId) }}</div>
              <div>{{ fileName(row.adminInterestFileId) }}</div>
            </div>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="{row}">
            <el-button v-if="row.interestFileId && downLoadFileName(row.interestFileId)" type="text" size="mini"
              @click="download(row.interestFileId)">下载利益冲突表（评审员上传）
            </el-button>
            <el-button v-if="row.adminInterestFileId && downLoadFileName(row.adminInterestFileId)" type="text" size="mini"
            @click="download(row.adminInterestFileId)">下载利益冲突表（管理员上传）
            </el-button>
            <el-button v-if="(row.interestFileId && downLoadFileName(row.interestFileId)) && !(row.adminInterestFileId && downLoadFileName(row.adminInterestFileId))" type="text" size="mini" @click="uploadInterestSheet(row)" :disabled="uploading" v-loading="row.accountId === reviewerId && uploading">上传盖章后的利益冲突表</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-card class="box-card" shadow="never" v-if="hos2.length && $route.query.personForm == 2">
      <div slot="header" class="clearfix">
        评审学员
      </div>
      <div v-for="(ho, index) in hos2" :key="index" style="margin-bottom:5px;">{{ ho.name }}</div>
    </el-card>

    <el-card class="box-card" shadow="never" v-if="revFile">
      <div slot="header" class="clearfix">
        评审员名单确认表
      </div>
      {{ revFile.fileName }}
      <el-button type="text" @click="preview(revFile.url)" style="margin-left:15px;">预览</el-button>
      <el-button type="text" @click="download2(revFile.fileId, revFile.downLoadFileName)" style="margin-left:15px;">下载</el-button>
    </el-card>

    <el-card v-if="$route.query.personForm == 2" class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        评审员分组表
      </div>
      <el-table :data="hosReviewers" :key="hosReviewers.length" border style="margin-top:15px;">
        <el-table-column label="分组" align="center">
          <b slot="header" style="color:black;">分组</b>
          <template>
            <b style="color:black;">评审员姓名</b>
          </template>
        </el-table-column>
        <el-table-column v-for="(domain, index) in allDomainListVOList" :key="index" :label="domain.domainName"
          :prop="domain.id" align="center">
          <span slot="header" style="color:black;font-weight: lighter;">{{ domain.domainName }}</span>
          <template slot-scope="{row}">
            {{ row[domain.id] || '(未分配)' }}
            <div v-if="row[domain.id] && AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_SUM_PASS == anuStatus">
              <el-button type="text" style="color:#409EFF;" @click="urgentReplace(domain)">【紧急更换】</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-card class="box-card" shadow="never">
      <el-tabs v-model.trim="activeName" @tab-click="tabClick" :key="hospitalName">
        <el-tab-pane v-for="(item, index) in hos" :key="item.accountId" :label="`${item.name}(${item.clauseNum})`"
          :name="`${item.accountId}`">
          <span v-if="!clauseList.length">此分组暂无款项，可前往分组管理分配款项!</span>
          <div v-else>
            <modify-table :key="index" :data="clauseList.map(clause => clause.clauseId)" :versionId="versionId"
              :showAll="false"></modify-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <el-dialog title="紧急更换评审员" :visible="replaceVisible" width="40%" destroy-on-close :close-on-click-modal="false"
      class="cycleVisible" :show-close="false">
      <div>
        <label for="">选择替补评审员：</label>
        <el-select v-model="candidateId" clearable filterable>
          <el-option v-for="({ accountId, name }, index) in candidateList" :key="index" :value="accountId"
            :label="name"></el-option>
        </el-select>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelReplace">取 消</el-button>
        <el-button type="primary" @click="submitReplace">确 定</el-button>
      </span>
    </el-dialog>

    <el-upload ref="upload" :action="action" :before-upload="beforeUpload" :on-success="onSuccess" :on-error="onError" :with-credentials="true" :headers="{
        Authorization: 'Bearer ' + getToken(),
      }" :data="{ applyNo: applyNo, reviewerId, downLoadFileName: `${reviewerName}-评审员利益冲突表-${allot.hospitalName}（管理员上传）.pdf` }" accept=".JPG,.JPEG,.PNG,.PDF" style="display: none">
      <span id="interestSheetUpload" slot="trigger">
      </span>
    </el-upload>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import ModifyTable from "@/views/hospitalSelfEvaluation/components/modifyTable.vue";
let that = this;
import request, { download } from "@/utils/request";
export default {
  name: "ReviewPlanAllotReviewDetail",
  data() {
    return {
      showAll: false,
      url: {
        detail: "/hospital/hos/plan/detail",
        submit: "",
      },
      hospital: {},
      allot: {},
      leader: null,
      userId: null,
      user: {},
      userList: [],
      clauseList: [],
      isIndeterminate: true,
      pageNum: 1,
      pageSize: 10,
      total: 0,
      hosReviewClauseNumInfos: [],
      hospitalForm: {},
      versionId: "",
      iAccountId: "",
      activeName: "",
      anuStatus: "",
      autCode: "",
      traineesList: [],
      revFileId: '',
      fileDetailMap: {},
      allDomainListVOList: [],


      needReplaceReviewer: null,
      candidateList: [],
      candidateId: '',
      replaceVisible: false,
      // 上传
      interest: null,
      uploading: false,
      uploadDom: null,
      reviewerId: null,
      reviewerName: null,
    };
  },

  watch: {
    $route: (to) => {
      if (to.path != "/reviewPlanAllot/reviewPlanAllotReviewDetail") return;
      if (!this && !that) return;
      if (
        to.query.applyNo != that.applyNo ||
        that.applyNo == "" ||
        to.query.personForm != that.personForm ||
        that.personForm == ""
      ) {
        let view = that.$store.state.tagsView.visitedViews.find(
          (view) => view.fullPath == that.$route.fullPath
        );
        if (view) {
          that.$set(
            view,
            "title",
            that.$route.query.personForm == 2
              ? "评审分配详情"
              : "审查分配详情"
          );
        }

        that.getDetail();
      }
    },
  },

  components: {
    ModifyTable
  },

  computed: {
    action() {
      return `${localStorage.baseUrl || process.env.VUE_APP_BASE_API}/system/reviewer/upload/adminInterestFile`;
    },
    revFile() {
      if (!this.revFileId) return false;
      let ids = this.revFileId.split(',');
      let maxId = Math.max(...ids);
      let fileList = this.fileDetailMap[maxId]
      if (!fileList || fileList.length == 0) return false;
      return fileList[0];
    },
    hos() {
      return this.hosReviewClauseNumInfos.filter((hos) => hos.clauseNum >= 0);
    },

    hos2() {
      return this.traineesList.filter(hos => hos.clauseNum === null)
    },

    hosReviewers() {
      let hosReviews = this.hosReviewClauseNumInfos.filter(reviewer => reviewer.accountId)
      // console.log(hosReviews)
      let list = [{}]
      hosReviews.forEach(({ fieldIds, name }) => {
        let data = list[0];
        if (!fieldIds) return [];
        let fieldIdList = fieldIds.split(',')
        if (!fieldIdList) {
          return [];
        }
        fieldIdList.forEach(id => {
          data[id] = name
        })
      })
      return list
    },

    fileName() {
      return id => {
        let list = this.fileDetailMap[id];
        let file = Array.isArray(list) ? list[0] : {}
        return file ? file.fileName : ''
      }
    },

    downLoadFileName() {
      return id => {
        let list = this.fileDetailMap[id];
        let file = Array.isArray(list) ? list[0] : {}
        return file ? file.downLoadFileName : ''
      }
    },
  },

  created() {

  },

  async mounted() {
    const versionId = await this.getEnabledVersionId();
    this.versionId = versionId || this.$store.getters.standardBaseVersionId;

    this.anuStatus = this.$route.query.anuStatus
    this.autCode = this.$route.query.autCode

    that = this;
    let view = this.$store.state.tagsView.visitedViews.find(
      (view) => view.fullPath == this.$route.fullPath
    );
    if (view) {
      this.$set(
        view,
        "title",
        this.$route.query.personForm == 2 ? "评审分配详情" : "审查分配详情"
      );
    }

    this.getDetail();
    this.getDetail2();
  },

  methods: {
    getToken,
    download(fileId) {
      download("/common/downloadFile", { fileId }, this.downLoadFileName(fileId));
    },
    download2(fileId, downLoadFileName) {
      download("/common/downloadFile", { fileId }, downLoadFileName);
    },
    preview(url) {
      window.open(url, '_blank');
    },
    tabClick() {
      this.pageNum = 1;
      this.getClause2();
    },

    toHospitalDetail() {
      this.$router.push({
        path: "/auditManagement/hospitalDetailMessage",
        query: { applyNo: this.$route.query.applyNo },
      });
    },
    handleClick(tab, event) {
      // console.log(tab, event);
    },

    getDetail() {
      this.applyNo = this.$route.query.applyNo;
      this.personForm = this.$route.query.personForm;
      this.hospitalName = this.$route.query.hospitalName;
      return request({
        url: this.url.detail,
        method: "post",
        data: {
          applyNo: this.applyNo,
          versionId: this.versionId,
          personForm: this.personForm,
        },
      }).then((res) => {
        this.hospital = res.hospitalAuthContact;
        this.userList = res.preExamList || [];
        this.fileDetailMap = res.fileDetailMap || {}
        this.hosReviewClauseNumInfos = res.hosReviewClauseNumInfoVOList || [];
        this.allDomainListVOList = res.allDomainListVOList || [];
        this.activeName = this.hosReviewClauseNumInfos[0].accountId;
        this.revFileId = res.revFileId
        this.userId = "";
        this.hospitalForm = {
          shadow: "never",
          list: [
            {
              label: "医院名称",
              value: this.hospitalName,
              type: "text",
            },
            {
              label: "联系人姓名",
              value: res.hospitalAuthContact.authContactName,
              type: "text",
            },
            {
              label: "联系人手机号",
              value: res.hospitalAuthContact.authContactMobile,
              type: "text",
            },
            {
              label: "联系人邮箱",
              value: res.hospitalAuthContact.authContactEmail,
              type: "text",
            },
          ],
        };

        this.getClause2();

        //
        this.showAll = true;
      });
    },

    getDetail2() {
      this.applyNo = this.$route.query.applyNo;
      this.personForm = this.$route.query.personForm;
      this.hospitalName = this.$route.query.hospitalName;
      return request({
        url: this.url.detail,
        method: "post",
        data: {
          applyNo: this.applyNo,
          versionId: this.versionId,
          personForm: 3,
        },
      }).then((res) => {
        this.traineesList = res.hosReviewClauseNumInfoVOList
      });
    },


    getClause2() {
      let accountId = this.activeName;
      request({
        url:
          "/hospital/hos/plan/clause/detail?" +
          `time=${Date.now()}`,
        method: "post",
        data: {
          personForm: this.personForm,
          applyNo: this.applyNo,
          versionId: this.versionId,
          accountId,
        },
      }).then((res) => {
        if (Array.isArray(res.rows) && res.rows[0]) {
          this.versionId = res.rows[0].versionId;
        }
        (this.pageNum1 = 1) &&
          this.$set(this, "clauseList", res.rows.filter(row => row.fitStatus != 1)) &&
          (this.total = res.total);
      });
    },

    urgentReplace({ id }) {
      let hosReviewClauseNumInfoVOList = this.hosReviewClauseNumInfos;
      let reviewer = hosReviewClauseNumInfoVOList.find(item => id == item.fieldIds)
      if (!reviewer) return;
      this.needReplaceReviewer = reviewer;
      this.candidateList = this.allDomainListVOList.find(domain => domain.id == id)?.reviewerList || []
      this.replaceVisible = true;
    },

    cancelReplace() {
      this.candidateList = []
      this.candidateId = ''
      this.needReplaceReviewer = null
      this.replaceVisible = false;
    },

    submitReplace() {
      let data = {
        id: this.needReplaceReviewer.hospitalReviewerId,
        reviewerId: this.candidateId,
        hasInterest: 0,
        interestFileId: '',
        interestDesc: '',
        autCode: this.autCode
      }
      request({
        url: '/system/reviewer/urgent/update/reviewer',
        method: 'post',
        data,
      }).then(res => {
        this.cancelReplace();
        this.getDetail();
        this.getDetail2();
      })
    },

    uploadInterestSheet({accountId, name}) {
      if (!this.uploadDom) {
        this.uploadDom = document.getElementById("interestSheetUpload");
      };
      this.reviewerId = accountId;
      this.reviewerName = name;
      this.uploadDom.click();
    },

    beforeUpload(file) {
      const list = '.JPG,.JPEG,.PNG,.PDF'.split(',').map(accept => accept.toLocaleUpperCase());
      const suffix = '.' + file.name.split('.').pop().toLocaleUpperCase();
      if (!list.includes(suffix)) {
        this.$message({
          type: 'warning',
          message: '请上传指定格式的文件！'
        })
        return false;
      }
      // 当fileSize大于0时才检查文件大小是否符合，为0或者小于0则不校验文件大小
      if (file.size / 1024 / 1024 > 100) {
        this.$message({
          type: 'warning',
          message: `上传的文件大小不得超过100M`
        })
        return false;
      }
      this.uploading = true;
      return true;
    },

    onSuccess(response, file, fileList) {
      if (response.code == 200) {
        let ids = [response.data?.fileId] || []
        if (ids.length) {
          // 管理员上传
          this.shareCreate(ids, this.$store.getters.userId, 'reviewer_evaluation_party', this.allot.hospitalName, this.reviewerName)
          this.getDetail();
        }
      } else {
        this.$message({
          type: 'error',
          message: response.msg || response.message
        })
      }
      this.uploading = false;
    },

    onError(error) {
      this.uploading = false;
      this.$message({
        type: 'error',
        message: error.msg
      })
    },
  },
};
</script>

<style lang='scss' scoped>
.review-plan-allot-detail {
  background-color: transparent !important;

  .box-card+.box-card {
    margin-top: 10px;
  }

  .interest-table {
    ::v-deep .el-button + .el-button {
      margin-left: 0px;
    }
  }

  p {
    margin: 24px 0;
    padding: 0 15px;
    font-size: 14px;
    display: flex;

    span:first-child {
      min-width: 60px;
    }
  }
}
</style>
