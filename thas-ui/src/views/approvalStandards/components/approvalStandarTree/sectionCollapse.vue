<template>
  <el-collapse
    accordion
    class="approvalStandar-tree-section section"
    @change="handleSectionChange"
    v-model.trim="collapse"
  >
    <el-collapse-item
      v-for="(section, index) in sectionVoList"
      :key="index"
      :name="index"
    >
      <template slot="title">
        <i
          class="arrow"
          :class="[
            arrow(index) ? 'el-icon-caret-bottom' : 'el-icon-caret-right',
          ]"
        ></i>
        <label style="font-weight: lighter">节：</label>
        <p>
          <span>{{section.sectionNo}}</span>
          <span>{{section.section}}</span>
        </p>
      </template>
      <div class="section-content">
        <clause-collapse :articleVoList='section.articleVoList'></clause-collapse>
      </div>
    </el-collapse-item>
  </el-collapse>
</template>

<script>
import clauseCollapse from "./clauseCollapse.vue";
export default {
  name: "approvalStandarTreeSection",
  components: {
    clauseCollapse,
  },
  props: {
    sectionVoList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      collapse: "first",
      activeName: "first",
    };
  },
  computed: {
    arrow() {
      return (name) => name === this.activeName;
    },
  },
  methods: {
    handleSectionChange(activeNames) {
      this.activeName = activeNames;
    },
  },
};
</script>

<style lang="scss" scoped>
.approvalStandar-tree .approvalStandar-tree-section.section {
  margin-top: 10px;
  font-size: 16px;
  ::v-deep .el-collapse-item__header {
    background-color: #fff;
    color: #888888;
    font-size: 16px;
    min-height: 58px;
    p {
      color: black;
      display: inline-block;
      margin-left: 46px;
      span + span {
        margin-left: 50px;
      }
    }
    .arrow {
      margin-right: 25px;
      margin-left: 20px;
    }
  }
  .section-content {
    padding-left: 48px;
  }
}
</style>