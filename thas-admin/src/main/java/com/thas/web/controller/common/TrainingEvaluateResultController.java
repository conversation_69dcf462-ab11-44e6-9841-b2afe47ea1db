package com.thas.web.controller.common;

import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;

import com.thas.web.domain.dto.TrainingEvaluateResultReq;
import com.thas.web.domain.vo.TrainingEvaluateResultRes;
import com.thas.web.domain.vo.TrainingEvaluateResultSubmitRes;
import com.thas.web.service.ITrainingEvaluateResultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * 培训评估结果Controller
 *
 * <AUTHOR>
 * @date 2022-09-18
 */
@RestController
@RequestMapping("/training-evaluate-result")
@Api(value = "TrainingEvaluateResultController", tags = "管理端-评审员信息审核列表-培训评估结果相关API")
public class TrainingEvaluateResultController extends BaseController {

    @Autowired
    private ITrainingEvaluateResultService iTrainingEvaluateResultService;

    /**
     * 培训教员做下拉框接口，返回用户为有效的评审员数据
     */
    @ApiOperation("查看评估结果-展示培训教员下拉框为有效的评审员数据")
    @GetMapping(value = "/trainer-valid-data-query")
    public AjaxResult trainerValidDataQuery(@NotNull(message = "角色名称不能为空") @ApiParam(value = "角色名称") String roleName) {
        return AjaxResult.success(iTrainingEvaluateResultService.trainerValidDataQuery(roleName));
    }

    @ApiOperation("理论培训评估结果查询")
    @PostMapping(value = "/theory-query")
    public AjaxResult theoryQuery(@RequestBody @Validated TrainingEvaluateResultRes res) {
        TrainingEvaluateResultReq trainingEvaluateResultReq = iTrainingEvaluateResultService.theoryQuery(res);
        return AjaxResult.success(trainingEvaluateResultReq);
    }

    @ApiOperation("理论培训评估结果提交")
    @PostMapping(value = "/theory-submit")
    public AjaxResult theorySubmit(@RequestBody @Validated TrainingEvaluateResultSubmitRes res) {
        iTrainingEvaluateResultService.theorySubmit(res);
        return AjaxResult.success();
    }

    @ApiOperation("现场评审评估结果查询")
    @PostMapping(value = "/site-review-query")
    public AjaxResult siteReviewQuery(@RequestBody @Validated TrainingEvaluateResultRes res) {
        TrainingEvaluateResultReq req = iTrainingEvaluateResultService.siteReviewQuery(res);
        return AjaxResult.success(req);
    }

    @ApiOperation("现场评审评估结果提交")
    @PostMapping(value = "/site-review-submit")
    public AjaxResult siteReviewSubmit(@RequestBody @Validated TrainingEvaluateResultSubmitRes res) {
        iTrainingEvaluateResultService.theorySubmit(res);
        return AjaxResult.success();
    }

}
