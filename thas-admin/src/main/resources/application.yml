# 项目相关配置
thas:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.1
  # 版权年份
  copyrightYear: 2021
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  #  profile: /Users/<USER>/pvt/ylmo2o/code/IdeaProjects/hku-szh-thas/files
  profile: D:/load/files
  #  profile: /opt/modulars/files
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math
  role:
    admin: common-admin,admin
    sup:
      admin: admin
    inspector: inspector,inspector-leader
    assessor: assessor,assessor-leader,senior-assessor,trainees_assessor
  pdf:
    config: D:/pdf/config

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.thas: debug
    org.springframework: warn
  paths: logs
  max:
    history: 180

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
     multipart:
       # 单个文件大小
       max-file-size:  50MB
       # 设置总上传的文件大小
       max-request-size:  50MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: **************
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password: THAS654321-redis
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
    # 令牌自定义标识
    header: Authorization,Cookie
    # 令牌密钥
    secret: abcdefghijklmnopqrstuvwxyz
    # 令牌有效期（默认30分钟）
    expireTime: 30
    # 是否允许账户多终端同时登录（true允许 false不允许）测试允许，生产不允许
    soloLogin: true

# MyBatis配置
mybatis:
    # 搜索指定包别名
    typeAliasesPackage: com.thas.**.domain
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath*:mapper/**/*Mapper.xml
    # 加载全局的配置文件
    configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: #/dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

email:
  # 测试环境不发送邮箱
  environment: test
  # environment: prod
  SMTPService: smtp.qq.com
  emailNo: <EMAIL>
  password: lrvxnasyiyqtbhia
  #生产邮箱数据
#  SMTPService: smtp.hku-szh.org
#  emailNo: <EMAIL>
#  password:

user:
  resetPassword: abc123
  encryptResetPassword: 52af3ad47de06699f242e35171c56387e01b806906aec5395776019a289622b1

sftp:
  image:
    host: *************
    port: 22
    username: thas-sftp
    password: THAS654321-sftp
    timeout: 5000
    #basePath: /data/sftp/thas-sftp/upload/image
    basePath: /upload/image
    platform: 1
    maxFileSize: 104857600 #100M
  video:
    host: *************
    port: 22
    username: thas-sftp
    password: THAS654321-sftp
    timeout: 5000
    #basePath: /data/sftp/thas-sftp/upload/video
    basePath: /upload/video
    platform: 2
    maxFileSize: 524288000 #500M

# 手机短信测试环境配置(测试不发送)
sms:
  environment: test
 # environment: prod

# CORS跨域配置白名单
cors:
  allowedOrigins: https://thas.ylmo2o.com,http://localhost:1024,http://**************:1020,http://**************:1021,http://**************:1028

# 外部跳转请求文件资源处理-url前缀 ：http://{fileHost}:{filePort}{prefix}{filePath}
outFileDownload:
  prefix: /resource/get
  fileHost: **************
  filePort: 1020
  #fileUrl: https://thas.ylmo2o.com
  fileUrl: http://**************:1020
  #resourceUrl: https://thas.ylmo2o.com
  resourceUrl: http://*************:80

# 加解密算法密钥配置
privacy:
  crypto:
    sm4Key: 43cc4f5dafec671f523f202120c6d69d
    aesKey: 43cc4f5dafec671f523f202120c6d69d
    sm2:
      privateKey: 4adf3b5d584081da413dc27f648e7408d45478567add7853f4de50a9b42782c1
      publicKey: 0422ea057a182ac0b747718fec98065c3132ded0ac46ef67555bb15420d0d0fc0ce96855724e286884bfbcfb4eed64c30ab435336aac137231812f46000d433b25
    # 生产配置
    # sm4Key: ad895bfd417cdda41354f31df9191cc8
    # aesKey: ad895bfd417cdda41354f31df9191cc8
    # sm2:
    #   privateKey: MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgGOlcTI3EyZLmjKhYhnzuULQKkHNalbvfJWPChpCflHigCgYIKoEcz1UBgi2hRANCAARYDhrKuJj60WF3xVKOfNQmIssPb65ofWlENV+mka61SV9BSS2INgriXixkneOjL9eAk7ogVAhzYKYh0hQvckVZ
    #   publicKey: MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEWA4ayriY+tFhd8VSjnzUJiLLD2+uaH1pRDVfppGutUlfQUktiDYK4l4sZJ3joy/XgJO6IFQIc2CmIdIUL3JFWQ==
