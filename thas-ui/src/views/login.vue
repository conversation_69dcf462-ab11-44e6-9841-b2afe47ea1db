<template>
  <div class="login">
    <div class="leftLogin">
      <!-- <p class="leftLogin-title">深圳卫健医院评审评价研究中心</p> -->
      <p class="leftLogin-title">
        <img src="@/assets/logo/logo.png" alt="logo" width="250px">
      </p>
      <h1 class="leftLogin-title">高效、权威的评审系统</h1>
      <h4 class="leftLogin-title">为您提供权威的评审服务</h4>
    </div>
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
      <h3 class="title">欢迎使用深圳卫健医院评审评价平台</h3>
      <el-form-item prop="username">
        <el-input v-model.trim="loginForm.username" type="text" auto-complete="off" placeholder="账号" :maxlength="50">
          <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input v-model.trim="loginForm.password" :maxlength="16" type="password" auto-complete="off" placeholder="密码" @keyup.enter.native="handleLogin">
          <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item v-if="captchaOnOff" prop="code" style="margin-bottom:0px">
        <el-input v-model.trim="loginForm.code" auto-complete="off" placeholder="验证码" :maxlength="6" style="width: 63%" @keyup.enter.native="handleLogin">
          <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" class="login-code-img" />
        </div>
      </el-form-item>
      <!-- <el-checkbox v-model.trim="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox> -->
      <el-form-item style="width: 100%">
        <div style="margin-top: 10px;">
          <router-link class="link-type" :to="'/forgot'">忘记密码</router-link>
          <div>
            <router-link class="link-type" :to="'/application'" style="float:left;">医院申请入口</router-link>
            <router-link class="link-type" :to="'/reviewerApplication'" style="float:right;">评审人员申请入口</router-link>
          </div>
        </div>
        <el-button v-loading="loading" :disabled="loading" size="medium" type="primary" style="width: 100%;" @click.native.prevent="handleLogin">
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
      <!-- <span>Copyright © 2018-2021 ruoyi.vip All Rights Reserved.</span> -->
    </div>
  </div>
</template>

<script>
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";

export default {
  name: "Login",
  data() {
    return {
      codeUrl: "",
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
        code: "",
        uuid: "",
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" },
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" },
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }],
      },
      loading: false,
      // 验证码开关
      captchaOnOff: false,
      // 注册开关
      register: false,
      redirect: undefined,
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },
  created() {
    this.getCode();
    this.getCookie();
    this.getBingConfigKey("whetherMdyPsw").then(res => {
      if (res.msg == 1) {
        localStorage.whetherMdyPsw = 1;
      }
    })
  },
  methods: {
    getCode() {
      getCodeImg().then((res) => {
        this.captchaOnOff =
          res.captchaOnOff === undefined ? true : res.captchaOnOff;
        if (this.captchaOnOff) {
          this.codeUrl = "data:image/gif;base64," + res.img;
          this.loginForm.uuid = res.uuid;
        }
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get("rememberMe");
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password:
          password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), {
              expires: 30,
            });
            Cookies.set("rememberMe", this.loginForm.rememberMe, {
              expires: 30,
            });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove("rememberMe");
          }
          this.$store
            .dispatch("Login", this.loginForm)
            .then(() => {
              const isFirstLogin = localStorage.getItem('isFirstLogin')
              if (isFirstLogin && isFirstLogin === '1') {
                this.$router.replace({ path: '/reset' }).catch(() => { })
              } else {
                // 下载的pdf跳转超链接时若token过期，则重新登陆进入（处理页面刷新问题）
                window.loginToPdf = true;
                this.$router.push({ path: this.redirect || "/" }).catch(() => { });
              }
            })
            .catch(() => {
              this.loading = false;
              if (this.captchaOnOff) {
                this.getCode();
              }
            });
        }
      });
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  // background-image: url("../assets/images/login-background.jpg");
  background-size: cover;
  background-color: #e8e8e8;
}
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
  font-weight: bold;
}

.leftLogin {
  width: 400px;
  height: 359px;
  background-color: #547df2;
  .leftLogin-title {
    color: white;
    text-align: center;
  }
  h1 {
    margin-top: 20px;
  }
  p {
    margin-left: 15px;
    margin-top: 15px;
    margin-bottom: 0;
    text-align: left !important;
  }
  h4 {
    letter-spacing: 1px;
  }
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  height: 359px;
  padding: 25px 25px 5px 25px;
  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.login-code-img {
  height: 38px;
}
</style>
