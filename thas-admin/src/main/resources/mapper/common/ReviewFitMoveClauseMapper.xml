<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.ReviewFitMoveClauseMapper">

    <resultMap type="ReviewFitMoveClause" id="ReviewFitMoveClauseResult">
        <result property="id" column="id"/>
        <result property="autCode" column="aut_code"/>
        <result property="clauseId" column="clause_id"/>
        <result property="fitStatus" column="fit_status"/>
        <result property="moveStatus" column="move_status"/>
        <result property="smallThemeId" column="small_theme_id"/>
        <result property="themeId" column="theme_id"/>
        <result property="groupId" column="group_id"/>
        <result property="status" column="status"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateId" column="update_id"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectReviewFitMoveClauseVo">
        select id, aut_code, clause_id, fit_status, move_status, small_theme_id, theme_id, group_id, status, create_id, create_time, update_id, update_time from review_fit_move_clause
    </sql>

    <select id="selectReviewFitMoveClauseList" parameterType="ReviewFitMoveClause"
            resultMap="ReviewFitMoveClauseResult">
        <include refid="selectReviewFitMoveClauseVo"/>
        <where>
            <if test="autCode != null  and autCode != ''">and aut_code = #{autCode}</if>
            <if test="clauseId != null  and clauseId != ''">and clause_id = #{clauseId}</if>
            <if test="fitStatus != null ">and fit_status = #{fitStatus}</if>
            <if test="moveStatus != null ">and move_status = #{moveStatus}</if>
            <if test="smallThemeId != null ">and small_theme_id = #{smallThemeId}</if>
            <if test="themeId != null ">and theme_id = #{themeId}</if>
            <if test="groupId != null ">and group_id = #{groupId}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="createId != null ">and create_id = #{createId}</if>
            <if test="updateId != null ">and update_id = #{updateId}</if>
        </where>
    </select>

    <select id="selectReviewFitMoveClauseById" parameterType="Long" resultMap="ReviewFitMoveClauseResult">
        <include refid="selectReviewFitMoveClauseVo"/>
        where id = #{id}
    </select>

    <insert id="insertReviewFitMoveClause" parameterType="ReviewFitMoveClause" useGeneratedKeys="true" keyProperty="id">
        insert into review_fit_move_clause
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="autCode != null and autCode != ''">aut_code,</if>
            <if test="clauseId != null and clauseId != ''">clause_id,</if>
            <if test="fitStatus != null">fit_status,</if>
            <if test="moveStatus != null">move_status,</if>
            <if test="smallThemeId != null ">and small_theme_id,</if>
            <if test="themeId != null ">and theme_id,</if>
            <if test="groupId != null ">and group_id,</if>
            <if test="status != null">status,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="autCode != null and autCode != ''">#{autCode},</if>
            <if test="clauseId != null and clauseId != ''">#{clauseId},</if>
            <if test="fitStatus != null">#{fitStatus},</if>
            <if test="moveStatus != null">#{moveStatus},</if>
            <if test="smallThemeId != null "> #{smallThemeId}</if>
            <if test="themeId != null ">#{themeId}</if>
            <if test="groupId != null ">#{groupId}</if>
            <if test="status != null">#{status},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateReviewFitMoveClause" parameterType="ReviewFitMoveClause">
        update review_fit_move_clause
        <trim prefix="SET" suffixOverrides=",">
            <if test="autCode != null and autCode != ''">aut_code = #{autCode},</if>
            <if test="clauseId != null and clauseId != ''">clause_id = #{clauseId},</if>
            <if test="fitStatus != null">fit_status = #{fitStatus},</if>
            <if test="moveStatus != null">move_status = #{moveStatus},</if>
            <if test="smallThemeId != null ">small_theme_id = #{smallThemeId}</if>
            <if test="themeId != null ">theme_id = #{themeId}</if>
            <if test="groupId != null ">group_id = #{groupId}</if>
            <if test="status != null">status = #{status},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteReviewFitMoveClauseById" parameterType="Long">
        delete from review_fit_move_clause where id = #{id}
    </delete>

    <delete id="deleteReviewFitMoveClauseByIds" parameterType="String">
        delete from review_fit_move_clause where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="qryReviewFitMoveClauseList" parameterType="ReviewFitMoveClauseReq"
            resultMap="ReviewFitMoveClauseResult">
        <include refid="selectReviewFitMoveClauseVo"/>
        <where>
            status = 1
            <if test="autCode != null and autCode != ''">and aut_code = #{autCode}</if>
            <if test="fitStatus != null ">and fit_status = #{fitStatus}</if>
            <if test="clauseIdList != null  and clauseIdList.size > 0 ">and clause_id in
                <foreach collection="clauseIdList" open="(" separator="," close=")" index="index" item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="batchInsertReviewFitMoveClause" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into review_fit_move_clause (aut_code, clause_id, fit_status)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.autCode,jdbcType=VARCHAR},
            #{item.clauseId,jdbcType=VARCHAR},
            ifnull(#{item.fitStatus,jdbcType=TINYINT},0)
            )
        </foreach>
    </insert>

    <insert id="moveClauseBatchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into review_fit_move_clause (aut_code, clause_id, move_status, small_theme_id, theme_id, group_id)
        values
        <foreach collection="list" index="index" item="item"  separator=",">
            (
            #{item.autCode},
            #{item.clauseId},
            #{item.moveStatus},
            #{item.smallThemeId},
            #{item.themeId},
            #{item.groupId}
            )
        </foreach>
    </insert>

    <update id="batchUpdateReviewFitMoveClause" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update review_fit_move_clause
            <set>
                <if test="item.autCode != null and item.autCode != ''">aut_code = #{item.autCode, jdbcType=VARCHAR},</if>
                <if test="item.clauseId != null and item.clauseId != ''">clause_id = #{item.clauseId, jdbcType=VARCHAR},</if>
                <if test="item.fitStatus != null">fit_status = #{item.fitStatus, jdbcType=TINYINT},</if>
                <if test="item.moveStatus != null">move_status = #{item.moveStatus},</if>
                <if test="item.smallThemeId != null">small_theme_id = #{item.smallThemeId},</if>
                <if test="item.themeId != null">theme_id = #{item.themeId},</if>
                <if test="item.groupId != null">group_id = #{item.groupId},</if>
                <if test="item.status != null">status = #{item.status, jdbcType=TINYINT},</if>
            </set>
            where id = #{item.id, jdbcType=BIGINT}
        </foreach>
    </update>

</mapper>