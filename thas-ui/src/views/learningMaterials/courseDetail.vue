<template>
  <div class="course-detail">
    <div style="text-align: center;">
      <video v-if="videoSrc" :src="videoSrc" controls preload="auto" poster="" webkit-playsinline="true" playsinline="true" x5-video-player-type="h5" x5-video-player-fullscreen="true" x-webkit-airplay="allow" x5-video-orientation="portraint" style="object-fit:fill" width="750px">
        <!-- <source src="" type="video/mp4">
        <source src="https://www.w3school.com.cn/i/movie.ogg" type="video/ogg; codecs=dirac, speex"> -->
        <p>你的浏览器不支持 <code>video</code> 标签.</p>
      </video>
    </div>
    <el-card class="article">
      <div class="title">{{ title }}</div>
      <div class="date">{{ createTime }}</div>
      <div v-if="videoSrc" class="content">{{ contentDescription }}</div>
      <div v-else class="ql-container ql-snow">
        <div class="ql-editor">
          <div v-html="contentDescription"></div>
        </div>
      </div>
    </el-card>
    <el-card v-if="examPaperId" class="answer">
      <div slot="header" class="clearfix">
        <span class="title">答题</span>
        <span class="count">{{ respondentsNum || 0 }}人已提交</span>
      </div>
      <div class="btns">
        <div class="left">
          <el-button type="primary" icon="el-icon-edit" @click="answer" v-if="hasAnswer == 0">开始答题</el-button>
          <el-button v-else type="primary" icon="el-icon-edit" @click="answerAgain">重新答题</el-button>
        </div>
        <div class="right">
          <div class="detail">
            <div v-if="highestScoreHistory" class="high-score">历史最高分：{{ highestScoreHistory }}分</div>
            <div v-if="hasAnswer != 0" class="to-detail" @click="toDetail">答题详情&nbsp;></div>
          </div>
        </div>
      </div>
    </el-card>
    <el-card class="message">
      <div slot="header" class="clearfix">
        <span class="title">留言&nbsp;{{ messageList && messageList.length ? messageList.length : 0 }}</span>
      </div>
      <div class="send-message">
        <el-avatar size="medium" src=""></el-avatar>
        <el-input v-model.trim="message" type="textarea" style="flex: 1;" :autosize="{ minRows: 5 }" maxlength="512" placeholder="说说你的学习心得吧..."></el-input>
      </div>
      <div class="send-message-btn">
        <el-button type="primary" @click="sendMessage">留言</el-button>
      </div>
      <div v-for="(item, index) in messageList" :key="index" class="message-item">
        <div class="item-title">
          <div class="left">
            <el-avatar size="medium" :src="item.userIcon"></el-avatar>
            <div class="name">{{item.createId}}</div>
          </div>
          <div class="right">
            <span v-if="item.hasLike" style="color: #ff8b2a;">{{item.likeNum}}</span>
            <svg v-if="item.hasLike" fill="#ff8b2a" style="cursor: pointer;" xmlns="http://www.w3.org/2000/svg" class="svg-icon-path-icon fill" viewBox="0 0 32 32" width="18" height="18" @click="like(item)">
              <path d="M10.663 28.441v-15.996c0.54-0.195 0.93-0.362 1.17-0.499 0.672-0.385 1.242-0.909 1.71-1.572 0.71-1.006 0.842-1.47 1.613-4.287 0.051-0.185 0.152-0.438 0.306-0.759 0.708-1.187 1.717-1.78 3.025-1.777 1.264 0 2.275 0.592 3.034 1.777 0.189 0.327 0.323 0.692 0.401 1.096s0.079 0.867 0 1.39l-1.399 4.883c-0.025 0.078-0.025 0.134 0 0.17 0.027 0.039 0.066 0.058 0.117 0.058h4.251l-0.005 0.010h0.554c1.65 0 3 1.3 3 2.95 0 0.25-0.050 0.5-0.1 0.75l-1.496 9.555c-0.3 1.3-1.5 2.25-2.9 2.25h-13.282zM8.885 28.441h-3.556c-0.982 0-1.778-0.796-1.778-1.778v-12.444c0-0.982 0.796-1.778 1.778-1.778h3.556v16z"></path>
            </svg>
            <span v-if="!item.hasLike">{{item.likeNum}}</span>
            <svg v-if="!item.hasLike" style="cursor: pointer;" xmlns="http://www.w3.org/2000/svg" class="svg-icon-path-icon fill" viewBox="0 0 32 32" width="18" height="18" @click="like(item)">
              <path d="M10.663 12.445c0.54-0.195 0.93-0.362 1.17-0.499 0.672-0.385 1.242-0.909 1.71-1.572 0.71-1.006 0.842-1.47 1.613-4.287 0.051-0.185 0.152-0.438 0.306-0.759 0.708-1.187 1.717-1.78 3.025-1.777 1.264 0 2.275 0.592 3.034 1.777 0.189 0.327 0.323 0.692 0.401 1.096s0.079 0.867 0 1.39l-1.399 4.883c-0.025 0.078-0.025 0.134 0 0.17 0.027 0.039 0.066 0.058 0.117 0.058h4.251l-0.005 0.010h0.554c1.65 0 3 1.3 3 2.95 0 0.25-0.050 0.5-0.1 0.75l-1.496 9.555c-0.3 1.3-1.5 2.25-2.9 2.25h-18.615c-0.982 0-1.778-0.796-1.778-1.778v-12.444c0-0.982 0.796-1.778 1.778-1.778l5.333 0.004zM10.663 14.409v12.254h13.261c0.45 0 0.85-0.3 0.95-0.75 1.193-6.368 1.789-9.694 1.789-9.977 0-0.55-0.45-1-1-1h-1.725l-0.009 0.018h-3.238c-0.714 0-1.28-0.245-1.699-0.736-0.628-0.736-0.59-1.383-0.362-2.204 0.521-1.87 0.962-3.489 1.323-4.857-0.010-0.96-0.451-1.5-1.323-1.622s-1.448 0.358-1.728 1.44l-0.745 2.583c-0.853 2.302-2.52 3.855-5.002 4.66l-0.492 0.19zM5.33 26.663h3.556v-12.444h-3.556v12.444z"></path>
            </svg>
          </div>
        </div>
        <div class="item-content">
          <div class="text">{{item.messageContent}}</div>
          <div class="reply">
            <!-- <span style="color: #666666; cursor: pointer;" @click="reply(index)">回复</span>
            <span style="margin: 0 18px; color: #999999;">•</span> -->
            <span style="color: #999999;">{{item.createTime}}</span>
          </div>
          <!-- <div v-for="(reply, i) in item.replyList" :key="i" class="sub-message-item">
            <div class="sub-item-title">
              <div class="sub-left">
                <el-avatar size="medium" src=""></el-avatar>
                <div class="sub-name">{{reply.userName}}</div>
              </div>
            </div>
            <div class="sub-item-content">
              <div class="sub-text">{{reply.content}}</div>
              <div class="sub-reply">
                <span style="color: #999999;">{{reply.time}}</span>
              </div>
            </div>
          </div> -->
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
let that;
import request from "@/utils/request";
export default {
  name: "CourseDetail",
  data() {
    return {
      message: "",
      videoSrc: "",
      title: "",
      createTime: "",
      contentDescription: "",
      messageList: [],
      respondentsNum: null,
      highestScoreHistory: null,
      examPaperId: null,
      hasAnswer: 0,
      flag: false,
    };
  },
  watch: {
    $route: (to) => {
      if (!that) return;
      if (to.path != "/learningMaterials/courseDetail") return;
      that.getResourceDetail();
    },
  },
  created() {
    // that = this
    this.getResourceDetail();
  },
  mounted() {
    that = this;
  },
  methods: {
    // 获取学习资源详情
    getResourceDetail(hasLearn) {
      request({
        url: "/system/resource/" + this.$route.query.id,
        method: "get",
      }).then((res) => {
        if (res.code != 200) return;
        if (res.data.type == 1) {
          this.videoSrc = JSON.parse(res.data.resourceAddress)[0].url || "";
        }
        this.title = res.data.title || "";
        this.createTime = res.data.createTime || "";
        this.contentDescription = res.data.contentDescription
          ? decodeURI(res.data.contentDescription)
          : "";
        this.messageList = res.data.traMessageList || [];
        this.highestScoreHistory = res.data.highestScoreHistory;
        this.hasAnswer = res.data.hasAnswer;
        this.respondentsNum = res.data.respondentsNum || 0;
        this.examPaperId = res.data.examPaperId;
        if (!hasLearn) {
          this.hasLearn(res.data.id);
        }
      });
    },
    // 开始答题
    answer() {
      // 重新答题
      if (!this.hasAnswer != 0) {
        this.answerAgain();
        return;
      }
      request({
        url: "/system/paper/" + this.examPaperId,
        method: "get",
      })
        .then((res) => {
          if (res && res.data) {
            this.$router.push({
              path: "/learningMaterials/answer",
              query: {
                examPaperId: this.examPaperId,
                id: this.$route.query.id,
              },
            });
          } else {
            this.$message({
              type: "warning",
              message: "考卷不存在",
            });
          }
        })
        .catch((error) => {
          console.loh(error);
        });
    },
    // 答题详情
    toDetail() {
      this.$router.push({
        path: "/learningMaterials/myPaper",
        query: { examPaperId: this.examPaperId },
      });
    },
    // 重新答题
    answerAgain() {
      this.$router.push({
        path: "/learningMaterials/answer",
        query: {
          examPaperId: this.examPaperId,
          id: this.$route.query.id,
        },
      });
    },
    // 留言
    sendMessage() {
      if (!this.message) {
        return;
      }
      request({
        url: "/system/message",
        method: "post",
        data: {
          learnResourceId: this.$route.query.id,
          messageContent: this.message,
        },
      }).then((res) => {
        if (res.code != 200) return;
        this.message = "";
        this.getResourceDetail();
      });
    },
    // 回复留言
    reply(index) {
      // console.log("回复留言", index);
    },
    // 点赞
    like(data) {
      if (this.flag) {
        return;
      }
      this.flag = true;
      request({
        url: "/system/message/like",
        method: "get",
        params: {
          messageId: data.id,
          type: data.hasLike == 1 ? "SUB" : "ADD",
        },
      })
        .then((res) => {
          if (res.code != 200) return;
          this.getResourceDetail(true);
          setTimeout(() => {
            this.flag = false;
          }, 500);
        })
        .catch((error) => {
          // console.log(error);
          setTimeout(() => {
            this.flag = false;
          }, 500);
        });
    },
    hasLearn(id) {
      request({
        url: "/system/resource/learn/num/" + id,
        method: "get",
      }).then((res) => {
        if (res.code != 200) return;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.course-detail {
  // max-width: 800px;
  padding: 24px;
  .article {
    margin-bottom: 20px;
    .title {
      font-size: 28px;
      font-weight: bold;
      word-break: break-all;
    }
    .date {
      font-size: 14px;
      line-height: 50px;
      color: #666666;
    }
    .content {
      word-break: break-all;
      ::v-deep img {
        width: 100%;
      }
    }
  }
  .answer {
    margin-bottom: 20px;
    .title {
      margin-right: 14px;
      font-size: 20px;
      font-weight: bold;
    }
    .count {
      font-size: 16px;
      font-weight: bold;
    }
    .btns {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 30px;
      .right {
        display: flex;
        align-items: center;
        .detail {
          margin-right: 20px;
          .high-score {
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: bold;
            color: #101010;
          }
          .to-detail {
            font-size: 14px;
            color: #1683fd;
            cursor: pointer;
          }
        }
      }
    }
  }
  .message {
    .title {
      margin-right: 14px;
      font-size: 20px;
      font-weight: bold;
    }
    .send-message {
      display: flex;
      margin-bottom: 20px;
      .el-avatar {
        margin-right: 15px;
      }
    }
    .send-message-btn {
      text-align: right;
    }
    .message-item {
      margin-top: 20px;
      .item-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .left {
          display: flex;
          align-items: center;
          font-weight: bold;
          .name {
            flex: 1;
            margin-left: 10px;
            font-size: 16px;
          }
        }
        .right {
          display: flex;
          align-items: center;
        }
      }
      .item-content {
        font-size: 14px;
        .text {
          margin-left: 46px;
          margin-top: 10px;
          word-break: break-all;
        }
        .reply {
          margin-left: 46px;
          margin-top: 10px;
        }
      }
    }
    .sub-message-item {
      margin-left: 46px;
      margin-top: 20px;
      .sub-item-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .sub-left {
          display: flex;
          align-items: center;
          font-weight: bold;
          .sub-name {
            flex: 1;
            margin-left: 10px;
            font-size: 16px;
          }
        }
      }
      .sub-item-content {
        font-size: 14px;
        .sub-text {
          margin-left: 46px;
          margin-top: 10px;
          word-break: break-all;
        }
        .sub-reply {
          margin-left: 46px;
          margin-top: 10px;
        }
      }
    }
  }
}
</style>
