<template>
  <div class="audit">
    <div v-if="applyNo && mySignFileId">
      <i class="el-icon-success"></i>
      <h2>平台审核中</h2>
      <p>我们将在7个工作日内审核申请,请留意当前页面变动</p>
      <p>如果通过审核,可提交医院自评资料</p>
    </div>
    <div v-else>
      <el-card class="box-card" shadow="never">
        <div slot="header" class="clearfix" style="text-align: left">
          确认申请
        </div>
        <div style="text-align: left; margin-top: 0">
          <span style="text-decoration: underline">{{
            applyInfo.hospitalName
          }}</span>
          承诺所提供的所有信息、数据及各类资料真实可靠，无瞒报、漏报，并可提供实地考察与复核，特申请国际医疗质量认证。
        </div>
        <br />
        <div style="text-align: left; margin-top: 0">
          期望评审日期为
          <span style="text-decoration: underline">{{ expectReviewDate }}</span>
          。
        </div>
      </el-card>

      <p style="color: #606266">请先下载申请表，确认无误后，盖公章上传</p>
      <div style="margin-top: 20px; margin-bottom: 20px">
        <el-button
          v-if="applyNo"
          type="primary"
          icon="el-icon-download"
          style="margin-right: 12px"
          @click="downloadApplyForm"
          >下载申请表</el-button
        >
      </div>
      <upload
        :accept="`.PDF`"
        :disabled="!isDownload"
        :action="`${uploadUrl}/binTang/common/uploadFile`"
        :files="files"
        :single="true"
        :data="{type:'1', downLoadFileName:`${applyInfo.hospitalName}-认证评审申请表.pdf`}"
        @uploadSuccess="uploadSuccess"
        @fileRemove="fileRemove"
      ></upload>
    </div>
  </div>
</template>

<script>
import { saveAs } from "file-saver";
import { getToken } from "@/utils/auth";
import request, { download2, downloadPDF } from "@/utils/request";
export default {
  props: {
    applyNo: {
      type: String,
      default: "",
    },
    signFileId: {
      type: String,
      default: "",
    },
    applyInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      coverFiles: [],
      files: [],
      mySignFileId: "",
      isDownload: false,
    };
  },
  computed: {
    uploadUrl() {
      return localStorage.baseUrl || process.env.VUE_APP_BASE_API;
    },

    expectReviewDate() {
      if (this.applyInfo.expectReviewDate) {
        let a = this.applyInfo.expectReviewDate.replace("-", " 年 ");
        a = a.replace("-", " 月 ");
        return a + " 日 ";
      }
    },
  },
  created() {
    this.mySignFileId = this.signFileId || "";
  },
  methods: {
    downloadApplyForm() {
      downloadPDF(
        {
          filename: `${this.applyInfo.hospitalName}-认证评审申请表.pdf`,
          ftlTemplateCode: "hos_info_gen",
          applyNo: this.applyNo,
        },
        (res) => {
          this.isDownload = res;
        },
        true
      );
    },
    fileRemove(file, length) {
      this.mySignFileId = "";
    },
    uploadSuccess(response, file) {
      if (response.code != 200)
        return this.$message.error(response.msg || response.message);
      this.mySignFileId = response.data.fileId;
      this.files.length = 0;
      this.files.push(file);

      let url = getToken()
        ? "/hospital/base/info/submit"
        : "/hospital/bing/base/info/submit";

      request({
        url: url,
        method: "post",
        data: {
          hospitalBaseInfo: {
            applyNo: this.applyNo,
            signFileId: response.data.fileId,
            signConfirmStatus: "1"
          },
          signFlag: '1'
        },
      })
        .then((res) => {
          if (res.code == 200) {
            this.$message({
              type: "success",
              message: "上传成功，请等待审核",
            });
            this.bus.$emit(
              "fileIdUpload",
              "signFileId",
              [response.data.fileId],
              true
            );
            this.$emit("componentReport", response.data.fileId);
          } else {
            return;
          }
        })
        .catch((error) => {
          // console.log(error);
          this.loading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.audit {
  text-align: center;
  ::v-deep .el-icon-success {
    color: #f7d067;
    font-size: 85px;
  }
  h2 {
    font-weight: bold;
    margin-top: 30px;
  }
  p {
    margin-bottom: 0;
    font-size: 14px;
    margin-top: 30px;
  }
  p + p {
    margin-top: 0;
    color: #797979;
    font-size: 12px;
  }

  .text {
    font-size: 14px;
  }

  .item {
    margin-bottom: 18px;
  }
}
</style>
