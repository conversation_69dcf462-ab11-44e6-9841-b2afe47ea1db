<template>
  <div class="application-base-info">
    <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="150px">
      <el-form-item label="医疗机构名称" prop="hospitalName">
        <el-input v-model.trim="formData.hospitalName" placeholder="请输入医院名称" clearable :maxlength="50" :style="{ width: '40%' }"></el-input>
      </el-form-item>
      <el-form-item label="医疗机构执业许可证登记号（如适用）" prop="practiceLicenseNo" :rules="practiceLicenseRules" class="toLong">
        <el-input v-model.trim="formData.practiceLicenseNo" placeholder="请输入医疗机构执业许可证登记号（如适用）" clearable :style="{ width: '90%' }" :maxlength="23" @blur="() => { formData.practiceLicenseNo && $emit('getDetail', formData.practiceLicenseNo) }"></el-input>
      </el-form-item>
      <el-form-item label="工商营业执照" prop="businessLicenseId" required>
        <!-- 上传图片/视频时 需要带入参数 type: 1 或者 2 -->
        <!-- 1是图片 2是视频 -->
        <upload :accept='`.JPG,.JPEG,.PNG,.PDF`' :action='`${uploadUrl}/binTang/common/uploadFile`' :files="files" :single="true" :data="params" @uploadSuccess="uploadSuccess" @fileRemove="fileRemove"></upload>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
export default {
  components: {},
  props: {
    initData: {
      type: [Object, null],
      default: () => null
    }
  },
  data() {
    return {
      formData: {
        hospitalName: "",
        practiceLicenseNo: '',
        businessLicenseId: "",
      },
      params: {
        type: '1'
      },
      rules: {
        hospitalName: [
          {
            required: true,
            message: "请输入医院名称",
            trigger: "blur",
          },
        ],
        practiceLicenseNo: [
          {
            required: true,
            message: "请输入医疗机构执业许可证登记号（如适用）",
            validator: this.practiceLicenseNoValid,
            trigger: "blur",
          },
        ],
        businessLicenseId: [
          {
            required: true,
            validator: (rule, value, callback) =>
              this.businessLicenseIdValidator(rule, value, callback),
            trigger: ["change", "blur"],
          },
        ],
      },
      businessLicenseIdAction: "https://jsonplaceholder.typicode.com/posts/",
      files: [],
      regPNo: ''
    };
  },
  computed: {
    uploadUrl() {
      return localStorage.baseUrl || process.env.VUE_APP_BASE_API
    },
    practiceLicenseRules() {
      return [{
        required: true,
        validator: this.practiceLicenseNoValid,
        trigger: ['blur', 'change']
      }]
    },
  },
  watch: {},
  created() { },
  mounted() {
    this.getBingConfigKey('reg.practiceLicenseNo').then(res => {
      this.regPNo = res.msg.trim();
    })
  },
  methods: {
    init(data) {
      let { hospitalBaseInfo = {}, fileDetails = {} } = data;
      for (const key in this.formData) {
        if (Object.hasOwnProperty.call(this.formData, key)) {
          this.$set(this.formData, key, hospitalBaseInfo[key])
        }
      }
      this.files = (fileDetails || {})[this.formData.businessLicenseId] || []
    },

    practiceLicenseNoValid(rule, value, callback) {
      if (!value) callback("请输入医疗机构执业许可证登记号（如适用）")
      if (!this.regPNo) callback();
      if (!new RegExp(this.regPNo).test(value)) callback('请输入正确的医疗机构执业许可证登记号（如适用）')
      callback();
    },

    businessLicenseIdValidator(rule, value, callback) {
      if (!this.formData.businessLicenseId) {
        callback("请上传工商营业执照");
      }
      callback();
    },
    submitForm() {
      this.$refs["elForm"].validate((valid) => {
        if (!valid) return;
        // TODO 提交表单
      });
    },
    resetForm() {
      this.$refs["elForm"].resetFields();
    },

    fileRemove(file, length) {
      if (length == 0) {
        this.$set(this.formData, "businessLicenseId", "");
        this.$refs.elForm.validateField("businessLicenseId");
      }
    },

    uploadSuccess(response, file) {
      if (response.code != 200) {
        return this.$message.error(response.msg || response.message);
      }
      this.$set(this.formData, "businessLicenseId", `${response.data.fileId}`);
      this.files.length = 0;
      this.files.push(file);
      this.$refs.elForm.validateField("businessLicenseId");
      this.bus.$emit('fileIdUpload', 'businessLicenseId', [response.data.fileId])
    },

    async getValue() {
      try {
        if (await this.$refs["elForm"].validate()) {
          return this.formData;
        }
      } catch (error) {
        return null;
      }
    },
  },
};
</script>
<style lang='scss' scoped>
.application-base-info {
  ::v-deep .el-form {
    .toLong .el-form-item__label {
      line-height: 18px;
    }
  }
}
.el-upload__tip {
  line-height: 1.2;
}
</style>
