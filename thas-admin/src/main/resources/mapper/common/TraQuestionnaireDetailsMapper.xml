<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.TraQuestionnaireDetailsMapper">

    <resultMap type="TraQuestionnaireDetails" id="TraQuestionnaireDetailsResult">
        <result property="id" column="id"/>
        <result property="questionnaireId" column="questionnaire_id"/>
        <result property="question" column="question"/>
        <result property="questionOptions" column="question_options"/>
        <result property="type" column="type"/>
        <result property="hasRequired" column="has_required"/>
    </resultMap>

    <sql id="selectTraQuestionnaireDetailsVo">
        select id, questionnaire_id, question, question_options, type, has_required
        from tra_questionnaire_details
    </sql>

    <select id="selectTraQuestionnaireDetailsList" parameterType="TraQuestionnaireDetails"
            resultMap="TraQuestionnaireDetailsResult">
        <include refid="selectTraQuestionnaireDetailsVo"/>
        <where>
            <if test="questionnaireId != null ">and questionnaire_id = #{questionnaireId}</if>
            <if test="question != null  and question != ''">and question = #{question}</if>
            <if test="questionOptions != null  and questionOptions != ''">and question_options = #{questionOptions}</if>
            <if test="type != null ">and type = #{type}</if>
            <if test="hasRequired != null ">and has_required = #{hasRequired}</if>
        </where>
    </select>

    <select id="selectTraQuestionnaireDetailsById" parameterType="Long" resultMap="TraQuestionnaireDetailsResult">
        <include refid="selectTraQuestionnaireDetailsVo"/>
        where id = #{id}
    </select>

    <select id="selectByQuestionnaireId" resultMap="TraQuestionnaireDetailsResult">
        select *
        from tra_questionnaire_details
        where questionnaire_id = #{questionnaireId}
    </select>

    <insert id="insertTraQuestionnaireDetails" parameterType="TraQuestionnaireDetails" useGeneratedKeys="true"
            keyProperty="id">
        insert into tra_questionnaire_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="questionnaireId != null">questionnaire_id,</if>
            <if test="question != null and question != ''">question,</if>
            <if test="questionOptions != null">question_options,</if>
            <if test="type != null">type,</if>
            <if test="hasRequired != null">has_required,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="questionnaireId != null">#{questionnaireId},</if>
            <if test="question != null and question != ''">#{question},</if>
            <if test="questionOptions != null">#{questionOptions},</if>
            <if test="type != null">#{type},</if>
            <if test="hasRequired != null">#{hasRequired},</if>
        </trim>
    </insert>

    <update id="updateTraQuestionnaireDetails" parameterType="TraQuestionnaireDetails">
        update tra_questionnaire_details
        <trim prefix="SET" suffixOverrides=",">
            <if test="questionnaireId != null">questionnaire_id = #{questionnaireId},</if>
            <if test="question != null and question != ''">question = #{question},</if>
            <if test="questionOptions != null">question_options = #{questionOptions},</if>
            <if test="type != null">type = #{type},</if>
            <if test="hasRequired != null">has_required = #{hasRequired},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTraQuestionnaireDetailsById" parameterType="Long">
        delete
        from tra_questionnaire_details
        where id = #{id}
    </delete>

    <delete id="deleteTraQuestionnaireDetailsByIds" parameterType="String">
        delete from tra_questionnaire_details where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByQuestionnaireId" parameterType="Long">
        delete
        from tra_questionnaire_details
        where questionnaire_id = #{questionnaireId}
    </delete>
</mapper>