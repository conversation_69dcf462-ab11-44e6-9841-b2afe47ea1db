<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.CstDomainMapper">

    <resultMap type="CstDomain" id="CstDomainResult">
        <result property="id"    column="id"    />
        <result property="domainName"    column="domain_name"    />
        <result property="groupName"    column="group_name"    />
        <result property="state"    column="state"    />
        <result property="remarks"    column="remarks"    />
        <result property="cstCount"    column="cst_count"    />
        <result property="versionId"    column="version_id"    />
        <result property="status"    column="status"    />
        <result property="groupIdList"    column="group_id_list"    />
        <result property="createId"    column="create_id"    />
        <result property="createTime"    column="create__time"    />
        <result property="updateId"    column="update_id"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCstDomainVo">
        select id,
               domain_name,
               group_name,
               state,
               remarks,
               cst_count,
               version_id,
               status,
               group_id_list,
               create_id,
               create__time,
               update_id,
               update_time
        from cst_domain
    </sql>

    <select id="selectCstDomainList" parameterType="CstDomain" resultMap="CstDomainResult">
        <include refid="selectCstDomainVo"/>
        <where>
            status = '0'
            <if test="domainName != null  and domainName != ''"> and domain_name like concat('%', #{domainName}, '%')</if>
            <if test="groupName != null  and groupName != ''"> and group_name like concat('%', #{groupName}, '%')</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="cstCount != null  and cstCount != ''"> and cst_count = #{cstCount}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createId != null "> and create_id = #{createId}</if>
            <if test="createTime != null "> and create__time = #{createTime}</if>
            <if test="updateId != null "> and update_id = #{updateId}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
        </where>
        order by create__time asc
    </select>

    <select id="selectCstDomainByVersionId" parameterType="Long" resultType="com.thas.web.dto.UnDomainListVO">
        SELECT
        id,
        domain_name domainName,
        group_name groupName
        FROM
        cst_domain
        WHERE
        state = 1
        and status = 0
        AND version_id = #{versionId}
    </select>

    <!--selectGroupNodeByNotInIds-->
    <select id="selectGroupNodeByNotInIds" parameterType="String" resultType="com.thas.web.domain.vo.DomainGroupNode">
        SELECT
        id groupId,
        group_name groupDetail,
        group_id_list groupIdList,
        0 type
        FROM
        cst_domain
        WHERE
        state = 1
        and status = 0
        AND id NOT IN
        <foreach collection="domainIds.split(',')" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by create__time asc
    </select>

    <select id="selectCstDomainGroup" parameterType="String" resultType="com.thas.web.domain.vo.DomainGroupNode">
        SELECT
            id groupId,
            group_name groupDetail,
            group_id_list groupIdList,
            0 type
        FROM
            cst_domain
        <where>
            state = 1
            and status = 0
            <if test="versionId != null and versionId != ''">
                and version_id = #{versionId}
            </if>
            <if test="domainIds != null and domainIds != ''">
                and id in
                <foreach collection="domainIds.split(',')" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>

    </select>

    <select id="selectCstDomainById" parameterType="Long" resultMap="CstDomainResult">
        <include refid="selectCstDomainVo"/>
        where id = #{id}
    </select>

    <select id="selectGroupNodeBydomainIds" resultType="com.thas.web.domain.vo.DomainGroupNode">
        SELECT
        id groupId,
        group_name groupDetail,
        group_id_list groupIdList,
        0 type
        FROM
        cst_domain
        WHERE
        state = 1
        and status = 0
        AND id IN
        <foreach collection="domainIds.split(',')" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>

    <insert id="insertCstDomain" parameterType="CstDomain" useGeneratedKeys="true"  keyProperty="id">
        insert into cst_domain
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="domainName != null and domainName != ''">domain_name,</if>
            <if test="groupName != null and groupName != ''">group_name,</if>
            <if test="state != null">state,</if>
            <if test="remarks != null">remarks,</if>
            <if test="cstCount != null">cst_count,</if>
            <if test="versionId != null and versionId != ''">version_id,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create__time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="domainName != null and domainName != ''">#{domainName},</if>
            <if test="groupName != null and groupName != ''">#{groupName},</if>
            <if test="state != null">#{state},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="cstCount != null">#{cstCount},</if>
            <if test="versionId != null and versionId != ''">#{versionId},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateCstDomain" parameterType="CstDomain">
        update cst_domain
        <trim prefix="SET" suffixOverrides=",">
            <if test="domainName != null and domainName != ''">domain_name = #{domainName},</if>
            <if test="state != null">state = #{state},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="cstCount != null">cst_count = #{cstCount},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="groupIdList != null and groupIdList != ''">group_id_list = #{groupIdList},</if>
            <if test="groupName != null and groupName != ''">group_name = #{groupName},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create__time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCstDomainById" parameterType="Long">
        delete from cst_domain where id = #{id}
    </delete>

    <delete id="deleteCstDomainByIds" parameterType="String">
        delete from cst_domain where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateCstDomainByIds" parameterType="String">
        update cst_domain set status = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateCstDomainByVersionId">
        update cst_domain set status =1 where version_id = #{autCsId} and status = 0
    </update>

</mapper>
