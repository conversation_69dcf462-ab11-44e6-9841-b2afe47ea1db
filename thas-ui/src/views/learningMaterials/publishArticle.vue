// 此组件已废弃
<template>
  <el-card class="publish-article">
    <el-form ref="form" :model="form" :rules="rules" label-position="left" label-width="80px">
      <el-form-item label="文章标题" prop="title">
        <div style="width: 420px; display: flex;">
          <el-input v-model.trim="form.title" maxlength="30" placeholder="请输入文章标题"></el-input>
          <span style="margin-left: 10px;">{{ form.title.length }}/30</span>
        </div>
      </el-form-item>
      <el-form-item label="设置封面" prop="cover">
        <upload :files="coverFiles" :data="{ type: '1' }" single :tip="false" :accept="`.JPG,.JPEG,.PNG`" @uploadSuccess="coverSuccess" @fileRemove="coverRemove"></upload>
      </el-form-item>
      <el-form-item label="文章内容" prop="content">
        <editor v-model.trim="form.content" :minHeight="200" @input="onEditorChange"></editor>
      </el-form-item>
      <el-form-item label="">
        <el-button type="primary" @click="publish">发表</el-button>
        <!-- <el-button plain>存为草稿</el-button> -->
        <el-button @click="back">返回</el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script>
import request from '@/utils/request'
export default {
  name: 'PublishArticle',
  data() {
    var validateContent = (rule, value, callback) => {
      if (value === '<p><br></p>') {
        callback(new Error('请输入文章内容'))
      } else {
        callback()
      }
    }
    return {
      form: {
        title: '',
        cover: '',
        content: ''
      },
      coverFiles: [],
      rules: {
        title: [{ required: true, message: '请输入文章标题', trigger: 'blur' }],
        cover: [{ required: true, message: '请设置封面', trigger: 'change' }],
        content: [
          { required: true, message: '请输入文章内容', trigger: 'blur' },
          { validator: validateContent, trigger: 'blur' }
        ]
      }
    }
  },
  created() {
  },
  methods: {
    coverSuccess(res, file) {
      if (res.code != 200) return
      this.$set(this.form, "cover", res.data.fileId)
      this.coverFiles = [file]
      this.$refs.form.validateField("cover")
    },
    coverRemove(file) {
      this.coverFiles = []
      this.form.cover = ''
      this.$refs.form.validateField('cover')
    },
    onEditorChange(params) {
      setTimeout(() => {
        this.$refs.form && this.$refs.form.validateField('content')
      }, 50)
    },
    publish() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          request({
            url: '/system/community',
            method: 'post',
            data: this.form,
          }).then((res) => {
            if (res.code != 200) return
            this.$store.dispatch("tagsView/delView", this.$route) &&
              this.$router.push({ path: "/learningMaterials/LearningCommunity" })
          })
        } else {
          return false
        }
      })
    },
    back() {
      this.$store.dispatch("tagsView/delView", this.$route) &&
        this.$router.push({ path: "/learningMaterials/learningCommunity" })
    }
  }
}
</script>

<style lang="scss" scoped>
.publish-article {
}
</style>