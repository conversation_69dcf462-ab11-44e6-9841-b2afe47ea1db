<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.TraExamPaperMapper">

    <resultMap type="com.thas.web.domain.vo.TraExamPaperListVO" id="TraExamPaperResult">
        <result property="id" column="id"/>
        <result property="learnResourceId" column="learn_resource_id"/>
        <result property="paperName" column="paper_name"/>
        <result property="learnResourceTitle" column="title"/>
        <result property="paperDiscription" column="paper_discription"/>
        <result property="answerSheetNum" column="answer_sheet_num"/>
        <result property="passScore" column="pass_score"/>
        <result property="status" column="status"/>
        <result property="validFlag" column="valid_flag"/>
        <result property="createId" column="create_id"/>
        <result property="createdTime" column="create_time"/>
    </resultMap>

    <sql id="selectTraExamPaperVo">
        select id,
               learn_resource_id,
               paper_name,
               paper_discription,
               answer_sheet_num,
               pass_score,
               status,
               valid_flag,
               create_id,
               create_time,
               update_id,
               update_time
        from tra_exam_paper
    </sql>

    <select id="selectTraExamPaperList" parameterType="TraExamPaper" resultMap="TraExamPaperResult">
        select tea.*, tlr.title
        from tra_exam_paper tea left join tra_learn_resource tlr on tlr.id = tea.learn_resource_id
        <where>
            tea.valid_flag != 2
            <if test="learnResourceTitle != null and learnResourceTitle != ''">and tlr.title LIKE concat('%',
                #{learnResourceTitle}, '%')
            </if>
            <if test="learnResourceId != null ">and tea.learn_resource_id = #{learnResourceId}</if>
            <if test="paperName != null  and paperName != ''">and tea.paper_name like concat('%', #{paperName}, '%')
            </if>
            <if test="paperDiscription != null  and paperDiscription != ''">and tea.paper_discription =
                #{paperDiscription}
            </if>
            <if test="answerSheetNum != null ">and tea.answer_sheet_num = #{answerSheetNum}</if>
            <if test="passScore != null  and passScore != ''">and tea.pass_score = #{passScore}</if>
            <if test="status != null ">and tea.status = #{status}</if>
            <if test="createId != null  and createId != ''">and tea.create_id = #{createId}</if>
            <if test="createdTime != null ">and tea.create_time = #{createdTime}</if>
            <if test="updateId != null  and updateId != ''">and tea.update_id = #{updateId}</if>
        </where>
    </select>

    <resultMap type="com.thas.web.domain.vo.TraExamDetailsVO" id="toDTO">
        <result property="id" column="id"/>
        <result property="learnResourceId" column="learnResourceId"/>
        <result property="paperName" column="paperName"/>
        <result property="learnResourceTitle" column="learnResourceTitle"/>
        <result property="paperDiscription" column="paperDiscription"/>
        <result property="totalScore" column="totalScore"/>
        <result property="passScore" column="passScore"/>
        <result property="status" column="status"/>
        <result property="validFlag" column="validFlag"/>
        <result property="createId" column="createId"/>
        <result property="createTime" column="createdTime"/>
        <collection property="details" ofType="com.thas.web.domain.TraExamDetails">
            <id property="id" column="detailId"/>
            <result property="examQuestion" column="examQuestion"/>
            <result property="score" column="score"/>
            <result property="type" column="type"/>
            <result property="questionOptions" column="questionOptions"/>
            <result property="correctAnswer" column="correctAnswer"/>
        </collection>
    </resultMap>

    <select id="selectTraExamPaperById" resultMap="toDTO">
        select tep.id                id,
               tep.learn_resource_id learnResourceId,
               tlr.title             learnResourceTitle,
               tep.paper_name        paperName,
               tep.paper_discription paperDiscription,
               tep.total_score       totalScore,
               tep.pass_score        passScore,
               tep.status            status,
               tep.valid_flag        validFlag,
               tep.create_id         createId,
               tep.create_time       createTime,
               ted.id                detailId,
               ted.exam_question     examQuestion,
               ted.score             score,
               ted.type              type,
               ted.question_options  questionOptions,
               ted.correct_answer    correctAnswer
        from tra_exam_paper tep
                 left join tra_learn_resource tlr
                           on tlr.id = tep.learn_resource_id
                 left join tra_exam_details ted on ted.exam_paper_id = tep.id
        where tep.id = #{id} and tep.valid_flag = #{validFlag}
    </select>

    <select id="editTraExamPaperById" resultMap="toDTO">
        select tep.id                id,
               tep.learn_resource_id learnResourceId,
               tlr.title             learnResourceTitle,
               tep.paper_name        paperName,
               tep.paper_discription paperDiscription,
               tep.total_score       totalScore,
               tep.pass_score        passScore,
               tep.status            status,
               tep.valid_flag        validFlag,
               tep.create_id         createId,
               tep.create_time       createTime,
               ted.id                detailId,
               ted.exam_question     examQuestion,
               ted.score             score,
               ted.type              type,
               ted.question_options  questionOptions,
               ted.correct_answer    correctAnswer
        from tra_exam_paper tep
                 left join tra_learn_resource tlr
                           on tlr.id = tep.learn_resource_id
                 left join tra_exam_details ted on ted.exam_paper_id = tep.id
        where tep.id = #{id} and tep.valid_flag in (0, 1)
    </select>



    <select id="selectByLearnResourceIdAndValid" parameterType="Long" resultType="com.thas.web.domain.TraExamPaper">
        <include refid="selectTraExamPaperVo"/>
        where learn_resource_id = #{learnResourceId} and valid_flag = 1
    </select>

    <select id="getIdByLearnSourceId" resultType="java.lang.Long">
        select id from tra_exam_paper where learn_resource_id = #{learnResourceId} and status = 1 and valid_flag = 1
    </select>

    <insert id="insertTraExamPaper" parameterType="TraExamPaper" useGeneratedKeys="true" keyProperty="id">
        insert into tra_exam_paper
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="learnResourceId != null">learn_resource_id,</if>
            <if test="paperName != null">paper_name,</if>
            <if test="paperDiscription != null">paper_discription,</if>
            <if test="answerSheetNum != null">answer_sheet_num,</if>
            <if test="totalScore != null and totalScore != ''">total_score,</if>
            <if test="passScore != null and passScore != ''">pass_score,</if>
            <if test="status != null">status,</if>
            <if test="validFlag != null">valid_flag,</if>
            <if test="createId != null and createId != ''">create_id,</if>
            <if test="createdTime != null">create_time,</if>
            <if test="updateId != null and updateId != ''">update_id,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="learnResourceId != null">#{learnResourceId},</if>
            <if test="paperName != null">#{paperName},</if>
            <if test="paperDiscription != null">#{paperDiscription},</if>
            <if test="answerSheetNum != null">#{answerSheetNum},</if>
            <if test="totalScore != null and totalScore != ''">#{totalScore},</if>
            <if test="passScore != null and passScore != ''">#{passScore},</if>
            <if test="status != null">#{status},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="createId != null and createId != ''">#{createId},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updateId != null and updateId != ''">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateTraExamPaper" parameterType="TraExamPaper">
        update tra_exam_paper
        <trim prefix="SET" suffixOverrides=",">
            <if test="learnResourceId != null">learn_resource_id = #{learnResourceId},</if>
            <if test="paperName != null">paper_name = #{paperName},</if>
            <if test="paperDiscription != null">paper_discription = #{paperDiscription},</if>
            <if test="answerSheetNum != null">answer_sheet_num = #{answerSheetNum},</if>
            <if test="totalScore != null and totalScore != ''">total_score = #{totalScore},</if>
            <if test="passScore != null and passScore != ''">pass_score = #{passScore},</if>
            <if test="status != null">status = #{status},</if>
            <if test="validFlag != null">valid_flag = #{validFlag},</if>
            <if test="createId != null and createId != ''">create_id = #{createId},</if>
            <if test="createdTime != null">create_time = #{createdTime},</if>
            <if test="updateId != null and updateId != ''">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateAnswerSheetNum">
        update tra_exam_paper set
        <choose>
            <when test="action = 'ADD'">answer_sheet_num = answer_sheet_num + 1</when>
            <otherwise>answer_sheet_num = answer_sheet_num - 1</otherwise>
        </choose>
        where id = #{id}
    </update>
    <update id="updateStatus">
        update tra_exam_paper set status = #{status}, valid_flag = #{validFlag} where id = #{id};
    </update>

    <delete id="deleteTraExamPaperById" parameterType="Long">
        delete
        from tra_exam_paper
        where id = #{id}
    </delete>

    <delete id="deleteTraExamPaperByIds" parameterType="String">
        update tra_exam_paper set valid_flag = 2 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>