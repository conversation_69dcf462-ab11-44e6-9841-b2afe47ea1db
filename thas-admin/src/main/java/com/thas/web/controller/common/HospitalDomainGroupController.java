package com.thas.web.controller.common;


import cn.hutool.json.JSONUtil;
import com.thas.web.domain.CstDomainVO;
import com.thas.web.domain.vo.DomainGroupNode;
import com.thas.web.service.IHospitalDomainGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;

import java.util.List;
import java.util.Map;


/**
 * 一个领域对应一个分组
 * 领域对应分组Controller
 *
 * <AUTHOR>
 * @date 2022-04-22
 */
@Slf4j
@RestController
@RequestMapping("/group")
public class HospitalDomainGroupController extends BaseController {

    @Autowired
    private IHospitalDomainGroupService hospitalDomainGroupService;

    /**
     * 新增领域对应分组 测试
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody CstDomainVO hospitalDomainGroup) {
        log.info("添加分组详情入参:{}", JSONUtil.toJsonStr(hospitalDomainGroup));
        hospitalDomainGroupService.insertHospitalDomainGroup(hospitalDomainGroup);
        return AjaxResult.success();
    }

    /**
     * 修改领域对应分组 测试
     */
    @PostMapping("/update")
    public AjaxResult update(@RequestBody CstDomainVO hospitalDomainGroup) {
        log.info("修改分组详情入参:{}", JSONUtil.toJsonStr(hospitalDomainGroup));
        hospitalDomainGroupService.updateHospitalDomainGroup(hospitalDomainGroup);
        return AjaxResult.success();
    }

    /**
     * 修改领域对应分组 测试
     */
    @PostMapping("/select")
    public AjaxResult select(@RequestBody Map<String, String> param) {
        log.info("查找分组详情入参:{}", JSONUtil.toJsonStr(param));
        List<DomainGroupNode> domainGroupNodes = hospitalDomainGroupService.selectDomainGroup(null, null);
        return AjaxResult.success(domainGroupNodes);
    }
}
