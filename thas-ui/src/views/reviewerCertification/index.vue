<template>
  <div class="reviewer-certification-application">
    <el-steps :active="active" finish-status="success">
      <el-step title="基本信息"></el-step>
      <el-step title="工作简历"></el-step>
      <el-step title="评审/学习经历"></el-step>
      <el-step title="资质材料"></el-step>
      <!-- <el-step title="线下培训"></el-step>
      <el-step title="线上学习"></el-step> -->
      <el-step title="提交审核"></el-step>
      <el-step title="认证审核结果"></el-step>
    </el-steps>
    <!-- <el-button type="primary" v-if="active == 0" @click="commonIdCheckVisible = true" style="margin-left:48px">查询申请进度</el-button> -->

    <div v-if="active == 0" style="width:80%;margin-bottom:50px;">
      <el-form class="commonIdForm" ref="commonIdForm" :model="checkForm" label-width="360px">
        <el-form-item :label="isReApplication ? '如需获取原有申请数据，请输入证件号码进行获取:' : '查询申请进度'">
          <el-form-item prop="select" :rules="selectRule" style="display:inline-block;">
            <el-select v-model="checkForm.select" placeholder="请选择证件类型" style="width:150px" clearable filterable>
              <el-option v-for="(cer, index) in certificateTypeOptions" :key="index" :label="cer.dictLabel"
                :value="cer.dictValue"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="commonId" :rules="commonIdRule" style="display:inline-block;margin-left:5px;">
            <el-input placeholder="请输入证件号" v-model="checkForm.commonId" class="input-with-select">
            </el-input>
          </el-form-item>
          <el-button v-if="isReApplication" type="primary" style="margin-left:10px;" @click="getReDetail">获取</el-button>
          <el-button v-else type="primary" style="margin-left:10px;" @click="checkInfoProgress">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div v-if="active == 0" style="width:95%;margin:0 auto;">
      <el-divider content-position="left" style="margin:0 auto; width:90%">{{ isReApplication ? '评审人员重新申请' : '评审人员申请' }}</el-divider>
    </div>
    <keep-alive>
      <component :key="active" :is="components[active]" :ref="components[active]" :refName="components[active]"
        :info="getInfo" @reWrite="reWrite" :experiences="getExperience"></component>
    </keep-alive>
    <div style="text-align: center">
      <el-button @click="backToMain">返回</el-button>
      <el-button v-if="active > 0 && active < 4" @click="active--" type="primary">上一步</el-button>
      <!-- <el-button v-if="active < 4" @click="saveAsDraft">保存草稿</el-button> -->
      <el-button @click="next" v-loading="submitting" type="primary" v-if="active < 4">下一步</el-button>

    </div>
  </div>
</template>

<script>
import BaseInfo from "./components/baseInfo.vue";
import WorkExperience from "./components/workExperience.vue";
import StudyExperience from "./components/studyExperience.vue";
import QualificationCertificate from "./components/qualificationCertificate.vue";
import TrainingRecords from "./components/trainingRecords.vue";
import Audit from "./components/audit.vue";
import TestScores from "./components/testScores.vue";
import Pass from "./components/pass.vue";

import request from "@/utils/request";
import store from "@/store";
import { getToken } from '@/utils/auth'

export default {
  name: "ReviewerCertificationApplication",
  activated() {
    // if (this.active == 5 || this.active == 4) {
    //   this.getComponent().query();
    // }
  },
  components: {
    BaseInfo,
    WorkExperience,
    StudyExperience,
    QualificationCertificate,
    TrainingRecords,
    Audit,
    TestScores,
    Pass,
  },
  beforeRouteLeave(to, from, next) {
    if (!this.submitted && this.active != 5 && this.active != 4) {
      let isSubmit = confirm(
        "当前页面未提交，离开后数据将重制，是否确定要离开？"
      );
      if (isSubmit) {
        next(true);
      } else {
        next(false);
      }
    } else {
      next(true);
    }
  },
  data() {
    return {
      components: [
        "BaseInfo",
        "WorkExperience",
        "StudyExperience",
        "QualificationCertificate",
        "Audit",
        "Pass",
      ],
      active: 0,
      url: "/reviewer/bing/base/info/submit",
      tUrl: '/reviewer/base/info/submit',
      certificateAbilityList: [],
      certificateList: [],
      reviewerBaseInfo: {
        accountId: store.getters.userId,
      },
      submitStatus: 2,
      workExperienceList: [],
      detail: {},
      checkForm: {
        select: '',
        commonId: ''
      },
      regIdMap: {},
      certificateTypeOptions: [],
      commonIdCheckVisible: false,
      submitted: false,

      fileIdMap: {},
      isReApplication: false,
      queryRevInfoCode: '',

      component_info: {
        "BaseInfo": "getBaseInfo",
        "WorkExperience": "getExperience",
        "StudyExperience": "getStudyEx",
        "QualificationCertificate": "getQualification",
      },
      refreshKey: 0,
      reviewer: {},
      submitting :false,

      waitSubmitData: {}
    };
  },

  computed: {
    getInfo() {
      if (this.isReApplication) {
        if (this.components[this.active] === 'BaseInfo') {
          return this.getBaseInfo;
        } else if (this.components[this.active] === 'StudyExperience') {
          return this.getStudyEx;
        } else if (this.components[this.active] === 'QualificationCertificate') {
          return this.getQualification;
        } else {
          return {
            authStatus: this.detail.authStatus
          }
        }
        return
      }
      return {
        authStatus: this.detail.authStatus
      };
    },

    commonIdRule() {
      return [{
        required: true,
        validator: this.commonIdValid,
        trigger: ['blur', 'change']
      }]
    },
    selectRule() {
      return [{
        required: true,
        validator: this.selectValid,
        trigger: ['blur', 'change']
      }]
    },

    getBaseInfo() {
      let { majorField = '', headPortrait, majorDirection, ...baseInfo } = this.reviewer?.reviewerBaseInfo || {}
      let majorFields = (majorField || '').split(',');
      let majorDirectionMap = {}
      if (majorDirection) {
        majorDirectionMap = JSON.parse(majorDirection)
      }
      return {
        fileDetails: this.reviewer?.fileDetails || {},
        headPortrait,
        majorField,
        majorFields,
        majorDirectionMap,
        ...JSON.parse(JSON.stringify(baseInfo))
      }
    },

    getExperience() {
      let workExperienceList = JSON.parse(JSON.stringify(this.reviewer?.workExperienceList || []))
      return workExperienceList
    },

    getAuditInfo() {
      let {
        hosRevIs,
        learnExperience,
        hosRevDetail,
        organization,
        reviewHospitalIs,
        reviewHospitalCoach,
        coachHospitalNum,
        trainReviewerIs,
        trainNum,
        trainOrganization,
        otherExperience,
        reviewHospitalYear,
        reviewHospitalNum,
        reviewMajor,
        trainWarning,
      } = this.reviewer?.reviewerBaseInfo || {};
      return {
        hosRevIs,
        learnExperience,
        hosRevDetail,
        organization,
        reviewHospitalIs,
        reviewHospitalCoach,
        coachHospitalNum,
        trainReviewerIs,
        trainNum,
        trainOrganization,
        otherExperience,
        reviewHospitalYear,
        reviewHospitalNum,
        reviewMajor,
        trainWarning,
      }
    },

    getStudyEx() {
      let checkList = (this.reviewer?.workExperienceList || []).map(({ abilityDictValue }) => Number(abilityDictValue))
      let organization_zero_label = (this.reviewer?.workExperienceList || []).find(({ abilityDictValue }) => abilityDictValue == 0)?.abilityName

      let hosRevDetail = JSON.parse(this.reviewer?.reviewerBaseInfo?.hosRevDetail || '[]');
      let reviewerTypeCheckList = hosRevDetail.map(({ reviewerTypeValue }) => reviewerTypeValue - 0)
      let reviewer_zero_hos_result = hosRevDetail.find(({ reviewerTypeValue }) => reviewerTypeValue == 0)?.hosRevOther || ''
      let auditInfo = {
        ...this.getAuditInfo
      };
      for (const key in auditInfo) {
        if (Object.hasOwnProperty.call(auditInfo, key)) {
          if ((key.includes('Num') || key.includes('Year')) && !auditInfo[key]) {
            this.$set(auditInfo, key, undefined)
          }
        }
      }
      return {
        reviewerTypeCheckList,
        organization_zero_label,
        reviewer_zero_hos_result,
        checkList,
        ...auditInfo
      }
    },

    getQualification() {
      return {
        list: this.reviewer.certificateList || []
      }
    }
  },

  created() {
    if (this.$route.path.includes('/re-reviewerApplication/')) {
      this.queryRevInfoCode = this.$route.path.split('/').pop()
    }
    this.isReApplication = this.$route.fullPath.includes('re-reviewerApplication')
  },

  mounted() {
    this.getBingConfigKey("reg.idCard").then(response => {
      this.regIdMap['1'] = response.msg.trim();
    });
    this.getBingConfigKey("reg.passport").then(response => {
      this.regIdMap['2'] = response.msg.trim();
    });
    this.getBingConfigKey("reg.reentry").then(response => {
      this.regIdMap['3'] = response.msg.trim();
    });
    this.getBingConfigKey("reg.hkCard").then(response => {
      this.regIdMap['4'] = response.msg.trim();
    });
    this.getBingConfigKey("reg.moCard").then(response => {
      this.regIdMap['5'] = response.msg.trim();
    });
    this.getBingConfigKey("reg.twCard").then(response => {
      this.regIdMap['6'] = response.msg.trim();
    });

    this.getDicts("certificate_type").then((response) => {
      this.certificateTypeOptions = response.data;
    });

    this.bus.$on('fileIdUpload', (key, ids, success) => {
      this.fileIdMap[key] = ids;
    })
  },

  methods: {
    getDetail(needRefresh) {
      request({
        url: "/reviewer/bing/query/submit/status",
        method: "post",
        data: {
          commonId: store.getters.userId + "",
        },
      }).then((res) => {
        this.detail = res || {};
        if (res && res.reviewerBaseInfo) {
          if (!needRefresh) return;
          if (res.infoStatus != 0 && res.infoStatus != null) {
            this.active = 7;
          } else if (res.infoStatus && res.reviewerBaseInfo.submitStatus == 1) {
            this.active = 6;
          }
        }
      });
    },

    backToMain() {
      if (getToken()) {
        this.$router.push({
          path: "/index",
        });
      } else {
        this.$router.push({
          path: "/login",
        });
      }
    },

    reWrite() {
      this.active = 0;
    },

    splitDetail() { },

    getComponent() {
      return this.$refs[this.components[this.active]];
    },

    selectValid(rule, value, callback) {
      if (!value) callback('请选择证件类型');
      callback();
    },

    commonIdValid(rule, value, callback) {
      let regString = this.regIdMap[this.checkForm.select]
      if (!value) callback('请输入证件号')
      else if (!regString) callback('获取校验规则失败，请刷新页面重试!')
      else if (regString) {
        let reg = new RegExp(regString);
        if (!reg.test(value)) callback('请输入正确的证件号')
        callback()
      }
      else callback()
    },

    checkInfoProgress() {
      // console.log(this.checkForm);
      this.$refs.commonIdForm.validate((valid) => {
        // console.log(valid);
        if (!valid) return;
        request({
          url: '/reviewer/bing/query/submit/status',
          data: this.checkForm,
          method: 'post'
        }).then(res => {
          // 0 1 2
          let { authStatus } = res.data;
          this.active = 5
          this.detail = {
            authStatus
          }
        }).finally(() => this.commonIdCheckVisible = false)
      })
    },

    getReDetail() {
      this.$refs.commonIdForm.validate((valid) => {
        if (!valid) return;
        request({
          url: '/reviewer/bing/qry-reviewer-detail',
          method: 'post',
          data: {
            queryRevInfoCode: this.queryRevInfoCode,
            certificateNumber: this.checkForm.commonId
          }
        }).then(res => {
          this.reviewer = res
          this.refreshKey++
        })
      })

    },

    async toSave(value, toNext) {
      if (!value) return;
      try {
        if (this.active === 0) {
          const { fields, ...data } = value;
          this.reviewerBaseInfo = {
            accountId: store.getters.userId,
            ...this.reviewerBaseInfo,
            ...data,
          };

          // await this.save({
          //   reviewerBaseInfo: this.reviewerBaseInfo,
          //   fields,
          // });
          // this.waitSubmitData = {
          //   reviewerBaseInfo: this.reviewerBaseInfo,
          //   fields,
          // }
        } else if (this.active === 1) {
          this.workExperienceList = [...value];
          // this.waitSubmitData = {
          //   ...this.waitSubmitData,
          //   reviewerBaseInfo: this.reviewerBaseInfo,
          //   workExperienceList: this.workExperienceList,
          // }
          // await this.save({
          //   reviewerBaseInfo: this.reviewerBaseInfo,
          //   workExperienceList: this.workExperienceList,
          // });
        } else if (this.active === 2) {
          const { organizationList, hosRevDetail, data } = value;
          this.certificateAbilityList = [...organizationList];
          this.reviewerBaseInfo = {
            ...this.reviewerBaseInfo,
            ...data,
            hosRevDetail: JSON.stringify(hosRevDetail),
          };
          // this.waitSubmitData = {
          //   ...this.waitSubmitData,
          //   certificateAbilityList: this.certificateAbilityList,
          //   reviewerBaseInfo: this.reviewerBaseInfo,
          // }
          // await this.save({
          //   certificateAbilityList: this.certificateAbilityList,
          //   reviewerBaseInfo: this.reviewerBaseInfo,
          // });
        } else if (this.active == 3) {
          this.certificateList = [...value];
          this.waitSubmitData = {
            ...this.waitSubmitData,
            reviewerBaseInfo: this.reviewerBaseInfo,
            certificateList: this.certificateList,
          }
          // await this.save({
          //   reviewerBaseInfo: this.reviewerBaseInfo,
          //   certificateList: this.certificateList,
          // });
        }
        if (!toNext) return;
        if (++this.active >= 4) {
          const data = {
            certificateAbilityList: this.certificateAbilityList,
            certificateList: this.certificateList,
            reviewerBaseInfo: {
              ...this.reviewerBaseInfo,
              submitStatus: 1
            },
            workExperienceList: this.workExperienceList,
            infoStatus: this.detail.infoStatus == 2 ? 0 : undefined,
          };
          this.submitting = true;
          let res = this.isReApplication ? await this.reSave(data, 1) : await this.save(data, 1);
          // console.log(res, res.msg);
          this.submitted = true;
          this.submitting = false;
          let fileList = Object.values(this.fileIdMap)
          let idList = [].concat.apply([], fileList)
          if (idList.length > 0) {
            // 评审学员上传
            this.shareCreate(idList, res.msg, 'reviewer_selection_appointment', '', '',['trainees_assessor'], false)
          }
        }
      } catch (error) {
        this.submitting = false;
        // console.log(error);
      }
    },

    async next() {
      const component = this.getComponent();
      const value = await component.getValue();
      this.toSave(value, 1)
    },

    saveAsDraft() {
      const component = this.getComponent();
      const value = component.getDraft();
      this.toSave(value)
    },

    async reSave(data, last) {
      if (!last) {
        delete data.reviewerBaseInfo.submitStatus;
      }
      if (last) {
        data.reviewerBaseInfo.authStatus = 0
        data.reviewerBaseInfo.infoStatus = 0
      }
      return request({
        url: '/reviewer/bing/base-info-again-submit',
        method: "post",
        data: {
          ...data,
          tempRevInfo: {
            queryRevInfoCode: this.queryRevInfoCode,
            certificateNumber: this.checkForm.commonId
          }
        }
      })
    },

    async save(data, last) {
      if (!last) {
        delete data.reviewerBaseInfo.submitStatus;
      }
      return request({
        url: getToken() ? this.tUrl : this.url,
        method: "post",
        data,
      })
    },
  },
};
</script>

<style lang='scss' scoped>
.reviewer-certification-application {
  padding-top: 40px;
  padding-bottom: 10px;
  background-color: #fff;
  min-height: inherit;

  ::v-deep .el-steps {
    width: 70%;
    margin: 0 auto 40px auto;

    .el-step__icon {
      width: 40px;
      height: 40px;
    }

    .el-step__head.is-process {
      color: #357cf4;
      border-color: #357cf4;

      &+.el-step__main .el-step__title {
        color: #357cf4;
      }
    }

    .el-step__head.is-success {
      color: #357cf4;
      border-color: #357cf4;

      &+.el-step__main .el-step__title {
        color: #357cf4;
      }

      .el-icon-check {
        font-weight: bold;
        font-size: 20px;
        color: white;
      }

      .el-step__icon.is-text {
        background-color: #357cf4;
      }
    }

    .el-step.is-horizontal .el-step__line {
      top: 19px;
    }
  }
}
</style>
