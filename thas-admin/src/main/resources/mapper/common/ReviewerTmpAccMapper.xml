<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.ReviewerTmpAccMapper">
    
    <resultMap type="ReviewerTmpAcc" id="ReviewerTmpAccResult">
        <result property="certificateNumber"    column="certificate_number"    />
        <result property="accountId"    column="account_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectReviewerTmpAccVo">
        select certificate_number, account_id, create_time, update_time from reviewer_tmp_acc
    </sql>

    <select id="selectReviewerTmpAccList" parameterType="ReviewerTmpAcc" resultMap="ReviewerTmpAccResult">
        <include refid="selectReviewerTmpAccVo"/>
        <where>  
            <if test="accountId != null  and accountId != ''"> and account_id = #{accountId}</if>
        </where>
    </select>
    
    <select id="selectReviewerTmpAccByCertificateNumber" parameterType="String" resultMap="ReviewerTmpAccResult">
        <include refid="selectReviewerTmpAccVo"/>
        where certificate_number = #{certificateNumber}
    </select>
        
    <insert id="insertReviewerTmpAcc" parameterType="ReviewerTmpAcc">
        insert into reviewer_tmp_acc
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="certificateNumber != null">certificate_number,</if>
            <if test="accountId != null and accountId != ''">account_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="certificateNumber != null">#{certificateNumber},</if>
            <if test="accountId != null and accountId != ''">#{accountId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateReviewerTmpAcc" parameterType="ReviewerTmpAcc">
        update reviewer_tmp_acc
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountId != null and accountId != ''">account_id = #{accountId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where certificate_number = #{certificateNumber}
    </update>

    <delete id="deleteReviewerTmpAccByCertificateNumber" parameterType="String">
        delete from reviewer_tmp_acc where certificate_number = #{certificateNumber}
    </delete>

    <delete id="deleteReviewerTmpAccByCertificateNumbers" parameterType="String">
        delete from reviewer_tmp_acc where certificate_number in 
        <foreach item="certificateNumber" collection="array" open="(" separator="," close=")">
            #{certificateNumber}
        </foreach>
    </delete>
</mapper>