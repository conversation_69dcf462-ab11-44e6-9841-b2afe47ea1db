<template>
  <div class="certificates">
    <el-form ref="certiForm" label-width="140px" :model="formData" :rules="rules">
      <el-form-item prop="expectReviewDate" label="期望评审日期">
        <el-date-picker v-model.trim="formData.expectReviewDate" value-format="yyyy-MM-dd" format="yyyy-MM-dd" type="date"
          :picker-options="{
            disabledDate: (time) => time.getTime() < Date.now() - 24 * 60 * 60 * 1000,
          }" placeholder="请选择期望评审日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="评审经历" prop="first">
        <el-radio-group v-model.trim="formData.first" @change="firstChange">
          <el-radio v-for="item in experienceOptions" :key="item.dictValue" :label="item.dictValue">
            {{ item.dictLabel }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formData.first == 2" prop="beforeReviewerDate" label="上次等级评审时间">
        <el-date-picker v-model.trim="formData.beforeReviewerDate" value-format="yyyy-MM" format="yyyy-MM" type="month"
          placeholder="请选择上次评审时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item v-if="formData.first == 2" prop="beforeReviewerConclusion" label="上次评审结论">
        <div v-for="level in hospitalLevelOptions" :key="level.dictValue">
          {{ level.dictLabel }} :
          <el-radio-group v-model.trim="formData.beforeReviewerConclusion" @change="conclusionChange">
            <el-radio v-for="item in hospitalClassOptions" :key="item.dictValue"
              :label="`${level.dictValue}-${item.dictValue}`">{{ item.dictLabel }}</el-radio>
          </el-radio-group>
        </div>
      </el-form-item>

      <el-form-item class="certification" prop="hasIntnPro" label="是否参加过国际认证项目">
        <el-radio-group v-model.trim="formData.hasIntnPro">
          <el-radio label="1">是</el-radio>
          <el-radio label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item class="certification" v-if="formData.hasIntnPro == 1" prop="certificateString"
        label="请说明您目前持有的任何其他类型的认证" style="width: fit-content;">
        <el-checkbox-group v-model.trim="checkList" @change='certificatesChange'>
          <span v-for="(cert, index) in formData.certificates" :key="cert.abilityDictValue" style="display:flex">
            <!-- :disabled="noneDisabled(cert.abilityDictValue)" -->
            <el-checkbox :label="cert.abilityDictValue" style="display: block; margin: 5px 0">
              {{ (cert.abilityDictValue === 'fail' && checkList.indexOf('fail') > -1) ? '' : cert.dictLabel }}
            </el-checkbox>
            <div v-if="showDate(cert.abilityDictValue)" style="display: inline-block; margin-left:10px; margin-top:5px">
              <div v-if="cert.abilityDictValue === 'fail'" style="display: inline-block; margin-right: 5px">
                <el-form-item :prop="`certificates.${index}.expireDate`" :rules="rules.expireDate"
                  style="display: inline-block">
                  <el-date-picker v-model.trim="cert.expireDate" type="month" format="yyyy-MM" value-format="yyyy-MM"
                    placeholder="请选择日期" style="margin-right: 5px">
                  </el-date-picker>
                  <span>参加</span>
                </el-form-item>
              </div>
              <div v-if="cert.abilityDictValue == 'otherAuthDueDate'" style="display: inline-block;">
                <el-form-item :prop="`certificates.${index}.abilityName`" :rules="rules.abilityName"
                  style="display: inline-block">
                  <el-input v-model.trim="cert.abilityName" type="text" :maxlength="50" placeholder="请填写相关认证"
                    style="width: 250px; margin-right: 5px" />
                  <span>，</span>
                </el-form-item>
              </div>
              <span v-if="cert.abilityDictValue !== 'otherAuthDueDate' && cert.abilityDictValue !== 'fail'">，</span>
              <el-form-item v-if="cert.abilityDictValue !== 'fail'" :rules="rules.expireDate" class="certTime"
                :prop="`certificates.${index}.expireDate`" style="display: inline-block">
                <el-date-picker v-model.trim="cert.expireDate" type="month" format="yyyy-MM" value-format="yyyy-MM"
                  placeholder="请选择到期日期">
                </el-date-picker>
                <span style="pointer-events: none;"> 到期</span>
              </el-form-item>
              <el-form-item v-if="cert.abilityDictValue === 'fail'" class="certTime"
                :prop="`certificates.${index}.abilityName`" style="display: inline-block" :rules="rules.abilityName">
                <el-input v-model.trim="cert.abilityName" type="text" :maxlength="50" placeholder="请填写相关认证"
                  style="width: 250px;" />
                <span style="pointer-events: none;">国际认证项目，未通过</span>
              </el-form-item>
            </div>
          </span>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "Certificates",
  props: {
    initData: {
      type: [Object, null],
      default: () => null
    }
  },
  data() {
    return {
      formData: {
        first: '2',
        beforeReviewerDate: undefined,
        beforeReviewerConclusion: undefined,
        beforeReviewerConclusionLevel: undefined,
        beforeReviewerConclusionGrade: undefined,
        certificates: [],
        certificateString: "",
        expectReviewDate: '',
        hasIntnPro: undefined,
      },
      rules: {
        first: [
          {
            required: true,
            message: "请选择评审信息",
            trigger: ["change", "blur"],
          },
        ],
        hasIntnPro: [{
          required: true,
          message: "请确认是否参加过国际认证项目",
          trigger: ["change", "blur"],
        }],
        beforeReviewerDate: [
          {
            required: true,
            message: "请选择上次评审时间",
            trigger: ["change", "blur"],
          },
        ],
        expectReviewDate: [
          {
            required: true,
            message: "请选择期望评审日期",
            trigger: ["change", "blur"],
          },
        ],
        beforeReviewerConclusion: [
          {
            required: true,
            message: "请选择上次评审结论",
            trigger: ["change", "blur"],
          },
        ],
        certificateString: [
          {
            required: true,
            validator: (rule, value, callback) =>
              this.certificateValidate(callback),
            trigger: ["change", "blur"],
          },
        ],
        expireDate: [
          {
            required: true,
            message: "请选择到期日期",
            trigger: ["change", "blur"],
          },
        ],
        abilityName: [
          {
            required: true,
            message: "请输入认证名称",
            trigger: ["change", "blur"],
          },
        ],
      },
      files: [],
      experienceOptions: [],
      hospitalLevelOptions: [],
      hospitalClassOptions: [],
      certificationOptions: [],
      checkList: [],
      certificatesTmp: []
    };
  },
  watch: {
    checkList: {
      immediate: true,
      deep: true,
      handler() {
        this.formData.certificateString = this.checkList.join(",");
      },
    },
  },
  computed: {
    noneDisabled() {
      return (dictValue) => {
        let disabled = true;
        let isNone = dictValue == 0;
        const hasNone = this.check("0");
        if (this.checkList.length === 0) {
          disabled = false;
        } else if (isNone && hasNone) {
          disabled = false;
        } else if (!isNone && !hasNone) {
          disabled = false;
        }
        return disabled;
      };
    },
    showDate() {
      return (dictValue) => {
        return this.check(dictValue) && dictValue != 0;
      };
    },
    check() {
      return (dictValue) => this.checkList.includes(dictValue);
    },
  },
  async mounted() {
    let dicts = await this.initDicts();
    this.experienceOptions = dicts[0].data;
    this.hospitalLevelOptions = dicts[1].data;
    this.hospitalClassOptions = dicts[2].data;
    this.certificationOptions = dicts[3].data;
    if (!this.initData) {
      this.formData.certificates = this.certificationOptions.map((certifiaction) => {
        let abilityName = certifiaction.dictLabel
        if (certifiaction.dictValue === "otherAuthDueDate") {
          abilityName = ''
        } else if (certifiaction.dictValue === "fail") {
          abilityName = ''
        }
        return {
          dictLabel: certifiaction.dictLabel,
          abilityDictType: certifiaction.dictType,
          abilityDictValue: certifiaction.dictValue,
          expireDate: "",
          abilityName
        };
      })
      this.certificatesTmp = JSON.parse(JSON.stringify(this.formData.certificates))
      return;
    };
    let { hospitalBaseInfo = {}, certificateAbilityList = [] } = this.initData;
    for (const key in this.formData) {
      if (Object.hasOwnProperty.call(this.formData, key)) {
        if (key == 'beforeReviewerConclusionGrade') {
          this.$set(this.formData, 'beforeReviewerConclusion', `${hospitalBaseInfo.beforeReviewerConclusionGrade}-${hospitalBaseInfo.beforeReviewerConclusionLevel}`)
        } else if (key == 'certificates') {
          let certificates = await this.getHoldCertification(certificateAbilityList)
          this.$set(this.formData, 'certificates', certificates)
          this.certificatesTmp = JSON.parse(JSON.stringify(this.formData.certificates))
        } else if (typeof hospitalBaseInfo[key] === 'number' && !isNaN(hospitalBaseInfo[key])) {
          this.$set(this.formData, key, `${hospitalBaseInfo[key]}`)
        } else {
          this.$set(this.formData, key, hospitalBaseInfo[key] || '')
        }
      }
    }
  },
  methods: {
    initDicts() {
      let promises = [this.getDicts("review_experience"), this.getDicts("hospital_level"), this.getDicts("hospital_class"), this.getDicts("hold_certification")];
      return Promise.all(promises)
    },
    getHoldCertification(certificateAbilityList) {
      let certificateAbilityMap = {}
      certificateAbilityList.forEach(item => {
        this.checkList.push(item.abilityDictValue);
        certificateAbilityMap[item.abilityDictValue] = item
      });
      let certificates = this.certificationOptions.map(
        (certifiaction) => {
          let abilityName = certifiaction.dictLabel
          if (certifiaction.dictValue === "otherAuthDueDate") {
            abilityName = certificateAbilityMap[certifiaction.dictValue]?.abilityName || ''
          } else if (certifiaction.dictValue === "fail") {
            abilityName = certificateAbilityMap[certifiaction.dictValue]?.abilityName || ''
          }
          return {
            dictLabel: certifiaction.dictLabel,
            abilityDictType: certifiaction.dictType,
            abilityDictValue: certifiaction.dictValue,
            expireDate: certificateAbilityMap[certifiaction.dictValue]?.expireDate || '',
            abilityName
          };
        }
      );
      return certificates;
    },
    conclusionChange(value) {
      if (!value) {
        this.formData.beforeReviewerConclusionGrade = undefined;
        this.formData.beforeReviewerConclusionLevel = undefined;
      } else {
        const [level, Class] = value.split("-");
        this.formData.beforeReviewerConclusionLevel = Class;
        this.formData.beforeReviewerConclusionGrade = level;
      }
    },

    certificateValidate(callback) {
      if (this.checkList.length === 0) callback("请至少选择一项认证");
      callback();
    },

    certificatesChange(value) {
      let last = [...value].pop();
      if (last == 0) {
        this.$set(this, 'checkList', value.filter(val => val == '0'));
        return;
      } else {
        if (this.checkList.includes('0')) {
          this.$set(this, 'checkList', value.filter(val => val != '0'));
        }
      }
    },

    async getValue() {
      try {
        if (await this.$refs["certiForm"].validate()) {
          const {
            certificates,
            certificateString,
            beforeReviewerDate,
            beforeReviewerConclusion,
            beforeReviewerConclusionGrade,
            beforeReviewerConclusionLevel,
            first,
            expectReviewDate,
            hasIntnPro,
            ...data
          } = this.formData;
          const list = (certificates || []).filter((cert) => {
            return this.checkList.includes(cert.abilityDictValue);
          });
          return first == 2 ? {
            first,
            expectReviewDate,
            beforeReviewerDate,
            beforeReviewerConclusion,
            beforeReviewerConclusionGrade,
            beforeReviewerConclusionLevel,
            hasIntnPro,
            certificates: list,
            ...data,
          } : {
            first,
            expectReviewDate,
            beforeReviewerDate: "",
            beforeReviewerConclusion: "",
            beforeReviewerConclusionGrade: "",
            beforeReviewerConclusionLevel: "",
            hasIntnPro: "",
            certificates: [],
            ...data,
          };
        }
      } catch (error) {
        return null;
      }
    },
    firstChange() {
      this.formData.beforeReviewerDate = undefined
      this.formData.beforeReviewerConclusion = undefined
      this.formData.beforeReviewerConclusionLevel = undefined
      this.formData.beforeReviewerConclusionGrade = undefined
      this.formData.hasIntnPro = undefined
      this.formData.certificateString = ''
      this.checkList = []
      this.formData.certificates = JSON.parse(JSON.stringify(this.certificatesTmp))
    }
  },
};
</script>

<style lang="scss" scoped>
::v-deep .certTime.el-form-item {
  margin-bottom: 15px;
}

::v-deep.certification.el-form-item .el-form-item__label {
  line-height: 18px;
}

::v-deep .el-form>.el-form-item {
  margin-bottom: 30px;
}
</style>
