<template>
  <div class="audit">
    <i class="el-icon-success" style="color:#E6A23C;transform: rotate(-45deg);"></i>
    <h2>平台审核中</h2>
    <p>我们将在7个工作日内审核申请,请留意当前页面变动</p>

    <el-descriptions :column="1" border style="width:450px;margin:15px auto;" :labelStyle="{textAlign:'right', color:'black', fontWeight:'bold'}">
      <el-descriptions-item label="提交信息时间："> {{info.updateTime || parseTime(new Date().getTime())}} </el-descriptions-item>
      <el-descriptions-item v-if="theResult" label="理论培训评估结果："> {{theResult}} <el-button type="text" icon="el-icon-document" @click="showResult(theory)"></el-button>
      </el-descriptions-item>
      <el-descriptions-item v-if="praResult" label="带教培训评估结果："> {{praResult}} <el-button type="text" icon="el-icon-document" @click="showResult(practice)"></el-button>
      </el-descriptions-item>
    </el-descriptions>

    <el-dialog :title="title" :visible.sync="dialogVisible" width="50%">
      <el-descriptions :column="2" border :labelStyle="{ textAlign:'right', color:'black', fontWeight:'bold', minWidth:'80px'}" :contentStyle="{minWidth: '200px'}">
        <el-descriptions-item label="学员名字"> {{formData.name}} </el-descriptions-item>
        <el-descriptions-item label="性别">{{gender}}</el-descriptions-item>
        <el-descriptions-item label="专业">{{major}}</el-descriptions-item>
        <el-descriptions-item label="工作单位">{{formData.company}} </el-descriptions-item>
        <el-descriptions-item label="现任职务"> {{formData.companyPost}}</el-descriptions-item>
        <el-descriptions-item label="课程时间"> {{userData.courseStartTime}} ~ {{userData.courseEndTime}}</el-descriptions-item>
        <el-descriptions-item label="课程形式">{{courseFormat(userData.courseFormat)}} </el-descriptions-item>
        <el-descriptions-item label="培训教员">
          <span>{{reviewer(userData)}}</span>
        </el-descriptions-item>
        <el-descriptions-item label="结论">{{userResult}} </el-descriptions-item>
        <el-descriptions-item label="附件">
          <div class="file" v-for="(file, index) in userData.fileInfoList || []" :key="index">
            <el-button type="text" @click="download(file)">{{file.fileName}}</el-button>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="备注">
          {{ userData.remark }}
        </el-descriptions-item>
      </el-descriptions>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关 闭</el-button>
        <!-- <el-button type="primary" @click="dialogVisible = false">确 定</el-button> -->
      </span>
    </el-dialog>
  </div>
</template>

<script>
import request, { download } from "@/utils/request";
import { parseTime } from '@/utils/ruoyi.js'
import { getToken } from '@/utils/auth'

export default {
  name: "",
  props: {
    info: {
      type: Object,
      default: () => { },
    },

    detail: {
      type: Object,
      default: () => { },
    },
  },
  data() {
    return {
      form: {},
      theResult: '',
      praResult: '',

      theMap: {
        10: '可豁免现场评审带教培训，直接成为评审员',
        11: '可参与现场评审带教培训',
        12: '需再培训后参与现场评审带教培训',
      },

      praMap: {
        20: '适合当评审员，可考虑培训为评审组长',
        21: '适合当评审员，不考虑培训为评审组长',
        22: '需再带教培训',
        23: '不适合当评审员',
      },

      courseTypeOptions: [],
      sexOptions: [],
      majorOptions: [],

      formData: {
        name: '',
        gender: '',
        major: '',
        majorDirection: '',
        company: '',
        companyPost: '',
      },
      theory: {},
      practice: {},
      userData: {},
      userResult: '',
      title: '',
      dialogVisible: false,
      reviewerList: [],
    };
  },
  watch: {
    detail: {
      immediate: true,
      deep: true,
      handler(value) {
        let { reviewerBaseInfo = {} } = value || {};
        this.formData.name = reviewerBaseInfo.reviewerName
        this.formData.gender = reviewerBaseInfo.reviewerGender
        this.formData.major = reviewerBaseInfo.majorField
        this.formData.majorDirection = reviewerBaseInfo.majorDirection
        this.formData.company = reviewerBaseInfo.company
        this.formData.companyPost = reviewerBaseInfo.companyPost
      }
    }
  },
  computed: {
    gender() {
      return (this.sexOptions.find(({ dictValue }) => dictValue == this.formData.gender) || {}).dictLabel || '-'
    },

    courseFormat() {
      return value => {
        return (this.courseTypeOptions.find(({ dictValue }) => dictValue == value) || {}).dictLabel || '-'
      }
    },

    major() {
      let major = (this.formData.major || '').split(',')
      let majorDir = JSON.parse(this.formData.majorDirection || '{}')
      let list = major.map(id => {
        let str = ''
        if (id != 5) {
          str = (this.majorOptions.find(({ dictValue }) => dictValue == id) || {}).dictLabel || '-'
          str += majorDir[id] ? `(${majorDir[id]})` : ''
        } else {
          str += majorDir[id] ? `${majorDir[id]}` : ''
        }
        return str
      })
      return list.join(';')
    },
    reviewer() {
      return userData => {
        let ids = userData.trainerId || ''
        let names = userData.trainerName || ''
        if (ids != '') {
          let idList = ids.split(',')
          idList.forEach(id => {
            let reviewer = this.reviewerList.find(({ userId }) => userId == id);
            if (reviewer) {
              if (names == '') {
                names += reviewer.nickName
              } else {
                names += '，' + reviewer.nickName
              }
            }
          });
        }
        return names
      }
    }
  },
  mounted() {
    this.getDicts('course_form').then(res => {
      this.courseTypeOptions = res.data;
    })

    this.getDicts('sys_user_sex').then(res => {
      this.sexOptions = res.data;
    })

    this.getDicts('reviewer_major').then(res => {
      this.majorOptions = res.data;
    })

    if (getToken()) {
      this.getTheoryQuery();
      this.getPracticeQuery();
    }
  },
  methods: {
    parseTime,

    getTheoryQuery() {
      request({
        url: "/training-evaluate-result/theory-query",
        method: "post",
        data: { traineesAssessorId: this.$store.getters.userId, roleName: '评审学员', reviewResultType: 1 },
        throw: false
      }).then(res => {
        if (res.code != 200) return;
        if (!res.data) return;
        this.theory = res.data;
        let { conclusion, otherRemark } = res.data;
        if (conclusion == 0) {
          this.theResult = otherRemark
        } else {
          this.theResult = this.theMap[conclusion]
        }

        this.getTeachers();
      })
    },

    getPracticeQuery() {
      request({
        url: "/training-evaluate-result/theory-query",
        method: "post",
        data: { traineesAssessorId: this.$store.getters.userId, roleName: '评审学员', reviewResultType: 2 },
        throw: false
      }).then(res => {
        if (res.code != 200) return;
        if (!res.data) return;
        this.practice = res.data;
        let { conclusion, otherRemark } = res.data;
        if (conclusion == 0) {
          this.praResult = otherRemark
        } else {
          this.praResult = this.praMap[conclusion]
        }
      })
    },

    getTeachers() {
      request({
        url: '/training-evaluate-result/trainer-valid-data-query',
        method: 'get',
        params: {
          roleName: '评审员'
        }
      }).then(res => {
        this.reviewerList = res.data.find(({ roleName }) => roleName == '评审员')?.sysUserList || []
      })
    },

    getDetail() {
      request({
        url: "/reviewer/query/reviewer/detail",
        method: "post",
        data: { commonId: this.$store.getters.userId },
      }).then(res => {
        let { reviewerBaseInfo = {} } = res;
        this.reviewerBaseInfo = reviewerBaseInfo
        this.formData.name = reviewerBaseInfo.reviewerName
        this.formData.gender = reviewerBaseInfo.reviewerGender
        this.formData.major = reviewerBaseInfo.majorField
        this.formData.majorDirection = reviewerBaseInfo.majorDirection
        this.formData.company = reviewerBaseInfo.company
        this.formData.companyPost = reviewerBaseInfo.companyPost
      })
    },

    showResult(data) {
      this.title = data.reviewResultType == 1 ? '理论培训评估结果' : '带教培训评估结果';
      this.dialogVisible = true
      this.userData = data;
      this.userResult = data.reviewResultType == 1 ? this.theResult : this.praResult
    },

    download(file) {
      download(
        "/common/downloadFile",
        { fileId: Number(file.fileId) },
        file.fileName
      );
    },
  }
};
</script>

<style lang="scss" scoped>
.audit {
  text-align: center;
  ::v-deep .el-icon-success {
    color: green;
    font-size: 85px;
  }
  h2 {
    font-weight: bold;
    margin-top: 30px;
  }
  p {
    margin-bottom: 0;
    font-size: 14px;
    margin-top: 30px;
  }
  p + p {
    margin-top: 5px;
    color: #797979;
    font-size: 12px;
  }

  ::v-deep.el-dialog:not(.is-fullscreen) {
    margin-top: 12vh !important;
    min-width: 757px;

    .el-dialog__header {
      text-align: left;
    }
    .el-dialog__body {
      padding-bottom: 10px;
      padding-top: 10px;


      .file {
        display: flex;
        font-size: 12px;

        .el-button {
          padding: 5px 0;
        }
      }
    }
  }
}
</style>
