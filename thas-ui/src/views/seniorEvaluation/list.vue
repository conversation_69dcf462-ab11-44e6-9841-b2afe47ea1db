<!--  -->
<template>
  <div class="senior-evaluation-list">
    <el-form ref="form" :model="queryData" inline label-width="130px">
      <el-form-item label="医院名称：" prop="distributeHospitalName">
        <el-input v-model.trim="queryData.distributeHospitalName" :maxlength="20" placeholder="请输入医院名称"></el-input>
      </el-form-item>
      <el-form-item label="状态：" prop="distributeStatus">
        <el-select v-model.trim="queryData.distributeStatus" placeholder="请选择状态" clearable>
          <el-option v-for="item in progressOptions" :key="item.dictValue" :label="item.dictLabel"
            :value="item.dictValue">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="">
        <el-button type="primary" size="small" @click="query">
          查 询
        </el-button>
        <el-button type="primary" size="small" @click="reset">
          重 置
        </el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataSource" border stripe>
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="scope">
         <div class="table-btn ">
          <el-button type="text" v-if="showCReportBtn(scope.row)" @click="checkReport(scope.row)">验证评审报告</el-button>
          <el-button type="text" @click="downloadReport(scope.row)">评审报告下载</el-button>
          <el-button v-if="showFarReport(scope.row)" type="text" @click="downloadRarReport(scope.row)">事实准确性查询表下载</el-button>
          <el-button v-if="showVerifyReport(scope.row)" type="text" @click="downloadVerifyReport(scope.row)">验证报告下载</el-button>

          <!-- <el-button v-if="showFarReport(scope.row)" type="text" @click="downloadReport2(scope.row)">事实准确性查询表下载</el-button> -->
         </div>
        <!-- <el-button
            type="text"
            v-if="showEReportBtn(scope.row)"
            @click="examineReport(scope.row)"
            >查看报告结果</el-button
              > -->
        </template>
      </el-table-column>
      <el-table-column label="医院名称" align="center" prop="hospitalName">
      </el-table-column>
      <el-table-column label="评审完成时间" align="center" prop="audSecondSubmitTime">
      </el-table-column>
      <el-table-column label="评审报告生成时间" align="center" prop="reportSubmitTime">
      </el-table-column>
      <el-table-column label="审查评审报告完成时间" align="center" prop="reviewReviewReportSubmitTime">
      </el-table-column>
      <el-table-column label="准确性审查完成时间" align="center" prop="accuracyReviewSubmitTime">
      </el-table-column>
      <el-table-column label="验证评审员验证完成时间" align="center" prop="seniorReviewerReviewSubmitTime">
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          {{ AutSaAudCurrentStatusEnum[scope.row.status] }}
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize"
      @pagination="query" />
  </div>
</template>

<script>
import request, { download } from "@/utils/request";

export default {
  name: "SeniorEvaluationList",
  components: {},
  props: {},
  watch: {},
  data() {
    return {
      queryData: {
        distributeHospitalName: "",
        distributeStatus: "",
        pageSize: 10,
        pageNum: 1,
      },
      url: {
        list: "/aut/sa/aud/queryList",
      },
      total: 0,
      dataSource: [],
    };
  },

  watch: {
    $route(to, from) {
      if (
        to.path == "/seniorEvaluationList" &&
        from.path == "/seniorAssessorIndex"
      ) {
        this && this.query && this.query();
      }
    },
  },

  computed: {
    showCReportBtn() {
      return (row) =>
        Number(row.status) ==
        this.AutSaAudCurrentStatusEnum.WAIT_SENIOR_REVIEWER_REVIEW ||
        Number(row.status) ==
        this.AutSaAudCurrentStatusEnum.SENIOR_REVIEWER_REVIEW_PROC ||
        Number(row.status) ==
        this.AutSaAudCurrentStatusEnum.SENIOR_REVIEWER_REVIEW_SUM;
    },
    showEReportBtn() {
      return (row) =>
        Number(row.status) >= this.AutSaAudCurrentStatusEnum.WAIT_REVIEW_REVIEW_REPORT;
    },
    progressOptions() {
      return this.AutSaAudCurrentStatusEnum.getStatus()
    },
    // showFarReport() {
    //   return row => {
    //     return (row.farClausemSubmitList || []).length > 0
    //   }
    // },

    showFarReport() {
      return row => {
        if (!row.fileDetailMap) return false;
        return Object.values(row.fileDetailMap).length && Object.values(row.fileDetailMap)[0].length
      }
    },

        // 获取queryList接口返回的列表中fileDetailMap中的验证报告
    showVerifyReport() {
      return row => {
        let fileMap = row.fileDetailMap || {};
        let fileMapList = Object.values(fileMap);
        let files = fileMapList.find(files => {
          let file = files.find(file => {
            return file.fileType === 'verify_review_report'
          })
          return file
        })
        let file = (files || [])[0];
        return file;
      }
    },
  },

  created() { },

  mounted() {
    this.query();
  },

  methods: {
    query() {
      request({
        url: this.url.list,
        method: "post",
        data: {
          accountId: this.$store.getters.userId + "",
          pageType: 'tr_clause',
          ...this.queryData
        },
      }).then((res) => {
        this.dataSource = (res.rows || [])
        // this.dataSource = (res.rows || []).map((row) => {
        //   for (const key in row) {
        //     if (
        //       Object.hasOwnProperty.call(row, key) &&
        //       !row[key] &&
        //       row[key] != 0
        //     ) {
        //       row[key] = "--";
        //     }
        //   }
        //   return row;
        // });
        this.total = res.total;
      }).catch(() => {
        this.dataSource = []
        this.total = 0
      });
    },
    checkReport({ autCode, autCsId }) {
      this.$router.push({
        path: "/seniorAssessorIndex",
        query: {
          autCode,
          type: this.AutSaAudCurrentStatusEnum.TR_CLAUSE,
        },
      });
    },
    examineReport({ autSaAudReport }) {
      let name =
        autSaAudReport.autSaAudResult == 1
          ? "BaseClausePass"
          : "BaseClauseReject";
      this.$router.push({
        name,
        params: {
          autSaAudReport,
        },
      });
    },
    reset() {
      this.$refs["form"].resetFields();
      this.query();
    },
    downloadReport({ reviewReportPdfFileId, reviewReportPdfFileName }) {
      download("/common/downloadFile", { fileId: reviewReportPdfFileId }, reviewReportPdfFileName);
    },

    downloadReport2(row) {
      try {
        let id = (row.farClausemSubmitList || [])[0]
        if (!id) {
          this.$message({
            type:'error',
            message:'文件不存在'
          })
          return;
        }
        let files = row.fileDetailMap[id]
        let { fileId, fileName } = files[0]
        download("/common/downloadFile", { fileId }, fileName);
      } catch (error) {
        console.log(error);
      }
    },


    downloadRarReport(row) {
      let fileDetailMap = row.fileDetailMap || {};
      let fileMapList = Object.values(fileDetailMap);
      let files = fileMapList.find(files => {
        let file = files.find(file => {
          return file.fileType === 'far_clause_m_submit'
        })
        return file
      })
      let file = (files || [])[0];
      let { fileId, downLoadFileName } = file;
      //  || `${row.hospitalName}-事实准确性确认表.pdf`
      download("/common/downloadFile", { fileId }, downLoadFileName);
    },

    downloadVerifyReport(row) {
      let file = this.showVerifyReport(row);
      let { fileId, downLoadFileName } = file
      download("/common/downloadFile", { fileId }, downLoadFileName);
    }
  },
};
</script>
<style lang='scss' scoped>
.senior-evaluation-list {
  min-height: inherit;
  background-color: white;
  padding: 12px;



  ::v-deep .table-btn {
    .el-button+.el-button {
      display: block;
      margin: 0 auto;
    }
  }
}
</style>
