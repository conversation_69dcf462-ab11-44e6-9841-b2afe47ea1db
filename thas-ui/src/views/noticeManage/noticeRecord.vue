<template>
  <el-card v-loading="loading" class="notice-record">
    <!-- <el-row :gutter="24">
      <el-col :span="24">
        <el-button icon="el-icon-refresh" size="small" @click="query">刷新</el-button>
      </el-col>
    </el-row> -->
    <el-row :gutter="24" style="margin-top: 24px">
      <el-col :span="24">
        <el-table :data="dataSource" border>
          <el-table-column label="序号" type="index" :index="indexMethod" align="center"></el-table-column>
          <el-table-column label="通知内容" prop="content" align="center"></el-table-column>
          <el-table-column label="状态" prop="status" align="center">
            <template slot-scope="scope">
              {{ scope.row.status === "1" ? '成功' : scope.row.status === "0" ? "失败" : "" }}
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="createTime" align="center"></el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query" />
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
import request from "@/utils/request"
export default {
  name: "NoticeRecord",
  components: {},
  data() {
    return {
      dataSource: [],
      queryData: {
        pageSize: 10,
        pageNum: 0
      },
      total: 0,
      loading: false
    }
  },
  created() {
    this.query()
  },
  methods: {
    query() {
      this.loading = true
      request({
        url: '/system/messageSendRecord/list',
        method: "get",
        params: this.queryData,
      }).then((res) => {
        this.loading = false
        if (res.code != 200) return
        this.dataSource = res.rows
        this.total = res.total
      }).catch((error) => {
        // console.log(error)
        this.loading = false
      })
    },
    indexMethod(index) {
      return this.queryData.pageSize * (this.queryData.pageNum - 1) + index + 1;
    }
  }
}
</script>

<style lang='scss' scoped>
.notice-record {
  padding: 24px;
  margin-bottom: 10px;
  ::v-deep .el-dialog {
    .el-dialog__header {
      background-color: #3c81f5;
      padding-top: 10px;
      .el-dialog__title {
        color: white;
      }
      .el-dialog__headerbtn {
        top: 15px;
        .el-dialog__close {
          color: white;
        }
      }
    }
    .el-dialog__body {
      padding-bottom: 0;
      & > span {
        color: black;
      }
    }
  }
}
</style>