<!--  -->
<template>
  <div ref="evaluationForm"></div>
</template>

<script>
import { download } from "@/utils/request";
import { deepClone } from "@/utils/index";

const defaultForm = {
  autResult: '',
  autDesc: '',
  clauseId: '',
  fileIds: '',
  riskImpact: '',
  riskPossibility: '',
  reviewerRespond: '',
  autAdvantage: '',
  autEvaluate: '',
  autProposal: '',
  autImprove: '',
  autDesc1: '',
  beforeAutDesc: '',
  // type: '',
  // submit_type: '',
}

export default {
  name: '',
  components: {},
  props: {
    aut: {
      type: [Object, null],
      default: () => null
    },
    clause: {
      type: Object,
      default: () => { }
    },
    status: {
      type: [Number, String],
      default: () => "0"
    },
    readonly: {
      type: Boolean,
      default: () => false
    },
    type: {
      type: String,
      default: () => ''
    },
    rejectType: {
      type: String,
      default: () => ''
    },
    startModify: {
      type: Boolean,
      default: () => false
    },
    params: {
      type: Object,
      default: () => { }
    },
  },
  watch: {
    aut: {
      immediate: true,
      deep: true,
      handler(value, oldVal) {
        this.initAutFormData(value, oldVal);
      }
    },

    'formData.autResult': {
      handler() {
        setTimeout(() => {
          if (this.$refs.evaluationForm) {
            this.$refs.evaluationForm.clearValidate();
          }
        }, 0);
      },
    }
  },
  data() {
    return {
      formData: {
        ...defaultForm
      },
      cloneFormData: {},
      files: [],
      modifying: false,
      defaultForm: defaultForm,
      hasRisk: false,
      reviewResultOptions: [],
      agreeOrNotOptions: [],
      checkSummaryOptions: [],
      yNOptions: [],
      descMaxLength: 1000,
      selfMaxLength: 500,
      autResultMessage: '',
      autDescMessage: '',
      submitLoading: false,
      labelWidth: '180px',
      beDestroyed: false,
      isSubmit: false,

      aut_backUp: {},
      formKey: 0,

    };
  },

  computed: {
    autResultRejected() {
      return this.formData.autResult && this.formData.autResult != 1
    },

    isBasicStar() {
      return this.formData.autResult != 2 && this.formData.autResult != 3
    },

    rules() {
      return {
        autResult: [
          {
            required: true,
            message: this.autResultMessage,
            trigger: "change",
          },
        ],
        autDesc: [
          {
            required: this.autDescValidate === undefined ? this.autResultRejected : this.autDescValidate,
            message: this.autDescMessage,
            trigger: "change",
          },
        ],
        autDesc1: [
          {
            required: this.autDescValidate === undefined ? this.autResultRejected : this.autDescValidate,
            message: this.autDescMessage,
            trigger: ["change"],
          },
        ],
        fileIds: [
          {
            required: this.formData.autResult == '6' || this.formData.autResult == '1',
            validator: (rule, value, callback) =>
              this.filesValidator(rule, value, callback),
            trigger: ["change"],
          },
        ],
        riskPossibility: [
          {
            required: true,
            message: "请选择可能性评分",
            trigger: ["change"],
          },
        ],
        riskImpact: [
          {
            required: true,
            message: "请选择影响评分",
            trigger: ["change"],
          },
        ],
        autAdvantage: [{
          required: this.formData.autResult == 1,
          message: '请填写亮点',
          trigger: ['change', 'blur']
        }],
        autEvaluate: [{
          required: true,
          message: '请填写不足',
          trigger: ['change', 'blur']
        }],
        autProposal: [{
          required: true,
          message: '请填写整改建议',
          trigger: ['change', 'blur']
        }],
        autImprove: [{
          required: false,
          message: '请填写改进机会',
          trigger: ['change', 'blur']
        }],
      }
    },
    hasAut() {
      return !!this.form
    },
    showRiskItem() {
      return this.hasRisk && this.formData.autResult == 5 && this.clause.isStar == 1
    },
    hasRiskValue() {
      return this.hasRisk && this.formData.riskPossibility && this.formData.riskImpact
    },
    riskPoint() {
      return this.hasRiskValue ? Number(this.formData.riskPossibility * this.formData.riskImpact) : 0
    },
    riskPointTag() {
      let point = this.riskPoint;
      let data = {
        label: '',
        type: '',
      }
      switch (point) {
        case 1:
          data.label = "低风险"
          data.type = 'success'
          break;
        case 2:
          data.label = "低风险"
          data.type = 'success'
          break;
        case 3:
          data.label = "中风险"
          data.type = 'info'
          break;
        case 4:
          data.label = "中风险"
          data.type = 'info'
          break;
        case 6:
          data.label = "高风险"
          data.type = 'warning'
          break;
        case 9:
          data.label = "严重风险"
          data.type = 'danger'
          break;
        default:
          data.label = ""
          data.type = ''
          break;
      }
      return data;
    },
  },

  created() {
    this.initDicts();
    this.bus.$on('autBack', ({ clauseId, submitType, success }) => {
      // this.submitLoading = false;
      this.edit = false;
      if (this.clause && this.clause.clauseId == clauseId && success && !this.beDestroyed) {
        if (this.type == submitType) {
          this.modifying = false;
          if (this.showForm === true) {
            this.showForm = false
          }
          if (this.aut && this.aut.submitType == this.AutSaAudCurrentStatusEnum.SR_CLAUSE && this.type == this.AutSaAudCurrentStatusEnum.SR_REPORT_M) {
            // todo
          } else {
            this.$message({
              type: 'success',
              message: '提交成功'
            })
          }
          this.componentCallBack();
        } else {
          if ((this.type == this.AutSaAudCurrentStatusEnum.far_clause_m &&
            submitType == this.AutSaAudCurrentStatusEnum.far_clause_m_skip) ||
            (this.type == this.AutSaAudCurrentStatusEnum.tr_clause_m &&
              submitType == this.AutSaAudCurrentStatusEnum.tr_clause_m_skip)) {
            this.$message({
              type: 'success',
              message: '提交成功'
            })
            this.componentCallBack();
          }
        }

      }
    })
  },

  destroyed() {
    // if (Array.isArray(this.bus._events.autBack) && this.bus._events.autBack.length > 1) {
    //   this.bus.$off("autBack");
    // }
    this.beDestroyed = true;
  },

  mounted() { },

  methods: {
    clearDesc() {
    },

    initAutFormData(value, oldVal) {
      if (value) {
        this.submitLoading = false
        this.aut_backUp = JSON.parse(JSON.stringify(value));
        if (!value.rejected || this.readonly) {
          this.modifying = false;
        }
        if (value.rejected) {
          this.modifying = true;
          this.noReject = false;
        } else {
          this.noReject = true;
        }
        let form = oldVal || this.defaultForm;
        for (const key in form) {
          if (Object.hasOwnProperty.call(form, key)) {
            if (!this.formData[key]) {
              if (key == 'autDesc' && value[key].includes('{') && value[key].includes('}')) {
                try {
                  let { autAdvantage, autEvaluate, autProposal, autImprove, autDesc } = JSON.parse(value[key])
                  this.$set(this.formData, 'autAdvantage', autAdvantage || '')
                  this.$set(this.formData, 'autEvaluate', autEvaluate || '')
                  this.$set(this.formData, 'autProposal', autProposal || '')
                  this.$set(this.formData, 'autImprove', autImprove || '')
                  this.$set(this.formData, 'autDesc', autDesc || '')
                  this.$set(this.formData, 'autDesc1', autDesc || '')
                } catch (error) {
                  this.$set(this.formData, key, value[key])
                }
              } else if (key == 'beforeAutDesc' && value[key] && value[key].includes('{') && value[key].includes('}')) {
                this.$set(this.formData, 'beforeAutDesc', value[key])
                try {
                  this.$set(this.formData, 'beforeAutDescMap', JSON.parse(value[key]))
                } catch (error) {
                  this.$set(this.formData, 'beforeAutDescMap', {})
                }
              } else {
                this.$set(this.formData, key, value[key])
              }
            } else if (form[key] != value[key]) {
              if (key == 'autResult' || key == 'autDesc' || key == 'reviewerRespond') {
                this.$set(this.formData, key, value[key])
              }
            }
          }
        }
        if (value.fileList && Array.isArray(value.fileList)) {
          if (this.files.length == 0) this.$set(this, 'files', [...value.fileList]);
        }
        if (this.status === this.AutSaAudCurrentStatusEnum.WAIT_CONFIRM_SA_SUM) {
          this.modifying = true;
        }

        for (const key in this.formData) {
          if (Object.hasOwnProperty.call(this.formData, key)) {
            if (this.formData[key] === undefined) {
              this.formData[key] = ''
            }
          }
        }

        this.backup = JSON.stringify(this.formData);

      } else {
        this.modifying = true;
      }

      if (this.startModify === true) {
        this.modifying = true;
      }

      this.isSubmit = false;
      // 拷贝一份表单，做驳回切换结果后的回显
      this.cloneFormData = deepClone(this.formData);
    },

    resultChange(val) {
      if (this.cloneFormData.autResult === val) {
        this.formData = deepClone(this.cloneFormData);
      } else {
        this.formKey++
        this.clearDesc();
      }
    },

    reply() {
      try {
        !this.isSubmit && this.backup && this.$set(this, 'formData', JSON.parse(this.backup))
      } catch (error) {
        // console.log(error);
      }
    },

    initDicts() {
      this.getDicts("review_result").then((response) => {
        this.reviewResultOptions = response.data;
      });
      this.getDicts("agree_or_not").then((res) => {
        this.agreeOrNotOptions = res.data;
      });
      this.getDicts("check_summary").then((res) => {
        this.checkSummaryOptions = res.data;
      });
      this.getDicts("yes_no").then((res) => {
        this.yNOptions = res.data;
      });
    },

    filesValidator(rule, value, callback) {
      if (!rule.required) callback()
      else if (!value) {
        callback("请上传证明材料");
      }
      else callback();
    },

    riskValidate(name, value) {
      if (name) {
        this.$refs.evaluationForm.validateField(name);
      }
    },

    showPreview(file, index) {
      let { fileName, url } = file;
      let lastIndex = fileName.lastIndexOf('.')
      if (lastIndex == -1) return;
      let suffix = fileName.substring(lastIndex + 1);
      let fileTypes = ['pdf'];
      if (fileTypes.includes(suffix)) {
        window.open(url, fileName)
      } else {
        this.$nextTick(() => {
          this.$refs[`file${index}`][0].$el.children[0].click();
        })
      }

    },

    submitEmit() {
      if(this.submitLoading) return;

      this.submitLoading = true;
      this.isSubmit = true;
      let { beforeAutDesc, ...data } = (this.submitForm || this.formData)

      this.bus.$emit('autSubmit', 'autBack', {
        data,
        clauseId: this.clause.clauseId,
        submitType: this.type,
        status: this.status,
        ...this.params
      });
    },

    rejectEmit() {
      if (!this.rejectType || !this.AutSaAudCurrentStatusEnum[this.rejectType]) {
        throw '拒绝类型错误'
      }
      if(this.submitLoading) return;

      this.submitLoading = true;
      this.isSubmit = true;

      this.bus.$emit('autSubmit', 'autBack', {
        data:this.submitForm || this.formData,
        clauseId: this.clause.clauseId,
        submitType: this.rejectType,
        status: this.status
      });
    },

    submitEmit_data(data) {
      this.submitLoading = true;
      this.isSubmit = true;
      this.bus.$emit('autSubmit', 'autBack', data);
    },

    download(file) {
      download(
        "/common/downloadFile",
        { fileId: Number(file.fileId) },
        file.fileName
      );
    },

    componentCallBack() { }
  }
}

</script>

<style lang='scss' scoped>
.evaluation {
  background-color: rgba(0, 0, 0, 0.02);

  .divider {
    .el-divider__text {
      font-size: 12px;
    }
  }

  .el-form {
    padding-top: 10px;

    .form-item {
      margin-bottom: 20px;

      ::v-deep .el-form-item__content {
        margin-right: 20px;

        span {
          word-break: break-all;
          word-wrap: break-word;
        }
      }

      &.hideStart {
        ::v-deep .el-form-item__label:before {
          content: "" !important;
        }
      }
    }

    .form-item.readonly {
      margin-bottom: 2px;
    }

    .form-item:last-child {
      margin-bottom: 0;
      padding-bottom: 10px;
    }
  }
}
</style>
