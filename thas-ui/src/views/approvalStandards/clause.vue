<template>
  <div class="clause-particulars">
    <div class="title">
      <h2>款项详情</h2>
      <el-divider></el-divider>
    </div>

    <standard-select v-if="show" onlyTitle="" :versionId='versionId' :tier="4" :labelWidth="`40px`" :sClauseId="sClauseId" @selectChange="selectChange"></standard-select>

    <el-card class="box-card" shadow="never" v-show="hasClause">
      <div slot="header" class="clearfix">
        <span>细则</span>
      </div>
      <h4>{{clause.detailRulesTitle || ''}}<el-button v-if="canModify" style="float: right" type="primary" icon="el-icon-edit" size="mini" @click="edit(1)" v-hasPermi="['approvalStandards:delete']">修改</el-button>
      </h4>
      <p>
      <pre>{{clause.detailRulesDesc || '暂无细则'}}</pre>
      </p>
    </el-card>

    <el-card class="box-card" shadow="never" v-show="hasClause">
      <!-- <div slot="header" class="clearfix" v-if="canModify">
        <el-button v-if="canModify" style="float: right" type="primary" icon="el-icon-edit" size="small" @click="edit(2)" v-hasPermi="['approvalStandards:delete']">修改</el-button>
      </div> -->
      <h4 v-if="clause.evidenceMaterial">达标佐证材料
        <el-button v-if="canModify" style="float: right" type="primary" icon="el-icon-edit" size="small" @click="edit(2)" v-hasPermi="['approvalStandards:delete']">修改</el-button>
      </h4>
      <p>
      <pre>{{clause.evidenceMaterial}}</pre>
      </p>
    </el-card>

    <el-card class="box-card" shadow="never" v-show="hasClause">
      <div slot="header" class="clearfix" v-if="canModify">
        <el-button v-if="canModify" style="float: right" type="primary" icon="el-icon-edit" size="small" @click="sEdit" v-hasPermi="['approvalStandards:delete']">修改</el-button>
      </div>
      <p>
        <span v-for="(item, index) in clause.cstEvaluationCriterionList" :key="index">
          <h4>{{item.evaluate}}</h4>
          <p style="margin-bottom:20px;"> {{item.standard || '暂无评审标准'}}</p>
        </span>
      </p>
    </el-card>

    <el-card class="box-card" shadow="never" v-show="hasClause">
      <div slot="header" class="clearfix">
        <span>相关政策文件</span>
        <el-button v-if="canModify" style="float: right" type="primary" icon="el-icon-edit" size="mini" @click="edit(3)" v-hasPermi="['approvalStandards:delete']">修改</el-button>
      </div>
      <p>
      <pre>{{clause.regulationFile || '暂无相关政策文件'}}</pre>
      </p>
    </el-card>

    <el-card class="box-card" shadow="never" v-show="hasClause">
      <div slot="header" class="clearfix">
        <span>国际参考文献</span>
        <el-button v-if="canModify" style="float: right" type="primary" icon="el-icon-edit" size="mini" @click="edit(4)" v-hasPermi="['approvalStandards:delete']">修改</el-button>
      </div>
      <p>
      <pre>{{clause.internationalReference || '暂无国际参考文献'}}</pre>
      </p>
    </el-card>

    <!-- 弹窗 -->
    <normal-dialog ref="nDialog" :isDetail='isDetail' @modify="modifyBack"></normal-dialog>
    <standards-dialog ref="sDialog" @modify="sModifyBack"></standards-dialog>
  </div>
</template>

<script>
import NormalDialog from "./components/dialog/normalDialog.vue";
import StandardsDialog from "./components/dialog/standardsDialog.vue";
import request from "@/utils/request";

export default {
  name: "ClauseParticulars",
  components: {
    NormalDialog,
    StandardsDialog,
  },
  data() {
    return {
      form: {},
      tier: 0,
      clause: {},
      sClauseId: "",
      isDetail: true,
      editArg: 0,
      showDialog: false,
      key: 0,
      versionId: "",
      show: false,
      canModify: false
    };
  },
  computed: {
    hasClause() {
      return Object.keys(this.clause).length;
    },
  },
  mounted() {
    this.sClauseId = this.$route.params.clauseId;
    this.versionId = this.$route.params.versionId;
    this.canModify = !!this.$route.params.canModify;
    this.show = true;
  },
  methods: {
    selectChange(tier, data) {
      this.tier = tier;
      this.clause = data;
      this.versionId = this.clause.versionId;
      if (!this.clause.finish) {
        this.getClausesDetail(this.versionId, this.clause.articleId, true)
          .then((res) => {
            this.clause = this.$store.getters.standardTypeItemByVersionId(this.versionId, 'clause', 'map')[this.clause.clauseId];
          });
      }
    },

    sEdit() {
      this.$refs["sDialog"].show(
        this.clause.cstEvaluationCriterionList,
        this.versionId,
        ''
      );
    },

    edit(arg) {
      this.isDetail = false;
      this.editArg = arg;
      this.showDialog = true;
      let maxlength = 50;
      if (arg == 1) {
        this.isDetail = true;
        this.form.type = "细则";
        this.form.title = this.clause.detailRulesTitle;
        this.form.description = this.clause.detailRulesDesc;
        maxlength = 2000
      } else if (arg == 2) {
        this.form.type = "达标佐证材料";
        this.form.description = this.clause.evidenceMaterial;
        maxlength = 500
      } else if (arg == 3) {
        this.form.type = "相关政策文件";
        this.form.description = this.clause.regulationFile;
        maxlength = 2500
      } else if (arg == 4) {
        this.form.type = "国际参考文献";
        this.form.description = this.clause.internationalReference;
        maxlength = 50000
      } else {
        this.form = {};
        maxlength = 250
      }
      this.$refs["nDialog"].show(this.form, maxlength);
    },

    modifyBack(form) {
      if (this.editArg == 1) {
        this.clause.detailRulesTitle = form.title;
        this.clause.detailRulesDesc = form.description;
      } else if (this.editArg == 2) {
        this.clause.evidenceMaterial = form.description;
      } else if (this.editArg == 3) {
        this.clause.regulationFile = form.description;
      } else if (this.editArg == 4) {
        this.clause.internationalReference = form.description;
      }
      request({
        url: "/system/standards/updateByClauseIdAndVersionId",
        data: this.clause,
        method: "PUT",
      }).finally(() => {
        this.getClausesDetail(this.versionId, this.clause.articleId, true)
          .then((res) => {
            this.clause = this.$store.getters.standardTypeItemByVersionId(this.versionId, 'clause', 'map')[this.clause.clauseId];
          });
      });
    },

    sModifyBack(form) {
      const list = form.list.map((li) => {
        return {
          ...li,
          certificationStandardsId: this.clause.clauseId,
        };
      });
      request({
        url: "/system/criterion/updateListByVersionIdAndClauseId",
        data: list,
        method: "PUT",
      }).finally(() => {
        this.getClausesDetail(this.versionId, this.clause.articleId, true)
          .then((res) => {
            this.clause = this.$store.getters.standardTypeItemByVersionId(this.versionId, 'clause', 'map')[this.clause.clauseId];
          });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.clause-particulars {
  padding: 18px;
  h2 {
    margin-top: 0;
    font-weight: bold;
  }
  ::v-deep .el-divider {
    margin-top: 0;
    margin-bottom: 18px;
  }
  .title {
    margin-bottom: 0;
  }

  ::v-deep .el-card {
    border: 0;
    padding: 0 20px;
    .el-card__header {
      .clearfix > span {
        line-height: 37px;
        font-weight: bold;
        font-size: 20px;
      }
    }
    .el-card__header {
      h4 {
        font-weight: 900;
        margin-top: 0;
        margin-bottom: 0;
      }
      p {
        font-weight: lighter;
        margin-top: 0;
        font-size: 15px;
      }
    }
    .el-card__body {
      h4 {
        font-weight: 900;
        margin-top: 10px;
        margin-bottom: 20px;
      }
      p {
        font-weight: lighter;
        margin-top: 0;
        margin-bottom: 0;
        font-size: 15px;

        h4 {
          font-weight: 900;
          margin-top: 0;
          font-size: 16px;
        }
      }
    }

    pre {
      white-space: pre-wrap;
      word-wrap: break-word;
      margin-bottom: 0;
    }
  }
}
</style>