<template>
  <div v-loading="loading" class="hospital-audit">
    <el-row :gutter="24">
      <el-col :span="24" :xs="24">
        <el-form ref="queryForm" :model="queryData" size="small" label-width="112px" inline>
          <el-form-item label="医院名称" prop="hospitalName">
            <el-input v-model.trim="queryData.hospitalName" placeholder="请输入医院名称" clearable :style="{ width: '100%' }" :maxlength="20"></el-input>
          </el-form-item>

          <el-form-item label="审核状态" prop="authStatus">
            <el-select v-model.trim="queryData.authStatus" :style="{ width: '100%' }" clearable filterable>
              <el-option v-for="auth in authStatusOptions" :key="auth.dictValue" :value="auth.dictValue" :label="auth.dictLabel"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="query">
              查询
            </el-button>
            <el-button type="primary" size="small" @click="resetForm">
              重 置
            </el-button>
            <el-button type="primary" size="small" @click="exportHospitals">导出</el-button>

          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

    <el-row style="padding: 0 24px;">
      <el-table :data="dataSource" border height="630">
        <el-table-column align="center" label="操作" width="150">
          <template slot-scope="scope">
            <el-button type="text" v-if="scope.row.authStatus == 1" @click="toAudit(scope.row.applyNo, 1)">审核</el-button>
            <el-button type="text" v-else @click="toDetail(scope.row.applyNo)">查看详情</el-button>
            <!-- <el-button type="text" @click="downloadPDF(scope.row.applyNo)">测试模板</el-button> -->
          </template>
        </el-table-column>
        <el-table-column align="center" label="医院名称" prop="hospitalName">
        </el-table-column>
        <el-table-column align="center" label="审核人" prop="authPersonName" width="150">
        </el-table-column>
        <el-table-column align="center" label="提交时间" prop="createTime"> </el-table-column>
        <el-table-column align="center" label="审核时间" prop="authDate"> </el-table-column>
        <el-table-column align="center" label="审核状态" prop="authStatus" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.authStatus == 1">
              {{ authStatus(`${scope.row.authStatus}`) }}
            </span>
            <span v-else-if="scope.row.authStatus == 2" style="color:green;">
              {{ authStatus(`${scope.row.authStatus}`) }}
            </span>
            <span v-else style="color:red;">
              {{ authStatus(`${scope.row.authStatus}`) }}
            </span>

          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query" />
    </el-row>
  </div>
</template>

<script>
import request, { downloadFile, downloadPDF } from "@/utils/request";

let that;

export default {
  name: "HospitalAudit",
  components: {},
  props: [],
  data() {
    return {
      queryData: {
        hospitalName: undefined,
        authContactName: undefined,
        authContactMobile: undefined,
        authStatus: undefined,
        pageNum: 0,
        pageSize: 10,
      },
      total: 0,
      url: {
        list: "/hospital/query/hospital/list",
      },
      dataSource: [],
      authStatusOptions: [],
      allReady: false,
      loading: false
    };
  },
  computed: {
    authStatus() {
      return (status) => {
        const auth = this.authStatusOptions.find(
          (auth) => auth.dictValue == status
        );
        return auth ? auth.dictLabel : status;
      };
    },
  },
  watch: {
    $route: (to) => {
      if (!that || !that.allReady) return;
      if (to.path != "/auditManagement/hospitalAudit") return;
      that.query();
    },
  },
  created() { },
  async mounted() {
    await this.initDicts();
    this.query();
    that = this;
    this.allReady = true;
  },
  methods: {
    async initDicts() {
      try {
        this.authStatusOptions = (await this.getDicts("auth_status")).data;
      } catch (error) {
        // console.log(error);
      }
    },
    submitForm() {
      this.$refs["queryForm"].validate((valid) => {
        if (!valid) return;
        // TODO 提交表单
      });
    },
    resetForm() {
      this.$refs["queryForm"].resetFields();
      this.queryData.pageNum = 1
      this.query()
    },

    downloadPDF(applyNo) {
      downloadPDF({
        filename: `CH反馈认证申请表Application form draft.pdf`,
        ftlTemplateCode: "hos_info_gen",
        applyNo
      })
    },

    query() {
      this.loading = true
      request({
        url:
          this.url.list +
          `?pageNum=${this.queryData.pageNum}&pageSize=${this.queryData.pageSize}`,
        method: "post",
        data: this.queryData,
      }).then((res) => {
        this.loading = false
        if (res.code != 200) return;
        let fields = [
          "hospitalName",
          "authContactName",
          "authContactMobile",
          "authStatus",
          "authPersonName",
          "createTime",
          "authDate",
        ];
        this.dataSource = res.rows.map((row) => {
          for (const key in row) {
            if (Object.hasOwnProperty.call(row, key)) {
              if (!row[key] && row[key] != 0 && fields.includes(key)) {
                row[key] = "--";
              }
            }
          }
          return row;
        });
        this.total = res.total;
      }).catch((error) => {
        // console.log(error)
        this.loading = false
      })
    },

    toAudit(applyNo, type) {
      this.$router.push({
        path: "/auditManagement/hospitalInfoAudit" + `?applyNo=${applyNo}`,
        query: { applyNo, type },
      });
    },

    toDetail(applyNo, type) {
      this.$router.push({
        path: "/auditManagement/hospitalDetailMessage" + `?applyNo=${applyNo}`,
        query: { applyNo, type },
      });
    },

    exportHospitals() {
      let { pageNum, pageSize, ...data } = this.queryData
      let filename = '认证申请医院名单.xlsx'
      // if (!isNaN(Number(data.authStatus))) {
      //   let status = this.authStatusOptions.find(({ dictValue }) => dictValue == data.authStatus);
      //   filename += status ? `-${status.dictLabel}.xlsx` : '.xlsx'
      // } else {
      //   filename += '.xlsx'
      // }
      downloadFile({
        url: '/hospital/list/export',
        params: data,
        type: 'application/vnd.ms-excel',
        filename
      }, res => {
        // console.log(res);
      });
      // request({
      //   url: '/hospital/list/export',
      //   method: 'post',
      //   responseType: "blob",
      //   data,
      // })
    }
  },
};
</script>

<style lang="scss" scoped>
.hospital-audit {
  padding: 24px 10px;
  background-color: #fff;
}
</style>

