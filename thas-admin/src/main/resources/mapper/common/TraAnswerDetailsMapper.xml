<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.TraAnswerDetailsMapper">

    <resultMap type="TraAnswerDetails" id="TraAnswerDetailsResult">
        <result property="id" column="id"/>
        <result property="answerSheetId" column="answer_sheet_id"/>
        <result property="examDetailsId" column="exam_details_id"/>
        <result property="answer" column="answer"/>
        <result property="score" column="score"/>
        <result property="correct" column="correct"/>
    </resultMap>

    <sql id="selectTraAnswerDetailsVo">
        select id, answer_sheet_id, exam_details_id, answer, score, correct
        from tra_answer_details
    </sql>

    <select id="selectTraAnswerDetailsList" parameterType="TraAnswerDetails" resultMap="TraAnswerDetailsResult">
        <include refid="selectTraAnswerDetailsVo"/>
        <where>
            <if test="answerSheetId != null ">and answer_sheet_id = #{answerSheetId}</if>
            <if test="examDetailsId != null ">and exam_details_id = #{examDetailsId}</if>
            <if test="answer != null  and answer != ''">and answer = #{answer}</if>
            <if test="score != null  and score != ''">and score = #{score}</if>
            <if test="correct != null ">and correct = #{correct}</if>
        </where>
    </select>

    <select id="selectTraAnswerDetailsById" parameterType="Long" resultMap="TraAnswerDetailsResult">
        <include refid="selectTraAnswerDetailsVo"/>
        where id = #{id}
    </select>

    <insert id="insertTraAnswerDetails" parameterType="TraAnswerDetails">
        insert into tra_answer_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="answerSheetId != null">answer_sheet_id,</if>
            <if test="examDetailsId != null">exam_details_id,</if>
            <if test="answer != null">answer,</if>
            <if test="score != null">score,</if>
            <if test="correct != null">correct,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="answerSheetId != null">#{answerSheetId},</if>
            <if test="examDetailsId != null">#{examDetailsId},</if>
            <if test="answer != null">#{answer},</if>
            <if test="score != null">#{score},</if>
            <if test="correct != null">#{correct},</if>
        </trim>
    </insert>

    <update id="updateTraAnswerDetails" parameterType="TraAnswerDetails">
        update tra_answer_details
        <trim prefix="SET" suffixOverrides=",">
            <if test="answerSheetId != null">answer_sheet_id = #{answerSheetId},</if>
            <if test="examDetailsId != null">exam_details_id = #{examDetailsId},</if>
            <if test="answer != null">answer = #{answer},</if>
            <if test="score != null">score = #{score},</if>
            <if test="correct != null">correct = #{correct},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTraAnswerDetailsById" parameterType="Long">
        delete
        from tra_answer_details
        where id = #{id}
    </delete>

    <delete id="deleteTraAnswerDetailsByIds" parameterType="String">
        delete from tra_answer_details where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>