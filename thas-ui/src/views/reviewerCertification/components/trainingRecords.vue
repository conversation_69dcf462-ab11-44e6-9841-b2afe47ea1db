<template>
  <div class="training-records">
    <el-table ref="multipleTable" :data="dataSource" stripe border :row-key="getKey" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" :reserve-selection="true">
      </el-table-column>
      <el-table-column label="培训名称" prop="trainingName"></el-table-column>
      <el-table-column label="举办时间" prop="trainingTime"></el-table-column>
      <el-table-column label="举办地点" prop="trainingLocation"></el-table-column>
      <el-table-column label="培训类型" prop="trainingType"></el-table-column>
      <el-table-column label="举办方" prop="organizers"></el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query" />
  </div>
</template>

<script>
import request from "@/utils/request";
const defaultExp = {
  trainTime: "",
  appraisalResult: "",
  trainingProgram: "",
};

export default {
  name: "TrainingRecords",
  data() {
    return {
      url: {
        list: "/reviewer/query/cstOffline/training",
        submit: "/reviewer/offline/training/submit",
      },
      dataSource: [],
      selectedIds: [],
      selectedIdsInList: [],
      queryData: {
        pageSize: 10,
        pageNum: 1,
      },
      total: 0,
    };
  },

  components: {},

  computed: {
    lastOne() {
      return (index) => this.formData.list.length === index + 1;
    },
  },

  mounted() {
    this.query();
  },

  methods: {
    query() {
      request({
        url:
          this.url.list +
          `?pageNum=${this.queryData.pageNum}&pageSize=${this.queryData.pageSize}`,
        method: "post",
        data: {
          accountId: this.$store.getters.userId + "",
          filterIs: "1",
          ...this.queryData,
        },
      })
        .then((res) => {
          if (res.code == 200) {
            let fields = ['trainingName',
              'trainingLocation',
              'trainingType',
              'organizers',]
            this.dataSource = res.rows.map(row => {
              for (const key in row) {
                if (Object.hasOwnProperty.call(row, key)) {
                  if (fields.includes(key)) {
                    row[key] = row[key] || '--'
                  }
                }
              }

              return row;
            });
            this.total = res.total;
            const selects = this.dataSource.filter((item) => {
              return item.participatesTraining == 0;
            });
            selects.forEach((row) => {
              if (!this.selectedIds.includes(row.id)) {
                if (!this.selectedIdsInList.includes(row.id)) {
                  this.selectedIdsInList.push(row.id);
                  this.$refs.multipleTable.toggleRowSelection(row);
                }
              }
            });
          }
        })
        .catch((error) => {
          // console.log(error);
        });
    },

    async getValue() {
      try {
        const result = await this.submit();
        if (!result) return false;
        if (result.code == 200) return true;
        return false;
      } catch (error) {
        return false;
      }
    },

    getKey(row) {
      return row.id;
    },

    handleSelectionChange(val) {
      this.selectedIds = val.map((item) => item.id);
    },

    getDraft() {
      try {
        this.submit();
        return false;
      } catch (error) {
        return false;
      }
    },

    submit() {
      if (!this.selectedIds.length) {
        this.$message({
          type: "warning",
          message: "至少需要选择一个线下培训项目！",
        });
        return false;
      }
      return request({
        url: this.url.submit,
        method: "post",
        data: this.selectedIds,
      });
    },
  },
};
</script>
<style lang='scss' scoped>
.training-records {
  padding: 24px;
}
</style>