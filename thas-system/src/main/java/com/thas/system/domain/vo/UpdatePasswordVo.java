package com.thas.system.domain.vo;

/**
 * 修改密码请求参数
 */
public class UpdatePasswordVo {
    /**
     * 邮箱
     */
    private String emailNo;
    /**
     * 密码
     */
    private String password;
    /**
     * 确认密码
     */
    private String confirmPwd;

    public UpdatePasswordVo() {
    }

    public UpdatePasswordVo(String emailNo, String password, String confirmPwd) {
        this.emailNo = emailNo;
        this.password = password;
        this.confirmPwd = confirmPwd;
    }

    public String getEmailNo() {
        return emailNo;
    }

    public void setEmailNo(String emailNo) {
        this.emailNo = emailNo;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getConfirmPwd() {
        return confirmPwd;
    }

    public void setConfirmPwd(String confirmPwd) {
        this.confirmPwd = confirmPwd;
    }

    @Override
    public String toString() {
        return "UpdatePasswordVo{" +
                "emailNo='" + emailNo + '\'' +
                ", password='" + password + '\'' +
                ", confirmPwd='" + confirmPwd + '\'' +
                '}';
    }
}
