<template>
  <div class="qualification-certificate">
    <el-form ref="elform" :model="formData">
      <el-table border size="mini" :data="formData.list">
        <el-table-column label="证书名称">
          <template slot-scope="scope">
            <el-form-item :prop="`list.${scope.$index}.certificate`" :rules="rules.certificate">
              <el-input size="small" v-model.trim="scope.row.certificate" style="width: 98%" placeholder="请输入证书名称" :maxlength="50"></el-input>
            </el-form-item>
            <div>如：1.学历学位证书；2.职称证书；3.评审员证书；4.社会兼职证明；5.英文能力证明等</div>
          </template>
        </el-table-column>
        <el-table-column label="证书文件">
          <template slot-scope="scope">
            <el-form-item :prop="`list.${scope.$index}.certificateId`" :rules="rules.certificateId">
              <el-button type="primary" @click="uploadFile(scope.$index)">
                上传
                <i class="el-icon-upload el-icon--right"></i>
              </el-button>
              <a style="margin-left:20px;">{{ scope.row.fileName }}</a>
            </el-form-item>
            <div>PS: 请上传纸质扫描文件或电子档文件；支持.JPG,.JPEG,.PNG,.PDF格式文件; 大小不超过100M</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <el-button type="text" v-if="formData.list.length > 1" @click="delWorkExp(scope.$index)">删除</el-button>
            <el-button type="text" v-if="lastOne(scope.$index)" @click="addWorkExp">新增</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form>

    <el-upload ref="upload" :action="action" :before-upload="beforeUpload" :on-success="onSuccess" :with-credentials="true" :on-error="onError" :data="{type : 1}" accept=".JPG, .JPEG, .PNG, .PDF" style="display: none">
      <span id="reviewerCertifiQualificationUpload" slot="trigger">上传图片</span>
    </el-upload>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";

const defaultExp = {
  certificateId: "",
  certificate: "",
  fileName: "",
};

export default {
  name: "WorkExperience",
  props: {
    info: {
      type: Object,
      default: () => { },
    },
  },
  watch: {
    info: {
      immediate: true,
      deep: true,
      handler() {
        // console.log(this.info);
        if (this.info && Object.keys(this.info).length) {
          for (const key in this.info) {
            if (Object.hasOwnProperty.call(this.info, key)) {
              if (key == 'list' && this.info[key].length == 0) {
                this.$set(this.formData, key, [{...defaultExp}]);
              } else {
                this.$set(this.formData, key, this.info[key]);
              }
            }
          }
        }
      },
    },
  },
  data() {
    return {
      formData: {
        list: [
          {
            ...defaultExp,
          },
        ],
      },
      rules: {
        certificateId: [
          {
            required: true,
            message: "请选择证书",
            trigger: "change",
          },
        ],
        certificate: [
          {
            required: true,
            message: "请输入证书名称",
            trigger: "change",
          },
        ],
      },
      fileMap: {},
      indexMap: {},
      currentIndex: -1,
      uploadDom: null,
    };
  },

  components: {},

  computed: {
    lastOne() {
      return (index) => this.formData.list.length === index + 1;
    },
    action() {
      return `${localStorage.baseUrl || process.env.VUE_APP_BASE_API
        }/binTang/common/uploadFile`;
    },
    header() {
      return {
        Authorization: this.action.includes('binTang') ? '' : "Bearer " + getToken(),
      }
    }
  },

  mounted() { },

  methods: {
    getToken,
    uploadFile(index) {
      this.currentIndex = index;
      if (!this.uploadDom) {
        this.uploadDom = document.getElementById(
          "reviewerCertifiQualificationUpload"
        );
      }
      this.uploadDom.click();
    },
    beforeUpload(file) {
      const list = '.JPG,.JPEG,.PNG,.PDF'.split(',').map(accept => accept.toLocaleUpperCase());
      const suffix = '.' + file.name.split('.').pop().toLocaleUpperCase();
      if (!list.includes(suffix)) {
        this.$message({
          type: 'warning',
          message: '请上传指定格式的文件！'
        })
        return false;
      }
      // 当fileSize大于0时才检查文件大小是否符合，为0或者小于0则不校验文件大小
      if (file.size / 1024 / 1024 > 100) {
        this.$message({
          type: 'warning',
          message: `上传的文件大小不得超过100M`
        })
        return false;
      }
      let fileKey = `${file.uid}/${file.name}`;
      let key = this.indexMap[this.currentIndex];
      if (key && fileKey != key) {
        delete this.indexMap[this.currentIndex];
        if (this.fileMap[fileKey] == this.currentIndex) {
          delete this.fileMap[fileKey];
        }
      } else {
        this.indexMap[this.currentIndex] = fileKey;
        this.fileMap[fileKey] = this.currentIndex;
      }
      return true;
    },
    onSuccess(response, file, fileList) {
      // console.log(response);
      const result = response.data;
      let fileKey = `${file.uid}/${file.name}`;
      let index = this.fileMap[fileKey];
      if (!isNaN(index)) {
        let key = this.indexMap[index];
        if (fileKey == key) {
          delete this.indexMap[index];
          delete this.fileMap[fileKey];
          this.$set(this.formData.list[index], "certificateId", result.fileId);
          this.$set(this.formData.list[index], "fileName", file.name);
        }
      }

      this.bus.$emit('fileIdUpload', 'certificateId', this.formData.list.map(({ certificateId }) => certificateId))
    },
    onError(err, file, fileList) {
      let fileKey = `${file.uuid}/${file.name}`;
      let index = this.fileMap[fileKey];
      if (!isNaN(index)) {
        let key = this.indexMap[index];
        if (fileKey == key) {
          delete this.indexMap[index];
          delete this.fileMap[fileKey];
          this.$set(this.formData.list[index], "certificateId", "");
          this.$set(this.formData.list[index], "fileName", "");
        }
      }
    },
    async getValue() {
      try {
        // const re = await this.$refs["elform"].validate();
        // if (re) return this.formData.list;
        if (this.formData.list.length == 1) {
          let data = this.formData.list[0]
          let flag = Object.values(data).some(value => !value)
          return flag ? [] : this.formData.list;
        }
        return this.formData.list;
      } catch (error) {
        return false;
      }
    },

    getDraft() {
      return this.formData.list;
    },

    addWorkExp() {
      this.formData.list.push({
        ...defaultExp,
      });
    },

    delWorkExp(index) {
      this.formData.list.length > 1 && this.formData.list.splice(index, 1);
    },
  },
};
</script>
<style lang='scss' scoped>
.qualification-certificate {
  padding: 24px;
  ::v-deep .el-table__cell {
    padding-bottom: 0;
  }
  ::v-deep .el-form-item {
    margin-bottom: 10px;
  }
}
</style>
