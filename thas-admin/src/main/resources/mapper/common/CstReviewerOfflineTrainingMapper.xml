<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.CstReviewerOfflineTrainingMapper">

    <resultMap type="com.thas.web.domain.CstReviewerOfflineTraining" id="CstReviewerOfflineTrainingResult">
        <result property="id" column="id"/>
        <result property="trainingId" column="training_id"/>
        <result property="reviewerId" column="reviewer_id"/>
        <result property="signFileId" column="sign_file_id"/>
        <result property="applyStatus" column="apply_status"/>
        <result property="partStatus" column="part_status"/>
        <result property="status" column="status"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateId" column="update_id"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectCstReviewerOfflineTrainingVo">
        select id, training_id, reviewer_id, sign_file_id, apply_status, part_status, status, create_id, create_time, update_id, update_time from cst_reviewer_offline_training
    </sql>

    <sql id="SQL_AES_ENCRYPT">'${@com.thas.web.config.SqlAesEncryptConfig@SQL_AES_ENCRYPT}'</sql>

    <delete id="deleteCstReviewerOfflineTraining" parameterType="CstReviewerOfflineTraining">
        delete
        from cst_reviewer_offline_training
        <where>
            <if test="trainingId != null  and trainingId != ''">and training_id = #{trainingId}</if>
            <if test="reviewerId != null  and reviewerId != ''">and reviewer_id = #{reviewerId}</if>
            <if test="applyStatus != null and applyStatus != ''">and apply_status = #{apply_status}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="createId != null  and createId != ''">and create_id = #{createId}</if>
            <if test="updateId != null  and updateId != ''">and update_id = #{updateId}</if>
        </where>
    </delete>

    <select id="selectCstReviewerOfflineTrainingList" parameterType="CstReviewerOfflineTraining"
            resultMap="CstReviewerOfflineTrainingResult">
        <include refid="selectCstReviewerOfflineTrainingVo"/>
        <where>
            <if test="reviewerId != null  and reviewerId != ''">and reviewer_id = #{reviewerId}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="createId != null  and createId != ''">and create_id = #{createId}</if>
            <if test="updateId != null  and updateId != ''">and update_id = #{updateId}</if>
            <if test="partStatus != null  and partStatus != ''">and part_status = #{partStatus}</if>
            <if test="trainingId != null  and trainingId != ''">and training_id in
            ( #{trainingId} )
            </if>
        </where>
    </select>

    <select id="selectCstReviewerOfflineTrainingById" parameterType="Long" resultMap="CstReviewerOfflineTrainingResult">
        <include refid="selectCstReviewerOfflineTrainingVo"/>
        where id = #{id}
    </select>

    <select id="selectCountByTrainingId" parameterType="String" resultType="Integer">
        select count(*)
        from cst_reviewer_offline_training
        where training_id = #{trainingId}
          and status = 1
    </select>

    <select id="selectCstReviewerOfflineTrainingByAccountId" parameterType="String" resultMap="CstReviewerOfflineTrainingResult">
        <include refid="selectCstReviewerOfflineTrainingVo"/>
        where reviewer_id = #{accountId}
            and status = 1
    </select>


    <select id="selectSysUserByTrainingId" resultType="com.thas.web.domain.vo.OfflineTrainingRegisteredVo">
        SELECT
            su.user_id userId,
            AES_DECRYPT(unhex(su.nick_name),<include refid="SQL_AES_ENCRYPT"/>) nickName,
            crft.part_status partStatus,
            crft.sign_file_id signFileId,
            ufi.origin fileName,
            ufi.path url,
            ufi.platform,
            crft.update_time registrationTime
        FROM
            cst_reviewer_offline_training crft
                LEFT JOIN sys_user su ON su.user_id = crft.reviewer_id
                LEFT JOIN upload_file_info ufi on crft.sign_file_id = ufi.id
        WHERE
            crft.training_id = #{trainingId}
          AND crft.STATUS = '1'
          AND su.STATUS = '0'
    </select>


    <insert id="insertCstReviewerOfflineTraining" parameterType="CstReviewerOfflineTraining" useGeneratedKeys="true"
            keyProperty="id">
        insert into cst_reviewer_offline_training
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="trainingId != null and trainingId != ''">training_id,</if>
            <if test="applyStatus != null and applyStatus != ''">apply_status,</if>
            <if test="reviewerId != null and reviewerId != ''">reviewer_id,</if>
            <if test="status != null">status,</if>
            <if test="createId != null and createId != ''">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null and updateId != ''">update_id,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="trainingId != null and trainingId != ''">#{trainingId},</if>
            <if test="applyStatus != null and applyStatus != ''">#{applyStatus},</if>
            <if test="reviewerId != null and reviewerId != ''">#{reviewerId},</if>
            <if test="status != null">#{status},</if>
            <if test="createId != null and createId != ''">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null and updateId != ''">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <insert id="batchInsertOfflineTraining" parameterType="CstReviewerOfflineTraining">
        insert into cst_reviewer_offline_training (training_id, reviewer_id, apply_status) values
        <foreach collection="list" item="reviewerOfflineTraining" separator="," close=";">
            (#{reviewerOfflineTraining.trainingId}, #{reviewerOfflineTraining.reviewerId}, #{reviewerOfflineTraining.applyStatus})
        </foreach>
    </insert>

    <update id="updateCstReviewerOfflineTraining" parameterType="CstReviewerOfflineTraining">
        update cst_reviewer_offline_training
        <trim prefix="SET" suffixOverrides=",">
            <if test="trainingId != null and trainingId != ''">training_id = #{trainingId},</if>
            <if test="applyStatus != null and applyStatus != ''">apply_status = #{applyStatus},</if>
            <if test="reviewerId != null and reviewerId != ''">reviewer_id = #{reviewerId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createId != null and createId != ''">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null and updateId != ''">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateStatusByAccountId">
        update cst_reviewer_offline_training set status = #{status} where reviewer_id = #{accountId}
    </update>

    <update id="updateInfoByAccountIdAndTraId" parameterType="CstReviewerOfflineTraining">
        update
            cst_reviewer_offline_training
        set
            sign_file_id = #{signFileId},
            part_status = #{partStatus}
        where
            reviewer_id = #{reviewerId}
          and
            training_id = #{trainingId}
          and
            status = 1
    </update>
</mapper>