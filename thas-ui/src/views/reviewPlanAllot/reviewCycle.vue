<template>
  <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="250px">
    <el-form-item v-for="(field, index) in reviewCycleOptions" :key="index" :label="field.dictLabel"
      :prop="field.dictValue" required :rules="rules[fields[field.dictValue]]">
      <!-- <el-date-picker type="daterange" v-model.trim="formData[field.dictValue]" format="yyyy-MM-dd"
        value-format="yyyy-MM-dd" :style="{ width: '90%' }" start-placeholder="开始日期" end-placeholder="结束日期"
        range-separator="至" clearable :picker-options="{ disabledDate: disabledDate }"
        @change="(value) => changeEvaluationTime(value, field.dictValue)"
        :disabled="pickerDisabled(index)"></el-date-picker> -->
      <div style="width: 90%; display: flex; align-items: center;">
        <el-date-picker v-model="formData[field.dictValue][0]" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
          placeholder="开始日期" clearable style="flex: 1;" @change="(value) => changeEvaluationTime(value, field.dictValue)">
        </el-date-picker>
        <span style="margin: 0 10px;">至</span>
        <el-date-picker v-model="formData[field.dictValue][1]" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
          placeholder="结束日期" clearable style="flex: 1;" @change="(value) => changeEvaluationTime(value, field.dictValue)">
        </el-date-picker>
      </div>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  name: 'ReviewCycle',
  components: {},
  props: ["applyNo", "time", "authStatus", "cycleStatus", 'progressOptions'],
  data() {
    return {
      value1: '',
      value2: '',
      formData: {},
      formDataTemp: {},
      fields: [
        "",
        "hosEvaluationTime",
        "preEvaluationTime",
        "reviewerEvaluationTime",
        "reportTime",
        "hosPerTime",
        "adminTime",
        "firstTime",
        "seniorTime",
        "lastTime",
      ],
      hosEvaluationTime: null,
      preEvaluationTime: null,
      reportTime: null,
      reviewerEvaluationTime: null,
      reviewCycleOptions: [],
    };
  },
  computed: {
    disabledDate() {
      const disabledDate = (time) => {
        return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
      };
      return disabledDate;
    },
    disableTime() {
      return () => {
        return;
      };
    },
    rules() {
      let rules = {};
      this.reviewCycleOptions.forEach((op) => {
        rules[this.fields[op.dictValue]] = [
          {
            trigger: "change",
            validator: (rule, value, callback) => {
              if (!value || !value[0] || !value[1]) {
                callback(`${op.dictLabel}日期不能为空`);
              } else if (value[0] && value[1] && (new Date(value[0]) > new Date(value[1]))) {
                callback('开始时间要小于等于结束时间');
              } else {
                callback();
              }
            }
          },
          {
            trigger: "change",
            validator: (rule, value, callback) =>
              this.timeRangeValidate(value, callback, Number(op.dictValue)),
          },
        ];
      });
      return rules;
    },
    currentNode() {
      let currentNode = null
      if (this.authStatus) {
        for (let index = 0; index < this.progressOptions.length; index++) {
          if (this.authStatus === this.progressOptions[index].currentStatus) {
            currentNode = this.progressOptions[index]
            break
          }
        }
      }
      return currentNode
    },
    currentCycle() {
      if (this.authStatus && this.progressOptions) {
        for (let index = 0; index < this.progressOptions.length; index++) {
          if (this.authStatus === this.progressOptions[index].currentStatus) {
            return Number(this.progressOptions[index].cycleStage)
          }
        }
      }
      return false
    }
  },
  watch: {
    time: {
      immediate: true,
      deep: true,
      handler() {
        if (this.time && this.time instanceof Array) {
          this.time.forEach((time) => {
            const times = time.cycle.split(",");
            this.$set(this.formData, time.stageValue, times);
            this.$set(this.formDataTemp, time.stageValue, times);
          });
        }
      },
    },
  },
  created() { },
  mounted() {
    this.initDicts();
  },
  methods: {
    initDicts() {
      this.getDicts("review_cycle").then((response) => {
        this.reviewCycleOptions = response.data;
        // 确保表单的字段和字段的启用字典数量一致
        this.reviewCycleOptions.forEach((option) => {
          if (!this.formData[option.dictValue]) {
            this.$set(this.formData, option.dictValue, ['', '']);
          }
        });
      });
    },
    resetForm() {
      this.$refs["elForm"].resetFields();
    },
    changeEvaluationTime(value, key) {
      this[this.fields[key]] = value && value.length ? new Date(value[1]).getTime() : null;
    },
    timeRangeValidate(value, callback, oField) {
      let field = oField === 8 ? oField - 2 : oField - 1;
      let preTimeRange = this.formData[field];
      if (!preTimeRange || !field) {
        this.$refs.elForm.validateField(field);
        return callback();
      } else {
        const [, end] = preTimeRange;
        const [nStart] = value;
        if (new Date(nStart).getTime() < new Date(end).getTime()) {
          let pre = this.reviewCycleOptions.find(
            (item) => item.dictValue == field
          );
          let ths = this.reviewCycleOptions.find(
            (item) => item.dictValue == oField
          );
          return callback(`${ths.dictLabel}时间需在${pre.dictLabel}时间之后`);
        }
        callback();
      }
    },
    async cycleToday() {
      let date = this.newFormatDate();
      let response = await this.getDicts("review_cycle");
      response.data.forEach((dict) => {
        this.$set(this.formData, dict.dictValue, [date, date]);
      });
    },
    newFormatDate() {
      var date = new Date();
      var year = date.getFullYear();
      var month =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      var day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return year + "-" + month + "-" + day;
    },
    async getValue() {
      try {
        const value = await this.$refs.elForm.validate();
        if (value) {
          const list = [];
          const timeList = this.time || [];
          for (const key in this.formData) {
            if (Object.hasOwnProperty.call(this.formData, key)) {
              let data = this.formData[key];
              if (data) {
                let id = (
                  timeList.find((t) => t.stageValue == key) || { id: undefined }
                ).id;
                let time = {
                  applyNo: this.applyNo,
                  stageValue: key,
                  cycle: `${data[0]},${data[1]}`,
                  id,
                };
                list.push(time);
              }
            }
          }
          return list;
        }
      } catch (error) {
        console.log(error)
        return false;
      }
    },
    // 日期选择器禁用
    pickerDisabled(index, type) {
      // let hospitalSelfStatuses = [
      //   this.AutSaAudCurrentStatusEnum.WAIT_TERM_SA,
      //   this.AutSaAudCurrentStatusEnum.TERM_SA_PROC,
      //   this.AutSaAudCurrentStatusEnum.WAIT_CONFIRM_SA_SUM,
      //   this.AutSaAudCurrentStatusEnum.WAIT_TERM_SA_SUM,
      // ]
      if (type === 2 && this.currentNode && this.currentNode.cycleStage === this.reviewCycleOptions[index].dictValue && this.formDataTemp[this.reviewCycleOptions[index].dictValue] && new Date(this.formDataTemp[this.reviewCycleOptions[index].dictValue]) < new Date()) {
        return true
      }
      if (Number(this.reviewCycleOptions[index].dictValue) < this.currentCycle) {
        return true
      }
      return false
      // if (!this.authStatus || this.cycleStatus == 2) {
      //   return false;
      // } else if (this.authStatus && hospitalSelfStatuses.includes(this.authStatus) && index < 1) {
      //   return true
      // } else if (this.authStatus && !hospitalSelfStatuses.includes(this.authStatus) && index < 3) {
      //   return true
      // } else {
      //   let statuses = [
      //     this.AutSaAudCurrentStatusEnum.WAIT_TERM_SA,
      //     this.AutSaAudCurrentStatusEnum.TERM_SA_PROC,
      //     this.AutSaAudCurrentStatusEnum.WAIT_CONFIRM_SA_SUM,
      //     this.AutSaAudCurrentStatusEnum.WAIT_TERM_SA_SUM,
      //     this.AutSaAudCurrentStatusEnum.TERM_SA_SUM,
      //     this.AutSaAudCurrentStatusEnum.WAIT_AUD_FIRST_TRIAL,
      //     this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_INQUIRE_PROC,
      //     this.AutSaAudCurrentStatusEnum.WAIT_CONFIRM_FIRST_TRIAL,
      //     this.AutSaAudCurrentStatusEnum.WAIT_AUD_FIRST_TRIAL_SUM,
      //     this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_SUM_REJECT,
      //     this.AutSaAudCurrentStatusEnum.ADMIN_ALLOT_ASSESSOR,
      //     this.AutSaAudCurrentStatusEnum.ASSESSOR_WRITE_SHEET,
      //     this.AutSaAudCurrentStatusEnum.ADMIN_CONFIRM_ASSESSOR,
      //     this.AutSaAudCurrentStatusEnum.HOSPITAL_AUDIT_ASSESSOR,
      //     this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_SUM_PASS
      //   ]
      //   return !statuses.includes(this.authStatus)
      // }
    },
    // 结束日期禁用规则
    getDisabledDateFn(index) {
      return (time) => {
        const today = new Date();
        // 将时间设置为当天的 00:00:00
        today.setHours(0, 0, 0, 0);
        if (this.formData[this.reviewCycleOptions[index].dictValue][0]) {
          return (new Date(time) < new Date(this.formData[this.reviewCycleOptions[index].dictValue][0])) || (time.getTime() < today.getTime())
        } else {
          return time.getTime() < today.getTime()
        }
      };
    }
  },
};
</script>

<style lang="scss" scoped></style>
