<template>
  <div class='practice-train-manage'>
    <el-row :gutter="24">
      <el-col :span="24" :xs="24">
        <el-form ref="queryForm" :model="queryData" size="small" label-width="80px" inline>
          <el-form-item label="课程名称" prop="subjectName">
            <el-input v-model.trim="queryData.subjectName" maxlength="128" placeholder="请输入课程名称" clearable :style="{ width: '100%' }"></el-input>
          </el-form-item>
          <el-form-item label="课程代码" prop="subjectCode">
            <el-input v-model.trim="queryData.subjectCode" maxlength="128" placeholder="请输入课程代码" clearable :style="{ width: '100%' }"></el-input>
          </el-form-item>
          <el-form-item label="课程形式" prop="subjectMode">
            <el-select v-model="queryData.subjectMode" clearable placeholder="请选择课程形式" :style="{ width: '100%' }">
              <el-option v-for="(option, index) in courseTypeOptions" :key="index" :value="option.dictValue" :label="option.dictLabel"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="search">
              查询
            </el-button>
            <el-button type="primary" size="small" @click="resetForm">
              重 置
            </el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-row class="row" :gutter="24">
      <el-button type="primary" @click="openPracticeDialog">新增实践培训课程</el-button>
      <!-- <el-button type="primary" @click="practice2DialogVisible = true">新增2</el-button> -->
    </el-row>
    <el-row class="row" :gutter="24">
      <el-table :data="dataSource" border stripe>
        <el-table-column align="center" label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="showTraineesDetail(scope.row, 1)">添加学员</el-button>
            <el-button type="text" @click="showTraineesSign(scope.row, 2)">学员签到</el-button>
            <el-button type="text" @click="showEdit(scope.row)">编辑</el-button>
            <el-button type="text" @click="deleteTrain(scope.row)" style="margin-left:10px;">删除</el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="课程名称" prop="subjectName"></el-table-column>
        <el-table-column align="center" label="课程代码" prop="subjectCode"></el-table-column>
        <el-table-column align="center" label="课程形式" prop="subjectMode">
          <template slot-scope="scope">
            <dict-span :noTag='true' :key="scope.row.subjectMode" :value="scope.row.subjectMode" :options="courseTypeOptions" type="default"></dict-span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="课程时间" prop="subjectTime" width="300"></el-table-column>
        <el-table-column align="center" label="现场评审带教时间" prop="sceneReviewTime" width="300"></el-table-column>
        <el-table-column align="center" label="带教评审员" prop="reviewerNames">
          <template slot-scope="{row}">
            {{ showNames(row) }}
          </template>
        </el-table-column>
      </el-table>
    </el-row>

    <el-dialog :title="courseForm.id ? '编辑课程' : '新增课程'" :visible.sync="practiceDialogVisible" :close-on-click-modal="false" width="60%">
      <el-form ref="createForm" :model="courseForm" :rules="rules" size="small" label-width="140px">
        <el-form-item label="课程名称" prop="subjectName">
          <el-input v-model.trim="courseForm.subjectName" maxlength="50" placeholder="请输入课程名称" clearable :style="{ width: '100%' }"></el-input>
        </el-form-item>
        <el-form-item label="课程代码" prop="subjectCode">
          <el-input v-model.trim="courseForm.subjectCode" maxlength="30" placeholder="请输入课程代码" clearable :style="{ width: '100%' }"></el-input>
        </el-form-item>
        <el-form-item label="课程形式" prop="subjectMode">
          <el-select v-model="courseForm.subjectMode" clearable placeholder="请选择课程形式" :style="{ width: '100%' }">
            <el-option v-for="(option, index) in courseTypeOptions" :key="index" :value="option.dictValue" :label="option.dictLabel"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="课程时间" prop="subjectTimes" :rules="subjectTimesRule">
          <!-- <el-input v-model.trim="courseForm.subjectTime" maxlength="128" placeholder="请选择课程时间" clearable :style="{ width: '100%' }"></el-input> -->
          <el-date-picker v-model="courseForm.subjectTimes" type="daterange" format="yyyy 年 MM 月 dd 日" value-format="yyyy 年 MM 月 dd 日" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" clearable :style="{ width: '100%' }">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="现场评审带教时间" prop="sceneReviewTimes" :rules="sceneReviewTimesRule">
          <!-- <el-input v-model.trim="courseForm.sceneReviewTime" maxlength="128" placeholder="请选择现场评审带教时间" clearable :style="{ width: '100%' }"></el-input> -->
          <el-date-picker v-model="courseForm.sceneReviewTimes" type="daterange" format="yyyy 年 MM 月 dd 日" value-format="yyyy 年 MM 月 dd 日" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" clearable :style="{ width: '100%' }">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="带教评审员" prop="teaReviewerIds" :rules="teaReviewerIdsRule">
          <el-select v-model="courseForm.teaReviewerIds" clearable multiple filterable placeholder="请选择带教评审员" :style="{ width: '100%' }">
            <el-option v-for="item in reviewers" :key="item.value" :label="item.nickName" :value="item.userId">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="practiceDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitSubject" style="margin-left:10px;">提 交</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="trainFlag == 1 ? '添加学员' : '上传签到表'" :visible.sync="practice2DialogVisible" width="80%" :close-on-click-modal="false">
      <el-form ref="editForm" :model="queryData" size="small" label-width="80px">
        <el-form-item v-if="trainFlag == 1" label="参与学员" prop="teaReviewerId">
          <el-select v-model="traineeIds" clearable multiple filterable placeholder="请选择学员" :style="{ width: '100%' }" @change="traineesChange">
            <el-option v-for="(item, index) in trainees" :key="index" :label="item.nickName" :value="item.userId">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <el-table v-if="trainFlag == 2" :data="traineesSource || []" border stripe>
        <el-table-column label="学员名称" align="center" prop="nickName"></el-table-column>
        <el-table-column label="签到表" align="center" prop="signFileId">
          <template slot-scope="scope">
            {{scope.row.file ? scope.row.file.fileName : ''}}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="180">
          <template slot-scope="scope">
            <el-button type="text" @click="uploadSignInSheet(scope.row)" :disabled="!!student && uploading" v-loading="student && scope.row.userId == student.userId && uploading">上传签到表</el-button>
            <el-button type="text" v-if="scope.row.file" @click="download(scope.row.file)" style="margin-left:10px;">下载</el-button>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeSignForm">取 消</el-button>
        <el-button type="primary" @click="addTrainees" :disabled="trainFlag == 1 && (!traineeIds || !traineeIds.length)" style="margin-left:10px;">保 存</el-button>
      </span>
    </el-dialog>

    <el-upload ref="upload" :action="action" :before-upload="beforeUpload" :on-success="onSuccess" :on-error="onError" :with-credentials="true" :headers="{
        Authorization: 'Bearer ' + getToken(),
      }" :data='{type : 1}' accept=".JPG,.JPEG,.PNG,.PDF" style="display: none">
      <span id="signInSheetUpload" slot="trigger">
      </span>
    </el-upload>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import request, { download } from "@/utils/request";

export default {
  name: 'PracticeTrainManage',
  data() {
    return {
      url: {
        // trainees: 'reviewer/query/trainees/review/list',
        fileAndReviewer: '/trainees/rec/fileAndReviewer',
        add: '/practice/subject',
        edit: '/practice/subject/edit',
        list: '/practice/subject/list',
        get: '/trainees/practice/list',
        detail: '/trainees/practice/fileAndReviewer',
        addTrain: '/trainees/practice/insert'
      },
      queryData: {
        subjectName: '',
        subjectMode: null,
        subjectCode: '',
        pageSize: 10,
        pageNum: 1,
      },
      dataSource: [],
      practiceDialogVisible: false,
      practice2DialogVisible: false,
      courseForm: {
        subjectCode: '',
        subjectName: '',
        subjectMode: '',
        subjectTime: '',
        sceneReviewTime: '',
        subjectTimes: [],
        sceneReviewTimes: [],
        teaReviewerId: '',
        teaReviewerIds: [],
      },
      checkList: [],
      students: [],
      student: null,
      courseTypeOptions: [],
      trainees: [],
      reviewers: [],
      traineesSource: [],
      traineeIds: [],
      subject: {},
      trainFlag: 1,
      uploading: false,
      hasTrainee: false,
    };
  },

  components: {},

  computed: {
    action() {
      return `${localStorage.baseUrl || process.env.VUE_APP_BASE_API}/common/uploadFile`;
    },

    rules() {
      return {
        subjectCode: [{
          required: true,
          message: '请输入课程编码',
          trigger: ['blur', 'change']
        }],
        subjectName: [{
          required: true,
          message: '请输入课程名称',
          trigger: ['blur', 'change']
        }],
        subjectMode: [{
          required: true,
          message: '请输入课程形式',
          trigger: ['blur', 'change']
        }],
      }
    },
    subjectTimesRule() {
      return [{
        required: true,
        validator: this.subjectTimesValid,
        trigger: ['blur', 'change']
      }]
    },
    sceneReviewTimesRule() {
      return [{
        required: true,
        validator: this.sceneReviewTimeValid,
        trigger: ['blur', 'change']
      }]
    },
    teaReviewerIdsRule() {
      return [{
        required: true,
        validator: this.teaReviewerIdValid,
        trigger: ['blur', 'change']
      }]
    }
  },

  created() {
    this.getDicts('course_form').then(res => {
      this.courseTypeOptions = res.data;
    })
  },


  mounted() {
    this.subjectQuery();
    this.getTrainees();
    this.getReviewers();
  },

  methods: {
    getToken,
    search() {
      this.queryData.pageNum = 1;
      this.subjectQuery();
    },
    showNames({teaReviewerId, teaReviewerMap}) {
      if (!teaReviewerMap || !teaReviewerId) return '--';
      let names = teaReviewerId.split(',').map(id => {
        return teaReviewerMap[id].name
      })
      return names.join('，');
    },
    getTrainees() {
      request({
        url: '/system/user/list?roleId=107',
        method: 'get',
      }).then(res => {
        this.trainees = res.rows
      })
    },
    getReviewers() {
      request({
        url: '/system/user/list?roleId=101',
        method: 'get',
      }).then(res => {
        this.reviewers = res.rows
      })
    },
    resetForm() {
      this.$refs["queryForm"].resetFields();
      this.queryData.pageNum = 1
      this.subjectQuery();
    },
    openPracticeDialog() {
      this.courseForm = {
        subjectCode: '',
        subjectName: '',
        subjectMode: '',
        subjectTime: '',
        sceneReviewTime: '',
        subjectTimes: [],
        sceneReviewTimes: [],
        teaReviewerId: '',
        teaReviewerIds: [],
      }
      if (this.$refs.createForm) {
        this.$refs.createForm.resetFields()
      }
      this.practiceDialogVisible = true
    },
    showEdit(subject) {
      let {
        subjectTime,
        sceneReviewTime,
        teaReviewerId,
        ...data
      } = subject

      let subjectTimes = subjectTime.split('-'),
        sceneReviewTimes = sceneReviewTime.split('-'),
        teaReviewerIds = teaReviewerId.split(',').map(id => Number(id));

      this.courseForm = {
        ...data,
        subjectTimes,
        sceneReviewTimes,
        teaReviewerIds
      }

      this.practiceDialogVisible = true;
    },

    showTraineesDetail(subject, trainFlag) {
      this.subject = subject;
      this.trainFlag = trainFlag
      this.traineesQuery(subject)
    },

    showTraineesSign(subject, trainFlag) {
      this.subject = subject;
      this.trainFlag = trainFlag
      this.traineesQuery(subject)
    },

    traineesQuery(subject) {
      if (!subject || !subject.id) return;
      request({
        url: this.url.get,
        method: 'get',
        params: {
          praId: subject.id
        }
      }).then(res => {
        this.practice2DialogVisible = true;
        let ids = res.rows.map(({ accountId }) => Number(accountId))
        this.traineeIds = [...new Set(ids)]
        this.hasTrainee = ids.length != 0;
        this.traineesChange(this.traineeIds)
        res.rows.forEach(({ id, accountId }) => this.getFile(id, accountId))
      })
    },

    deleteTrain(row) {
      request({
        url: '/practice/subject/' + row.id,
        method: 'delete',
      }).then(() => {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        this.subjectQuery()
      })
    },

    download(file) {
      download("/common/downloadFile", { fileId: file.fileId }, file.fileName);
    },

    getFile(id, accountId) {
      request({
        url: this.url.detail,
        method: 'get',
        params: { id }
      }).then(res => {
        if (res.data.fileInfo) {
          this.traineesSource.forEach(trainee => {
            if (trainee.userId == accountId) {
              this.$set(trainee, 'file', res.data.fileInfo)
            }
          })
        }
      })
    },

    subjectQuery() {
      request({
        url: this.url.list,
        method: 'get',
        params: this.queryData
      }).then(res => {
        if (!res) return;
        this.dataSource = res.rows || []
      })
    },

    traineesChange(value) {
      let selectTrainees = this.trainees.filter(({ userId }) => value.includes(Number(userId)));
      if (this.traineesSource.length == 0) {
        this.traineesSource = selectTrainees;
      } else {
        let ids = selectTrainees.map(({ userId }) => Number(userId))
        let no = ids.filter(id => !this.traineesSource.includes(`${id}`)).map(id => {
          return this.trainees.find(({ userId }) => userId == id)
        })
        this.traineesSource = no;
      }
    },

    submitSubject() {
      this.$refs.createForm.validate(valid => {
        if (!valid) return;
        this.courseForm.subjectTime = this.courseForm.subjectTimes[0] + '-' + this.courseForm.subjectTimes[1]
        this.courseForm.sceneReviewTime = this.courseForm.sceneReviewTimes[0] + '-' + this.courseForm.sceneReviewTimes[1]
        this.courseForm.teaReviewerId = this.courseForm.teaReviewerIds.join(',')

        let {
          subjectTimes,
          sceneReviewTimes,
          teaReviewerIds,
          ...data
        } = this.courseForm
        request({
          url: this.url.add,
          method: !data.id ? 'post' : 'put',
          data
        }).then(res => {
          this.practiceDialogVisible = false;
          this.$refs.createForm.resetFields()
          this.subjectQuery()
        })
      })
    },

    uploadSignInSheet({ userId }) {
      let student = this.traineesSource.find(student => student.userId === userId);
      if (student) {
        this.student = student;
        document.getElementById('signInSheetUpload').click()
      } else {
        this.student = null
      }
    },

    beforeUpload(file) {
      const list = '.JPG,.JPEG,.PNG,.PDF'.split(',').map(accept => accept.toLocaleUpperCase());
      const suffix = '.' + file.name.split('.').pop().toLocaleUpperCase();
      if (!list.includes(suffix)) {
        this.$message({
          type: 'warning',
          message: '请上传指定格式的文件！'
        })
        return false;
      }
      // 当fileSize大于0时才检查文件大小是否符合，为0或者小于0则不校验文件大小
      if (file.size / 1024 / 1024 > 100) {
        this.$message({
          type: 'warning',
          message: `上传的文件大小不得超过100M`
        })
        return false;
      }
      this.uploading = true;
      return true;
    },

    onSuccess(response, file, fileList) {
      if (response.code == 200) {
        if (this.student?.fileIds) {
          this.shareDelete([this.student.fileIds])
        }
        this.student.file = response.data;
        this.student.fileIds = response.data.fileId
        this.uploading = false;
        this.student = null
      }
    },

    onError() {
      this.uploading = false;
      this.student = null
    },


    addTrainees() {
      if (this.traineesSource.length == 0) return;
      let data = this.traineesSource.map(trainees => {
        return {
          accountId: trainees.userId,
          praId: this.subject.id,
          traName: trainees.nickName,
          traGender: trainees.sex,
          signFileId: trainees.file ? trainees.file.fileId : undefined
        }
      })
      request({
        url: this.url.addTrain,
        method: 'post',
        data
      }).then(() => {
        if (this.trainFlag == 2) {
          let ids = [...new Set(this.traineesSource.filter(({ file }) => file).map(({ file }) => file.fileId) || [])]
          if (ids.length) {
            // 管理员上传
            this.shareCreate(ids, this.$store.getters.userId, 'reviewer_continuous_training');
          }
        }
        this.closeSignForm();
      })
    },

    closeSignForm() {
      this.practice2DialogVisible = false
      this.traineesSource = []
      this.traineeIds = []
    },

    subjectTimesValid(rule, value, callback) {
      if (!Array.isArray(value) || value.length != 2) callback('请选择课程时间')
      callback();
    },

    sceneReviewTimeValid(rule, value, callback) {
      if (!Array.isArray(value) || value.length != 2) callback('请选择现场评审带教时间')
      callback();
    },

    teaReviewerIdValid(rule, value, callback) {
      if (!Array.isArray(value) || value.length == 0) callback('请选择带教评审员')
      callback();
    }
  }
}

</script>
<style lang='scss'>
.practice-train-manage {
  padding: 24px;

  .el-button + .el-button {
    margin-left: 0;
  }
}
.row + .row {
  margin-top: 15px;
}
</style>
