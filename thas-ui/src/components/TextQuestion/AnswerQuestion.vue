<template>
  <div class="answer-question">
    <el-form ref="elForm" :model="formData" size="medium" :rules='rules' label-width="100px">
      <el-form-item :label="title" :class="className">
        {{ formData.question }}
      </el-form-item>
      <el-form-item label="" prop="value">
        <el-radio-group v-if="formData.questionType == 1" v-model.trim="formData.value"  size="medium"  >
          <el-radio v-for="(item, index) in formData.singleAnswerOptions" :key="index" :label="index">
            {{ item.label }}
          </el-radio>
        </el-radio-group>
        <el-checkbox-group v-if="formData.questionType == 2" v-model.trim="formData.value" :max="4" size="medium">
            <el-checkbox v-for="(item, index) in formData.multipleAnswerOptions" :key="index" :label="index">
                {{ item.label }}
            </el-checkbox>
        </el-checkbox-group>
        <el-input :type="inputType" v-model.trim="formData.value" v-if="isTextBox"></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "AnswerQuestion",
  props: {
    data: {
        type: Object,
        default: () => {}
    },
    index: {
        type: String,
        default: () => "1",
    },
    showIndex: {
        type: Boolean,
        default: () => true,
    }
  },
  watch: {
    data: {
        immediate:true,
        deep:true,
        handler() {
            const answer = this.data.questionType == 2 ? [] : ''
            this.formData = Object.assign({ value: answer }, this.data);
        }
    }
  },
  data() {
    return {
      formData: {
        question: "",
      },
      rules: {
          value: {
              require:true,
              validator:(rule, value, callback) => this.answerValidator(rule, value, callback),
              trigger: ['blur']
          }
      }
    };
  },
  computed: {
      className() {
          const className =  this.showIndex ? '' : 'xing'
          return ['title'].concat([className]).join(" ")
      },
      title() {
        return this.showIndex ? `${this.index}、` : '* '
      },
      inputType() {
          return this.formData.questionType == 3 ? 'text' : 'textarea'
      },
      isSelected() {
          return this.formData.questionType == 1 || this.formData.questionType == 2
      },
      isTextBox() {
          return this.formData.questionType == 3 || this.formData.questionType == 4
      }
  },
  methods: {
    setValue(value) {
        const answer = value.questionType == 2 ? [] : ''
        this.formData = Object.assign({ value: answer }, value);
    },
    submittt() {
        this.$refs.elForm.validate();
    },
    answerValidator(rule, value, callback) {
        if (this.isSelected) {
            if (!value || value.length == 0) {
                callback('请选择答案')
            }
            callback();
        }
        if (this.isTextBox) {
            if (!value) {
                callback('请填写内容')
            }
            if (this.formData.checkout && !(new RegExp(this.formData.checkout, 'g').test(value))) {
                callback('请填写正确内容');
            }
            callback();
        }
    },
    async getValue() {
        try {
            if (await this.$refs['elForm'].validate()) {
                return this.formData;
            }
      } catch {
        return false;
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.title {
  margin-bottom: 0;
  ::v-deep .el-form-item__label {
    padding-right: 0;
  }
}
::v-deep .el-radio {
  display: block;
  padding: 10px 0 0 0;
}
::v-deep .el-checkbox {
  display: block;
}
.xing ::v-deep .el-form-item__label{
    color: red;
    padding-right: 5px;
}
</style>