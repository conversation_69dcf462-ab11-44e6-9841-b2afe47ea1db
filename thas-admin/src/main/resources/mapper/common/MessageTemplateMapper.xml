<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.MessageTemplateMapper">

    <resultMap type="MessageTemplate" id="MessageTemplateResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="content"    column="content"    />
        <result property="type"    column="type"    />
        <result property="sendType"    column="send_type"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMessageTemplateVo">
        select id, name, content, type, send_type, status, del_flag, create_by, create_time, update_by, update_time from message_template
    </sql>

    <select id="selectMessageTemplateList" parameterType="MessageTemplate" resultMap="MessageTemplateResult">
        <include refid="selectMessageTemplateVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="sendType != null  and sendType != ''"> and send_type = #{sendType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectMessageTemplateListByName" parameterType="MessageTemplate" resultMap="MessageTemplateResult">
        <include refid="selectMessageTemplateVo"/>
        <where>
            <if test="name != null  and name != ''"> and name = #{name} </if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="sendType != null  and sendType != ''"> and send_type = #{sendType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        ORDER BY create_time DESC

    </select>

    <select id="selectCountByTypeAndStatus" resultType="int">
        select count(*) from message_template where type = #{type} and status = #{status}
    </select>

    <select id="selectMessageTemplateById" parameterType="Long" resultMap="MessageTemplateResult">
        <include refid="selectMessageTemplateVo"/>
        where id = #{id}
    </select>


    <insert id="insertMessageTemplate" parameterType="MessageTemplate" useGeneratedKeys="true" keyProperty="id">
        insert into message_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="sendType != null and sendType != ''">send_type,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="sendType != null and sendType != ''">#{sendType},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMessageTemplate" parameterType="MessageTemplate">
        update message_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="sendType != null and sendType != ''">send_type = #{sendType},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMessageTemplateById" parameterType="Long">
        delete from message_template where id = #{id}
    </delete>

    <delete id="deleteMessageTemplateByIds" parameterType="String">
        delete from message_template where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
