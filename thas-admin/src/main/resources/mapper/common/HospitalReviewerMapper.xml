<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.HospitalReviewerMapper">

    <resultMap type="com.thas.web.domain.HospitalReviewer" id="HospitalReviewerResult">
        <result property="id" column="id"/>
        <result property="applyNo" column="apply_no"/>
        <result property="autCode" column="aut_code"/>
        <result property="reviewerId" column="reviewer_id"/>
        <result property="fieldIdList" column="field_id_list"/>
        <result property="leaderIs" column="leader_is"/>
        <result property="hasInterest" column="has_interest"/>
        <result property="interestDesc" column="interest_desc"/>
        <result property="interestFileId" column="interest_file_id"/>
        <result property="adminInterestFileId" column="admin_interest_file_id"/>
        <result property="reviewerStatus" column="reviewer_status"/>
        <result property="passIds" column="pass_ids"/>
        <result property="isShare" column="is_share"/>
        <result property="isSubEvaluate" column="is_sub_evaluate"/>
        <result property="status" column="status"/>
        <result property="creator" column="creator"/>
        <result property="createTime" column="create_time"/>
        <result property="updater" column="updater"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="SQL_AES_ENCRYPT">'${@com.thas.web.config.SqlAesEncryptConfig@SQL_AES_ENCRYPT}'</sql>

    <sql id="selectHospitalReviewerVo">
        select id,
               apply_no,
               aut_code,
               reviewer_id,
               field_id_list,
               leader_is,
               status,
               has_interest,
               interest_desc,
               interest_file_id,
               admin_interest_file_id,
               creator,
               create_time,
               updater,
               update_time,
               reviewer_status,
               pass_ids,
               is_share,
               is_sub_evaluate
        from hospital_reviewer
    </sql>

    <select id="selectHospitalReviewerList" parameterType="HospitalReviewer" resultMap="HospitalReviewerResult">
        <include refid="selectHospitalReviewerVo"/>
        <where>
            <if test="applyNo != null  and applyNo != ''">and apply_no = #{applyNo}</if>
            <if test="autCode != null  and autCode != ''">and aut_code = #{autCode}</if>
            <if test="reviewerId != null  and reviewerId != ''">and reviewer_id = #{reviewerId}</if>
            <if test="leaderIs != null ">and leader_is = #{leaderIs}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="reviewerStatus != null ">and reviewer_status = #{reviewerStatus}</if>
            <if test="passIds != null  and passIds != ''">and pass_ids = #{passIds}</if>
            <if test="isShare != null ">and is_share = #{isShare}</if>
            <if test="isSubEvaluate != null ">and is_sub_evaluate = #{isSubEvaluate}</if>
            <if test="creator != null  and creator != ''">and creator = #{creator}</if>
            <if test="updater != null  and updater != ''">and updater = #{updater}</if>
        </where>
    </select>

    <select id="selectHospitalReviewerById" parameterType="Long" resultMap="HospitalReviewerResult">
        <include refid="selectHospitalReviewerVo"/>
        where id = #{id}
    </select>

    <select id="selectHospitalReviewerByLeader" resultMap="HospitalReviewerResult">
        <include refid="selectHospitalReviewerVo"/>
        where apply_no = #{applyNo} and leader_is = #{leaderIs} and status = 1
    </select>

    <select id="isAllocation" resultType="java.lang.Integer">
        select count(1)
        from hospital_reviewer
        where apply_no = #{applyNo}
          and status = #{status}
          and field_id_list != 'SENIOR_REVIEW'
          and field_id_list != 'TRAINEES_REVIEW'
    </select>

    <select id="selectHospitalReviewerByApplyNo" parameterType="String" resultMap="HospitalReviewerResult">
        <include refid="selectHospitalReviewerVo"/>
        where apply_no = #{applyNo} and status = 1
    </select>

    <select id="selectReviewerUserInfoByApplyNo" parameterType="String" resultType="Map">
        SELECT hr.field_id_list fieldIdList,
        hr.leader_is leaderIs,
        hr.reviewer_status reviewerStatus,
        su.user_id accountId,
        AES_DECRYPT(unhex(su.nick_name),<include refid="SQL_AES_ENCRYPT"/>) name
        FROM hospital_reviewer hr
        LEFT JOIN sys_user su ON hr.reviewer_id = su.user_id
        WHERE hr.apply_no = #{applyNo}
        AND hr.status = 1
    </select>

    <select id="selectHospitalReviewerByApplyNoAndFieldId" parameterType="String" resultMap="HospitalReviewerResult">
        <include refid="selectHospitalReviewerVo"/>
        where apply_no = #{applyNo} and status = 1
        <if test="fieldIdList == null or fieldIdList == ''">
            and field_id_list != 'SENIOR_REVIEW' and field_id_list != 'TRAINEES_REVIEW'
        </if>
        <if test="fieldIdList != null and fieldIdList != ''">
            and field_id_list = #{fieldIdList}
        </if>
    </select>

    <select id="selectHospitalReviewerByApplyNoAndAccountId" parameterType="String" resultMap="HospitalReviewerResult">
        <include refid="selectHospitalReviewerVo"/>
        where apply_no = #{applyNo} and reviewer_id = #{accountId} and status = 1
    </select>

    <select id="selectHosPlanUserInfoByAccountId" parameterType="HospitalPreExam"
            resultType="com.thas.web.dto.HosPlanUserInfoVO">
        select
        su.user_id accountId,
        AES_DECRYPT(unhex(su.nick_name),<include refid="SQL_AES_ENCRYPT"/>) name,
        rbi.first_batch firstBatch
        from sys_user su
        left join reviewer_base_info rbi on su.user_id = rbi.account_id
        where su.user_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.reviewerId}
        </foreach>
    </select>

    <insert id="insertHospitalReviewer" parameterType="HospitalReviewer" useGeneratedKeys="true" keyProperty="id">
        insert into hospital_reviewer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">apply_no,</if>
            <if test="autCode != null and autCode != ''">aut_code,</if>
            <if test="reviewerId != null and reviewerId != ''">reviewer_id,</if>
            <if test="fieldIdList != null and fieldIdList != ''">field_id_list,</if>
            <if test="leaderIs != null ">leader_is,</if>
            <if test="status != null">status,</if>
            <if test="reviewerStatus != null">reviewer_status,</if>
            <if test="creator != null and creator != ''">creator,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updater != null and updater != ''">updater,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="passIds != null">pass_ids,</if>
            <if test="isShare != null">is_share,</if>
            <if test="isSubEvaluate != null">is_sub_evaluate,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">#{applyNo},</if>
            <if test="autCode != null and autCode != ''">#{autCode},</if>
            <if test="reviewerId != null and reviewerId != ''">#{reviewerId},</if>
            <if test="fieldIdList != null and fieldIdList != ''">#{fieldIdList},</if>
            <if test="leaderIs != null ">#{leaderIs},</if>
            <if test="status != null">#{status},</if>
            <if test="reviewerStatus != null">#{reviewerStatus},</if>
            <if test="passIds != null">#{passIds},</if>
            <if test="isShare != null">#{isShare},</if>
            <if test="isSubEvaluate != null">#{isSubEvaluate},</if>
            <if test="creator != null and creator != ''">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updater != null and updater != ''">#{updater},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateHospitalReviewer" parameterType="HospitalReviewer">
        update hospital_reviewer
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">apply_no = #{applyNo},</if>
            <if test="reviewerId != null and reviewerId != ''">reviewer_id = #{reviewerId},</if>
            <if test="fieldIdList != null and fieldIdList != ''">field_id_list = #{fieldIdList},</if>
            <if test="leaderIs != null ">leader_is = #{leaderIs},</if>
            <if test="hasInterest != null ">has_interest = #{hasInterest},</if>
            <if test="interestDesc != null">interest_desc = #{interestDesc},</if>
            <if test="interestFileId != null ">interest_file_id = #{interestFileId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="reviewerStatus != null">reviewer_status = #{reviewerStatus},</if>
            <if test="passIds != null">pass_ids = #{passIds},</if>
            <if test="isShare != null">is_share = #{isShare},</if>
            <if test="isSubEvaluate != null">is_sub_evaluate = #{isSubEvaluate},</if>
            <if test="creator != null and creator != ''">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateHospitalReviewerByApplyNo" parameterType="HospitalReviewer">
        update hospital_reviewer
        <trim prefix="SET" suffixOverrides=",">
            <if test="reviewerId != null and reviewerId != ''">reviewer_id = #{reviewerId},</if>
            <if test="fieldIdList != null and fieldIdList != ''">field_id_list = #{fieldIdList},</if>
            <if test="adminInterestFileId != null and adminInterestFileId != ''">admin_interest_file_id =
                #{adminInterestFileId},
            </if>
            <if test="leaderIs != null ">leader_is = #{leaderIs},</if>
            <if test="status != null">status = #{status},</if>
            <if test="reviewerStatus != null">reviewer_status = #{reviewerStatus},</if>
            <if test="creator != null and creator != ''">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="passIds != null">pass_ids = #{passIds},</if>
            <if test="isShare != null">is_share = #{isShare},</if>
            <if test="isSubEvaluate != null">is_sub_evaluate = #{isSubEvaluate},</if>
        </trim>
        where apply_no = #{applyNo}
        <if test="reviewerId != null and reviewerId != ''">and reviewer_id = #{reviewerId}</if>
        and status = 1
    </update>

    <update id="updateHosRecByAplNAndFil" parameterType="HospitalReviewer">
        update hospital_reviewer
        <trim prefix="SET" suffixOverrides=",">
            <if test="reviewerId != null and reviewerId != ''">reviewer_id = #{reviewerId},</if>
            <if test="fieldIdList != null and fieldIdList != ''">field_id_list = #{fieldIdList},</if>
            <if test="leaderIs != null ">leader_is = #{leaderIs},</if>
            <if test="status != null">status = #{status},</if>
            <if test="reviewerStatus != null">reviewer_status = #{reviewerStatus},</if>
            <if test="creator != null and creator != ''">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where apply_no = #{applyNo} and field_id_list = #{fieldIdList} and status = 1
    </update>

    <select id="selectHospitalSeniorReviewByApplyNo" parameterType="String" resultMap="HospitalReviewerResult">
        <include refid="selectHospitalReviewerVo"/>
        where apply_no = #{applyNo} and field_id_list = 'SENIOR_REVIEW' and reviewer_status = 1 and status = 1
    </select>

    <select id="selectSeniorReviewerList" parameterType="String" resultType="com.thas.web.dto.SysUserBaseInfo">
        SELECT su.user_id accountId,
        su.user_name userName,
        AES_DECRYPT(unhex(su.nick_name),<include refid="SQL_AES_ENCRYPT"/>) name
        FROM sys_user su
        LEFT JOIN sys_user_role sur ON su.user_id = sur.user_id
        LEFT JOIN sys_role sr ON sur.role_id = sr.role_id
        LEFT JOIN reviewer_base_info rbi ON su.user_id = rbi.account_id
        WHERE su.is_leave = 0
        AND sr.role_key = #{roleKey}
        AND su.STATUS = 0
        AND rbi.STATUS = 1
    </select>

    <delete id="deleteHospitalReviewerById" parameterType="Long">
        delete
        from hospital_reviewer
        where id = #{id}
    </delete>

    <delete id="deleteHospitalReviewerByIds" parameterType="String">
        delete from hospital_reviewer where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteHospitalReviewerByReviewerIdsAndApplyNo">
        delete from hospital_reviewer where reviewer_id in
        <foreach item="item" index="index" collection="reviewerIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        and apply_no = #{applyNo}
        and status = 1
    </delete>

    <update id="updateReviewerStatusByApplyNoAndStatus" parameterType="String">
        UPDATE hospital_reviewer
        SET status = 2
        WHERE
        apply_no = #{applyNo}
        <if test='reviewerFlag != null and reviewerFlag == "SENIOR_REVIEW"'>
            and field_id_list = 'SENIOR_REVIEW'
        </if>
        <if test='reviewerFlag == null or reviewerFlag == ""'>
            and field_id_list != 'SENIOR_REVIEW'
        </if>
        AND reviewer_status = 2
        AND status = 1
    </update>

    <update id="updateReviewerStatusByApplyNo" parameterType="string">
        UPDATE hospital_reviewer
        SET reviewer_status = 1
        WHERE
        apply_no = #{applyNo}
        <if test='reviewerFlag != null and reviewerFlag == "SENIOR_REVIEW"'>
            and field_id_list = 'SENIOR_REVIEW'
        </if>
        <if test='reviewerFlag == null or reviewerFlag == ""'>
            and field_id_list != 'SENIOR_REVIEW'
        </if>
        AND reviewer_status = 0
        AND status = 1
    </update>

    <update id="updateHospitalReviewerByApplyNoList">
        update hospital_reviewer set status =2 where apply_no in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and status = 1
    </update>

    <select id="selectApplyNosByReviewerId" parameterType="String" resultType="com.thas.web.domain.HospitalBaseInfo">
        select distinct t.apply_no applyNo, ttt.hospital_name hospitalName
        from hospital_reviewer t
        left join hospital_planned_distribution tt on t.apply_no = tt.apply_no and tt.status = 1
        left join hospital_base_info ttt on ttt.apply_no = tt.apply_no and ttt.status = 1
        where t.reviewer_id = #{reviewerId}
        and t.status = 1
        <if test="roleType != null and roleType != '' ">
            and tt.senior_review_dis_complete = 1
            and t.field_id_list = #{roleType}
        </if>
        <if test="roleType == null  or roleType == ''">
            and tt.review_dis_complete = 1
            and t.field_id_list != 'TRAINEES_REVIEW'
        </if>
    </select>

    <select id="queryReviewManage" parameterType="com.thas.web.dto.ReviewManageDTO"
            resultType="com.thas.web.dto.ReviewManageVO">
        SELECT
        hbi.apply_no applyNo,
        hbi.hospital_name hospitalName,
        hr.reviewer_id accountId,
        AES_DECRYPT(unhex(su.nick_name),<include refid="SQL_AES_ENCRYPT"/>) reviewerName,
        AES_DECRYPT(unhex(su.phonenumber),<include refid="SQL_AES_ENCRYPT"/>) mobile,
        hr.leader_is leaderIs,
        AES_DECRYPT(unhex(sy.nick_name),<include refid="SQL_AES_ENCRYPT"/>) disPersonName,
        hr.create_time disTime,
        su.is_leave leaveIs
        FROM
        hospital_reviewer hr
        LEFT JOIN hospital_base_info hbi ON hr.apply_no = hbi.apply_no
        LEFT JOIN sys_user su ON hr.reviewer_id = su.user_id
        LEFT JOIN sys_user sy ON hr.creator = sy.user_id
        <where>
            hr.status = 1 and hbi.auth_status = 2 and hbi.status = 1
            <if test="hospitalName != null  and hospitalName != ''">and hbi.hospital_name like concat('%',
                #{hospitalName}, '%')
            </if>
        </where>
    </select>

    <select id="selectStatusByReviewerId" parameterType="Long" resultType="int">
        SELECT COUNT(1) FROM hospital_reviewer hr
        LEFT JOIN aut_sa_relation asr ON hr.apply_no = asr.hospital_apply_no
        WHERE
        asr.`status` = 1
        AND hr.`status` = 1
        AND hr.reviewer_id = #{reviewerId}
        AND asr.aut_status != '050202'
    </select>

    <select id="selectHospitalReviewerIsCrewIdByApplyNo" resultType="java.lang.String">
        SELECT reviewer_id
        FROM hospital_reviewer
        WHERE apply_no = #{applyNo}
          AND leader_is = 2
          AND `status` = 1
          AND field_id_list != 'SENIOR_REVIEW'
		  AND field_id_list != 'TRAINEES_REVIEW'
    </select>
    <select id="selectHospitalReviewerReviewerIdsByApplyNo" resultType="java.lang.String">
        SELECT reviewer_id
        FROM hospital_reviewer
        WHERE apply_no = #{applyNo}
          AND `status` = 1
    </select>
    <select id="selectHospitalReviewerByReviewerIdAndApplyNo" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM hospital_reviewer
        WHERE apply_no = #{applyNo}
          AND reviewer_id = #{reviewerId}
          AND leader_is = 1
          AND `status` = 1
    </select>
    <select id="selectHospitalReviewerApplyNosByReviewerId" resultType="java.lang.String">
        SELECT apply_no
        FROM hospital_reviewer
        WHERE reviewer_id = #{reviewerId}
        <if test="fieldIdList != null  and fieldIdList != ''">AND field_id_list = #{fieldIdList}</if>
        AND `status` = 1
    </select>

    <select id="selectRejInterestFileId" resultType="String">
        select interest_file_id
        from hospital_reviewer
        where apply_no = #{applyNo}
          and status = 1
          and reviewer_status = 2
          and field_id_list != 'SENIOR_REVIEW'
          and field_id_list != 'TRAINEES_REVIEW'
    </select>

    <select id="selectHospitalReviewerReviewerIdsByApplyNoAndRoleKey" resultType="java.lang.String">
        SELECT hr.reviewer_id
        FROM hospital_reviewer hr
                 LEFT JOIN sys_user_role sur ON sur.user_id = hr.reviewer_id
                 LEFT JOIN sys_role sr ON sr.role_id = sur.role_id
        WHERE hr.apply_no = #{applyNo}
          AND hr.`status` = 1
          AND sr.role_key = #{roleKey}
    </select>

    <select id="selectReviewInterestVOByAccountId" parameterType="String"
            resultType="com.thas.web.domain.vo.ReviewInterestVO">
        SELECT
            hr.id id,
            hr.reviewer_id accountId,
            hbi.apply_no applyNo,
            hbi.hospital_name hospitalName,
            hr.interest_file_id interestFileId,
            ufi.id fileId,
            ufi.origin fileName,
            ufi.path url,
            ufi.platform platFrom
        FROM
            hospital_planned_distribution hpd
                LEFT JOIN hospital_reviewer hr ON hpd.apply_no = hr.apply_no
                LEFT JOIN upload_file_info ufi ON hr.interest_file_id = ufi.id
                LEFT JOIN hospital_base_info hbi ON hr.apply_no = hbi.apply_no
        WHERE
            hpd.review_dis_complete != 2
          AND hpd.`status` = 1
          AND hr.reviewer_id = #{accountId}
          AND hr.`status` = 1
          ORDER BY hr.create_time desc
    </select>

    <select id="selectCheckInterestFile" parameterType="String" resultMap="HospitalReviewerResult">
        SELECT hr.id,
               hr.apply_no,
               hr.aut_code,
               hr.reviewer_id,
               hr.field_id_list,
               hr.leader_is,
               hr.status,
               hr.interest_file_id
        FROM
            hospital_reviewer hr
                LEFT JOIN hospital_planned_distribution hpd ON hr.apply_no = hpd.apply_no
        WHERE
            hr.apply_no = #{applyNo}
          AND hr.`status` = 1
          AND hr.field_id_list != 'SENIOR_REVIEW'
          AND hr.field_id_list != 'TRAINEES_REVIEW'
          AND hpd.review_dis_complete = 3
          AND hpd.`status` = 1
    </select>

    <select id="selectHosRevByAlnAndAct" parameterType="String" resultMap="HospitalReviewerResult">
        <include refid="selectHospitalReviewerVo"/>
        where apply_no = #{applyNo} and reviewer_id = #{accountId} and status = 1
    </select>
    <select id="selectHospitalReviewerAndAutSaRelationByReviewerIds"
            resultType="com.thas.web.domain.vo.HospitalReviewerAndAutSaRelationVO">
        SELECT hr.apply_no applyNo, hr.reviewer_id reviewerId, asr.evaluate_flag evaluateFlag
        FROM hospital_reviewer hr
        LEFT JOIN aut_sa_relation asr ON hr.apply_no = asr.hospital_apply_no
        WHERE
        hr.`status` = 1 AND
        asr.`status` = 1 AND
        hr.reviewer_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>
    <select id="selectHospitalReviewerByAutCode" resultMap="HospitalReviewerResult">
        <include refid="selectHospitalReviewerVo"/>
        where aut_code = #{autCode} AND status = 1
    </select>

    <select id="selectUserByField" parameterType="list" resultType="com.thas.web.dto.SysUserBaseInfo">
        SELECT
        su.user_id accountId,
        su.user_name username,
        AES_DECRYPT(unhex(su.nick_name),<include refid="SQL_AES_ENCRYPT"/>) name,
        AES_DECRYPT(unhex(su.email),<include refid="SQL_AES_ENCRYPT"/>) email,
        AES_DECRYPT(unhex(su.phonenumber),<include refid="SQL_AES_ENCRYPT"/>) phonenumber,
        rfi.field_code domainId
        FROM
        sys_user su
        LEFT JOIN reviewer_field_info rfi ON su.user_id = rfi.account_id
        LEFT JOIN reviewer_base_info rbi ON rfi.account_id = rbi.account_id
        LEFT JOIN sys_user_role sur ON su.user_id = sur.user_id
        LEFT JOIN sys_role sr ON sur.role_id = sr.role_id
        WHERE
        rfi.field_code in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND sr.role_key = 'assessor'
        AND rfi.status = 1
        AND su.status = 0
        AND rbi.auth_status = 1
        AND rbi.status = 1
    </select>

    <select id="selectTraineesReviewHospitalReviewerByReviewerIds" resultType="java.lang.String">
        SELECT hr.reviewer_id
        FROM aut_sa_relation asr
        LEFT JOIN hospital_reviewer hr ON hr.aut_code = asr.aut_code
        WHERE
        hr.reviewer_id IN
        <foreach collection="reviewerIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND asr.`status` = 1
        AND hr.`status` = 1
        AND asr.aut_status != '050202'
        AND asr.evaluate_flag = 1
        AND hr.field_id_list = 'TRAINEES_REVIEW'
    </select>

    <select id="selectExcludedReviewHospitalReviewerByReviewerIds" resultType="java.lang.String">
        SELECT reviewer_id FROM hospital_reviewer
        WHERE
        reviewer_id IN
        <foreach collection="reviewerIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND `status` = 1
        AND field_id_list != 'TRAINEES_REVIEW'
    </select>

    <select id="selectHospitalReviewerByTraineesAssessorReviewerId"
            resultType="com.thas.web.domain.vo.HospitalReviewerVo">
            SELECT hr.reviewer_id reviewerId,asr.aut_status autStatus,asr.aut_code autCode , asr.hospital_apply_no applyNo FROM hospital_reviewer hr
            LEFT JOIN aut_sa_relation asr ON asr.aut_code = hr.aut_code
            WHERE
            hr.status = 1 AND
            asr.status = 1 AND
            hr.reviewer_id = #{reviewerId} AND
            hr.field_id_list = 'TRAINEES_REVIEW' AND
            asr.aut_status IN(
            '030201',
            '030202',
            '030203',
            '030204',
            '0302041',
            '030205',
            '0302061',
            '0302062',
            '030206'
            )
    </select>

    <select id="selectHospitalReviewerAndCstDomainByAutCode" resultType="com.thas.web.domain.vo.HospitalReviewerVo">
         SELECT hr.aut_code autCode,hr.reviewer_id reviewerId,hr.field_id_list fieldIdList,cd.domain_name domainName FROM hospital_reviewer hr
         LEFT JOIN cst_domain cd ON cd.id = hr.field_id_list
         WHERE
         hr.status = 1 AND
         hr.aut_code = #{autCode} AND
         hr.field_id_list NOT IN('SENIOR_REVIEW','TRAINEES_REVIEW')
    </select>

    <select id="workReviewer" resultType="com.thas.web.dto.ReviewCycleJudgeVo">
        SELECT hr.apply_no applyNo ,hr.reviewer_id reviewId,hr.leader_is leaderIs ,hr.`field_id_list` fieldIdList,`asc`.cycle_stage cycleStage,
        asr.aut_status autStatus
        FROM hospital_reviewer hr
        LEFT JOIN aut_sa_relation asr ON asr.hospital_apply_no = hr.apply_no
        LEFT JOIN asa_status_config `asc` ON `asc`.current_status = asr.aut_status
        WHERE
        hr.`status` = 1 AND
        asr.`status` = 1 AND
        `asc`.current_status != 050202 AND
        hr.reviewer_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="fieldIdList != null  and fieldIdList != ''"> AND hr.`field_id_list` = #{fieldIdList}</if>

    </select>

</mapper>
