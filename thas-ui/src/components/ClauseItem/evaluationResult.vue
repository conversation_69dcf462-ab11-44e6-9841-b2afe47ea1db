<template>
  <div class="evaluation-autResult">
    <el-divider></el-divider>
    <el-form ref="form" :model="formData" :rules="rules" label-width="110px">
      <el-form-item v-if="showExaminer && readOnly" :label="`${examiner}：`"></el-form-item>
      <el-form-item :label="`${reviewResult}：`" prop="autResult" :class="{ readonly: !(!readOnly && hasSaAud && itsMine) }">
        <el-select v-if="!readOnly && hasSaAud && itsMine" v-model.trim="formData.autResult" clearable filterable :placeholder="`请选择${this.reviewResult}`">
          <el-option v-for="(item, index) in currentOptions" :key="index" :value="item.dictValue" :label="item.dictLabel"></el-option>
        </el-select>
        <span v-else> {{ getResultTitle(formData.autResult) }}</span>
      </el-form-item>
      <el-form-item :label="`${reviewDesc}：`" prop="autDesc" :class="{ readonly: !(!readOnly && hasSaAud && itsMine) }" :rules="autDescRule">
        <el-input v-if="!readOnly && hasSaAud && itsMine" v-model.trim="formData.autDesc" type="textarea" :rows="4" :maxlength="50"></el-input>
        <span v-else> {{ formData.autDesc }}</span>
      </el-form-item>
      <el-form-item label="风险：" v-if="showRiskWithResult">
        <el-popover placement="top" width="400" trigger="click">
          <el-image :src="require('../../assets/images/able.png')"></el-image>
          <el-button type="text" slot="reference" icon="el-icon-question" style="float: left"></el-button>
        </el-popover>
        <div class="risk-content" style="float: left">
          <el-form-item class="risk" label="可能性：" prop="riskPossibility" :rules="rules.riskPossibility">
            <el-radio-group v-model.trim="formData.riskPossibility" :disabled="!(!readOnly && hasSaAud && itsMine)">
              <el-radio :label="1">低</el-radio>
              <el-radio :label="2">中</el-radio>
              <el-radio :label="3">高</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item class="risk" label="影响：" prop="riskImpact" :rules="rules.riskImpact">
            <el-radio-group v-model.trim="formData.riskImpact" :disabled="!(!readOnly && hasSaAud && itsMine)">
              <el-radio :label="1">低</el-radio>
              <el-radio :label="2">中</el-radio>
              <el-radio :label="3">高</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item class="risk" label="总分：">
            {{ totalRangeText }}
          </el-form-item>
        </div>
      </el-form-item>

      <el-form-item v-if="!readOnly && hasSaAud && itsMine">
        <el-button type="primary" @click="submit" v-loading="submitLoading" :disabled="submitLoading">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "EvaluationResult",
  props: {
    label: {
      type: String,
      default: () => "",
    },
    status: {
      type: [String, Number],
      default: () => false,
    },
    saAud: {
      type: Object,
      default: () => { },
    },
    clause: {
      type: Object,
      default: () => { },
    },
    readOnly: {
      require: true,
      type: Boolean,
      default: () => true,
    },
  },
  watch: {
    saAud: {
      immediate: true,
      deep: true,
      handler() {
        const {
          autResult = null,
          autDesc = null,
          riskPossibility = null,
          riskImpact = null,
        } = this.saAud || {};
        this.formData = {
          autResult,
          autDesc,
          riskPossibility,
          riskImpact,
        };
        this.submitLoading = false;
      },
    },
  },
  data() {
    return {
      showExaminer: false,
      formData: {
        autResult: "",
        autDesc: "",
      },

      agreeOrNotOptions: [],
      reviewResultOptions: [],
      checkSummaryOptions: [],
      entry: 0,
      leader: 0,
      showRisk: false,
      submitLoading: false,
    };
  },
  computed: {
    autDescRule() {
      let required = true;
      if (this.isSaF || this.isAudF) {
        required = this.formData.autResult == 1 ? false : true
      }
      return [
        {
          required,
          message: `请选择${this.label}描述`,
          trigger: ["change", "blur"],
        }
      ]
    },
    rules() {
      return {
        autResult: [
          {
            required: true,
            message: `请选择${this.label}结果`,
            trigger: "change",
          },
        ],
        autDesc: [
          {
            required: true,
            message: `请选择${this.label}描述`,
            trigger: ["change", "blur"],
          },
        ],
        riskPossibility: [
          {
            required: true,
            message: "请选择可能性评分",
            trigger: ["change", "blur"],
          },
        ],
        riskImpact: [
          {
            required: true,
            message: "请选择影响评分",
            trigger: ["change", "blur"],
          },
        ],
      };
    },
    examiner() {
      return `${this.label}员`;
    },
    reviewResult() {
      return `${(this.isAudS || this.isSaS) ? '组员' : ''}${this.label}结果`;
    },
    reviewDesc() {
      let desc = `${this.label}描述`;
      if (this.isAudF) {
        switch (Number(this.formData.autResult)) {
          case 1:
            desc = "亮点"
            break;
          case 4:
            desc = "整改建议"
            break;
          case 5:
            desc = "整改建议"
            break;
          default:
            desc = `${this.label}描述`;
            break;
        }
      }
      return desc;
    },
    hasSaAud() {
      return !this.saAud || !!this.saAud.isReject || !!this.saAud.toReject;
    },
    itsMine() {
      // 用户类型：0医院 1审查员 2审查组长 3评审员 4评审组长
      const types = [
        this.AutSaAudSubmitTypeEnum.AUD_FIRST_TRIAL_REVIEW_PROC,
        this.AutSaAudSubmitTypeEnum.AUD_SECOND_TRIAL_REVIEW_PROC,
      ];

      const statuses = [
        this.AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL_REVIEW,
        this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_REVIEW_PROC,
        this.AutSaAudStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW,
        this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC,
      ];
      if (this.entry == 0) return false;
      if (this.entry == 1 || this.entry == 3) {
        if (this.hasSaAud) {
          return true;
        }
        return !types.includes(`${this.saAud.type}`);
      }

      if (
        (this.entry == 2 || this.entry == 4) &&
        this.saAud &&
        types.includes(`${this.saAud.type}`)
      )
        return this.leader == 1;
      if (statuses.includes(Number(this.status)) && this.leader == 1)
        return true;
      return false;
    },

    currentOptions() {
      this.showRisk = false;
      if (!this.readOnly) {
        if (
          this.status == this.AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL ||
          this.status == this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_INQUIRE_PROC ||
          this.status == this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_REVIEW_REJECT
        ) {
          return this.checkSummaryOptions;
        } else if (
          this.status == this.AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL_REVIEW ||
          this.status == this.AutSaAudStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW ||
          this.status == this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_REVIEW_PROC ||
          this.status == this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC
        ) {
          return this.agreeOrNotOptions;
        } else if (
          this.status == this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_SUM_PASS ||
          this.status ==
          this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_INQUIRE_PROC ||
          this.status == this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_REVIEW_REJECT
        ) {
          this.showRisk = true;
          return this.reviewResultOptions;
        }
      }

      // 0 自评 1总结 2形式审查 3审查审核 4审查总结 5是现场评审 6是现场评审审核 7评审总结
      if (this.saAud && this.saAud.type) {
        let type = this.saAud.type;
        if (type == this.AutSaAudSubmitTypeEnum.AUD_FIRST_TRIAL_INQUIRE_PROC) {
          return this.checkSummaryOptions;
        } else if (
          type == this.AutSaAudSubmitTypeEnum.AUD_FIRST_TRIAL_REVIEW_PROC ||
          type == this.AutSaAudSubmitTypeEnum.AUD_SECOND_TRIAL_REVIEW_PROC
        ) {
          return this.agreeOrNotOptions;
        } else if (
          type == this.AutSaAudSubmitTypeEnum.AUD_SECOND_TRIAL_INQUIRE_PROC
        ) {
          this.showRisk = true;
          return this.reviewResultOptions;
        }
      }
      return [];
    },
    isSaF() {
      return (
        this.status == this.AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL ||
        this.status == this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_REVIEW_REJECT
      );
    },
    isSaS() {
      return (
        this.status == this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_REVIEW_PROC ||
        this.status == this.AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL_REVIEW
      );
    },
    isAudF() {
      return (
        this.status == this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_SUM_PASS ||
        this.status == this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_INQUIRE_PROC ||
        this.status == this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_REVIEW_REJECT
      );
    },
    isAudS() {
      return (
        this.status == this.AutSaAudStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW ||
        this.status == this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC ||
        this.status == this.AutSaAudStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW
      );
    },
    getResultTitle() {
      return (value) => {
        return (
          this.currentOptions.find((option) => option.dictValue == value) || {}
        ).dictLabel;
      };
    },
    showRiskWithResult() {
      return this.showRisk && this.formData.autResult == 5 && this.clause.isStar == 1;
    },

    totalRange() {
      return (
        Number(this.formData.riskImpact) * Number(this.formData.riskPossibility)
      );
    },
    totalRangeText() {
      let risk = "";
      switch (this.totalRange) {
        case 0:
          risk = "无风险";
          break;
        case 1:
          risk = "低风险";
          break;
        case 2:
          risk = "低风险";
          break;
        case 3:
          risk = "中风险";
          break;
        case 4:
          risk = "中风险";
          break;
        case 6:
          risk = "高风险";
          break;
        case 9:
          risk = "严重风险";
          break;
      }
      return `${this.totalRange} (${risk})`;
    },
  },

  mounted() {
    this.initDicts();
    this.entry = this.$route.query.entry || 0;
    this.leader = this.$route.query.isLeader || 0;
  },

  methods: {
    initDicts() {
      this.getDicts("agree_or_not").then((res) => {
        this.agreeOrNotOptions = res.data;
      });
      this.getDicts("review_result").then((res) => {
        this.reviewResultOptions = res.data;
      });
      this.getDicts("check_summary").then((res) => {
        this.checkSummaryOptions = res.data;
      });
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.submitLoading = true;
          let type = this.AutSaAudSubmitTypeEnum.AUD_FIRST_TRIAL_INQUIRE_PROC;
          if (this.isSaF) {
            type = this.AutSaAudSubmitTypeEnum.AUD_FIRST_TRIAL_INQUIRE_PROC;
          } else if (this.isSaS) {
            type = this.AutSaAudSubmitTypeEnum.AUD_FIRST_TRIAL_REVIEW_PROC;
          } else if (this.isAudF) {
            type = this.AutSaAudSubmitTypeEnum.AUD_SECOND_TRIAL_INQUIRE_PROC;
          } else if (this.isAudS) {
            type = this.AutSaAudSubmitTypeEnum.AUD_SECOND_TRIAL_REVIEW_PROC;
          }
          let clauseId;
          if (this.saAud) {
            clauseId = this.saAud.clauseId;
          } else {
            clauseId = this.clause.clauseId;
          }
          this.$emit("onComplete", null, this.formData, clauseId, type);
          // 提交超时3s后重置按钮loading
          setTimeout(() => {
            this.submitLoading = false;
          }, 5000);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.evaluation-autResult {
  ::v-deep .el-divider {
    margin-bottom: 10px;
  }
  ::v-deep .el-form-item {
    margin-bottom: 24px;
    .el-form-item__content {
      overflow: visible;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
