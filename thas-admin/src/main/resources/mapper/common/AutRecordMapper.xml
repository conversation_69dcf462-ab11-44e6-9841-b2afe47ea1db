<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.AutRecordMapper">

    <resultMap type="com.thas.web.domain.AutRecord" id="AutRecordResult">
        <result property="id" column="id"/>
        <result property="accountId" column="account_id"/>
        <result property="autStatus" column="aut_status"/>
        <result property="audId" column="aud_id"/>
        <result property="audDesc" column="aud_desc"/>
        <result property="audResult" column="aud_result"/>
        <result property="audSubmitTime" column="aud_submit_time"/>
        <result property="status" column="status"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateId" column="update_id"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectAutRecordVo">
        select id,
               account_id,
               aut_status,
               aud_id,
               aud_desc,
               aud_result,
               aud_submit_time,
               status,
               create_id,
               create_time,
               update_id,
               update_time
        from aut_record
    </sql>

    <select id="selectAutRecordByAccountId" resultMap="AutRecordResult">
        <include refid="selectAutRecordVo"/>
        where account_id = #{accountId} and status = 1
    </select>

    <insert id="insertAutRecord" parameterType="com.thas.web.domain.AutRecord" useGeneratedKeys="true"
            keyProperty="id">
        insert into aut_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountId != null and accountId != ''">account_id,</if>
            <if test="autStatus != null">aut_status,</if>
            <if test="audId != null">aud_id,</if>
            <if test="audDesc != null">aud_desc,</if>
            <if test="audResult != null">aud_result,</if>
            <if test="audSubmitTime != null">aud_submit_time,</if>
            <if test="status != null">status,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountId != null and accountId != ''">#{accountId},</if>
            <if test="autStatus != null">#{autStatus},</if>
            <if test="audId != null">#{audId},</if>
            <if test="audDesc != null">#{audDesc},</if>
            <if test="audResult != null">#{audResult},</if>
            <if test="audSubmitTime != null">#{audSubmitTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateAutRecordByCondition" parameterType="com.thas.web.domain.AutRecord">
        update aut_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="autStatus != null">aut_status = #{autStatus},</if>
            <if test="audId != null">aud_id = #{audId},</if>
            <if test="audDesc != null">aud_desc = #{audDesc},</if>
            <if test="audResult != null">aud_result = #{audResult},</if>
            <if test="audSubmitTime != null">aud_submit_time = #{audSubmitTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where account_id = #{accountId} and status = 1
    </update>


    <select id="selectAutRecordList" parameterType="com.thas.web.domain.AutRecord" resultMap="AutRecordResult">
        <include refid="selectAutRecordVo"/>
        <where>
            <if test="accountId != null  and accountId != ''">and account_id = #{accountId}</if>
            <if test="autStatus != null">and aut_status = #{autStatus},</if>
            <if test="audId != null">and aud_id = #{audId},</if>
            <if test="audDesc != null">and aud_desc = #{audDesc},</if>
            <if test="audResult != null">and aud_result = #{audResult},</if>
            <if test="audSubmitTime != null">and aud_submit_time = #{audSubmitTime},</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="createId != null ">and create_id = #{createId}</if>
            <if test="updateId != null ">and update_id = #{updateId}</if>
        </where>
    </select>

    <select id="selectAutRecordById" parameterType="Long" resultMap="AutRecordResult">
        <include refid="selectAutRecordVo"/>
        where id = #{id}
    </select>

    <update id="updateAutRecord" parameterType="com.thas.web.domain.AutRecord">
        update aut_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountId != null and accountId != ''">account_id = #{accountId},</if>
            <if test="autStatus != null">aut_status = #{autStatus},</if>
            <if test="audId != null">and aud_id = #{audId},</if>
            <if test="audDesc != null">and aud_desc = #{audDesc},</if>
            <if test="audResult != null">and aud_result = #{audResult},</if>
            <if test="audSubmitTime != null">and aud_submit_time = #{audSubmitTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAutRecordByAccountId" parameterType="String">
        delete
        from aut_record
        where account_id = #{accountId}
    </delete>

    <delete id="deleteAutRecordByIds" parameterType="String">
        delete from aut_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
