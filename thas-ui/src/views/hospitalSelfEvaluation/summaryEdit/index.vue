<!--  -->
<template>
  <div class="summary-edit">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <h4> 生成自评报告 </h4>
      </div>
      <el-form ref="summaryForm" :model="summaryForm" :rules="rules" label-width="120px">
        <!-- 医院自评 -->
        <div v-if="showWaitSaSum || showSaSum">
          <el-form-item label="自评总结：" prop="autAdvantage">
            <el-input v-model="summaryForm.autAdvantage" placeholder="请输入自评总结" type="textarea" :rows="10" :maxlength="10000" @input="autAdvantageChange"></el-input>
          </el-form-item>
          <!-- <el-form-item label="亮点：" prop="autAdvantage">
            <el-input v-model.trim="summaryForm.autAdvantage" placeholder="请输入亮点" type="textarea" :rows="4" :maxlength="1000"></el-input>
          </el-form-item>
          <el-form-item label="不足：" prop="autEvaluate">
            <el-input v-model.trim="summaryForm.autEvaluate" placeholder="请输入不足" type="textarea" :rows="4" :maxlength="1000"></el-input>
          </el-form-item>
          <el-form-item label="改进计划：" prop="autProposal">
            <el-input v-model.trim="summaryForm.autProposal" placeholder="请输入改进计划" type="textarea" :rows="4" :maxlength="1000"></el-input>
          </el-form-item> -->
          <el-form-item label="自评报告：" :rules="downloadPDFRule">
            <el-button @click="submitSummary(1)" type="text" size="small" v-loading="fileDownloadLoading" :disabled="isAutAdvantageEmpty || fileDownloadLoading">点击下载 {{detail.hospitalName}}-自评报告初稿.pdf</el-button>
            <!-- <el-button v-else @click="downloadReport" type="text" size="small" v-loading="fileDownloadLoading" :disabled="fileDownloadLoading">点击下载 {{detail.hospitalName}}-自评报告.pdf</el-button> -->
          </el-form-item>
          <el-form-item label="上传报告：" prop="fileIds">
            <!-- <span slot="label">
              上传报告：
            </span> -->
            <!-- <el-tooltip content="请上传签名盖章后的自评报告">
              <i class="el-icon-info" style="color:#40baef"></i>
            </el-tooltip> -->
            <upload :accept='`.PDF`' single :disabled="!fileDownloaded" :data="{type: '1', downLoadFileName: `${detail.hospitalName}-自评报告初稿.pdf`}" :tip="true"  :tips="`请上传签名盖章后的自评报告；`" :files="files" @uploadSuccess="onSuccess" @fileRemove="onRemove"></upload>
          </el-form-item>
          <el-form-item label=" ">
            <el-button v-if="!hideSubmit" @click="$emit('backToSa', summaryForm.autAdvantage) && (fileDownloaded = false)">返回修改自评内容</el-button>
            <el-button type="primary" :disabled="!summaryForm.fileIds" @click="submitSummary(2)" v-loading="summaryLoading">提交自评报告</el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import request, { downloadPDF } from "@/utils/request";
import DictSpan from "@/components/DetailMessage/dictSpan.vue";
import ModifyTable from '../components/modifyTable.vue'

export default {
  name: "SummaryEdit",
  components: {
    DictSpan,
    ModifyTable
  },
  props: {
    status: {
      type: String,
      default: () => '',
    },
    relation: {
      type: Object,
      default: () => { },
    },
    detail: {
      type: Object,
      default: () => { },
    },
    cacheAutAdvantage: {
      type: String,
      default: () => '',
    }
  },
  watch: {
    detail: {
      deep: true,
      immediate: true,
      handler() {
        setTimeout(() => {
          this.$refs.summaryForm && this.$refs.summaryForm.clearValidate();
        }, 0);
        if (this.detail.autSaAudListMap && this.detail.autSaAudListMap[this.AutSaAudCurrentStatusEnum.SA_REPORT_DESC]) {
          let gen = this.detail.autSaAudListMap[this.AutSaAudCurrentStatusEnum.SA_REPORT_DESC][0]
          // console.log(gen);
          try {
            let autDesc = JSON.parse(gen.autDesc)
            // 自评总结赋值过程，缓存数据 > 接口返回数据 > 默认值
            this.summaryForm.autAdvantage = this.cacheAutAdvantage || autDesc.autAdvantage || '请按以下提纲填写（5000字以内）：\n（一）医院概况\n\n1.医疗服务与特色\n2.学科建设与发展\n\n（二）认证/评审历程\n认证/评审工作总结（认证/评审准备、历程、具体举措等）\n\n（三）工作成效与亮点\n\n（四）存在问题\n\n（五）改进计划\n\n';
            if (!this.fileDownloaded) {
              this.summaryForm.fileDownloaded = false;
            }
          } catch (error) {
            console.log(error);
          }
        }
      }
    },
    'summaryForm.autAdvantage': {
      handler() {
        this.clearFiles();
      }
    }
  },
  data() {
    return {
      files: [],
      reviewSummaryOptions: [],
      checkSummaryOptions: [],
      reviewResultOptions: [],
      summaryLoading: false,
      fileDownloadLoading: false,
      summaryForm: {
        autAdvantage: '请按以下提纲填写（5000字以内）：\n（一）医院概况\n\n1.医疗服务与特色\n2.学科建设与发展\n\n（二）认证/评审历程\n认证/评审工作总结（认证/评审准备、历程、具体举措等）\n\n（三）工作成效与亮点\n\n（四）存在问题\n\n（五）改进计划\n\n',
        autDesc: '',
        autResult: '1',
        fileIds: '',
        fileDownloaded: false,
      },
      url: {
        report: '/pdf/generate/ftlToPdf',
      },

      fileDownloaded: false
    };
  },

  computed: {
    // 待生成自评报告 展示不足等
    showWaitSaSum() {
      return this.status === this.AutSaAudCurrentStatusEnum.WAIT_TERM_SA_SUM
    },
    // 提交自评总结
    showSaSum() {
      return this.status === this.AutSaAudCurrentStatusEnum.TERM_SA_SUM
    },

    isAutAdvantageEmpty() {
      let cloneAutAdvantage = '请按以下提纲填写（5000字以内）：\n（一）医院概况\n\n1.医疗服务与特色\n2.学科建设与发展\n\n（二）认证/评审历程\n认证/评审工作总结（认证/评审准备、历程、具体举措等）\n\n（三）工作成效与亮点\n\n（四）存在问题\n\n（五）改进计划\n\n';
      let cloneAutAdvantageStr = cloneAutAdvantage.replace(/[\n\s]+/g, '');
      let autAdvantageStr = this.summaryForm.autAdvantage.replace(/[\n\s]+/g, '');
      return !autAdvantageStr || autAdvantageStr === cloneAutAdvantageStr
    },

    rules() {
      return {
        autAdvantage: [{
          required: true,
          validator: (rule, value, callback) => {
            if (this.isAutAdvantageEmpty) callback('请输入自评总结')
            callback()
          },
          trigger: ["change", "blur"],
        }],
        autDesc: [{
          required: true,
          message: "请输入自评总结",
          trigger: ["change", "blur"],
        }],
        fileIds: [{
          required: this.fileDownloaded,
          message: '请上传盖章后的自评报告',
          trigger: ['change', 'blur'],
        }],
      }
    },
    downloadPDFRule() {
      return [{
        required: true,
        validator: this.downloadPDFValid,
        trigger: ['change', 'blur']
      }]
    },
    // 隐藏提交操作
    hideSubmit() {
      if (
        this.detail
        && this.detail
        && this.detail.autSaAudStatus
        && (this.detail.autSaAudStatus === '010104'
          || this.detail.autSaAudStatus === '010105')
        && this.detail.rejectAutSaAudListMap
        && this.detail.rejectAutSaAudListMap.fr_clause
        && this.detail.rejectAutSaAudListMap.fr_clause.length
        && this.detail.rejectAutSaAudListMap.sa_clause
        && this.detail.rejectAutSaAudListMap.sa_clause.length
      ) {
        return true
      } else {
        return false
      }
    }
  },

  created() { },

  mounted() {
    this.initDicts();
  },

  methods: {
    initDicts() {
      this.getDicts("review_summary").then((res) => {
        this.reviewSummaryOptions = res.data;
      });
      this.getDicts("check_summary").then((res) => {
        this.checkSummaryOptions = res.data;
      });
      this.getDicts("review_result").then((res) => {
        this.reviewResultOptions = res.data;
      });
    },

    downloadPDFValid(rule, value, callback) {
      if (!this.fileDownloaded || !this.summaryForm.fileDownloaded) callback('请下载自评报告');
      callback();
    },

    async submitAutSaAud(data) {
      return request({
        url: "/aut/sa/aud/submitAutSaAud",
        method: "post",
        data,
      });
    },

    clearFiles() {
      this.summaryForm.fileIds = '';
      this.files = [];
      this.fileDownloaded = false;
      this.summaryForm.fileDownloaded = false;
    },

    downloadReport() {
      downloadPDF({
        filename: `${this.detail.hospitalName}-自评报告初稿.pdf`,
        autCode: this.relation.autCode,
        ftlTemplateCode: 'aud_sa_report',
        checkStatus: "N"
      }, (flag) => {
        if (flag) {
          setTimeout(() => {
            this.fileDownloadLoading = false;
            this.summaryForm.fileDownloaded = true;
            this.fileDownloaded = true;
          }, 250);
          this.$emit("summarySubmit");
        }
      });
    },

    /**
     * @param submitType 提交类型 1是：待生成自评报告，亮点不足等
     *   2是：自评总结提交
     */
    submitSummary(submitType) {
      if (submitType == 1 && this.summaryForm.fileDownloaded === true) {
        this.downloadReport();
        return;
      }
      // 防重
      if (this.summaryLoading) return;
      if (submitType != 1) {
        this.summaryLoading = true;
      }
      this.$refs.summaryForm.validate(async (valid) => {
        if (valid) {
          if (submitType == 1) {
            this.fileDownloadLoading = true;
          }
          let data = {
            autCode: this.relation.autCode,
            accountId: this.$store.getters.userId + "",
            autSaAudLists: []
          }
          if (submitType == 1) {
            let {
              autAdvantage,
              ...other
            } = this.summaryForm;

            data.submitType = this.AutSaAudCurrentStatusEnum.SA_REPORT_DESC

            let autDesc = JSON.stringify({
              autAdvantage,
            })
            other.autDesc = autDesc;
            data.autSaAudLists = [{
              ...other,
              clauseId: ''
            }]
          } else {
            data.submitType = this.AutSaAudCurrentStatusEnum.SA_SUMMARY
            let {
              autAdvantage,
              autResult,
              fileIds
            } = this.summaryForm;
            data.autSaAudLists = [{
              autDesc: JSON.stringify({
                autAdvantage
              }),
              autResult,
              fileIds,
              clauseId: ''
            }]
          }

          try {
            await this.submitAutSaAud(data);

            if (data.autSaAudLists[0]?.fileIds) {
              let ids = data.autSaAudLists[0].fileIds.split(',')
              // 医院上传
              this.shareCreate(ids, this.$store.getters.userId, 'hospital_review');
            }

            if (submitType == 1) {
              this.downloadReport();
            } else {
              setTimeout(() => {
                this.summaryLoading = false;
              }, 2000);
              this.$emit("summarySubmit");
            }
          } catch (error) {
            this.$message({
              type: 'error',
              message: error.message
            })
          }
        } else {
          this.summaryLoading = false;
        }
      })
    },

    filesValidator(rule, value, callback) {
      if (!value) {
        callback("请上传自评报告");
      }
      callback();
    },

    onSuccess(res, file) {
      file.fileId = res.data.fileId;
      this.files.push(file);
      this.summaryForm.fileIds = (
        this.files.map((file) => file.fileId) || []
      ).join(",");
      this.$refs.summaryForm.validateField("fileIds");
    },

    onRemove(errorOrFile) {
      let f_idx = this.files.findIndex(({fileId}) =>fileId == errorOrFile.fileId);
      this.files.splice(f_idx, 1);
      this.summaryForm.fileIds = (
        this.files.map((file) => file.fileId) || []
      ).join(",");
    },
    autAdvantageChange(value) {
      this.$emit('autAdvantageChange', value)
    }
  },
};
</script>
<style lang="scss" scoped>
.summary-edit {
  .box-card {
    margin-bottom: 10px;
  }
  // padding: 24px;
  ::v-deep .el-card {
    border: 0;
    .el-card__header {
      padding-bottom: 15px;
      .clearfix h4 {
        float: left;
        font-weight: bold;
        margin: 0;
      }
    }

    .grade .el-select .el-input.is-disabled .el-input__inner {
      background-color: #ffffff;
      border: 1px solid #dcdfe6;
      color: #606266;
    }
  }
}
</style>
