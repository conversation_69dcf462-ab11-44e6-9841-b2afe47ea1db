<template>
  <div class='theory-basic-form'>
    <el-descriptions class="margin-top" :column="1">
      <el-descriptions-item label="受评医院名称">{{formData.applyName}}</el-descriptions-item>
      <el-descriptions-item label="联系人"> {{formData.needName}}</el-descriptions-item>
      <el-descriptions-item label="联系电话">{{formData.phoneNumber}}</el-descriptions-item>
      <el-descriptions-item label="联系邮箱">{{formData.email}}</el-descriptions-item>
      <el-descriptions-item label="评审日期">{{formData.sceneReviewTime[0]}} 至 {{formData.sceneReviewTime[1]}} </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
import mixin from './mixin.vue'
export default {
  name: 'HospitalBasicForm',
  mixins: [mixin],
  data() {
    return {
      applyNo: '',
    }
  },
  computed: {
    validateForm() {
      return this.$refs.form;
    }
  },

  methods: {
    getValue() {
      return this.formData;
    }
  }
}
</script>