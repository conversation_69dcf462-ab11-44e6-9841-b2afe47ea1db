<template>
  <div class="">
    <el-form :label-width="labelWidth">
      <el-form-item
        v-if="showByTier(1)"
        class="audit-select"
        :label="onlyTitle || '章：'"
      >
        <el-select
          v-model.trim="chapter"
          clearable filterable
          @change="(value) => selectChange(value, 1)"
          style="width: 60%"
        >
          <el-option
            v-for="(chap, index) in chapterList"
            :key="index"
            :value="chap.chapterId"
            :label="commonLabel(chap.chapterNo || index, chap.chapter, '章')"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="showByTier(2)"
        class="audit-select"
        :class="{ hiddenLabel: onlyTitle }"
        label="节："
      >
        <el-select
          v-model.trim="section"
          clearable filterable
          @change="(value) => selectChange(value, 2)"
          style="width: 60%"
        >
          <el-option
            v-for="(sect, index) in sectionList"
            :key="index"
            :value="sect.sectionId"
            :label="commonLabel(sect.sectionNo || index, sect.section, '节')"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="showByTier(3)"
        class="audit-select"
        :class="{ hiddenLabel: onlyTitle }"
        label="条："
      >
        <el-select
          v-model.trim="article"
          clearable filterable
          @change="(value) => selectChange(value, 3)"
          :popper-class="`clausePopperClass`"
          style="width: 60%"
        >
          <el-option
            v-for="(article, index) in articleList"
            :key="index"
            :value="article.articleId"
            :label="
              commonLabel(article.articleNo || index, article.article, '条')
            "
            :title="article.article"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="showByTier(4)"
        class="audit-select"
        :class="{ hiddenLabel: onlyTitle }"
        label="款："
      >
        <el-select
          v-model.trim="clause"
          clearable filterable
          @change="(value) => selectChange(value, 4)"
          :popper-class="`clausePopperClass`"
          style="width: 90%"
        >
          <el-option
            v-for="(clause, index) in clauseList"
            :key="index"
            :value="clause.clauseId"
            :label="
              clauseLabel(
                clause.clauseNo,
                clause.clause,
                clause.isStar,
                clause.clauseId
              )
            "
            :title="clause.clause"
          >
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getStandard } from "@/api/system/standard";

export default {
  name: "StandardSelect",
  components: {},
  props: {
    tier: {
      type: Number,
      default: () => 4,
      validator: function (value) {
        return value === 3 || value === 4;
      },
    },
    clauseIds: {
      type: Array,
      default: () => [],
    },
    onlyTitle: {
      type: String,
      default: () => null,
    },
    labelWidth: {
      type: String,
      default: () => "80px",
    },
    sClauseId: {
      type: [String, Number],
      default: () => "",
    },
    versionId: {
      type: [String, Number],
      default: () => "",
    },
    force: {
      type: Boolean,
      default: () => false,
    },
  },
  data() {
    return {
      chapterList: [],
      sectionList: [],
      articleList: [],
      clauseList: [],

      chapter: "",
      section: "",
      article: "",
      clause: "",

      chapterIds: [],
      sectionIds: [],
      articleIds: [],
    };
  },

  computed: {
    showByTier() {
      return (tier) => this.tier >= tier;
    },
    commonLabel() {
      return (index, content, title) => ` ${index} ${content}`;
    },

    clauseLabel() {
      return (index, content, isStar, id) =>
        `${isStar == 1 ? "★" : ""}${index} ${content}`;
    },
  },

  watch: {
    sClauseId: {
      immediate: true,
      handler() {
        if (this.sClauseId == "") return;
        this.checkSClauseId();
      },
    },
  },

  async created() {
    const data = await getStandard(this.versionId, this.force);
    try {
      if (!this.clauseIds.length) {
        data && (this.chapterList = data);
        this.checkSClauseId();
      } else {
        const clauses = this.$store.getters.standardTypeItemByVersionId(
          this.versionId,
          "clause",
          "list"
        );
        const clauseList = clauses.filter(
          (clause) =>
            this.clauseIds.includes(`${clause.clauseId}`) ||
            this.clauseIds.includes(clause.clauseId)
        );
        const chapters = this.$store.getters.standardTypeItemByVersionId(
          this.versionId,
          "chapter",
          "map"
        );
        const articles = this.$store.getters.standardTypeItemByVersionId(
          this.versionId,
          "article",
          "map"
        );
        const sections = this.$store.getters.standardTypeItemByVersionId(
          this.versionId,
          "section",
          "map"
        );
        this.chapterIds = clauseList.map(
          (clause) => chapters[clause.chapterId].chapterId
        );
        this.sectionIds = clauseList.map(
          (clause) => sections[clause.sectionId].sectionId
        );
        this.articleIds = clauseList.map(
          (clause) => articles[clause.articleId].articleId
        );

        this.chapterList = data.filter((chapter) => {
          if (this.chapterIds.length)
            return this.chapterIds.includes(chapter.chapterId);
          return true;
        });
      }
    } catch (error) {
      // console.log(error);
      data && (this.chapterList = data);
      this.checkSClauseId();
    }
    this.$emit("loaded");
  },

  methods: {
    checkSClauseId() {
      if (this.sClauseId != "" && this.chapterList && this.tier === 4) {
        let pid, sid, aid;
        this.chapterList.some((chapter) => {
          return chapter.sectionVoList.some((section) => {
            return section.articleVoList.some((article) => {
              let hasAid = article.clauseVoList.some(
                (clause) => clause.clauseId == this.sClauseId
              );
              if (hasAid) {
                pid = chapter.chapterId;
                sid = section.sectionId;
                aid = article.articleId;
              }
              return hasAid;
            });
          });
        });
        if (pid && sid && aid && this.sClauseId) {
          this.selectChange(pid, 1);
          this.selectChange(sid, 2);
          this.selectChange(aid, 3);
          this.selectChange(this.sClauseId, 4);

          this.$nextTick(() => {
            this.chapter = pid;
            this.section = sid;
            this.article = aid;
            this.clause = this.sClauseId;
          });
        }

        if (aid) {
          this.getClausesDetail(this.versionId, aid);
        }
      }
    },

    selectChange(value, tier) {
      if (tier == 1) {
        const chapter =
          this.chapterList.find((chapt) => chapt.chapterId == value) || {};
        this.sectionList = (chapter.sectionVoList || []).filter((section) => {
          if (this.sectionIds.length)
            return this.sectionIds.includes(section.sectionId);
          return true;
        });
        this.section = "";
        this.article = "";
        this.clause = "";
      } else if (tier == 2) {
        const section =
          this.sectionList.find((sect) => sect.sectionId == value) || {};
        this.articleList = (section.articleVoList || []).filter((article) => {
          if (this.articleIds.length)
            return this.articleIds.includes(article.articleId);
          return true;
        });
        this.article = "";
        this.clause = "";
      } else if (tier == 3) {
        const article =
          this.articleList.find((article) => article.articleId == value) || {};
        this.clauseList = (article.clauseVoList || []).filter((clause) => {
          if (this.clauseIds.length) {
            return this.clauseIds.includes(`${clause.clauseId}`) ||  this.clauseIds.includes(clause.clauseId);
          }
          return true;
        });
        if (this.tier === tier) {
          this.$emit("selectChange", tier, this.clauseList, {
            chapter: this.chapter,
            section: this.section,
            article: this.article,
          });
        }
        this.clause = "";
      } else if (tier == 4) {
        const clause =
          this.clauseList.find((clause) => clause.clauseId == value) || {};
        this.$emit("selectChange", tier, clause);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .hiddenLabel.el-form-item {
  .el-form-item__label {
    visibility: hidden;
  }
}
</style>
<style lang="scss">
.clausePopperClass {
  max-width: 600px !important;
  .el-select-dropdown__item {
    span {
      text-overflow: ellipsis;
    }
  }
  .el-scrollbar__bar {
    opacity: 1;
  }
}
</style>
