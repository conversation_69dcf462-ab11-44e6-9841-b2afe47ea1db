<template>
  <div class="field-management-detail" v-loading="loading">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>{{ formData.id ? "编辑" : "添加" }}分组</span>
      </div>

      <el-form ref="elForm" :model="formData" :rules="rules" label-width="96px">
        <!-- <el-form-item label="分组名称：" prop="groupName">
          <el-input :maxlength="50" v-model.trim="formData.groupName" style="width: 50%"></el-input>
        </el-form-item> -->
        <el-form-item label="分组名称：" prop="groupName">
          <el-input :maxlength="50" v-model.trim="formData.groupName" style="width: 500px"></el-input>
        </el-form-item>
        <el-form-item label="总体说明：">
          <el-input type="textarea" :rows="6" v-model.trim="formData.remarks" :maxlength="50" style="width: 500px"></el-input>
        </el-form-item>
        <el-form-item label="状态：">
          <el-radio-group v-model.trim="formData.state">
            <el-radio :label="1">开启</el-radio>
            <el-radio :label="0">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="认证标准：">
          <el-select v-model.trim="formData.versionId" @change="versionIdChange" v-if="!formData.id" style="width: 500px" clearable filterable>
            <el-option v-for="version in versionList" :key="version.id" :value="version.versionId" :label="version.versionName"></el-option>
          </el-select>
          <span v-else>
            {{versionName}}
          </span>
        </el-form-item>
      </el-form>

      <el-form label-width="110px">
        <el-form-item>
          <label slot="label">
            <el-tooltip class="item" effect="dark" placement="right">
              <div slot="content">
                请先选择主题(如无请新增主题)，<br>
                再选择小主题(如无请新增小主题)，<br>
                最后选择具体款项。
              </div>
              <i class="el-icon-info" style="color:#1890ff"></i>
            </el-tooltip>
            选择主题：
          </label>
          <div style="margin-bottom: 20px;">
            <el-radio-group v-model="theme" style="max-width: 400px; margin-right: 20px;" @change="themeChange">
              <div v-for="item in themeList" :key="item.value" style="margin-top: 10px;">
                <el-radio :label="item" class="theme-radio">
                  <div class="radio-label">{{ item.groupDetail }}</div>
                  <div class="theme-btns-box">
                    <div class="theme-btns">
                      <el-button type="text" @click="editTheme(item)">编辑</el-button>
                      <el-button type="text" @click="deleteTheme(item)">删除</el-button>
                    </div>
                  </div>
                </el-radio>
              </div>
            </el-radio-group>
            <el-button type="primary" icon="el-icon-plus" style="margin-left: 20px;" @click="openThemeDialog">新增主题</el-button>
          </div>
        </el-form-item>
        <el-form-item label="选择小主题：">
          <div style="margin-bottom: 20px;">
            <el-radio-group v-model="subTheme" style="max-width: 400px; margin-right: 20px;" @change="subThemeChange">
              <div v-for="item in subThemeList" :key="item.groupDetail" style="margin-top: 10px;">
                <el-radio :label="item" class="theme-radio">
                  <div class="radio-label">{{ item.groupDetail }}</div>
                  <div class="theme-btns-box">
                    <div class="theme-btns">
                      <el-button type="text" @click="editSubTheme(item)">编辑</el-button>
                      <el-button type="text" @click="deleteSubTheme(item)">删除</el-button>
                    </div>
                  </div>
                </el-radio>
              </div>
            </el-radio-group>
            <el-button type="primary" icon="el-icon-plus" :disabled="!theme" style="margin-left: 20px;" @click="openSubThemeDialog">新增小主题</el-button>
          </div>
        </el-form-item>
      </el-form>
      <div v-show="subTheme">
        <standard-select ref="standardSelect" v-if="formData.versionId" :tier="3" :labelWidth="`96px`" :versionId="formData.versionId" :key="formData.versionId + (subTheme && subTheme.groupDetail || '')" onlyTitle="选择款：" @selectChange="selectChange" @loaded="loaded" :force="true"></standard-select>
        <div class="checkBox">
          <el-checkbox-group v-model.trim="checkedClauseList" @change="handleCheckedClauseChange">
            <!-- <el-checkbox v-for="clause in currentClauseList" :label="clause.clauseId" :key="clause.clauseId" :disabled="hasDisabled(clause.domainId)">款{{ clause.clauseId }}: -->
            <el-checkbox v-for="clause in currentClauseList" :label="clause.clauseId" :key="clause.clauseId" :disabled="clause.disabled">
              <i v-if="clause.isStar != 1" style="margin-left: 10px"></i>
              <i v-else style="font-style:normal;">★</i>
              {{ clause.clauseNo }}：
              {{ clause.clause }}
              <a style="color: #409eff" @click="toClauseDetail(clause)">查看详情</a>
            </el-checkbox>
          </el-checkbox-group>
          <el-checkbox :disabled='disabledAll' v-show="currentClauseList.length" :indeterminate="isIndeterminate" v-model.trim="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
        </div>
        <!-- <el-button type="primary" @click="prev100">前100条</el-button>
        <el-button type="primary" @click="next86" style="margin-left:20px">后86条</el-button>
        <el-button type="primary" @click="total186" style="margin-left:20px">全部186条</el-button> -->
      </div>
    </el-card>
    <div style="margin: 15px 0; background-color: #f9f9f9"></div>
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>已选{{ selectedNum }}款</span>
      </div>
      <el-form label-width="110px">
        <el-form-item v-for="(item, index) in selectedClauseList" :key="item.clauseId" :label="`${item.isStar == 1 ? '★' : ''}${item.clauseNo}`" v-show="showSelectItem(index + 1)">
          {{item.clause}}
          <br />
          <a style="color: #409eff" @click="toClauseDetail(item)">查看详情</a>

          <el-popover placement="top" trigger="click">
            <span style="white-space: pre-wrap">{{ belong(item) }}</span>
            <a slot="reference" style="color: #409eff; margin-left: 20px">所属章节</a>
          </el-popover>
        </el-form-item>
      </el-form>
      <el-pagination v-show="selectedClauseList.length > 0" background layout="prev, pager, next" :page-size="pageSize" :current-page="currentPage" :total="selectedClauseList.length" @current-change="currentChange" style="text-align: center">
      </el-pagination>
      <div style="margin: 15px 0; height: 10px"></div>
      <div style="text-align: center">
        <el-button type="primary" @click="validNSave">保存</el-button>
        <el-button @click="
            $store.dispatch('tagsView/delView', $route) &&
              $router.push({ name: 'FieldManagementList' })
          ">返回</el-button>
      </div>
    </el-card>
    <el-dialog :title="themeTitle" :visible.sync="addThemeDialog" :close-on-click-modal="false" width="40%" @closed="closeThemeDialog">
      <el-form ref="themeForm" :model="themeForm" :rules="themeFormRules" @submit.native.prevent>
        <el-form-item label="主题名称" prop="groupDetail" label-width="100px">
          <el-input v-model="themeForm.groupDetail" maxlength="32" autocomplete="off" placeholder="请输入主题名称"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeThemeDialog">取 消</el-button>
        <el-button type="primary" @click="addTheme">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :title="subThemeTitle" :visible.sync="addSubThemeDialog" :close-on-click-modal="false" width="40%" @closed="closeSubThemeDialog">
      <el-form ref="subThemeForm" :model="subThemeForm" :rules="subThemeFormRules" @submit.native.prevent>
        <el-form-item label="小主题名称" prop="groupDetail" label-width="100px">
          <el-input v-model="subThemeForm.groupDetail" maxlength="32" autocomplete="off" placeholder="请输入小主题名称"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeSubThemeDialog">取 消</el-button>
        <el-button type="primary" @click="addSubTheme">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import request from "@/utils/request";
import { getStandard } from "@/api/system/standard";
import store from "@/store";

export default {
  name: "FieldManagementEdit",
  data() {
    return {
      formData: {
        groupName: "",
        groupName: "",
        remarks: "",
        state: 1,
        versionId: "",
      },
      rules: {
        groupName: {
          required: true,
          message: "请输入分组名称",
          trigger: "change",
        },
        groupName: {
          required: true,
          message: "请输入分组名称",
          trigger: "change",
        },
      },

      url: {
        add: "/system/domain/add",
        edit: "/system/domain/edit",
        detail: "/system/domain",
      },

      currentClauseList: [],
      checkedClauseList: [],
      pageSize: 6,
      currentPage: 1,

      isIndeterminate: false,

      checkedClauseIndexMap: {},
      checkedClauseMap: {},
      selectedClauseList: [],
      finalSelectedClauseIdList: [],
      checkAll: false,

      versionId: "",
      versionList: [],
      chapter: "",
      section: "",
      article: "",
      clause: "",

      isDetail: false,

      waitCheckList: [],
      waitCheckIndexMap: {},
      loading: false,
      theme: null,
      themeList: [],
      subTheme: null,
      subThemeList: [],
      themeTitle: "新增主题",
      themeIndex: null,
      addThemeDialog: false,
      subThemeTitle: "新增小主题",
      subThemeIndex: null,
      addSubThemeDialog: false,
      themeForm: {
        groupDetail: "",
      },
      subThemeForm: {
        groupDetail: "",
      },
      themeFormRules: {
        groupDetail: [
          { required: true, message: "请输入主题名称", trigger: "blur" },
        ],
      },
      subThemeFormRules: {
        groupDetail: [
          { required: true, message: "请输入小主题名称", trigger: "blur" },
        ],
      },
      dataToJSON: "",
      formToJSON: "",
    };
  },

  // watch: {
  //   checkedClauseList: {
  //     immediate: true,
  //     deep: true,
  //     handler() {
  //       this.setSelectedClauseList();
  //     },
  //   },
  // },

  computed: {
    versionName() {
      return (
        this.versionList.find(
          (version) => version.versionId == this.formData.versionId
        ) || {}
      ).versionName;
    },

    selectedNum() {
      return this.selectedClauseList.length;
    },
    showSelectItem() {
      return (index) =>
        index <= this.currentPage * this.pageSize &&
        index > (this.currentPage - 1) * this.pageSize;
    },
    hasDisabled() {
      return (domainId) => {
        if (!domainId) {
          return false;
        }
        return domainId != this.formData.id;
      };
    },
    disabledAll() {
      let a = !!this.currentClauseList.find(
        (clause) => clause.domainId != 0 && clause.domainId != this.formData.id
      );
      let b = false;
      let flag = false;
      for (let j = 0; j < this.currentClauseList.length; j++) {
        if (
          this.checkedClauseList.includes(this.currentClauseList[j].clauseId)
        ) {
          flag = true;
          break;
        }
      }
      if (flag) {
        if (
          this.subTheme &&
          this.subTheme.children &&
          this.subTheme.children.length
        ) {
          let currentSubThemeClauseIdList = [];
          for (let n = 0; n < this.subTheme.children.length; n++) {
            currentSubThemeClauseIdList.push(
              this.subTheme.children[n].groupDetail
            );
          }
          for (let n = 0; n < this.currentClauseList.length; n++) {
            if (
              !currentSubThemeClauseIdList.includes(
                this.currentClauseList[n].clauseId
              ) &&
              this.checkedClauseList.includes(
                this.currentClauseList[n].clauseId
              )
            ) {
              b = true;
              break;
            }
          }
        } else {
          b = true;
        }
      }
      return a || b;
    },
    belong() {
      return (clause) => {
        const { chapterId, sectionId, articleId } = clause;
        const chapterMap = this.$store.getters.standardTypeItemByVersionId(
          this.versionId,
          "chapter",
          "map"
        );
        const sectionMap = this.$store.getters.standardTypeItemByVersionId(
          this.versionId,
          "section",
          "map"
        );
        const articleMap = this.$store.getters.standardTypeItemByVersionId(
          this.versionId,
          "article",
          "map"
        );

        const chapter = (chapterMap || {})[chapterId];
        const section = (sectionMap || {})[sectionId];
        const article = (articleMap || {})[articleId];
        if (!chapter || !section || !article) return "";
        return `
        ${chapter.chapterNo} ${chapter.chapter}
          ${section.sectionNo} ${section.section}
            ${article.articleNo} ${article.article}
        `;
      };
    },
  },

  async mounted() {
    this.loading = true;
    this.getDetail(true, true);
  },

  methods: {
    getDetail(clearCheckedClauseList = true, refresh = false) {
      this.isDetail = false;
      let id = this.$route.params.id || 0;
      let versionId = this.formData.versionId;
      this.isDetail = true;
      request({
        url: `${this.url.detail}/${id}${
          versionId ? `?versionId=${new String(versionId)}` : ""
        }`,
        method: "get",
      }).then((res) => {
        if (res.code == 200) {
          const { versionId, cstDomain, standardsList, versionList, children } =
            res.data;
          this.versionId = versionId;
          this.$set(this, "versionList", versionList);
          if (cstDomain != null && refresh) {
            this.formData = {
              ...cstDomain,
              groupId: children.length ? children[0].groupId : "",
            };
          }
          this.$set(this.formData, "versionId", versionId);
          if (clearCheckedClauseList) {
            this.$set(
              this,
              "checkedClauseList",
              id == 0 ? [] : standardsList || []
            );
          }
          this.$set(this, "currentClauseList", []);
          if (id != 0 && refresh) {
            this.themeList =
              children && children.length ? children[0].children : [];
          }
          for (let i = 0; i < this.themeList.length; i++) {
            if (
              this.themeList[i].children &&
              this.themeList[i].children.length
            ) {
              for (let j = 0; j < this.themeList[i].children.length; j++) {
                if (
                  this.themeList[i].children[j].children &&
                  this.themeList[i].children[j].children.length
                ) {
                  for (
                    let k = 0;
                    k < this.themeList[i].children[j].children.length;
                    k++
                  ) {
                    this.themeList[i].children[j].children[k].groupDetail =
                      Number(
                        this.themeList[i].children[j].children[k].groupDetail
                      );
                  }
                }
              }
            }
          }
          if (id != 0) {
            this.$set(this.formData, "delGroupIds", res.data.delGroupIds);
            this.dataToJSON =
              children && children.length
                ? JSON.stringify(children[0].children)
                : "[]";
            this.formToJSON = JSON.stringify(this.formData);
          }
          this.loading = false;
        }
      });
    },

    setSelectedClauseList() {
      let clauseMap = this.$store.getters.standardTypeItemByVersionId(
        this.versionId,
        "clause",
        "map"
      );
      if (!clauseMap || Object.keys(clauseMap).length === 0) return [];
      let cList = [];
      if (
        this.subTheme &&
        this.subTheme.children &&
        this.subTheme.children.length
      ) {
        cList = this.subTheme.children.map((item) => item.groupDetail);
      }
      cList.sort((a, b) => a - b);
      const list = cList.map((id) => clauseMap[id]);
      this.$set(this, "selectedClauseList", list);
    },

    loaded() {
      this.setSelectedClauseList();
    },

    selectChange(tier, data, { chapter, section, article }) {
      let currentSubThemeClauseIdList = [];
      for (let i = 0; i < this.subTheme.children.length; i++) {
        currentSubThemeClauseIdList.push(
          Number(this.subTheme.children[i].groupDetail)
        );
      }
      for (let index = 0; index < data.length; index++) {
        if (!data[index].domainId) {
          data[index].disabled =
            this.checkedClauseList.includes(data[index].clauseId) &&
            !currentSubThemeClauseIdList.includes(data[index].clauseId);
        } else {
          if (data[index].domainId != this.formData.id) {
            data[index].disabled = true;
          } else {
            data[index].disabled =
              this.checkedClauseList.includes(data[index].clauseId) &&
              !currentSubThemeClauseIdList.includes(data[index].clauseId);
          }
        }
      }
      this.currentClauseList = data;
      this.chapter = chapter;
      this.section = section;
      this.article = article;
      let currentIds = data.map((da) => da.clauseId);
      this.checkAll = currentIds.every((id) =>
        this.checkedClauseList.includes(id)
      );
      let subThemeIds = [];
      if (this.subTheme.children) {
        subThemeIds = this.subTheme.children.map((item) => item.groupDetail);
      }
      this.isIndeterminate = currentIds.some((id) => subThemeIds.includes(id));
    },

    handleCheckAllChange(val) {
      let nCheck = this.currentClauseList.map((clause) => clause.clauseId);
      let list = [];
      let check = this.checkedClauseList;
      list = [...check];
      nCheck.forEach((id, index) => {
        let _index = this.checkedClauseList.indexOf(id);
        if (_index !== -1) {
          this.checkedClauseList.splice(_index, 1);
        }
        if (this.subTheme.children) {
          for (let i = 0; i < this.subTheme.children.length; i++) {
            if (this.subTheme.children[i].groupDetail == id) {
              this.subTheme.children.splice(i, 1);
              break;
            }
          }
        }
      });
      list = [...this.checkedClauseList];
      if (val) {
        list = list.concat(nCheck);
        this.currentClauseList.forEach((item) => {
          this.subTheme.children.push({
            groupDetail: item.clauseId,
            type: "3",
          });
        });
      } else {
        this.subTheme.children = [];
      }
      list.sort((a, b) => a - b);
      list = Array.from(new Set(list));
      this.$set(this, "checkedClauseList", list);
      this.isIndeterminate = false;
      this.setSelectedClauseList();
    },

    handleCheckedClauseChange(value) {
      this.$set(this.checkedClauseIndexMap, this.article, [...value]);
      let currentClauseListIds = this.currentClauseList.map(
        (item) => item.clauseId
      );
      let checkedCount = value.filter((item) =>
        currentClauseListIds.includes(item)
      ).length;
      let disabledClauseList = this.currentClauseList.filter(
        (item) => item.disabled
      );
      this.checkAll =
        checkedCount + disabledClauseList.length ===
        this.currentClauseList.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.currentClauseList.length;
      let themeCheckedClauseList = [];
      for (let i = 0; i < this.themeList.length; i++) {
        if (this.themeList[i].children && this.themeList[i].children.length) {
          for (let j = 0; j < this.themeList[i].children.length; j++) {
            if (
              this.themeList[i].children[j].children &&
              this.themeList[i].children[j].children.length
            ) {
              for (
                let k = 0;
                k < this.themeList[i].children[j].children.length;
                k++
              ) {
                themeCheckedClauseList.push(
                  this.themeList[i].children[j].children[k].groupDetail
                );
              }
            }
          }
        }
      }
      if (this.subTheme.children && this.subTheme.children.length) {
        for (let m = 0; m < this.subTheme.children.length; m++) {
          if (
            !this.checkedClauseList.includes(
              this.subTheme.children[m].groupDetail
            )
          ) {
            this.subTheme.children.splice(m, 1);
          }
        }
      }
      for (let index = 0; index < this.checkedClauseList.length; index++) {
        if (!themeCheckedClauseList.includes(this.checkedClauseList[index])) {
          this.subTheme.children.push({
            groupDetail: this.checkedClauseList[index],
            type: "3",
          });
        }
      }
      this.setSelectedClauseList();
    },

    pegging() {
      if (this.isDetail && this.waitCheckList.length) {
        const list = this.currentClauseList.filter((current) =>
          this.waitCheckList.includes(current.clauseId)
        );
        let ids = [...new Set(list.map((li) => li.clauseId))];
        if (list.length) {
          ids.push(...this.checkedClauseIndexMap[this.article]);
          ids = [...new Set(ids)];
          this.$set(this.checkedClauseIndexMap, this.article, ids);
          this.checkedClauseList.push(...ids);
        }
      }
    },

    versionIdChange(value) {
      if (value != this.versionId) {
        this.loading = true;
        this.getDetail();
      }
    },

    currentChange(currentPage) {
      this.currentPage = currentPage;
    },

    toClauseDetail({ clauseId, versionId }) {
      this.$router.push({
        name: "ClauseParticulars",
        params: {
          clauseId,
          versionId,
        },
      });
    },

    validRequired() {
      if (!this.theme) {
        this.$message({
          type: "warning",
          message: "请选择主题！",
        });
        return;
      }
      if (!this.subTheme) {
        this.$message({
          type: "warning",
          message: "请选择小主题！",
        });
        return;
      }
      if (!this.formData.versionId || !this.checkedClauseList.length) {
        this.$message({
          type: "warning",
          message: "请选择款！",
        });
        return;
      }
      return true;
    },

    validNSave() {
      if (
        this.formData.id &&
        this.dataToJSON == JSON.stringify(this.themeList) &&
        this.formToJSON == JSON.stringify(this.formData)
      ) {
        this.$store.dispatch("tagsView/delView", this.$route);
        this.$router.push("/filedManagement/fieldManagementList");
        return;
      }
      this.$refs["elForm"].validate((valid) => {
        if (valid && this.validRequired()) {
          this.loading = true;
          const { createTime, updateTime, ...data } = this.formData;
          let clauseIds = [];
          for (let i = 0; i < this.themeList.length; i++) {
            if (
              this.themeList[i].children &&
              this.themeList[i].children.length
            ) {
              for (let j = 0; j < this.themeList[i].children.length; j++) {
                if (
                  this.themeList[i].children[j].children &&
                  this.themeList[i].children[j].children.length
                ) {
                  for (
                    let k = 0;
                    k < this.themeList[i].children[j].children.length;
                    k++
                  ) {
                    clauseIds.push(
                      this.themeList[i].children[j].children[k].groupDetail
                    );
                  }
                }
              }
            }
          }
          const form = {
            ...data,
            clauseIds,
            children: this.themeList,
            versionId: this.formData.versionId,
            domainId: data.id,
          };
          let url = form.id ? this.url.edit : this.url.add;
          request({
            url: url,
            method: form.id ? "put" : "post",
            data: form,
          })
            .then((res) => {
              if (res.code == 200) {
                this.getClauseByIds(this.checkedClauseList);
                this.$message({ type: "success", message: res.msg });
                this.$store.dispatch("tagsView/delView", this.$route);
                this.$router.push("/filedManagement/fieldManagementList");
                this.$nextTick(() => {
                  this.getStandard(this.formData.versionId, true, false);
                });
              }
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },

    save() {
      this.$refs["elForm"].validate((valid) => {
        if (valid) {
          const { createTime, updateTime, ...data } = this.formData;
          const form = {
            ...data,
            clauseIds: this.waitCheckList,
            versionId: this.formData.versionId,
            domainId: data.id,
          };
          let url = form.id ? this.url.edit : this.url.add;
          request({
            url: url,
            method: form.id ? "put" : "post",
            data: form,
          }).then((res) => {
            if (res.code == 200) {
              this.getClauseByIds(this.finalSelectedClauseIdList);
              this.$message({ type: "success", message: res.msg });
              this.$store.dispatch("tagsView/delView", this.$route);
              this.$router.push("/filedManagement/fieldManagementList");
            }
          });
        }
      });
    },

    getClauseListByTest() {
      if (!this.formData.versionId) return // console.log("没有版本号");
      let list = this.$store.getters.standardTypeItemByVersionId(
        this.formData.versionId,
        "clause",
        "list"
      );
      return (list || []).map((li) => Number(li.clauseId));
    },

    prev100() {
      let list = this.getClauseListByTest();
      let selected = list.filter((li, index) => index <= 99);
      this.waitCheckList = selected;
      this.save();
    },
    next86() {
      let list = this.getClauseListByTest();
      let selected = list.filter((li, index) => index > 99);
      this.waitCheckList = selected;
      this.save();
    },
    total186() {
      this.waitCheckList = this.getClauseListByTest();
      this.save();
    },

    getClauseByIds(clauseIds) {
      if (clauseIds.length == 0) return;
      request({
        url: "system/standards/selectByClauseIdsAndVersionId",
        method: "post",
        data: {
          versionId: store.getters.standardBaseVersionId,
          clauseIds,
        },
      }).then((res) => {
        if (res.code == 200) {
          const list = res.data;
          list.forEach((li) => {
            this.$store.dispatch("SetClause", li);
          });
          // this.$store.dispatch("tagsView/delView", this.$route);
          // this.$router.push({ name: "FieldManagementList" });
        }
      });
    },
    openThemeDialog() {
      this.themeTitle = "新增主题";
      this.addThemeDialog = true;
    },
    openSubThemeDialog() {
      this.subThemeTitle = "新增小主题";
      if (this.theme) {
        this.addSubThemeDialog = true;
      }
    },
    addTheme() {
      this.$refs.themeForm.validate((valid) => {
        if (valid) {
          for (let index = 0; index < this.themeList.length; index++) {
            if (
              this.themeList[index].groupDetail === this.themeForm.groupDetail
            ) {
              this.$message({
                type: "warning",
                message: "请勿添加重复的主题",
              });
              return;
            }
          }
          if (this.themeTitle === "编辑主题") {
            this.themeList[this.themeIndex].groupDetail =
              this.themeForm.groupDetail;
          } else {
            this.themeList.push({
              groupDetail: this.themeForm.groupDetail,
              type: "1",
            });
          }
          this.addThemeDialog = false;
        } else {
          return false;
        }
      });
    },
    editTheme(data) {
      this.themeTitle = "编辑主题";
      for (let index = 0; index < this.themeList.length; index++) {
        if (this.themeList[index].groupDetail === data.groupDetail) {
          this.themeIndex = index;
          this.themeForm.groupDetail = data.groupDetail;
          this.addThemeDialog = true;
          break;
        }
      }
    },
    deleteTheme(data) {
      this.$confirm("此操作将删除该主题, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          for (let index = 0; index < this.themeList.length; index++) {
            if (this.themeList[index].groupDetail === data.groupDetail) {
              if (data.groupDetail == this.theme.groupDetail) {
                this.theme = null;
                this.subTheme = null;
                this.subThemeList = [];
              }
              this.themeList.splice(index, 1);
              break;
            }
          }
          this.$message({
            type: "success",
            message: "删除成功!",
          });
        })
        .catch(() => {});
    },
    addSubTheme() {
      this.$refs.subThemeForm.validate((valid) => {
        if (valid) {
          if (this.theme.children) {
            for (let index = 0; index < this.theme.children.length; index++) {
              if (
                this.theme.children[index].groupDetail ===
                this.subThemeForm.groupDetail
              ) {
                this.$message({
                  type: "warning",
                  message: "请勿添加重复的小主题",
                });
                return;
              }
            }
            if (this.subThemeTitle === "编辑小主题") {
              this.subThemeList[this.subThemeIndex].groupDetail =
                this.subThemeForm.groupDetail;
            } else {
              this.theme.children.push({
                groupDetail: this.subThemeForm.groupDetail,
                type: "2",
                children: [],
              });
            }
          } else {
            this.theme.children = [
              {
                groupDetail: this.subThemeForm.groupDetail,
                type: "2",
                children: [],
              },
            ];
          }
          this.subThemeList = this.theme.children ? this.theme.children : [];
          this.addSubThemeDialog = false;
        } else {
          return false;
        }
      });
    },
    editSubTheme(data) {
      this.subThemeTitle = "编辑小主题";
      for (let index = 0; index < this.subThemeList.length; index++) {
        if (this.subThemeList[index].groupDetail === data.groupDetail) {
          this.subThemeIndex = index;
          this.subThemeForm.groupDetail = data.groupDetail;
          this.addSubThemeDialog = true;
          break;
        }
      }
    },
    deleteSubTheme(data) {
      this.$confirm("此操作将删除该小主题, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          for (let index = 0; index < this.subThemeList.length; index++) {
            if (this.subThemeList[index].groupDetail === data.groupDetail) {
              this.subThemeList.splice(index, 1);
              break;
            }
          }
          this.$message({
            type: "success",
            message: "删除成功!",
          });
        })
        .catch(() => {});
    },
    themeChange(value) {
      this.subThemeList = value.children ? value.children : [];
      this.subTheme = null;
    },
    subThemeChange(value) {
      this.loaded();
      this.loading = true;
      this.getDetail(false);
    },
    closeThemeDialog() {
      this.themeForm.groupDetail = "";
      this.$refs.themeForm.resetFields();
      this.addThemeDialog = false;
    },
    closeSubThemeDialog() {
      this.subThemeForm.groupDetail = "";
      this.$refs.subThemeForm.resetFields();
      this.addSubThemeDialog = false;
    },
  },
};
</script>

<style lang='scss' scoped>
.field-management-detail {
  .el-card {
    border: 0;
  }
  .checkBox {
    padding-left: 96px;
    padding-right: 30px;
    ::v-deep .el-checkbox {
      width: 100%;
      margin-bottom: 24px;
      .el-checkbox__input {
        vertical-align: top;
        & > span {
          vertical-align: sub;
        }
      }
      .el-checkbox__input.is-checked + .el-checkbox__label {
        color: #606266;
      }
      .el-checkbox__label {
        width: 100%;
        white-space: normal;
        padding-left: 20px;
      }
    }
  }
  .theme-btns {
    display: none;
    .el-button--text {
      padding: 0;
      border: none;
    }
  }
  .theme-radio {
    position: relative;
    .radio-label {
      max-width: 300px;
      margin-right: 80px;
      word-break: break-word;
    }
    .theme-btns-box {
      width: 70px;
      display: inline-block;
      position: absolute;
      right: 0;
      top: 0;
    }
  }
  .theme-radio:hover {
    .theme-btns {
      display: block;
    }
  }
  ::v-deep .el-radio__label {
    text-overflow: ellipsis;
    white-space: normal;
    vertical-align: top;
    display: inline-block;
  }
}
</style>
