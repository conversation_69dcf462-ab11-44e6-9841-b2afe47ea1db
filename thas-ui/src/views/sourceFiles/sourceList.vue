<template>
  <div class='source-list'>
    <el-form ref="queryForm" size="small" :inline="true" :model="queryData" label-width="100px">
      <el-form-item label="资料文件夹" prop="source">
        <el-cascader v-model="queryData.source" :options="options" :props="{ label:'dictLabel',  value: 'dictValue', multiple: true, emitPath: false }" :collapse-tags='true' clearable style="width: 100%;"></el-cascader>
      </el-form-item>
      <el-form-item label="医院名称" prop="hospitalName">
        <el-input v-model="queryData.hospitalName" maxlength="50" placeholder="请输入医院名称" clearable></el-input>
      </el-form-item>
      <el-form-item label="评审员名称" prop="reviewerName">
        <el-input v-model="queryData.reviewerName" maxlength="50" placeholder="请输入评审员名称" clearable></el-input>
      </el-form-item>
      <el-form-item v-if="onlyCommonAdmit" label="上传人" prop="uploaderName">
        <el-input v-model="queryData.uploaderName" maxlength="50" placeholder="请输入上传人" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button style="margin-left: 30px;" type="primary" size="small" @click="query">查询</el-button>
        <el-button size="small" @click="resetQuery">重置</el-button>
      </el-form-item>

      <el-row :gutter="24" v-if="canUpload">
        <el-col :span="8">
          <el-button size="small" type="primary" @click="uploadSource">上传资源</el-button>
          <el-button v-if="onlyCommonAdmit" size="small" type="primary" @click="batchShare">批量分享</el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-table :data="fileSource" border stripe @selection-change="handleSelectionChange" style="margin-top:12px;">
      <el-table-column v-if="onlyCommonAdmit" type="selection" :selectable="selectable"></el-table-column>
      <el-table-column align="center" label="文件名称" prop="fileName"></el-table-column>
      <el-table-column align="center" label="上传时间" prop="upLoadTime"></el-table-column>
      <el-table-column align="center" label="上传人" prop="upLoaderName"></el-table-column>
      <el-table-column align="center" label="资料文件夹">
        <template slot-scope="{row}">
          {{getLabelByType(row.source)}}
        </template>
      </el-table-column>
      <el-table-column align="center" label="医院名称" prop="hospitalName">
        <template slot-scope="{row}">
          {{row.hospitalName || '-'}}
        </template>
      </el-table-column>
      <el-table-column align="center" label="评审员名称" prop="reviewerName">
        <template slot-scope="{row}">
          {{row.reviewerName || '-'}}
        </template>
      </el-table-column>
      <el-table-column align="center" label="权限">
        <template slot-scope="{row}">
          <span>
            {{ onlyCommonAdmit ? formateRole(row.roleKeyList): formateRole([row.roleKey])}}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="{row}">
          <el-button v-if="onlyCommonAdmit" type="text" @click="del(row)">删除</el-button>
          <el-button v-if="onlyCommonAdmit && isAdmitUpload(row)" type="text" @click="share(row)">分享</el-button>
          <el-button type="text" @click="look(row)">查看</el-button>
          <el-button type="text" @click="download(row)">下载</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query" />

    <el-upload ref="upload" :action="action" :before-upload="beforeUpload" :on-success="onSuccess" :with-credentials="true" :headers="header" :on-error="onError" :data="{type : 1}" :accept="accept" style="display: none">
      <span id="sourceUpload" slot="trigger">上传图片</span>
    </el-upload>

    <el-dialog :title="isBatch ? '批量分享': '分享'" :visible.sync="dialogVisible" width="30%" :close-on-click-modal="false" :close-on-press-escape='false' :modal-append-to-body='false'>
      <span>请选择需要分享的角色 （未勾选将取消原有分享）</span>
      <el-checkbox-group v-model="checkList">
        <el-checkbox v-for="(role, index) in roleKeys" :key="index" :label="role.role" style="display:block;margin-top:12px;">{{role.name}}</el-checkbox>
      </el-checkbox-group>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="changeCreate">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="上传资源" :visible.sync="showUploadDialog" :close-on-click-modal="false" width="500px">
      <el-form ref="uploadForm" :model="uploadForm" label-width="120px" :rules="uploadRules">
        <div class="upload-tip">
          <p class="tip-title">温馨提示：</p>
          <p class="tip-desc">* 如需上传到指定文件夹，请选择需要上传的资料文件夹和医院。</p>
          <p class="tip-desc">* 如未选择，将上传到默认文件夹“平台资源”下。</p>
        </div>
        <el-form-item label="资料文件夹：" prop="source">
          <el-cascader v-model="uploadForm.source" :options="options" :props="{ label:'dictLabel',  value: 'dictValue', multiple: false, emitPath: false }" :collapse-tags='true' clearable style="width: 100%;"></el-cascader>
        </el-form-item>
        <el-form-item label="医院：" prop="hospitalApplyNo">
          <el-select v-model="uploadForm.hospitalApplyNo" filterable clearable placeholder="请选择" style="width: 100%;">
            <el-option v-for="item in hospitalList" :key="item.applyNo" :value="item.applyNo" :label="item.hospitalName">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="showUploadDialog = false">返 回</el-button>
        <el-button type="primary" size="small" @click="saveUpload">上 传</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import request, { download } from "@/utils/request"
import { getToken } from "@/utils/auth";
import { hospitalFileTypeTree, reviewerFileTypeTree, inspectorFileTypeTree, getLabelByType } from './mixin'
import { tansParams } from "@/utils/ruoyi";
export default {
  name: 'sourceList',
  data() {
    return {
      url: {
        upload: '/common/uploadFile',
        list: '/file/share/query',
        create: '/file/share/create',
        bing_create: '/file/share/bing-create',
        delete: '/file/share/delete'
      },
      queryData: {
        roleKey: '',
        fileName: '',
        hospitalName: '',
        reviewerName: '',
        source: [],
        uploaderName: '',
        pageSize: 10,
        pageNum: 1,
      },
      total: 0,
      fileSource: [],
      uploadDom: null,
      multipleSelection: [],
      roleKeys: [
        {
          name: '医疗机构',
          role: 'hospital'
        },
        // {
        //   name: '验证评审员',
        //   role: 'senior-assessor'
        // },
        {
          name: '评审员',
          role: 'assessor'
        },
        {
          name: '评审学员',
          role: 'trainees_assessor'
        }],
      dialogVisible: false,
      checkList: [],
      isBatch: false,
      typeOptions: [],
      // 上传资源到对应医院的文件夹
      showUploadDialog: false,
      hospitalList: [],
      uploadForm: {
        source: 'sourceUpload',
        hospitalApplyNo: ''
      },
      uploadRules: {
        source: [
          { required: false, message: "请选择资料文件夹", trigger: "change" }
        ],
        hospitalApplyNo: [
          { required: false, message: "请选择医院", trigger: "change" },
        ]
      }
    };
  },

  components: {},

  computed: {
    action() {
      return `${localStorage.baseUrl || process.env.VUE_APP_BASE_API
        }${this.url.upload}`;
    },

    header() {
      return {
        Authorization: 'Bearer ' + getToken(),
      }
    },

    suffixes() {
      return ['.JPG', '.JPEG', '.PNG', '.PDF', '.XLS', '.XLSX', '.DOC', '.DOCX', '.MP4', '.XLSM']
    },

    accept() {
      return this.suffixes.join(',')
    },

    canUpload() {
      // let roles = this.roleKeys.map(({ role }) => role);
      // let has = false;
      // roles.forEach(role => {
      //   if (!has) {
      //     has = this.$store.getters.roles.includes(role)
      //   }
      // })
      return this.$store.getters.roles.includes('common-admin')
    },

    onlyCommonAdmit() {
      return this.$store.getters.roles.includes('common-admin')
    },

    isAdmitUpload() {
      return row => {
        return row.roleKey === 'common-admin' && row.source === "sourceUpload"
      }
    },

    formateRole() {
      return roles => {
        let str = ['管理员'];
        (roles || []).forEach((role) => {
          if (!role.includes('admin')) {
            let rol = this.roleKeys.find(ro => {
              return ro.role === role
            })
            if (rol) {
              str.push(rol.name)
            }
          }
        })
        return str.join('、')
      }
    },

    options() {
      if (this.queryData.roleKey.includes('admin')) {
        return [{ dictLabel: '平台资源', dictValue: 'sourceUpload' }, { dictLabel: '医院', children: [...hospitalFileTypeTree] }, { dictLabel: '评审员', children: [...reviewerFileTypeTree] }, { dictLabel: '员工', children: [...inspectorFileTypeTree] }]
      } else if (this.queryData.roleKey == 'hospital') {
        return [{ dictLabel: '平台资源', dictValue: 'sourceUpload' }, ...hospitalFileTypeTree]
      } else if (this.queryData.roleKey == 'assessor' || this.queryData.roleKey === 'trainees_assessor' || this.queryData.roleKey === 'senior-assessor') {
        return [{ dictLabel: '平台资源', dictValue: 'sourceUpload' }, ...reviewerFileTypeTree]
      } else if (this.queryData.roleKey == 'inspector') {
        return [{ dictLabel: '平台资源', dictValue: 'sourceUpload' }, ...inspectorFileTypeTree]
      }
      return [{ dictLabel: '平台资源', dictValue: 'sourceUpload' }]
    },

    queryForm() {
      let { roleKey, fileName, hospitalName, reviewerName, source, uploaderName } = this.queryData
      return {
        roleKey,
        fileName,
        source,
        uploaderName,
        hospitalName,
        reviewerName
      }
    },

    getLabelByType() {
      return type => getLabelByType(type)
    }
  },

  mounted() {
    if (this.$store.getters.roles.includes('admin')) {
      this.queryData.roleKey = 'common-admin'
    } else {
      this.queryData.roleKey = this.$store.getters.roles[0]
    }
    this.query();
    this.getHospitalList();
  },

  methods: {
    query() {
      request({
        url: this.url.list,
        method: 'post',
        data: this.queryForm,
        params: {
          pageNum: this.queryData.pageNum,
          pageSize: this.queryData.pageSize,
        }
      }).then(res => {
        this.fileSource = res.rows;
        this.total = res.total;
      })
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.$refs["queryForm"].resetFields();
      this.query();
    },

    createFileBelong(roleKey, fileIdList) {
      let hospitalItem = this.hospitalList.filter(item => item.applyNo === this.uploadForm.hospitalApplyNo) || [];
      let hospitalName = hospitalItem[0]?.hospitalName || '';
      request({
        url: this.url.create,
        method: 'post',
        data: {
          roleKey,
          fileIdList,
          source: this.uploadForm.source,
          ownerId: this.$store.getters.userId,
          applyNo: this.uploadForm.hospitalApplyNo,
          hospitalName
        }
      }).then(() => {
        this.query();

        this.fileId = null;
        this.checkList = [];
        this.multipleSelection = [];
        this.dialogVisible = false;
        this.isBatch = false;
      })
    },

    uploadSource() {
      this.showUploadDialog = true;
    },

    saveUpload() {
      this.$refs.uploadForm.validate((valid) => {
        if (valid) {
          if (!this.uploadDom) {
            this.uploadDom = document.getElementById(
              "sourceUpload"
            );
          }
          this.uploadDom.click();
        }
      })
    },

    getHospitalList() {
      request({
        url: "/hospital/query/Unassigned/info",
        method: "post",
        data: {},
      }).then((res) => {
        console.log('res: ', res);
        this.hospitalList = res;
      });
    },

    del(row) {
      this.$confirm('此操作将永久删除该文件记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        request({
          url: this.url.delete,
          method: 'post',
          data: {
            fileIdList: [row.fileId]
          }
        }).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功'
          });
          this.query();
        })
      })
    },

    share(row) {
      let { roleKeyList = [] } = row
      this.fileId = row.fileId;
      this.checkList = [...roleKeyList]
      this.dialogVisible = true;
      this.isBatch = false;
    },

    look(row) {
      let url = row.url;
      let lastPointIndex = url.lastIndexOf('.')
      let fileName = url.substring(lastPointIndex + 1).toLowerCase();
      let accept = ['png', 'jpg', 'jpeg', 'pdf', 'mp4']
      if (accept.includes(fileName)) {
        window.open(row.url, '_blank');
      } else {
        this.download(row)
      }
    },

    download(row) {
      download(
        "/common/downloadFile",
        { fileId: Number(row.fileId) },
        row.fileName
      );
    },


    batchShare() {
      if (this.multipleSelection.length === 0) return this.$message.warning('请勾选需要分享的文件');
      this.dialogVisible = true
      this.checkList = []
      this.isBatch = true;
    },

    changeCreate() {
      let fileIds = [];
      if (!this.isBatch) {
        fileIds = [this.fileId]
      } else {
        fileIds = this.multipleSelection.map(({ fileId }) => fileId);
      }
      this.createFileBelong(this.checkList, fileIds)
    },

    selectable(row) {
      return row.source === 'sourceUpload'
    },

    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    beforeUpload(file) {
      const suffix = '.' + file.name.split('.').pop().toLocaleUpperCase();
      if (!this.suffixes.includes(suffix)) {
        this.$message({
          type: 'warning',
          message: '请上传指定格式的文件！'
        })
        return false;
      }

      if (file.size == 0) {
        this.$message({
          type: 'warning',
          message: '上传的文件大小不能为0KB'
        })
        return false;
      }

      // 当fileSize大于0时才检查文件大小是否符合，为0或者小于0则不校验文件大小
      if (file.size / 1024 / 1024 > 100) {
        this.$message({
          type: 'warning',
          message: '上传的文件大小不得超过100M'
        })
        return false;
      }
      return true;
    },

    onSuccess(response, file, fileList) {
      if (response.code != 200) {
        return this.onError(response)
      }
      const result = response.data;
      if (result.fileId) {
        this.createFileBelong(['common-admin'], [result.fileId])
      }
      this.showUploadDialog = false;
    },

    onError(error) {
      this.showUploadDialog = false;
      this.$message.error(error.msg)
    },

  }
}

</script>

<style lang='scss' scoped>
.source-list {
  padding: 20px;

  .pre {
    font-family: auto;
    text-align: center;
    margin: 0;
    white-space: pre;
  }

  ::v-deep .el-dialog__body {
    padding: 5px 20px;
  }
  ::v-deep .el-form-item__content {
    width: 240px;
  }

  .upload-tip {
    margin-bottom: 20px;
    >p {
      margin: 0;
    }
    .tip-title {
      font-weight: bold;
      color: #303133;
    }
    .tip-desc {
      color: #606266;
      margin-left: 20px;
      margin-top: 5px;
      padding-left: 10px;
    }
  }
}
</style>
