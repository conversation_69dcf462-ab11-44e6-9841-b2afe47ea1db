package com.thas.web.domain.dto;

import com.thas.web.domain.HospitalBaseInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class HospitalBaseInfosDTO extends HospitalBaseInfo {

    /**
     * 被授权人姓名
     */
    @ApiModelProperty(value = "被授权人姓名", required = true)
    private String authContactName;

    /**
     * 被授权人手机号
     */
    @ApiModelProperty(value = "手机号", required = true)
    private String authContactMobile;

    /**
     * 被授权人邮箱
     */
    @ApiModelProperty(value = "被授权人邮箱", required = true)
    private String authContactEmail;

    /**
     * 被授权人联系地址
     */
    @ApiModelProperty(value = "联系地址", required = true)
    private String contactAddress;

    /**
     * 法人姓名
     */
    @ApiModelProperty(value = "法人姓名 ", required = true)
    private String legalPersonName;

    /**
     * 法人性别(1:男；2:女；3:其他)
     */
    @ApiModelProperty(value = "法人性别(1:男；2:女；3:其他) ", required = true)
    private Integer gender;

    /**
     * 法人职务
     */
    @ApiModelProperty(value = "法人职务 ", required = true)
    private String legalPersonPost;

    /**
     * 法人职称
     */
    @ApiModelProperty(value = "法人职称 ", required = true)
    private String legalPersonTitle;

    /**
     * 法人电话
     */
    @ApiModelProperty(value = "法人电话 ", required = true)
    private String legalPersonPhone;

    /**
     * 法人传真
     */
    @ApiModelProperty(value = "法人传真 ")
    private String legalPersonFax;

    /**
     * 法人手机
     */
    @ApiModelProperty(value = "法人手机 ", required = true)
    private String legalPersonMobile;

    /**
     * 法人邮箱
     */
    @ApiModelProperty(value = "法人邮箱 ", required = true)
    private String legalPersonEmail;

    private String hospitalTypeCZ;
    private String formOwnershipCZ;
    private String natureOperationCZ;
    private String managementAffiliationCZ;
    private String hospitalLevelCZ;
    private String teachingCategoryCZ;


    //医疗结构类别:1:综合医院 2:专科医院
    public static Map<String, String> hospitalTypeMap;
    //所有制形式:1全民 2集体 3私人 4股份 5中外合资（合作）6其他
    public static Map<String, String> formOwnershipMap;
    //经营性质:1政府举办非营利性 2非政府举办非营利性 3营利性
    public static Map<String, String> natureOperationMap;
    //管理隶属关系:1部属（管）2省自治区、直辖市属 3省辖市区、地辖市属 4县(旗)属 5其他
    public static Map<String, String> managementAffiliationMap;
    //医院级别:1一级医院 2二级医院 3三级医院
    public static Map<String, String> hospitalLevelMap;
    //教学类别:1大学直属附属医院 2大学非直属附属医院 3教学医院 4其他
    public static Map<String, String> teachingCategoryMap;

    static {
        hospitalTypeMap = new HashMap();
        hospitalTypeMap.put("1", "综合医院");
        hospitalTypeMap.put("2", "专科医院");

        formOwnershipMap = new HashMap();
        formOwnershipMap.put("1", "全民");
        formOwnershipMap.put("2", "集体");
        formOwnershipMap.put("3", "私人");
        formOwnershipMap.put("4", "股份");
        formOwnershipMap.put("5", "中外合资（合作）");
        formOwnershipMap.put("6", "其他");

        natureOperationMap = new HashMap();
        natureOperationMap.put("1", "政府举办非营利性");
        natureOperationMap.put("2", "非政府举办非营利性");
        natureOperationMap.put("3", "营利性");

        managementAffiliationMap = new HashMap();
        managementAffiliationMap.put("1", "部属（管）");
        managementAffiliationMap.put("2", "省自治区、直辖市属");
        managementAffiliationMap.put("3", "省辖市区、地辖市属");
        managementAffiliationMap.put("4", "县(旗)属");
        managementAffiliationMap.put("5", "其他");

        hospitalLevelMap = new HashMap();
        hospitalLevelMap.put("1", "一级医院");
        hospitalLevelMap.put("2", "二级医院");
        hospitalLevelMap.put("3", "三级医院");

        teachingCategoryMap = new HashMap();
        teachingCategoryMap.put("1", "大学直属附属医院");
        teachingCategoryMap.put("2", "大学非直属附属医院");
        teachingCategoryMap.put("3", "教学医院");
        teachingCategoryMap.put("4", "其他");
    }

}
