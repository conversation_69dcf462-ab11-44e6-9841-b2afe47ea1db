<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.CstVersioningMapper">

    <resultMap type="CstVersioning" id="CstVersioningResult">
        <result property="id"    column="id"    />
        <result property="versionId"    column="version_id"    />
        <result property="versionName"    column="version_name"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createId"    column="create_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateId"    column="update_id"    />
        <result property="updateTime"    column="update_time"    />
        <association property="createName"  column="create_id" select="selectUserByUserNameId" javaType="string">
        </association>
    </resultMap>


    <sql id="selectCstVersioningVo">
        select id, version_name, status,version_id, del_flag, create_id, create_time, update_id, update_time from cst_versioning
    </sql>

    <sql id="SQL_AES_ENCRYPT">'${@com.thas.web.config.SqlAesEncryptConfig@SQL_AES_ENCRYPT}'</sql>

    <select id="selectCstVersioningList" parameterType="CstVersioning" resultMap="CstVersioningResult">
        <include refid="selectCstVersioningVo"/>
        where del_flag = '0'
        <if test="versionName != null  and versionName != ''"> and version_name like concat('%', #{versionName}, '%')</if>
        <if test="status != null  and status != ''"> and status = #{status}</if>
        ORDER BY `status` DESC , update_time DESC
    </select>

    <select id="selectEffCstVersion" resultType="Long">
        select version_id from cst_versioning where status = 1
    </select>

    <select id="selectUserByUserNameId"  resultType="string">
        select AES_DECRYPT(unhex(nick_name),<include refid="SQL_AES_ENCRYPT"/>) nick_name from sys_user where user_id = #{id}
    </select>

    <select id="selectCstVersioningById" parameterType="Long" resultMap="CstVersioningResult">
        <include refid="selectCstVersioningVo"/>
        where id = #{id}
    </select>

    <insert id="insertCstVersioning" parameterType="CstVersioning" useGeneratedKeys="true" keyProperty="id">
        insert into cst_versioning
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="versionName != null and versionName != ''">version_name,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="versionName != null and versionName != ''">#{versionName},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCstVersioning" parameterType="CstVersioning">
        update cst_versioning
        <trim prefix="SET" suffixOverrides=",">
            <if test="versionName != null and versionName != ''">version_name = #{versionName},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCstVersioningById" parameterType="Long">
        create temporary table temp_delete_cst_versioning (select t.aut_code,t.hospital_apply_no,t.aut_cs_id from aut_sa_relation t where t.aut_cs_id = (
                    select version_id from cst_versioning where id = #{id} and status != 1) and status = 1);
        update aut_sa_aud set status = 0 where aut_code in (select aut_code from temp_delete_cst_versioning) and status = 1;
        update aut_sa_relation set status = 0 where aut_code in (select aut_code from temp_delete_cst_versioning) and status = 1;
        update hospital_pre_exam set status =2 where apply_no in (select hospital_apply_no from temp_delete_cst_versioning) and status = 1;
        update hospital_reviewer set status =2 where apply_no in (select hospital_apply_no from temp_delete_cst_versioning) and status = 1;
        update hospital_review_cycle set status =2 where apply_no in (select hospital_apply_no from temp_delete_cst_versioning) and status = 1;
        update hospital_planned_distribution set status =2 where apply_no in (select hospital_apply_no from temp_delete_cst_versioning) and status = 1;
        update cst_domain set status =1 where version_id = (select DISTINCT aut_cs_id from temp_delete_cst_versioning) and status = 0;
        update cst_versioning set del_flag = 1 where id = #{id} and status != 1;
        drop table temp_delete_cst_versioning;
    </delete>

    <delete id="deleteCstVersioningByIds" parameterType="String">
        delete from cst_versioning where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateStatusDisableByOtherId" parameterType="long">
        update cst_versioning set  status = '0' where id != #{id} and status='1'
    </update>

    <update id="updateCstVersioningById">
        update cst_versioning set del_flag = 1 where id = #{id} and status != 1
    </update>

    <select id="selectCstVersioningByVersionId" parameterType="long" resultMap="CstVersioningResult">
        <include refid="selectCstVersioningVo"/>
        where version_id = #{versionId} and del_flag = 0
    </select>

    <select id="selectCstVersioningListByDomainId" parameterType="long" resultMap="CstVersioningResult">
        <include refid="selectCstVersioningVo"/>
        where version_id in (select distinct ( version_id ) from cst_certification_standards where domain_id in ('0',#{id}))
        and del_flag = 0 ORDER BY create_time desc
    </select>

</mapper>
