package com.thas.web.controller.common;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.thas.quartz.util.ScheduleUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.enums.BusinessType;
import com.thas.web.domain.MessageReceiveRecord;
import com.thas.web.service.IMessageReceiveRecordService;
import com.thas.common.utils.poi.ExcelUtil;
import com.thas.common.core.page.TableDataInfo;

/**
 * 消息接收记录Controller
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
@Api(value="MessageReceiveRecordController",tags = "消息接收记录Controller")
@RestController
@RequestMapping("/system/messageReceiveRecord")
public class MessageReceiveRecordController extends BaseController
{
    @Autowired
    private IMessageReceiveRecordService messageReceiveRecordService;

    /**
     * 查询消息接收记录列表
     */
//    @PreAuthorize("@ss.hasPermi('system:record:list')")
    @ApiOperation("查询消息接收记录列表")
    @GetMapping("/list")
    public TableDataInfo list(MessageReceiveRecord messageReceiveRecord)
    {
        startPage();
        List<MessageReceiveRecord> list = messageReceiveRecordService.selectMessageReceiveRecordList(messageReceiveRecord);
        return getDataTable(list);
    }

    /**
     * 导出消息接收记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:record:export')")
    @Log(title = "消息接收记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MessageReceiveRecord messageReceiveRecord)
    {
        List<MessageReceiveRecord> list = messageReceiveRecordService.selectMessageReceiveRecordList(messageReceiveRecord);
        ExcelUtil<MessageReceiveRecord> util = new ExcelUtil<MessageReceiveRecord>(MessageReceiveRecord.class);
        util.exportExcel(response, list, "消息接收记录数据");
    }

    /**
     * 获取消息接收记录详细信息
     */
//    @PreAuthorize("@ss.hasPermi('system:record:query')")
    @ApiOperation("获取消息接收记录详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(messageReceiveRecordService.selectMessageReceiveRecordById(id));
    }

    /**
     * 新增消息接收记录
     */
//    @PreAuthorize("@ss.hasPermi('system:record:add')")
    @ApiOperation("新增消息接收记录")
    @Log(title = "消息接收记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MessageReceiveRecord messageReceiveRecord)
    {
        return toAjax(messageReceiveRecordService.insertMessageReceiveRecord(messageReceiveRecord));
    }

    /**
     * 修改消息接收记录
     */
//    @PreAuthorize("@ss.hasPermi('system:record:edit')")
    @ApiOperation("修改消息接收记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键id", dataType = "Long", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "isRead", value = "是否已读（0未读 1已读）", dataType = "String", dataTypeClass = String.class)
    })
    @Log(title = "消息接收记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MessageReceiveRecord messageReceiveRecord)
    {
        return toAjax(messageReceiveRecordService.updateMessageReceiveRecord(messageReceiveRecord));
    }

    /**
     * 删除消息接收记录
     */
//    @PreAuthorize("@ss.hasPermi('system:record:remove')")
    @ApiOperation("删除消息接收记录")
    @Log(title = "消息接收记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(messageReceiveRecordService.deleteMessageReceiveRecordByIds(ids));
    }
}
