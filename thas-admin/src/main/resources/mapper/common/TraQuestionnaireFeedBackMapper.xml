<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.TraQuestionnaireFeedBackMapper">
    
    <resultMap type="TraQuestionnaireFeedBack" id="TraQuestionnaireFeedBackResult">
        <result property="feedBackId"    column="feed_back_id"    />
        <result property="jsonData"    column="json_data"    />
        <result property="questionnaireId"    column="questionnaire_id"    />
        <result property="userId"    column="user_id"    />
        <result property="assessorLeaderIds"    column="assessor_leader_ids"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="applyName"    column="apply_name"    />
        <result property="autCode"    column="aut_code"    />
        <result property="feedBackType"    column="feed_back_type"    />
        <result property="flag"    column="flag"    />
        <result property="creator"    column="creator"    />
        <result property="createTime"    column="create_time"    />
        <result property="updater"    column="updater"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTraQuestionnaireFeedBackVo">
        select feed_back_id, json_data, questionnaire_id, user_id, assessor_leader_ids, apply_no, apply_name, aut_code, feed_back_type, flag, creator, create_time, updater, update_time from tra_questionnaire_feed_back
    </sql>

    <select id="selectTraQuestionnaireFeedBackList" parameterType="TraQuestionnaireFeedBack" resultMap="TraQuestionnaireFeedBackResult">
        <include refid="selectTraQuestionnaireFeedBackVo"/>
        <where>  
           <!-- <if test="jsonData != null  and jsonData != ''"> and json_data = #{jsonData}</if>-->
            <if test="questionnaireId != null "> and questionnaire_id = #{questionnaireId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="assessorLeaderIds != null "> and assessor_leader_ids = #{assessorLeaderIds}</if>
            <if test="applyNo != null  and applyNo != ''"> and apply_no = #{applyNo}</if>
            <if test="applyName != null  and applyName != ''"> and apply_name like concat('%', #{applyName}, '%')</if>
            <if test="autCode != null  and autCode != ''"> and aut_code = #{autCode}</if>
            <if test="feedBackType != null "> and feed_back_type = #{feedBackType}</if>
            <if test="flag != null "> and flag = #{flag}</if>
            <if test="creator != null  and creator != ''"> and creator = #{creator}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
            <if test="autCodeList != null  and autCodeList.size > 0 ">
                and aut_code in
                <foreach collection="autCodeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectTraQuestionnaireFeedBackByFeedBackId" parameterType="Long" resultMap="TraQuestionnaireFeedBackResult">
        <include refid="selectTraQuestionnaireFeedBackVo"/>
        where feed_back_id = #{feedBackId}
    </select>
        
    <insert id="insertTraQuestionnaireFeedBack" parameterType="TraQuestionnaireFeedBack" useGeneratedKeys="true" keyProperty="feedBackId">
        insert into tra_questionnaire_feed_back
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="jsonData != null">json_data,</if>
            <if test="questionnaireId != null">questionnaire_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="assessorLeaderIds != null">assessor_leader_ids,</if>
            <if test="applyNo != null and applyNo != ''">apply_no,</if>
            <if test="applyName != null and applyName != ''">apply_name,</if>
            <if test="autCode != null  and autCode != ''">aut_code,</if>
            <if test="feedBackType != null">feed_back_type,</if>
            <if test="flag != null">flag,</if>
            <if test="creator != null">creator,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updater != null">updater,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="jsonData != null">#{jsonData},</if>
            <if test="questionnaireId != null">#{questionnaireId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="assessorLeaderIds != null">#{assessorLeaderIds},</if>
            <if test="applyNo != null and applyNo != ''">#{applyNo},</if>
            <if test="applyName != null and applyName != ''">#{applyName},</if>
            <if test="autCode != null  and autCode != ''">#{autCode},</if>
            <if test="feedBackType != null">#{feedBackType},</if>
            <if test="flag != null">#{flag},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updater != null">#{updater},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTraQuestionnaireFeedBack" parameterType="TraQuestionnaireFeedBack">
        update tra_questionnaire_feed_back
        <trim prefix="SET" suffixOverrides=",">
            <if test="jsonData != null">json_data = #{jsonData},</if>
            <if test="questionnaireId != null">questionnaire_id = #{questionnaireId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="assessorLeaderIds != null">assessor_leader_ids = #{assessorLeaderIds},</if>
            <if test="applyNo != null and applyNo != ''">apply_no = #{applyNo},</if>
            <if test="applyName != null and applyName != ''">apply_name = #{applyName},</if>
            <if test="autCode != null  and autCode != ''">aut_code = #{autCode},</if>
            <if test="feedBackType != null">feed_back_type = #{feedBackType},</if>
            <if test="flag != null">flag = #{flag},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where feed_back_id = #{feedBackId}
    </update>

    <delete id="deleteTraQuestionnaireFeedBackByFeedBackId" parameterType="Long">
        delete from tra_questionnaire_feed_back where feed_back_id = #{feedBackId}
    </delete>

    <delete id="deleteTraQuestionnaireFeedBackByFeedBackIds" parameterType="String">
        delete from tra_questionnaire_feed_back where feed_back_id in 
        <foreach item="feedBackId" collection="array" open="(" separator="," close=")">
            #{feedBackId}
        </foreach>
    </delete>
</mapper>