<template>
  <div v-loading="loading" class="hospital-account-application">
    <h1 class="headTitle">深圳卫健医院评审评价研究中心</h1>
    <el-steps :active="active" finish-status="success">
      <el-step title="基本信息"></el-step>
      <el-step title="详细信息"></el-step>
      <el-step title="临床服务"></el-step>
      <el-step title="评审信息"></el-step>
      <!-- <el-step title="法人信息"></el-step> -->
      <el-step title="提交审核"></el-step>
      <el-step title="通过审核"></el-step>
    </el-steps>

    <!-- <el-button @click="downloadApplyForm2">下载测试</el-button> -->

    <div class="hospital-account-application-content">
      <keep-alive>
        <component :initData="initData" :applyInfo="applyInfo" :key="active" :is="components[active]"
          :ref="components[active]" :refName="components[active]" :apply-no="applyNo" :sign-file-id="signFileId"
          @getDetail="getDetail" @componentReport="componentReport"></component>
      </keep-alive>
      <div class="action-buttons" style="margin-left: 150px">
        <el-button type="primary" v-if="active != 0 && active < 4 || (active == 4 && show)" @click="prev">上一步</el-button>
        <el-button @click="backToMain">返回</el-button>
        <el-button v-if="active < 4" type="primary" v-loading="submitting" @click="next">下一步</el-button>
        <el-button v-if="active === 0" type="primary" icon="el-icon-download" style="margin-right: 12px;"
          @click="downloadApplyForm">下载申请表模板</el-button>
      </div>

      <div v-if="active === 0 && !isModify" class="search">
        <p>已提交申请？输入医疗机构执业许可证登记号查询审核结果吧~</p>
        <el-input v-model.trim="practiceLicenseNo" placeholder="请输入医疗机构执业许可证登记号" @keyup.enter.native="handleQuery">
          <el-button slot="append" icon="el-icon-search" @click="handleQuery"></el-button>
        </el-input>
      </div>
      <!-- <el-empty v-if="showEmpty && active === 0" description="暂无结果"></el-empty> -->

      <el-dialog title="邮箱验证" :visible.sync="modifyEmailVerifyVisible" width="30%" :before-close="verifyEmailClose"
        :close-on-click-modal="false" :close-on-press-escape="false">
        <el-form ref="verifyEmailForm" :model="emailVerifyData" label-width="80px" :rules="emailVerifyRules" size="small">
          <el-form-item label="联系邮箱" prop="email">
            <el-input v-model.trim="emailVerifyData.email" placeholder="请输入联系邮箱"></el-input>
          </el-form-item>
          <el-form-item label="验证码" prop="emailCode">
            <el-input v-model.trim="emailVerifyData.emailCode" placeholder="请输入邮箱验证码" :maxlength="6"
              style="width: calc(100% - 130px)"></el-input>
            <el-button type="primary" style="margin-left: 10px;" :disabled="isCountingDown" @click="getEmailCode">{{isCountingDown ? `${countdown}秒后重新获取` : '获取验证码'}}</el-button>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="modifyEmailVerifyVisible = false">取 消</el-button>
          <el-button type="primary" @click="getHosInfo">确 定</el-button>
        </span>
      </el-dialog>

    </div>
  </div>
</template>

<script>
import request from "@/utils/request";

import BaseInfo from "./components/baseInfo.vue";
import DetailInfo from "./components/detailInfo.vue";
import Certificates from "./components/certificates.vue";
// import AuthorizerInfo from "./components/authorizerInfo.vue";
import Audit from "./components/audit.vue";
import Approved from "./components/approved.vue";
import Clinical from "./components/clinical.vue";
import { getToken } from '@/utils/auth'

export default {
  name: "HospitalApplication",

  components: {
    BaseInfo,
    DetailInfo,
    Clinical,
    Certificates,
    // AuthorizerInfo,
    Audit,
    Approved,
  },

  beforeRouteLeave(to, from, next) {
    let isSubmit = confirm(
      getToken() ? "请确定是否返回首页?" : "请确定是否返回到登录页面?"
    );
    if (isSubmit) {
      next(true);
    } else {
      next(false);
    }
  },

  data() {
    return {
      active: 0,
      components: [
        "BaseInfo",
        "DetailInfo",
        "Clinical",
        "Certificates",
        // "AuthorizerInfo",
        "Audit",
        "Approved",
      ],
      url: {
        create: '/file/share/bing-create',
      },

      data: {
        submitUrl: "/hospital/base/info/submit",
        // 基本信息
        hospitalBaseInfo: {},
        //
        certificateAbilityList: [],
        // 临床服务
        hospitalDepartmentList: [],
        // 被授权人
        hospitalAuthContact: {},
        // 法人
        hospitalLegalPerson: {},
      },
      submitted: false,
      sumbitCode: "",
      practiceLicenseNo: "",
      loading: false,
      showEmpty: false,
      applyNo: "",
      signFileId: "",
      isModify: false,
      queryHosInfoCode: '',
      initData: null,
      fileIdMap: {

      },

      applyInfo: {
        hospitalName: '',
        practiceLicenseNo: '',
        expectReviewDate: '',
      },

      submitting: false,
      show: true,

      modifyEmailVerifyVisible: false,
      emailVerifyData: {
        email: '',
        emailCode: '',
        verifyCode: '',
        practiceLicenseNo: ''
      },
      emailVerifyRules: {
        email: [{
          required: true,
          message: '请输入联系邮箱',
          trigger: ['blur']
        },
        {
          required: true,
          validator: (rule, value, callback) => this.emailValid(rule, value, callback),
          trigger: ["blur"],
        }],
        emailCode: [{
          required: true,
          message: '请输入邮箱验证码',
          trigger: ['blur']
        }]
      },
      regEmail: '',
      // 验证码倒计时
      timer: null,
      isCountingDown: false,
      countdown: 60
    };
  },

  created() {
    this.timer && clearInterval(this.timer);
    if (this.$route.path.includes('/re-application/')) {
      this.isModify = true
      this.queryHosInfoCode = this.$route.path.split('/').pop()
    }

    this.bus.$on('fileIdUpload', (key, ids, success) => {
      this.fileIdMap[key] = ids;
      if (this.applyNo && success) {
        this.shareCreate(ids, this.applyNo, 'hospital_data', '', '', ['hospital'], false)
      }
    })

    this.getBingConfigKey("reg.email").then(response => {
      this.regEmail = response.msg.trim();
    });
  },

  methods: {
    emailValid(rule, value, callback) {
      if (!value) callback('请输入邮箱')
      let regString = this.regEmail;
      if (!regString) callback();
      let reg = new RegExp(regString);
      if (!reg.test(value)) callback('请输入正确的邮箱')
      callback();
    },


    async next() {
      try {
        const value = await this.$refs[this.components[this.active]].getValue();
        if (!value) return;
        if ((this.active === 0 || this.active === 1)) {
          if (!this.isModify && this.active === 0) {
            const res = await request({
              url: "/hospital/bing/hospital/status",
              method: "get",
              params: { practiceLicenseNo: value.practiceLicenseNo },
            });
            if (res.code == 200) {
              if (res.data?.authStatus) {
                if (res.data?.authStatus == 3 || (res.data?.authStatus == 1 && res.data?.signConfirmStatus == 0)) {
                  this.emailVerifyData.practiceLicenseNo = value.practiceLicenseNo;
                  this.modifyEmailVerifyVisible = this.emailVerifyData.verifyCode ? false : true;
                  this.applyNo = res.data.applyNo
                  // this.active = 4
                } else {
                  this.$message({
                    type: "warning",
                    message: "请勿重复提交申请！",
                  });
                }
                return;
              }
            }
          }
          if (value.legal) {
            this.hospitalLegalPerson = {
              ...value.legal,
            };
            this.hospitalBaseInfo = {
              ...this.hospitalBaseInfo,
              ...value.data,
            };
          } else {
            this.hospitalBaseInfo = {
              ...this.hospitalBaseInfo,
              ...value,
            };
          }

          this.applyInfo['hospitalName'] = this.hospitalBaseInfo.hospitalName
          this.applyInfo['practiceLicenseNo'] = this.hospitalBaseInfo.practiceLicenseNo
        } else if (this.active == 2) {
          this.hospitalDepartmentList = [...value];
        } else if (this.active == 3) {
          const { certificates, first, expectReviewDate, beforeReviewerDate, beforeReviewerConclusion, ...data } = value;
          this.certificateAbilityList = [...certificates];
          this.hospitalBaseInfo = {
            ...this.hospitalBaseInfo,
            ...data,
            first,
            expectReviewDate,
            beforeReviewerDate,
            beforeReviewerConclusion
          };
          this.applyInfo['expectReviewDate'] = expectReviewDate
        }
        let index = this.active;
        if (++index >= 2) {
          const data = {
            hospitalBaseInfo: {
              ...this.hospitalBaseInfo,
              applyNo: this.initData?.hospitalBaseInfo?.applyNo || this.applyNo
            },
            hospitalDepartmentList: this.hospitalDepartmentList,
            certificateAbilityList: this.certificateAbilityList,
            hospitalAuthContact: this.hospitalAuthContact,
            hospitalLegalPerson: this.hospitalLegalPerson,
            stepFlag: String(index),
            signFlag: '0'
          };

          this.submitting = true;

          let submitFun = this.isModify ? this.modifyApplication : this.submitApplication
          submitFun(data).then((res) => {
            if (res.code == 200) {
              ++this.active;
              this.applyNo =
                res.data && res.data.applyNo ? res.data.applyNo : res.msg;
              this.submitted = true;
              this.emailVerifyData.verifyCode = res.data.verifyCode;
              this.emailVerifyData.email = data.hospitalBaseInfo.contactsEmail;
              this.emailVerifyData.practiceLicenseNo = data.hospitalBaseInfo.practiceLicenseNo;

              let fileList = Object.values(this.fileIdMap)
              let idList = [].concat.apply([], fileList)
              // 不管当前提交是不是管理员驳回的再次提交，在当前提交成功之后，都要转为二次提交 以防用户再次提交。
              this.isModify = true;

              this.shareCreate(idList, this.applyNo, 'hospital_data', '', '', ['hospital'], false)
            }
          }).catch((error) => {
            // console.log(error);
          }).finally(() => {
            this.submitting = false;
          });
        } else {
          ++this.active;
        }
        this.submitting = false;
      } catch (error) {
        this.submitting = false;
        // console.log(error);
      }
    },

    submitApplication(data) {
      let url = getToken() ? "/hospital/base/info/submit" : '/hospital/bing/base/info/submit'
      return request({
        url: url,
        method: "post",
        data,
      })
    },

    modifyApplication(data) {
      return request({
        url: '/hospital/bing/again/base/info/submit',
        method: "post",
        data: {
          ...data,
          tempHosInfo: {
            practiceLicenseNo: data.hospitalBaseInfo.practiceLicenseNo,
            queryHosInfoCode: this.queryHosInfoCode,
            ...this.emailVerifyData,
          }
        },
      })
    },

    prev() {
      if (this.active == 4) {
        this.$refs[this.components[this.active]].isDownload = false
      }
      if (this.active-- < 0) this.active = 0;
    },

    backToMain() {
      if (getToken()) {
        this.$router.push({
          path: "/index",
        });
      } else {
        this.$router.push({
          path: "/login",
        });
      }
    },

    getDetail(value) {
      if (this.isModify && this.queryHosInfoCode && value) {
        this.loading = true;
        request({
          url: "/hospital/bing/hospital/detail",
          method: "post",
          data: {
            practiceLicenseNo: value,
            queryHosInfoCode: this.queryHosInfoCode
          },
        })
          .then((res) => {
            this.loading = false;
            this.initData = res.data
            this.$refs[this.components[this.active]].init(this.initData);
            this.emailVerifyData.email = this.initData.hospitalBaseInfo.contactsEmail;
            this.emailVerifyData.practiceLicenseNo = this.initData.hospitalBaseInfo.practiceLicenseNo;
          })
          .catch((error) => {
            // console.log(error);
            this.loading = false;
          });

      }
    },

    handleQuery() {
      if (!this.practiceLicenseNo) {
        return;
      }
      this.loading = true;
      request({
        url: "/hospital/bing/hospital/status",
        method: "get",
        params: { practiceLicenseNo: this.practiceLicenseNo },
      })
        .then((res) => {
          this.loading = false;
          let value = 0;
          this.active = 5
          if (res.code == 200 && res.data && res.data.authStatus) {
            value = res.data.authStatus
          }
          this.$nextTick(() => {
            this.$refs[this.components[5]].setValue(value)
          })
        })
        .catch((error) => {
          // console.log(error);
          this.loading = false;
        });
    },

    downloadApplyForm() {
      let url = getToken() ? '/file/template/down/load' : '/bing/file/template/down/load'
      request({
        url: url,
        method: "post",
        responseType: "blob",
        params: { fileTemplate: "hos_info_template" },
      })
        .then((res) => {
          this.loading = false;
          const blob = new Blob([res], {
            type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          });
          saveAs(blob, "认证评审申请表.docx");
        })
        .catch((error) => {
          // console.log(error);
          this.loading = false;
        });
    },

    downloadApplyForm2() {
      let url = getToken() ? '/pdf/generate/ftlToPdf' : '/pdf/generate/bing/ftlToPdf'
      request({
        url: 'http://101.33.233.156:1024/prod-api' + url,
        method: "post",
        responseType: "blob",
        data: {
          ftlTemplateCode: "hos_info_gen",
          otherParam: { applyNo: "202209131748116b6876" },
        },
      })
        .then((res) => {
          this.loading = false;
          const blob = new Blob([res], { type: "application/pdf" });
          saveAs(blob, "CH反馈认证申请表Application form draft.pdf");
        })
        .catch((error) => {
          // console.log(error);
          this.loading = false;
        });
    },

    componentReport(data) {
      if (this.active == 4) {
        this.show = false;
      }
    },

    verifyEmailClose() {
      this.modifyEmailVerifyVisible = false;
    },

    startCountdown() {
      this.isCountingDown = true;
      this.countdown = 60;

      this.timer = setInterval(() => {
        this.countdown--;

        if (this.countdown <= 0) {
          clearInterval(this.timer);
          this.isCountingDown = false;
        }
      }, 1000);
    },

    getEmailCode() {
      this.$refs.verifyEmailForm.validateField('email', msg => {
        if (msg) return;
        request({
          url: '/hospital/bing/sendEmailCode',
          method: 'post',
          data: {
            toEmailNo: this.emailVerifyData.email,
            emailTitle: '修改医院申请',
            applyNo: this.applyNo
          }
        }).then(res => {
          if (res.code === 200) {
            // 发送验证码
            this.startCountdown();
            this.$message({
              type: 'success',
              message: '发送成功'
            })
          }
        })
      })
    },

    // 页面刷新后，输入邮箱和验证码获取申请的数据
    getHosInfo() {
      this.$refs.verifyEmailForm.validate(flag => {
        if (!flag) return;

        request({
          url: "/hospital/bing/hospital/detail",
          method: "post",
          data: {
           ...this.emailVerifyData
          },
        })
          .then((res) => {
            this.isModify = true;
            this.loading = false;
            this.initData = res.data
            this.$refs[this.components[this.active]].init(this.initData);

            this.modifyEmailVerifyVisible = false;
          })
          .catch((error) => {
            // console.log(error);
            this.loading = false;
          });
      })

    }
  },
};
</script>

<style lang="scss" scoped>
.hospital-account-application {
  height: 100%;

  .headTitle {
    background-color: #3d81f4;
    color: white;
    margin: 0;
    padding: 20px 0;
    font-size: 18px;
    font-weight: bold;
    text-indent: 100px;
    letter-spacing: 1px;
  }

  ::v-deep .el-steps {
    width: 70%;
    margin: 40px auto;

    .el-step__icon {
      width: 40px;
      height: 40px;
    }

    .el-step__head.is-process {
      color: #357cf4;
      border-color: #357cf4;

      &+.el-step__main .el-step__title {
        color: #357cf4;
      }
    }

    .el-step__head.is-success {
      color: #357cf4;
      border-color: #357cf4;

      &+.el-step__main .el-step__title {
        color: #357cf4;
      }

      .el-icon-check {
        font-weight: bold;
        font-size: 20px;
        color: white;
      }

      .el-step__icon.is-text {
        background-color: #357cf4;
      }
    }

    .el-step.is-horizontal .el-step__line {
      top: 19px;
    }
  }

  .hospital-account-application-content {
    width: 85%;
    margin: 40px auto;
    padding: 24px 0;
    padding-top: 0;
  }

  .action-buttons {
    margin-top: 24px;
  }

  .search {
    width: 60%;
    margin-top: 20px;
  }
}
</style>
