<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.CertificateAbilityMapper">
    
    <resultMap type="com.thas.web.domain.CertificateAbility" id="CertificateAbilityResult">
        <result property="id"    column="id"    />
        <result property="processCode"    column="process_code"    />
        <result property="abilityDictType"    column="ability_dict_type"    />
        <result property="abilityDictValue"    column="ability_dict_value"    />
        <result property="abilityName"    column="ability_name"    />
        <result property="fileId"    column="file_id"    />
        <result property="expireDate"    column="expire_date"    />
        <result property="status"    column="status"    />
        <result property="creator"    column="creator"    />
        <result property="createTime"    column="create_time"    />
        <result property="updater"    column="updater"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCertificateAbilityVo">
        select id, process_code, ability_dict_type, ability_dict_value, ability_name, file_id, expire_date, status, creator, create_time, updater, update_time from certificate_ability
    </sql>

    <select id="selectCertificateAbilityList" parameterType="CertificateAbility" resultMap="CertificateAbilityResult">
        <include refid="selectCertificateAbilityVo"/>
        <where>  
            <if test="processCode != null  and processCode != ''"> and process_code = #{processCode}</if>
            <if test="abilityDictType != null  and abilityDictType != ''"> and ability_dict_type = #{abilityDictType}</if>
            <if test="abilityDictValue != null  and abilityDictValue != ''"> and ability_dict_value = #{abilityDictValue}</if>
            <if test="abilityName != null  and abilityName != ''"> and ability_name like concat('%', #{abilityName}, '%')</if>
            <if test="fileId != null "> and file_id = #{fileId}</if>
            <if test="expireDate != null  and expireDate != ''"> and expire_date = #{expireDate}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="creator != null  and creator != ''"> and creator = #{creator}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
        </where>
    </select>
    
    <select id="selectCertificateAbilityById" parameterType="Long" resultMap="CertificateAbilityResult">
        <include refid="selectCertificateAbilityVo"/>
        where id = #{id}
    </select>

    <select id="selectCertificateAbilityByProcessCode" parameterType="String" resultMap="CertificateAbilityResult">
        <include refid="selectCertificateAbilityVo"/>
        where process_code = #{processCode} and status = 1
    </select>
        
    <insert id="insertCertificateAbility" parameterType="CertificateAbility" useGeneratedKeys="true" keyProperty="id">
        insert into certificate_ability
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="processCode != null and processCode != ''">process_code,</if>
            <if test="abilityDictType != null and abilityDictType != ''">ability_dict_type,</if>
            <if test="abilityDictValue != null and abilityDictValue != ''">ability_dict_value,</if>
            <if test="abilityName != null and abilityName != ''">ability_name,</if>
            <if test="fileId != null">file_id,</if>
            <if test="expireDate != null and expireDate != ''">expire_date,</if>
            <if test="status != null">status,</if>
            <if test="creator != null and creator != ''">creator,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updater != null and updater != ''">updater,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="processCode != null and processCode != ''">#{processCode},</if>
            <if test="abilityDictType != null and abilityDictType != ''">#{abilityDictType},</if>
            <if test="abilityDictValue != null and abilityDictValue != ''">#{abilityDictValue},</if>
            <if test="abilityName != null and abilityName != ''">#{abilityName},</if>
            <if test="fileId != null">#{fileId},</if>
            <if test="expireDate != null and expireDate != ''">#{expireDate},</if>
            <if test="status != null">#{status},</if>
            <if test="creator != null and creator != ''">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updater != null and updater != ''">#{updater},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCertificateAbility" parameterType="CertificateAbility">
        update certificate_ability
        <trim prefix="SET" suffixOverrides=",">
            <if test="processCode != null and processCode != ''">process_code = #{processCode},</if>
            <if test="abilityDictType != null and abilityDictType != ''">ability_dict_type = #{abilityDictType},</if>
            <if test="abilityDictValue != null and abilityDictValue != ''">ability_dict_value = #{abilityDictValue},</if>
            <if test="abilityName != null and abilityName != ''">ability_name = #{abilityName},</if>
            <if test="fileId != null">file_id = #{fileId},</if>
            <if test="expireDate != null and expireDate != ''">expire_date = #{expireDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="creator != null and creator != ''">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateAccountId" parameterType="String">
        update certificate_ability set process_code = #{userId} where process_code = #{accountId} and status = 1
    </update>

    <delete id="deleteCertificateAbilityById" parameterType="Long">
        delete from certificate_ability where id = #{id}
    </delete>

    <delete id="deleteCertificateAbilityByProcessCode" parameterType="String">
        delete from certificate_ability where process_code = #{applyNo}
    </delete>

    <delete id="deleteCertificateAbilityByIds" parameterType="String">
        delete from certificate_ability where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>