<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.HospitalDepartmentMapper">

    <resultMap type="com.thas.web.domain.HospitalDepartment" id="HospitalDepartmentResult">
        <result property="id"    column="id"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="departmentDictType"    column="department_dict_type"    />
        <result property="departmentDictValue"    column="department_dict_value"    />
        <result property="departmentName"    column="department_name"    />
        <result property="outpatientService"    column="outpatient_service"    />
        <result property="inpatientService"    column="inpatient_service"    />
        <result property="departmentDesc"    column="department_desc"    />
        <result property="status"    column="status"    />
        <result property="creator"    column="creator"    />
        <result property="createTime"    column="create_time"    />
        <result property="updater"    column="updater"    />
        <result property="updateTime"    column="update_time"    />
        <result property="hospitalName"    column="hospital_name"    />
    </resultMap>

    <sql id="selectHospitalDepartmentVo">
        select id, apply_no, department_dict_type, department_dict_value, department_name, outpatient_service, inpatient_service, department_desc, status, creator, create_time, updater, update_time from hospital_department
    </sql>

    <select id="selectHospitalDepartmentList" parameterType="HospitalDepartment" resultMap="HospitalDepartmentResult">
        <include refid="selectHospitalDepartmentVo"/>
        <where>
            <if test="applyNo != null  and applyNo != ''"> and apply_no = #{applyNo}</if>
            <if test="departmentDictType != null  and departmentDictType != ''"> and department_dict_type = #{departmentDictType}</if>
            <if test="departmentDictValue != null  and departmentDictValue != ''"> and department_dict_value = #{departmentDictValue}</if>
            <if test="departmentName != null  and departmentName != ''"> and department_name like concat('%', #{departmentName}, '%')</if>
            <if test="outpatientService != null "> and outpatient_service = #{outpatientService}</if>
            <if test="inpatientService != null "> and inpatient_service = #{inpatientService}</if>
            <if test="departmentDesc != null  and departmentDesc != ''"> and department_desc = #{departmentDesc}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="creator != null  and creator != ''"> and creator = #{creator}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
        </where>
    </select>

    <select id="selectHospitalDepartmentById" parameterType="Long" resultMap="HospitalDepartmentResult">
        <include refid="selectHospitalDepartmentVo"/>
        where id = #{id}
    </select>

    <select id="selectHospitalDepartmentByApplyNo" parameterType="String" resultMap="HospitalDepartmentResult">
        SELECT hd.id, hd.apply_no, hd.department_dict_type, hd.department_dict_value, hd.department_name,
        hd.outpatient_service, hd.inpatient_service, hd.department_desc, hd.status, hd.creator, hd.create_time, hd.updater,
        hd.update_time, hbi.hospital_name from hospital_department hd
        LEFT JOIN hospital_base_info hbi ON hd.apply_no = hbi.apply_no
        where hd.apply_no = #{applyNo} and hd.status = 1
    </select>



    <insert id="insertHospitalDepartment" parameterType="HospitalDepartment">
        insert into hospital_department
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="applyNo != null and applyNo != ''">apply_no,</if>
            <if test="departmentDictType != null and departmentDictType != ''">department_dict_type,</if>
            <if test="departmentDictValue != null and departmentDictValue != ''">department_dict_value,</if>
            <if test="departmentName != null and departmentName != ''">department_name,</if>
            <if test="outpatientService != null">outpatient_service,</if>
            <if test="inpatientService != null">inpatient_service,</if>
            <if test="departmentDesc != null and departmentDesc != ''">department_desc,</if>
            <if test="status != null">status,</if>
            <if test="creator != null and creator != ''">creator,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updater != null and updater != ''">updater,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="applyNo != null and applyNo != ''">#{applyNo},</if>
            <if test="departmentDictType != null and departmentDictType != ''">#{departmentDictType},</if>
            <if test="departmentDictValue != null and departmentDictValue != ''">#{departmentDictValue},</if>
            <if test="departmentName != null and departmentName != ''">#{departmentName},</if>
            <if test="outpatientService != null">#{outpatientService},</if>
            <if test="inpatientService != null">#{inpatientService},</if>
            <if test="departmentDesc != null and departmentDesc != ''">#{departmentDesc},</if>
            <if test="status != null">#{status},</if>
            <if test="creator != null and creator != ''">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updater != null and updater != ''">#{updater},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateHospitalDepartment" parameterType="HospitalDepartment">
        update hospital_department
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">apply_no = #{applyNo},</if>
            <if test="departmentDictType != null and departmentDictType != ''">department_dict_type = #{departmentDictType},</if>
            <if test="departmentDictValue != null and departmentDictValue != ''">department_dict_value = #{departmentDictValue},</if>
            <if test="departmentName != null and departmentName != ''">department_name = #{departmentName},</if>
            <if test="outpatientService != null">outpatient_service = #{outpatientService},</if>
            <if test="inpatientService != null">inpatient_service = #{inpatientService},</if>
            <if test="departmentDesc != null and departmentDesc != ''">department_desc = #{departmentDesc},</if>
            <if test="status != null">status = #{status},</if>
            <if test="creator != null and creator != ''">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHospitalDepartmentById" parameterType="Long">
        delete from hospital_department where id = #{id}
    </delete>

    <delete id="deleteHospitalDepartmentByIds" parameterType="String">
        delete from hospital_department where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectHospitalExclusiveDepartmentByApplyNo" parameterType="string" resultMap="HospitalDepartmentResult">
        select id,
               apply_no,
               department_service department_dict_type,
               department_sort    department_dict_value,
               department_name,
               outpatient_service,
               inpatient_service,
               department_desc,
               status,
               creator,
               create_time,
               updater,
               update_time
        from hos_exclusive_department
        where apply_no = #{applyNo}
    </select>

    <insert id="insertHospitalExclusiveDepartment" parameterType="com.thas.web.domain.HospitalDepartment">
        insert into hos_exclusive_department
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">apply_no,</if>
            <if test="departmentDictType != null and departmentDictType != ''">department_service,</if>
            <if test="departmentDictValue != null and departmentDictValue != ''">department_sort,</if>
            <if test="departmentName != null and departmentName != ''">department_name,</if>
            <if test="outpatientService != null">outpatient_service,</if>
            <if test="inpatientService != null">inpatient_service,</if>
            <if test="departmentDesc != null and departmentDesc != ''">department_desc,</if>
            <if test="status != null">status,</if>
            <if test="creator != null and creator != ''">creator,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updater != null and updater != ''">updater,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">#{applyNo},</if>
            <if test="departmentDictType != null and departmentDictType != ''">#{departmentDictType},</if>
            <if test="departmentDictValue != null and departmentDictValue != ''">#{departmentDictValue},</if>
            <if test="departmentName != null and departmentName != ''">#{departmentName},</if>
            <if test="outpatientService != null">#{outpatientService},</if>
            <if test="inpatientService != null">#{inpatientService},</if>
            <if test="departmentDesc != null and departmentDesc != ''">#{departmentDesc},</if>
            <if test="status != null">#{status},</if>
            <if test="creator != null and creator != ''">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updater != null and updater != ''">#{updater},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <select id="selectClinicalServicesByApplyNos" parameterType="String" resultMap="HospitalDepartmentResult">
        SELECT hd.id, hd.apply_no, hd.department_dict_type, hd.department_dict_value, hd.department_name,
        hd.outpatient_service, hd.inpatient_service, hd.department_desc, hd.status, hd.creator, hd.create_time, hd.updater,
        hd.update_time, hbi.hospital_name from hospital_department hd
        LEFT JOIN hospital_base_info hbi ON hd.apply_no = hbi.apply_no
        WHERE hd.apply_no IN
        <foreach item="applyNos" collection="list" open="(" separator="," close=")">
            #{applyNos}
        </foreach>
        AND hd.department_dict_type = 'clinical_service'
        AND hd.status = 1
    </select>
    <select id="selectMedicalServiceByApplyNos" parameterType="String" resultMap="HospitalDepartmentResult">
        SELECT hd.id, hd.apply_no, hd.department_dict_type, hd.department_dict_value, hd.department_name,
        hd.outpatient_service, hd.inpatient_service, hd.department_desc, hd.status, hd.creator, hd.create_time, hd.updater,
        hd.update_time, hbi.hospital_name from hospital_department hd
        LEFT JOIN hospital_base_info hbi ON hd.apply_no = hbi.apply_no
        WHERE hd.apply_no IN
        <foreach item="applyNos" collection="list" open="(" separator="," close=")">
            #{applyNos}
        </foreach>
        AND hd.department_dict_type = 'medical_service'
        AND hd.status = 1
    </select>

    <delete id="deleteHospitalDepartmentByApplyNo" parameterType="String">
        delete from hospital_department where apply_no = #{applyNo}
    </delete>

    <delete id="deleteHospitalExclusiveDepartmentByApplyNo" parameterType="String">
        delete from hos_exclusive_department where apply_no = #{applyNo}
    </delete>

</mapper>
