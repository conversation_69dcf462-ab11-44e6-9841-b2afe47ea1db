<template>
  <el-card v-loading="loading" class="answer-list">
    <el-row :gutter="24">
      <el-col :span="24" :xs="24">
        <el-form ref="queryForm" :model="queryData" size="small" label-width="80px" inline>
          <el-form-item label="考卷名称" prop="examPaperId">
            <el-select v-model.trim="queryData.examPaperId" placeholder="请选择考卷" @change="paperChange" clearable filterable>
              <el-option v-for="(item, index) in paperOptions" :key="index" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model.trim="queryData.status" placeholder="请选择状态" @change="statusChange" clearable filterable>
              <el-option v-for="statusItem in answerStatusOptions" :key="statusItem.dictValue" :value="statusItem.dictValue" :label="statusItem.dictLabel"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="search">查 询</el-button>
            <el-button type="primary" size="small" @click="resetForm">重 置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-row :gutter="24" style="margin-top: 24px">
      <el-col :span="24">
        <el-table :data="dataSource" border>
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="toDetail(scope.row)">答卷详情</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" label="序号" type="index" :index="indexMethod"></el-table-column>
          <el-table-column align="center" label="答题人" prop="createId"></el-table-column>
          <el-table-column align="center" label="试卷" prop="examPaperName"></el-table-column>
          <el-table-column align="center" label="学习资源" prop="learnResourceName"></el-table-column>
          <el-table-column align="center" label="得分" prop="score"></el-table-column>
          <el-table-column align="center" label="状态" prop="status">
            <template slot-scope="scope">
              {{ status(`${scope.row.status}`) }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="提交时间" prop="createTime"></el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query" />
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
import request from '@/utils/request'
export default {
  name: "AnswerList",
  components: {},
  data() {
    return {
      dataSource: [],
      queryData: {
        examPaperId: null,
        status: null,
        pageSize: 10,
        pageNum: 1
      },
      total: 0,
      answerStatusOptions: [],
      addForm: {
        name: '',
        content: '',
        type: null,
        sendEmail: null,
        status: null,
        role: null
      },
      props: { multiple: true },
      paperOptions: [],
      loading: false
    }
  },
  computed: {
    status() {
      return (value) => {
        const status = this.answerStatusOptions.find(
          (status) => status.dictValue == value
        )
        return status ? status.dictLabel : value
      }
    },
    course() {
      return (value) => {
        const type = this.noticeTemplateTypeOptions.find(
          (type) => type.dictValue == value
        )
        return type ? type.dictLabel : value
      }
    }
  },
  created() {
    this.initDicts()
    this.query()
  },
  methods: {
    initDicts() {
      this.getDicts("answer_status").then((response) => {
        this.answerStatusOptions = response.data
      })
      request({
        url: '/system/paper/list',
        method: 'get'
      }).then((res) => {
        if (res.code != 200) return
        this.paperOptions = res.rows.map((item) => {
          return {
            value: item.id,
            label: item.paperName
          }
        })
      })
    },
    search() {
      this.queryData.pageNum = 1
      this.query()
    },
    query() {
      this.loading = true
      request({
        url: '/system/sheet/list',
        method: "get",
        params: this.queryData
      }).then((res) => {
        this.loading = false
        if (res.code != 200) return
        for (let index = 0; index < res.rows.length; index++) {
          res.rows[index].number = index
        }
        this.dataSource = res.rows
        this.total = res.total
      }).catch((error) => {
        // console.log(error)
        this.loading = false
      })
    },
    resetForm() {
      this.$refs["queryForm"].resetFields();
      this.query();
    },
    toDetail(data) {
      // console.log('跳转到答卷详情')
      this.$router.push({
        path: '/learningMaterials/answerDetail',
        query: {
          id: data.id,
          from: this.$route.path
        }
      })
    },
    paperChange(value) {
      // console.log(value)
      this.query()
    },
    statusChange(value) {
      // console.log(value)
      this.query()
    },
    indexMethod(index) {
      return (this.queryData.pageNum - 1) * this.queryData.pageSize + index + 1
    }
  }
}
</script>

<style lang='scss' scoped>
.answer-list {
  padding: 24px;
  ::v-deep .el-dialog {
    .el-dialog__header {
      background-color: #3c81f5;
      padding-top: 10px;
      .el-dialog__title {
        color: white;
      }
      .el-dialog__headerbtn {
        top: 15px;
        .el-dialog__close {
          color: white;
        }
      }
    }
    .el-dialog__body {
      padding-bottom: 0;
      & > span {
        color: black;
      }
    }
  }
}
</style>
