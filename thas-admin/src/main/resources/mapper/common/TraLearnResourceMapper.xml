<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.TraLearnResourceMapper">

    <resultMap type="TraLearnResource" id="TraLearnResourceResult">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="type" column="type"/>
        <result property="category" column="category"/>
        <result property="resourceCoverAddress" column="resource_cover_address"/>
        <result property="resourceAddress" column="resource_address"/>
        <result property="contentDescription" column="content_description"/>
        <result property="status" column="status"/>
        <result property="learnNum" column="learn_num"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateId" column="update_id"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTraLearnResourceVo">
        select id,
               title,
               type,
               category,
               resource_cover_address,
               resource_address,
               content_description,
               status,
               learn_num,
               create_id,
               create_time,
               update_id,
               update_time
        from tra_learn_resource
    </sql>

    <select id="selectTraLearnResourceList" parameterType="TraLearnResource" resultMap="TraLearnResourceResult">
        <include refid="selectTraLearnResourceVo"/>
        <where>
            <if test="title != null  and title != ''">and title like concat('%', #{title}, '%')</if>
            <if test="type != null ">and type = #{type}</if>
            <if test="category != null">and category = #{category}</if>
            <if test="resourceCoverAddress != null  and resourceCoverAddress != ''">and resource_cover_address =
                #{resourceCoverAddress}
            </if>
            <if test="resourceAddress != null  and resourceAddress != ''">and resource_address = #{resourceAddress}</if>
            <if test="contentDescription != null  and contentDescription != ''">and content_description =
                #{contentDescription}
            </if>
            <if test="status != null ">and status = #{status}</if>
            <if test="learnNum != null ">and learn_num = #{learnNum}</if>
            <if test="createId != null  and createId != ''">and create_id = #{createId}</if>
            <if test="updateId != null  and updateId != ''">and update_id = #{updateId}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectTraLearnResourceById" parameterType="Long" resultMap="TraLearnResourceResult">
        select *
        from tra_learn_resource
        where id = #{id}
    </select>

    <insert id="insertTraLearnResource" parameterType="TraLearnResource" useGeneratedKeys="true" keyProperty="id">
        insert into tra_learn_resource
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="type != null">type,</if>
            <if test="category != null">category,</if>
            <if test="resourceCoverAddress != null and resourceCoverAddress != ''">resource_cover_address,</if>
            <if test="resourceAddress != null">resource_address,</if>
            <if test="contentDescription != null and contentDescription != ''">content_description,</if>
            <if test="status != null">status,</if>
            <if test="learnNum != null">learn_num,</if>
            <if test="createId != null and createId != ''">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null and updateId != ''">update_id,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="type != null">#{type},</if>
            <if test="category != null">#{category},</if>
            <if test="resourceCoverAddress != null and resourceCoverAddress != ''">#{resourceCoverAddress},</if>
            <if test="resourceAddress != null">#{resourceAddress},</if>
            <if test="contentDescription != null and contentDescription != ''">#{contentDescription},</if>
            <if test="status != null">#{status},</if>
            <if test="learnNum != null">#{learnNum},</if>
            <if test="createId != null and createId != ''">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null and updateId != ''">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateTraLearnResource" parameterType="TraLearnResource">
        update tra_learn_resource
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="type != null">type = #{type},</if>
            <if test="category != null">category = #{category},</if>
            <if test="resourceCoverAddress != null and resourceCoverAddress != ''">resource_cover_address =
                #{resourceCoverAddress},
            </if>
            <if test="resourceAddress != null">resource_address = #{resourceAddress},</if>
            <if test="contentDescription != null and contentDescription != ''">content_description =
                #{contentDescription},
            </if>
            <if test="status != null">status = #{status},</if>
            <if test="learnNum != null">learn_num = #{learnNum},</if>
            <if test="createId != null and createId != ''">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null and updateId != ''">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="learnNumPlus">
        update tra_learn_resource
        set learn_num = learn_num + 1
        where id = #{id};
    </update>

    <delete id="deleteTraLearnResourceById" parameterType="Long">
        delete
        from tra_learn_resource
        where id = #{id}
    </delete>

    <delete id="deleteTraLearnResourceByIds" parameterType="String">
        delete from tra_learn_resource where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>