<template>
  <div class='conflict-of-interest-list'>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-button type="primary" @click="query" style="margin-bottom:12px;">刷新</el-button>
      </el-col>
    </el-row>
    <el-table :data="dataSource" border stripe>
      <el-table-column type="index" label="序号"></el-table-column>
      <el-table-column label="医院名称" prop="hospitalName"></el-table-column>
      <el-table-column label="利益冲突表">
        <template slot-scope="scope">
          <a :href="scope.row.url" target="_blank">{{scope.row.fileName}}</a>
        </template>
      </el-table-column>
      <el-table-column label="状态">
        <template slot-scope="scope">
          {{scope.row.interestFileId ? '已提交': '未提交'}}
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button v-if="!scope.row.interestFileId" type="text" @click="toSubmit(scope.row)">填表</el-button>
          <!-- <span v-else>--</span> -->
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query" />

  </div>
</template>

<script>
import request from "@/utils/request";

export default {
  name: 'ConflictOfInterestList',
  data() {
    return {
      url: {
        list: '/reviewer/interest/list'
      },
      dataSource: [],
      queryData: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.query();
  },

  methods: {
    query() {
      request({
        url: this.url.list,
        method: 'post',
        data: this.queryData
      }).then(res => {
        this.dataSource = res.rows;
      })
    },

    toSubmit({ accountId, applyNo, hospitalName, id }) {
      this.$router.push({
        path: "/conflictOfInterest/detail",
        query: {
          accountId,
          applyNo,
          hospitalName,
          id
        },
      });
    },
  }
}

</script>
<style lang='scss' scoped>
.conflict-of-interest-list {
  background-color: transparent !important;
}
</style>