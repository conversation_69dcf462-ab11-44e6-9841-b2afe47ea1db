package com.thas.web.controller.common;

import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.page.TableDataInfo;
import com.thas.common.enums.BusinessType;
import com.thas.web.domain.TraMessage;
import com.thas.web.service.ITraMessageService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 留言管理Controller
 *
 * <AUTHOR>
 * @date 2022-01-20
 */
@RestController
@RequestMapping("/system/message")
public class TraMessageController extends BaseController {
    @Autowired
    private ITraMessageService traMessageService;

    /**
     * 查询留言管理列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TraMessage traMessage) {
        startPage();
        List<TraMessage> list = traMessageService.selectTraMessageList(traMessage);
        return getDataTable(list);
    }

    /**
     * 新增留言管理
     */
    @Log(title = "新增留言管理", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(httpMethod = "POST", value = "新增留言管理")
    public AjaxResult add(@RequestBody TraMessage traMessage) {
        traMessage.setCreateId(getUsername());
        traMessage.setUpdateId(getUsername());
        return toAjax(traMessageService.insertTraMessage(traMessage));
    }

    /**
     * 删除留言管理
     */
    @Log(title = "删除留言管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(httpMethod = "DELETE", value = "删除留言管理")
    public AjaxResult remove(@ApiParam(name = "留言id", value = "ids") @PathVariable Long[] ids) {
        return toAjax(traMessageService.deleteTraMessageByIds(ids));
    }

    /**
     * 点赞/取消点赞
     */
    @Log(title = "点赞", businessType = BusinessType.INSERT)
    @GetMapping("/like")
    @ApiOperation(httpMethod = "GET", value = "点赞/取消点赞")
    public AjaxResult doLike(@ApiParam(name = "留言id", value = "messageId") @RequestParam("messageId") Long messageId, @ApiParam(name = "操作类型(ADD=点赞, SUB=取消点赞)", value = "type") @RequestParam("type") String type) {
        return toAjax(traMessageService.doLike(messageId, type));
    }
}
