import Vue from "vue";

import Cookies from "js-cookie";

import Element from "element-ui";
import SimpleUploader from "vue-simple-uploader";
import "./assets/styles/element-variables.scss";

import "@/assets/styles/index.scss"; // global css
import "@/assets/styles/ruoyi.scss"; // ruoyi css
import App from "./App";
import store from "./store";
import router from "./router";
import directive from "./directive"; // directive
import plugins from "./plugins"; // plugins
import { download, shareCreate, shareDelete } from "@/utils/request";
import bus from "./bus/bus";

import {
  getStandard,
  getEnabledVersionId,
  getClausesDetail,
} from "@/api/system/standard";
import "./assets/icons"; // icon
import "./permission"; // permission control
import { getDicts, getDictList } from "@/api/system/dict/data";
import { getConfigKey, getBingConfigKey } from "@/api/system/config";
import {
  parseTime,
  resetForm,
  addDateRange,
  selectDictLabel,
  selectDictLabels,
  handleTree,
} from "@/utils/ruoyi";
// 分页组件
import Pagination from "@/components/Pagination";
// 自定义表格工具组件
import RightToolbar from "@/components/RightToolbar";
// 富文本组件
import Editor from "@/components/Editor";
// 文件上传组件
import FileUpload from "@/components/FileUpload";
// 图片上传组件
import ImageUpload from "@/components/ImageUpload";
// 图片预览组件
import ImagePreview from "@/components/ImagePreview";
// 字典标签组件
import DictTag from "@/components/DictTag";
import DictSpan from "@/components/DetailMessage/dictSpan.vue";

// 头部标签组件
import VueMeta from "vue-meta";
// 字典数据组件
import DictData from "@/components/DictData";
// 详情信息组件
import DetailMessage from "@/components/DetailMessage";
// 上传组件
import Upload from "@/components/Upload";
// 评审款项组件
import ClauseItem from "@/components/ClauseItem";
// 试题的创建
import EditQuestion from "@/components/TextQuestion/EditQuestion.vue";
// 试题的使用
import AnswerQuestion from "@/components/TextQuestion/AnswerQuestion.vue";
//
import StandardSelect from "@/components/StandardSelect";
import StandardCard from "@/components/StandardCard";

// 分片上传、秒传、断点续传
import ShardUploader from "@/components/ShardUploader";

// 枚举
// 认证自评审核状态枚举
import AutSaAudStatusEnum from "@/enum/autSaAudStatusEnum";
import AutSaAudSubmitTypeEnum from "@/enum/autSaAudSubmitTypeEnum";
import AutSaAudCurrentStatusEnum from "@/enum/autSaAudCurrentStatusEnum";

import EvaluationBase from "@/components/EvaluationBase";

// 全局方法挂载
Vue.prototype.getDicts = getDicts;
Vue.prototype.getDictList = getDictList;
Vue.prototype.getStandard = getStandard;
Vue.prototype.getClausesDetail = getClausesDetail;
Vue.prototype.getEnabledVersionId = getEnabledVersionId;
Vue.prototype.getConfigKey = getConfigKey;
Vue.prototype.getBingConfigKey = getBingConfigKey;
Vue.prototype.parseTime = parseTime;
Vue.prototype.resetForm = resetForm;
Vue.prototype.addDateRange = addDateRange;
Vue.prototype.selectDictLabel = selectDictLabel;
Vue.prototype.selectDictLabels = selectDictLabels;
Vue.prototype.download = download;
Vue.prototype.handleTree = handleTree;

Vue.prototype.AutSaAudStatusEnum = AutSaAudStatusEnum;
Vue.prototype.AutSaAudSubmitTypeEnum = AutSaAudSubmitTypeEnum;
Vue.prototype.AutSaAudCurrentStatusEnum = AutSaAudCurrentStatusEnum;

Vue.prototype.shareCreate = shareCreate;
Vue.prototype.shareDelete = shareDelete;

// 全局组件挂载
Vue.component("DictTag", DictTag);
Vue.component("Pagination", Pagination);
Vue.component("RightToolbar", RightToolbar);
Vue.component("Editor", Editor);
Vue.component("FileUpload", FileUpload);
Vue.component("ImageUpload", ImageUpload);
Vue.component("ImagePreview", ImagePreview);
Vue.component("ClauseItem", ClauseItem);
Vue.component("EditQuestion", EditQuestion);
Vue.component("AnswerQuestion", AnswerQuestion);
Vue.component("Upload", Upload);
Vue.component("DetailMessage", DetailMessage);
Vue.component("DictSpan", DictSpan);
Vue.component("StandardSelect", StandardSelect);
Vue.component("StandardCard", StandardCard);
Vue.component("ShardUploader", ShardUploader);
Vue.component("EvaluationBase", EvaluationBase);

Vue.use(directive);
Vue.use(plugins);
Vue.use(VueMeta);
Vue.use(bus);
Vue.use(SimpleUploader);
DictData.install();

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
  size: Cookies.get("size") || "medium", // set element-ui default size
});

Vue.config.productionTip = false;

const vue = new Vue({
  el: "#app",
  router,
  store,
  render: (h) => h(App),
});

window.vue = vue;

// console.log重写
/**
 *
 * 这段代码创建了一个自执行函数，并在其中重写了 console.log。在重写的 console.log 中，我们使用 new Error().stack 来获取调用栈信息，并解析出所在文件名和行数。
 * 要注意的是，由于调用栈信息的获取方式依赖于 JavaScript 引擎的实现，不同的环境下可能会有差异。在某些情况下，可能需要对解析调用栈信息的代码进行调整。
 * 使用这个重写后的 console.log，当调用 console.log 时，除了原始的输出信息外，还会在每条日志前打印文件名和行数。
 *
 */

// (() => {
//   const originalLog = console.log;
//   const log = function () {
//     var stackInfo = getStackInfo();

//     // 打印文件名和行数
//     var fileName = stackInfo.fileName;
//     var lineNumber = stackInfo.lineNumber;
//     originalLog.apply(
//       console,
//       ["[%s:%d]", fileName, lineNumber].concat(
//         Array.prototype.slice.call(arguments)
//       )
//     );
//   };

//   function getStackInfo() {
//     const stack = new Error().stack;
//     const stackLines = stack.split('\n');

//     // 解析调用栈信息
//     let callerIndex = 0;
//     for (let i = 2; i < stackLines.length; i++) {
//       if (!stackLines[i].includes('vue.runtime.esm.js')) {
//         callerIndex = i;
//         break;
//       }
//     }

//     const stackLine = stackLines[callerIndex];
//     const matchResult = stackLine.match(/(webpack-internal:\/\/\/.+\/)([^\/]+):(\d+):(\d+)/);

//     const fileName = matchResult[2];
//     const lineNumber = matchResult[3];

//     return {
//       fileName: fileName,
//       lineNumber: lineNumber,
//     };
//   }

//   console.log = log;
//   console.originalLog = originalLog;
// })();
