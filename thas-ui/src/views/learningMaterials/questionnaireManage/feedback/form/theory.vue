<template>
  <div class='theory-basic-form'>
    <el-descriptions class="margin-top" :column="1">
      <el-descriptions-item label="学员名字">{{formData.needName}}</el-descriptions-item>
      <el-descriptions-item label="性别"> {{gender}}</el-descriptions-item>
      <el-descriptions-item label="专业">{{major}}</el-descriptions-item>
      <el-descriptions-item label="联系电话">{{formData.phoneNumber}}</el-descriptions-item>
      <el-descriptions-item label="联系邮箱">{{formData.email}}</el-descriptions-item>
      <el-descriptions-item label="课程时间">{{formData.courseStartTime}} 至 {{formData.courseEndTime}} </el-descriptions-item>
      <el-descriptions-item label="课程形式">{{courseFormat}}</el-descriptions-item>
      <el-descriptions-item label="培训教员">{{reviewerNames}}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
import mixin from './mixin.vue'
export default {
  name: 'TheoryBasicForm',
  mixins: [mixin],
  computed: {
    validateForm() {
      return this.$refs.form;
    }
  },

}
</script>