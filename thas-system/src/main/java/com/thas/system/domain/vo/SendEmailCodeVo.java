package com.thas.system.domain.vo;

import cn.hutool.core.collection.CollectionUtil;
import com.thas.common.enums.AutSaAudRoleEnum;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import java.util.List;

/**
 * 发送邮箱验证码请求参数
 * <AUTHOR>
 */
@Data
public class SendEmailCodeVo {

    /**
     * 图片验证码
     */
    private String imgCode;

    /**
     * 图片标识码
     */
    private String uuid;

    /**
     * 邮箱接受方账号
     */
    private String toEmailNo;

    /**
     * 邮箱title
     */
    private String emailTitle;

    /**
     * 消息模板id
     */
    private Long messageTemplateId;

    /**
     * 模板参数
     */
    private List<String> contentParams;

    /**
     * 当前发送短信的角色（暂时用于区别医院信息与用户信息做校验用）
     * */
    private AutSaAudRoleEnum roleEnum;

    /**
     * 医院编号
     * */
    private String applyNo;

    @AssertTrue(message = "模板id不为空对应模板参数也不能为空")
    public boolean isMessageTemplate() {
        return messageTemplateId == null || CollectionUtil.isNotEmpty(contentParams);
    }
}
