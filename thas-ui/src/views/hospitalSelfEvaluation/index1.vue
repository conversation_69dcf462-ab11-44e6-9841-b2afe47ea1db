<template>
  <!-- v-if="relation && relation.autCode" -->
  <div class="audit-evaluation" v-loading="loading">
    <div v-if="distributed">
      <el-card shadow="always" style="margin-bottom:10px;margin-left:-5px;">
        <h1 style="margin:0;">{{detail.hospitalName}}</h1>
      </el-card>
      <!-- 总结详情 -->
      <summary-show v-if="!hasTerm && standardLoadedKey" :autSaAudStatus="autSaAudStatus" :showOnlyHospital="showOnlyHospital" :detail="detail" :summaryForm="summaryForm" :isConclusion="isConclusion" :conclusionText="conclusionText"></summary-show>
      <!-- 评价  -->
      <el-card v-show="!isConclusion" class="box-card" shadow="never" style="margin-top:15px;">
        <div slot="header" class="clearfix">
          <h4>{{ evaluationText2 }}</h4>
          <!-- 剩余 -->
          <evaluation-of-residual v-if="standardLoadedKey" :reviewResultOptions="reviewResultOptions" :autSaAudStatus="autSaAudStatus" :detail='detail' :relation="relation" :termMap="termMap" :clauseIds='clauseIds' :allArticleId="allArticleId" :showTerm="showTerm" :isHospital="isHospital" :isInspector="isInspector" :isAssessor="isAssessor" :isSeniorAssessor="isSeniorAssessor" @clearClauseList="clearClauseList"></evaluation-of-residual>
        </div>
        <!-- 选择款项 -->
        <standard-card ref="standardCard" v-if="show" :key="clauseIds4Select.toString() || relation.autCsId" :clauseIds="clauseIds4Select" :versionId="relation.autCsId" @selectChange="selectChange" @loaded="standardLoaded">
        </standard-card>
        <!-- 条小结 -->
        <el-form v-if="showTerm(article)" ref="articleSumMap" :model="articleSumMap" :rules="articleRules" style="margin-top:15px" label-width="90px">
          <el-form-item v-if="article" label="自评小结:" :prop="`${article}`">
            <el-input v-if="!termMap[article]" type="textarea" v-model.trim="articleSumMap[article]" :maxlength="50"></el-input>
            <span v-else>{{articleSumMap[article]}}</span>
          </el-form-item>
          <el-form-item label="" v-if="!termMap[article]">
            <el-button type="primary" @click="submitTerm" v-loading="termLoading" :disabled="termLoading">提交</el-button>
          </el-form-item>
        </el-form>
        <!-- 自评 / 审查 / 评审 -->
        <el-form :key="loadingKeys" label-width="100px">
          <clause-item v-for="(clause, index) in clauseList" v-show="isThisPage(index + 1)" :showOnlyHospital="showOnlyHospital" :type="type" :isLeader="isLeader" :readOnly="readOnly" :key="clause.clauseId" :clause="clause" :autSaAudStatus="autSaAudStatus" :aut="autMap[clause.clauseId]" :sa="saMap[clause.clauseId]" :sas="saSMap[clause.clauseId]" :aud="audMap[clause.clauseId]" :audS="audSMap[clause.clauseId]" :sac="saCMap[clause.clauseId]" :hat="hatMap[clause.clauseId]" :audF="audFMap[clause.clauseId]" :sAud="sAudMap[clause.clauseId]" :audSS="audSSMap[clause.clauseId]" @complete="onComplete"></clause-item>
        </el-form>
        <pagination v-show="total > limit" :total="total" :page.sync="pageNum" :limit.sync="limit" :pageSizes="[limit]" @pagination="changePage" />
      </el-card>

      <!-- 总结 提交 -->
      <summary-edit v-if="isConclusion && standardLoadedKey" :key="loadingKeys" :autSaAudStatus="autSaAudStatus" :showOnlyHospital="showOnlyHospital" :detail="detail" :isConclusion="isConclusion" :conclusionText="conclusionText" :summaryForm="summaryForm" :relation="relation" @summarySubmit='summaryEditBack' @reGetData='formateDetail'></summary-edit>
      <!-- 操作 -->
      <!-- <el-button @click="download2">下载</el-button> -->
      <!-- 弹窗 -->
      <test-case v-if="dialogVisible" :clauseList="clauseIds" :dialogVisible='dialogVisible' @closeTestCase='closeTestCase'></test-case>
    </div>
  </div>
</template>

<script>
import request, { download2 } from "@/utils/request";
import store from "@/store";

import ClauseItem from "@/components/ClauseItem";
import DictSpan from "@/components/DetailMessage/dictSpan.vue";
import SummaryShow from "./summaryShow/index.vue";
import SummaryEdit from "./summaryEdit/index.vue";
import EvaluationOfResidual from "./components/evaluationOfResidual.vue";
import StandardsDialog from '../approvalStandards/components/dialog/standardsDialog.vue';
import TestCase from './testCase'

const PAGE_SIZE = 6;

const USER_TYPE = {};

export default {
  name: "HospitalSelfEvaluationIndex",
  components: {
    ClauseItem,
    DictSpan,
    SummaryShow,
    SummaryEdit,
    StandardsDialog,
    TestCase,
    EvaluationOfResidual
  },
  props: {
    self: {
      type: [Boolean, null],
      default: () => null,
    },
    waitInit: {
      type: Boolean,
      default: () => false,
    },
  },
  watch: {
    $route(to) {
      if (to && to.query && to.query.autCode) {
        let autCode = to.query.autCode;
        if (this && this.relation && this.relation.autCode) {
          let autCode2 = this.relation.autCode;
          if (autCode2 != autCode) {
            this.loading = true;
            this.distributed = false;
            this.$nextTick(() => {
              this.init();
            })
          }
        }
      }
    },
  },
  data() {
    return {
      dialogVisible: false,
      show: false,
      loading: true,
      roles: [],
      clauseList: [],
      allClauseIndexFormMap: {},
      clauseIdList: [],
      relation: {},
      autMap: {}, // 自评列表
      saMap: {}, // 形式审查列表
      saSMap: {}, // 审查审核列表
      audMap: {}, // 现场评审列表
      audSMap: {}, // 现场评审审核列表
      saCMap: {}, // 审查组长 审查评审报告列表
      hatMap: {}, // 事实准确性确认
      audFMap: {}, // 评审员第一次修改
      sAudMap: {}, // 验证评审员验证
      audSSMap: {}, // 评审员第二次修改
      termMap: {},
      detail: {},
      pageNum: 1,
      standardLoadedKey: false,
      limit: PAGE_SIZE,
      summaryForm: {
        fileIds: "",
        autResult: "1",
        autDesc: "", // 理由 文本框
        reviewerRespond: "", // 下面几个属性的包裹json字符串
        autEvaluate: "", // 一般性评价 文本框
        autSuggestion: "", // 建议 选择
        autProposal: "", // 建议 文本框
        autAdvantage: "", // 优点 文本框
      },
      distributed: false,
      loadingKeys: 0,
      files: [],
      readOnly: false,
      reviewResultOptions: [],
      leader: 0,
      clauseIds: [],
      type: this.AutSaAudSubmitTypeEnum.AUD_SECOND_TRIAL_SUM_PROC,
      entry: 0,
      clauseIds4Select: [],
      clauseIds4SelectIndex: -1,
      showAuditResult: false,
      assignStatus: 0,
      allIds: false,
      article: 0,
      report: '/aut/sa/aud/generateSelfAssessmentReport',
      termLoading: false
    };
  },

  computed: {
    basicTotal() {
      let num = 0
      for (const key in this.basic) {
        if (Object.hasOwnProperty.call(this.basic, key)) {
          num += this.basic[key]
        }
      }
      return num;
    },
    nonBasicTotal() {
      let num = 0
      for (const key in this.nonBasic) {
        if (Object.hasOwnProperty.call(this.nonBasic, key)) {
          num += this.nonBasic[key]
        }
      }
      return num;
    },
    isLeader() {
      return this.leader == 1;
    },
    isHospital() {
      // 医疗机构
      return (
        this.$store.getters.roles.includes("hospital") || this.showOnlyHospital
      );
    },
    isAssessor() {
      // 评审员
      return (
        this.$store.getters.roles.includes("assessor") ||
        this.$store.getters.roles.includes("assessor-leader")
      );
    },
    isSeniorAssessor() {
      // 评审员
      return this.$store.getters.roles.includes("senior-assessor");
    },
    isInspector() {
      // 审查员
      return (
        this.$store.getters.roles.includes("inspector") ||
        this.$store.getters.roles.includes("inspector-leader")
      );
    },

    hasTerm() {
      let statues = [
        this.AutSaAudStatusEnum.WAIT_TERM_SA_SUM,
      ]
      return statues.includes(Number(this.autSaAudStatus));
    },

    // 总结
    isConclusion() {
      if (
        this.autSaAudStatus == this.AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL_SUM
      ) {
        const type = this.AutSaAudSubmitTypeEnum.AUD_FIRST_TRIAL_INQUIRE_PROC;
        let list = this.detail.autSaAudListMap[type];
        let flag = list.some((li) => li.autResult == 2);
        if (flag) {
          this.summaryForm.autResult = "2";
        } else {
          this.summaryForm.autResult = "1";
        }
      } else if (
        this.autSaAudStatus ==
        this.AutSaAudStatusEnum.REVIEW_REVIEW_REPORT_SUM ||
        this.autSaAudStatus ==
        this.AutSaAudStatusEnum.FACTUAL_ACCURACY_REVIEW_SUM
      ) {
        this.summaryForm.autResult = "1";
      }

      if (
        this.autSaAudStatus == this.AutSaAudStatusEnum.TERM_SA_SUM &&
        this.isHospital
      )
        return true;
      if (
        this.autSaAudStatus ==
        this.AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL_SUM &&
        this.isInspector &&
        this.isLeader
      )
        return true;
      if (
        this.autSaAudStatus ==
        this.AutSaAudStatusEnum.WAIT_AUD_SECOND_TRIAL_SUM &&
        this.isAssessor &&
        this.isLeader
      )
        return true;
      if (
        this.autSaAudStatus ==
        this.AutSaAudStatusEnum.REVIEW_REVIEW_REPORT_SUM &&
        this.isInspector &&
        this.isLeader
      )
        return true;
      if (
        this.autSaAudStatus ==
        this.AutSaAudStatusEnum.FACTUAL_ACCURACY_REVIEW_SUM &&
        this.isHospital
      )
        return true;
      if (this.autSaAudStatus == this.AutSaAudStatusEnum.WAIT_TERM_SA_SUM)
        return true;
      if (
        this.autSaAudStatus ==
        this.AutSaAudStatusEnum.SENIOR_REVIEWER_REVIEW_SUM &&
        this.isSeniorAssessor
      )
        return true;
      return false;
    },

    conclusionText() {
      let text = "自评";
      if (
        this.autSaAudStatus == this.AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL_SUM
      ) {
        text = "审查";
      } else if (
        this.autSaAudStatus == this.AutSaAudStatusEnum.WAIT_AUD_SECOND_TRIAL_SUM
      ) {
        text = "评审";
      } else if (
        this.autSaAudStatus == this.AutSaAudStatusEnum.REVIEW_REVIEW_REPORT_SUM
      ) {
        text = "审查评审报告";
      } else if (
        this.autSaAudStatus ==
        this.AutSaAudStatusEnum.FACTUAL_ACCURACY_REVIEW_SUM
      ) {
        text = "事实准确性确认";
      } else if (
        this.autSaAudStatus ==
        this.AutSaAudStatusEnum.SENIOR_REVIEWER_REVIEW_SUM
      ) {
        text = "评审报告验证";
        this.summaryForm.autResult = "";
      }
      if (this.showOnlyHospital) {
        text = "自评";
      }
      return text;
    },

    autSaAudStatus() {
      return this.showOnlyHospital &&
        Number(this.detail.autSaAudStatus) !=
        this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_INQUIRE_PROC
        ? this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_INQUIRE_PROC
        : Number(this.detail.autSaAudStatus);
    },

    total() {
      return this.clauseList.length;
    },

    isThisPage() {
      return (index) => {
        return index <= this.pageNum * 6 && index > (this.pageNum - 1) * 6;
      };
    },

    // 是否展示某个条小结
    showTerm() {
      return (articleId) => {
        if (!articleId) return false;
        const versionId = this.relation.autCsId || store.getters.standardBaseVersionId;
        const map =
          store.getters.standardTypeItemByVersionId(
            versionId,
            "article",
            "map"
          ) || [];

        const clauseIds = map[articleId].clauseVoList.map(clause => clause.clauseId);
        let type0 = this.AutSaAudSubmitTypeEnum.TERM_SA_PROC;
        if (!this.detail.autSaAudListMap) {
          return false;
        }
        const autSaAudList = this.detail.autSaAudListMap[type0] || [];
        const flag = clauseIds.map(id => autSaAudList.some(aut => aut.clauseId == id)).reduce((a, b) => {
          return a && b
        }, true);
        return flag
      }
    },

    allArticleId() {
      try {
        const versionId =
          this.relation.autCsId || store.getters.standardBaseVersionId;
        const list =
          store.getters.standardTypeItemByVersionId(
            versionId,
            "article",
            "list"
          ) || [];
        return list.map((item) => item.articleId);
      } catch (error) {
        // console.log('allArticleId error', error);
        return [];
      }
    },

    evaluationText2() {
      // debugger;
      let text = "医院自评";
      let status = Number(this.autSaAudStatus);
      let status1 = [
        this.AutSaAudStatusEnum.WAIT_TERM_SA,
        this.AutSaAudStatusEnum.TERM_SA_SUM,
      ];
      if (status1.includes(status)) {
        text = "医院自评";
      }

      let status2 = [
        this.AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL,
        this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_INQUIRE_PROC,
        this.AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL_REVIEW,
        this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_REVIEW_PROC,
        this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_REVIEW_REJECT,
        this.AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL_SUM,
        this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_SUM_PASS,
      ];
      if (status2.includes(status)) {
        if (this.isHospital || this.isAssessor) {
          text = "医院自评详情";
        } else if (
          (status == this.AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL_REVIEW ||
            status == this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_REVIEW_PROC) &&
          this.isLeader
        ) {
          text = "自评审查审核";
        } else {
          text = "自评形式审查";
        }
      }

      let status3 = [
        this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_INQUIRE_PROC,
        this.AutSaAudStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW,
        this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC,
        this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_REVIEW_REJECT,
        this.AutSaAudStatusEnum.WAIT_AUD_SECOND_TRIAL_SUM,
        this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_SUM_REJECT,
        this.AutSaAudStatusEnum.AUD_PASS,
        this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_SUM_CONDITIONAL_PASS,
      ];
      if (status3.includes(status)) {
        if (this.isHospital || this.isInspector) {
          text = "医院自评详情";
        } else if (
          (status == this.AutSaAudStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW ||
            status == this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC) &&
          this.isLeader
        ) {
          text = "自评现场评审审核";
        } else {
          text = "自评现场评审";
        }
      }

      let status4 = [
        this.AutSaAudStatusEnum.REVIEW_REVIEW_REPORT_PROC,
        this.AutSaAudStatusEnum.REVIEW_REVIEW_REPORT_SUM,
        this.AutSaAudStatusEnum.WAIT_REVIEW_REVIEW_REPORT,
      ];
      let status5 = [
        this.AutSaAudStatusEnum.WAIT_FACTUAL_ACCURACY_REVIEW,
        this.AutSaAudStatusEnum.FACTUAL_ACCURACY_REVIEW_PROC,
        this.AutSaAudStatusEnum.FACTUAL_ACCURACY_REVIEW_SUM,
        this.AutSaAudStatusEnum.CONFIRM_REVIEW_FACTUAL,
      ];
      let status6 = [
        this.AutSaAudStatusEnum.CHECK_MODIFIED_TERMS_FIRST,
        this.AutSaAudStatusEnum.REVIEWER_MODIFICATION_REVIEW,
        this.AutSaAudStatusEnum.CHECK_MODIFIED_TERMS_SECOND,
        this.AutSaAudStatusEnum.REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW,
      ];

      let status7 = [
        this.AutSaAudStatusEnum.WAIT_SENIOR_REVIEWER_REVIEW,
        this.AutSaAudStatusEnum.SENIOR_REVIEWER_REVIEW_PROC,
        this.AutSaAudStatusEnum.SENIOR_REVIEWER_REVIEW_SUM,
      ];

      if (status >= this.AutSaAudStatusEnum.WAIT_REVIEW_REVIEW_REPORT) {
        if (this.isHospital || this.isInspector || this.isAssessor) {
          text = "医院自评详情";
        }
        if (status4.includes(status)) {
          text = "医院自评详情";
          if (this.isInspector && this.isLeader) {
            text = "审查评审报告";
          }
        } else if (status5.includes(status)) {
          text = "医院自评详情";
          if (this.isHospital && this.assignStatus) {
            text = "事实准确性确认";
          }
        } else if (status7.includes(status)) {
          text = "医院自评详情";
          if (this.isSeniorAssessor) {
            text = "验证评审员验证";
          }
        } else if (status6.includes(status)) {
          text = "医院自评详情";
          if (this.isSeniorAssessor) {
            text = "自评评审修改";
          }
        }
      }
      return text;
    },


    articleRules() {
      return {
        [`${this.article}`]: [
          {
            required: true,
            message: '请输入自评小结'
          }
        ]
      }
    },
  },

  created() {
    this.bus.$on("autSubmit", (event, data, clauseId) => {
      this.onComplete(event, data, clauseId, 0);
    });
  },

  destroyed() {
    this.bus.$off("autSubmit");
  },

  async mounted() {
    if (
      Object.keys(this.$route.query).length == 0 &&
      this.$route.path !=
      "/hospitalSelfEvaluation/hospitalSelfEvaluationIndex" &&
      this.$route.path != "/hospitalSelfEvaluation/hAccuracy"
    )
      return;
    !this.waitInit && this.init();
  },

  methods: {
    clearClauseList(list) {

    },
    closeTestCase() {
      this.dialogVisible = false;
    },

    initBefore(data) {
      this.assignStatus = data.assignStatus || 0;
      this.allIds = data.allIds;
    },
    async queryList() {
      return request({
        url: "/aut/sa/aud/queryList",
        method: "post",
        data: {
          accountId: store.getters.userId + "",
          ...this.$route.query,
        },
      });
    },
    async init() {
      try {
        let data = {};
        if (this.$route.query.autCode && this.$route.query.nl != 1) {
          let { rows } = await this.queryList();
          if (!rows || !rows.length || !rows[0]) {
            throw "获取评审数据失败";
          }
          data = rows[0];
        }
        this.leader = data.isLeader || 0;
        this.autCode = data.autCode || this.$route.query.autCode;
        this.clauseIds = this.allIds ? [] : (data.waitModifyClauseIdList || data.distributeClauseIdList || []);
        this.clauseIds4Select = this.clauseIds;
        //
        this.entry = this.$route.query.entry;
        this.readOnly = this.$route.query.readOnly == 'true';
        this.showOnlyHospital = !!this.$route.query.showOnlyHospital;
        this.type = this.$route.query.autType || undefined;

        this.initDicts();
        if (this.$route.query.isHistory) {
          this.relation.autCode = this.$route.query.autCode;
          this.formateDetail();
        } else {
          const relation = await this.relationQuery();
          if (relation.code == 200) {
            this.relation = relation.data;
            if (this.relation.autStatus < this.assignStatus) {
              this.$message({
                type: "warning",
                message: "审查组长未完成评审报告审查，无法进行事实准确性确认",
              });
              throw "";
            }
            if (relation.data.autCode) {
              this.formateDetail();
            }
          }
        }

      } catch (error) {
        if (
          this.$route.path ==
          "/hospitalSelfEvaluation/hospitalSelfEvaluationIndex"
        ) {
          this.$store.dispatch("tagsView/delView", this.$route);
          this.$router.push({ path: "/" });
        } else {
          this.$emit("summarySubmit");
        }
      }
    },
    initDicts() {
      this.getDicts("review_result").then((res) => {
        this.reviewResultOptions = res.data;
      });
    },

    async loadDetail() {
      let clauseList = this.clauseList;
      if (clauseList.length <= 0) return;
      let articleId = clauseList[0].articleId,
        versionId = clauseList[0].versionId;
      const data = await this.getClausesDetail(versionId, articleId);
    },
    changePage() { },
    standardLoaded() {
      this.$nextTick(() => this.standardLoadedKey = true)
    },
    selectChange(tier, data, { chapter, section, article }) {
      this.clauseList = data;
      const clauseIdList = this.clauseList.map((clause) => clause.clauseId);
      this.clauseIdList.push(clauseIdList);
      this.$set(this, "clauseIdList", [...new Set(this.clauseIdList)]);
      this.clauseIdList.forEach((clause) => {
        if (this.allClauseIndexFormMap[clause.clauseId] == undefined) {
          this.allClauseIndexFormMap[clause.clauseId] = 0;
        }
      });
      if (article) {
        if (!this.articleSumMap[article]) {
          this.$set(this.articleSumMap, article, '')
        }
        this.article = article;
      } else {
        this.article = 0;
      }
      this.loadDetail();
    },

    summaryEditBack() {
      this.formateDetail();
      this.$emit('summarySubmit')
    },

    onComplete(event, formData, clauseId, type) {
      if (!type) {
        type = this.AutSaAudSubmitTypeEnum.TERM_SA_PROC;
      }
      formData.clauseId = clauseId + "";
      this.allClauseIndexFormMap[clauseId] = formData;
      const { complete, ...data } = formData;
      this.submitAutSaAud({
        autCode: this.relation.autCode,
        accountId: store.getters.userId + "",
        type,
        autSaAudLists: [data],
      })
        .then((res) => {
          if (res.code == 200) {
            this.$message({
              type: "success",
              message: "评价提交成功",
            });
            if (event) {
              this.bus.$emit(event, res.code == 200, clauseId, true);
            }
            this.formateDetail();
          }
        })
        .catch((error) => {
          if (event) {
            this.bus.$emit(event, false, clauseId);
          }
        });
    },

    relationQuery() {
      return request({
        url: "/aut/sa/relation/query",
        method: "post",
        data: {
          accountId: store.getters.userId,
          autCode: this.autCode,
        },
      });
    },

    // 0 自评 1总结 2形式审查 3审查审核 4审查总结 5是现场评审 6是现场评审审核 7评审总结
    queryDetail() {
      return request({
        url: "/aut/sa/aud/queryDetail",
        method: "post",
        data: {
          // autCode: this.relation.autCode,
          autCode: this.$route.query.autCode,
          accountId: store.getters.userId,
        },
      });
    },

    async formateDetail() {
      const res = await this.queryDetail();
      this.distributed = true;
      if (res.code == 200) {
        this.detail = res.data;
        this.getStandard(this.relation.autCsId, false, false, (res) => {
          this.showAuditResult = res;
          this.show = true;
        });
        this.relation.autCsId = this.detail.versionId;
        if (this.detail.seniorReviewerReviewSum) {
          let { reviewerRespond } = this.detail.seniorReviewerReviewSum;
          if (typeof reviewerRespond == "string") {
            try {
              let respond = JSON.parse(reviewerRespond);
              for (const key in respond) {
                if (Object.hasOwnProperty.call(respond, key)) {
                  this.detail.seniorReviewerReviewSum[key] = respond[key];
                }
              }
            } catch (error) {
              // console.log(error);
              // ...
            }
          }
        } else if (this.detail.autSaAudSum) {
          let { autDesc } = this.detail.autSaAudSum;
          if (typeof autDesc == "string") {
            try {
              let desc = JSON.parse(autDesc);
              for (const key in desc) {
                if (Object.hasOwnProperty.call(desc, key)) {
                  this.detail.autSaAudSum[key] = desc[key];
                }
              }
            } catch (error) {
              // console.log(error);
              // ...
            }
          }
        }
        // 自评
        if (this.detail.autSaAudListMap) {
          let type0 = this.AutSaAudSubmitTypeEnum.TERM_SA_PROC;
          if (this.detail.autSaAudListMap[type0]) {
            const autSaAudList = this.detail.autSaAudListMap[type0] || [];
            (autSaAudList || []).forEach((aut) => {
              this.autMap[aut.clauseId] = aut;
              let fileIds = aut.fileIds.split(",");
              const fileList = [];
              fileIds.forEach((fileId) => {
                if (fileId) {
                  const files = this.detail.fileDetailMap[fileId];
                  if (files) {
                    files[0] && fileList.push(files[0]);
                  }
                }
              });
              aut.fileList = fileList;
            });
            const rejectAutSaAudListMap =
              this.detail.rejectAutSaAudListMap || {};
            const rejectAutSaAudList = rejectAutSaAudListMap[type0] || [];
            rejectAutSaAudList.forEach((aut) => {
              const fileList = [];
              let fileIds = aut.fileIds.split(",");
              fileIds.forEach((fileId) => {
                if (fileId) {
                  const files = this.detail.fileDetailMap[fileId];
                  if (files) {
                    files[0] && fileList.push(files[0]);
                  }
                }
              });
              aut.type == type0 &&
                !this.autMap[aut.clauseId] &&
                (this.autMap[aut.clauseId] = {
                  ...aut,
                  isReject: true,
                  fileList,
                });
            });
          }

          // 条小结
          let type16 = this.AutSaAudSubmitTypeEnum.TERM_SUM;
          if (this.detail.autSaAudListMap[type16]) {
            const termList = this.detail.autSaAudListMap[type16];
            termList.forEach(term => {
              this.$set(this.articleSumMap, term.clauseId, term.autDesc);
              this.$set(this.termMap, term.clauseId, true);
            })
          }


          // 形式审查
          let type2 = this.AutSaAudSubmitTypeEnum.AUD_FIRST_TRIAL_INQUIRE_PROC;
          if (this.detail.autSaAudListMap[type2]) {
            const autSaAudList = this.detail.autSaAudListMap[type2] || [];
            (autSaAudList || []).forEach((sa) => {
              this.saMap[sa.clauseId] = sa;
            });
            const rejectAutSaAudListMap =
              this.detail.rejectAutSaAudListMap || {};
            const rejectAutSaAudList = rejectAutSaAudListMap[type2] || [];
            rejectAutSaAudList.forEach((sa) => {
              sa.type == type2 &&
                !this.saMap[sa.clauseId] &&
                (this.saMap[sa.clauseId] = {
                  ...sa,
                  isReject: true,
                });
            });
          }
          // 审查审核
          let type3 = this.AutSaAudSubmitTypeEnum.AUD_FIRST_TRIAL_REVIEW_PROC;
          if (this.detail.autSaAudListMap[type3]) {
            const autSaAudList = this.detail.autSaAudListMap[type3] || [];
            (autSaAudList || []).forEach((sa) => {
              this.saSMap[sa.clauseId] = sa;
            });
            const rejectAutSaAudListMap =
              this.detail.rejectAutSaAudListMap || {};
            const rejectAutSaAudList = rejectAutSaAudListMap[type3] || [];
            rejectAutSaAudList.forEach((sas) => {
              sas.type == type3 &&
                !this.saSMap[sas.clauseId] &&
                (this.saSMap[sas.clauseId] = {
                  ...sas,
                  toReject: true,
                });
            });
          }

          // 现场评审
          let type5 = this.AutSaAudSubmitTypeEnum.AUD_SECOND_TRIAL_INQUIRE_PROC;
          if (this.detail.autSaAudListMap[type5]) {
            const autSaAudList = this.detail.autSaAudListMap[type5] || [];
            (autSaAudList || []).forEach((aud) => {
              this.audMap[aud.clauseId] = aud;
            });
            const rejectAutSaAudListMap =
              this.detail.rejectAutSaAudListMap || {};
            const rejectAutSaAudList = rejectAutSaAudListMap[type5] || [];
            rejectAutSaAudList.forEach((aud) => {
              aud.type == type5 &&
                !this.audMap[aud.clauseId] &&
                (this.audMap[aud.clauseId] = {
                  ...aud,
                  isReject: true,
                });
            });
          }

          // 现场评审审核
          let type6 = this.AutSaAudSubmitTypeEnum.AUD_SECOND_TRIAL_REVIEW_PROC;
          if (this.detail.autSaAudListMap[type6]) {
            const autSaAudList = this.detail.autSaAudListMap[type6] || [];
            (autSaAudList || []).forEach((aud) => {
              this.audSMap[aud.clauseId] = aud;
            });
            const rejectAutSaAudListMap =
              this.detail.rejectAutSaAudListMap || {};
            const rejectAutSaAudList = rejectAutSaAudListMap[type6] || [];
            rejectAutSaAudList.forEach((aud) => {
              aud.type == type6 &&
                !this.audSMap[aud.clauseId] &&
                (this.audSMap[aud.clauseId] = {
                  ...aud,
                  toReject: true,
                });
            });
          }

          // 审查组长 审查评审报告
          let type8 = this.AutSaAudSubmitTypeEnum.REVIEW_REVIEW_REPORT;
          if (this.detail.autSaAudListMap[type8]) {
            const autSaAudList = this.detail.autSaAudListMap[type8] || [];
            (autSaAudList || []).forEach((sac) => {
              this.saCMap[sac.clauseId] = sac;
            });
          }

          // 医院 事实准确性确认
          let type10 = this.AutSaAudSubmitTypeEnum.FACTUAL_ACCURACY_REVIEW;
          if (this.detail.autSaAudListMap[type10]) {
            const autSaAudList = this.detail.autSaAudListMap[type10] || [];
            (autSaAudList || []).forEach((hat) => {
              this.hatMap[hat.clauseId] = hat;
            });
          }

          // 第一次修改
          let type12 = this.AutSaAudSubmitTypeEnum.REVIEWER_MODIFICATION_REVIEW;
          if (this.detail.autSaAudListMap[type12]) {
            const autSaAudList = this.detail.autSaAudListMap[type12] || [];
            (autSaAudList || []).forEach((audF) => {
              this.audFMap[audF.clauseId] = audF;
            });
          }

          let type13 = this.AutSaAudSubmitTypeEnum.SENIOR_REVIEWER_REVIEW;
          if (this.detail.autSaAudListMap[type13]) {
            const autSaAudList = this.detail.autSaAudListMap[type13] || [];
            (autSaAudList || []).forEach((sAud) => {
              this.sAudMap[sAud.clauseId] = sAud;
            });
          }

          let type15 =
            this.AutSaAudSubmitTypeEnum
              .REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW;
          if (this.detail.autSaAudListMap[type15]) {
            const autSaAudList = this.detail.autSaAudListMap[type15] || [];
            (autSaAudList || []).forEach((audSS) => {
              this.audSSMap[audSS.clauseId] = audSS;
            });
          }
        }
        // 页面大致加载完毕后
        this.loadingKeys++;
        this.$nextTick(() => this.loading = false)
      }
    },

    async checkDetail() {
      const res = await this.queryDetail();
      if (res.code == 200) {
        const clauseList = res.data.autSaAudLists;
        if (clauseList.length !== this.clauseTotal.length) {
          return this.$message({
            type: "warning",
            message: "请自评完毕",
          });
        }
      }
    },

    download2() {
      download2(this.report, 'abc.pdf');
    },

    submitTerm() {
      this.$refs.articleSumMap.validate(async valid => {
        if (valid) {
          this.termLoading = true;
          const data = {
            autCode: this.relation.autCode,
            accountId: store.getters.userId + "",
            type: this.AutSaAudSubmitTypeEnum.TERM_SUM,
          };
          data.autSaAudLists = [{
            autDesc: this.articleSumMap[this.article],
            autResult: "0",
            clauseId: this.article,
          }]
          try {
            const res = await this.submitAutSaAud(data);
            this.$message("批量提交成功-仅测试提示");
            this.formateDetail();
          } catch (error) {
            if (error.code == 1000003) {
              this.formateDetail();
            }
          } finally {
            setTimeout(() => {
              this.termLoading = false;
            }, 2000);
          }
        }
      })
    },

    submitAutSaAud(data) {
      return request({
        url: "/aut/sa/aud/submitAutSaAud",
        method: "post",
        data,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.audit-evaluation {
  min-height: inherit;
  .box-card + .box-card {
    margin-top: 15px;
  }

  // padding: 24px;
  ::v-deep .el-card {
    border: 0;
    .el-card__header {
      padding-bottom: 15px;
      .clearfix h4 {
        float: left;
        font-weight: bold;
        margin: 0;
      }
    }

    .grade .el-select .el-input.is-disabled .el-input__inner {
      background-color: #ffffff;
      border: 1px solid #dcdfe6;
      color: #606266;
    }
  }
  .firstCheckNumTip {
    cursor: pointer;
  }
  .firstCheckNumTip:last-child > a {
    display: none;
  }
  .firstCheckNumTip.lightHigh {
    color: blue;
  }
}
</style>
