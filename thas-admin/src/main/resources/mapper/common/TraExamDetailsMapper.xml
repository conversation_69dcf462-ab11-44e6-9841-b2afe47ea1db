<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.TraExamDetailsMapper">

    <resultMap type="TraExamDetails" id="TraExamDetailsResult">
        <result property="id" column="id"/>
        <result property="examPaperId" column="exam_paper_id"/>
        <result property="examQuestion" column="exam_question"/>
        <result property="score" column="score"/>
        <result property="type" column="type"/>
        <result property="questionOptions" column="question_options"/>
        <result property="correctAnswer" column="correct_answer"/>
    </resultMap>

    <sql id="selectTraExamDetailsVo">
        select id, exam_paper_id, exam_question, score, type, question_options, correct_answer
        from tra_exam_details
    </sql>

    <select id="selectTraExamDetailsList" parameterType="TraExamDetails" resultMap="TraExamDetailsResult">
        <include refid="selectTraExamDetailsVo"/>
        <where>
            <if test="examPaperId != null ">and exam_paper_id = #{examPaperId}</if>
            <if test="examQuestion != null  and examQuestion != ''">and exam_question = #{examQuestion}</if>
            <if test="score != null  and score != ''">and score = #{score}</if>
            <if test="type != null ">and type = #{type}</if>
            <if test="questionOptions != null  and questionOptions != ''">and question_options = #{questionOptions}</if>
            <if test="correctAnswer != null  and correctAnswer != ''">and correct_answer = #{correctAnswer}</if>
        </where>
    </select>

    <select id="selectTraExamDetailsById" parameterType="Long" resultMap="TraExamDetailsResult">
        <include refid="selectTraExamDetailsVo"/>
        where id = #{id}
    </select>

    <insert id="insertTraExamDetails" parameterType="TraExamDetails" useGeneratedKeys="true" keyProperty="id">
        insert into tra_exam_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="examPaperId != null">exam_paper_id,</if>
            <if test="examQuestion != null">exam_question,</if>
            <if test="score != null">score,</if>
            <if test="type != null">type,</if>
            <if test="questionOptions != null">question_options,</if>
            <if test="correctAnswer != null">correct_answer,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="examPaperId != null">#{examPaperId},</if>
            <if test="examQuestion != null">#{examQuestion},</if>
            <if test="score != null">#{score},</if>
            <if test="type != null">#{type},</if>
            <if test="questionOptions != null">#{questionOptions},</if>
            <if test="correctAnswer != null">#{correctAnswer},</if>
        </trim>
    </insert>

    <insert id="insertBatchExamDetails" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into tra_exam_details (exam_paper_id, exam_question, score, `type`,
                                      question_options, correct_answer)
            values
        <foreach collection="list" item="item" index="index" separator="," close=";">
            (#{item.examPaperId}, #{item.examQuestion}, #{item.score},
             #{item.type}, #{item.questionOptions}, #{item.correctAnswer})
        </foreach>
    </insert>

    <update id="updateTraExamDetails" parameterType="TraExamDetails">
        update tra_exam_details
        <trim prefix="SET" suffixOverrides=",">
            <if test="examPaperId != null">exam_paper_id = #{examPaperId},</if>
            <if test="examQuestion != null">exam_question = #{examQuestion},</if>
            <if test="score != null">score = #{score},</if>
            <if test="type != null">type = #{type},</if>
            <if test="questionOptions != null">question_options = #{questionOptions},</if>
            <if test="correctAnswer != null">correct_answer = #{correctAnswer},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTraExamDetailsById" parameterType="Long">
        delete
        from tra_exam_details
        where id = #{id}
    </delete>

    <delete id="deleteTraExamDetailsByIds" parameterType="String">
        delete from tra_exam_details where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>