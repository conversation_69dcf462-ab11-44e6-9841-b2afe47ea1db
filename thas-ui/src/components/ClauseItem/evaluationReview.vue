<template>
  <div class="evaluation-autResult">
    <el-divider></el-divider>
    <el-form ref="form" :model="formData" :rules="rules" label-width="110px">
      <el-form-item :label="`${reviewResult}：`" prop="autResult" :class="{ readonly: !(!readOnly && hasSaAud && itsMine) }">
        <el-select v-if="!readOnly && hasSaAud && itsMine" v-model.trim="formData.autResult" clearable filterable :placeholder="`请选择${this.reviewResult}`">
          <el-option v-for="(item, index) in agreeOrNotOptions" :key="index" :value="item.dictValue" :label="item.dictLabel"></el-option>
        </el-select>
        <span v-else> {{ getResultTitle(formData.autResult) }}</span>
      </el-form-item>
      <el-form-item :label="`${reviewDesc}：`" prop="autDesc" :class="{ readonly: !(!readOnly && hasSaAud && itsMine) }">
        <el-input v-if="!readOnly && hasSaAud && itsMine" v-model.trim="formData.autDesc" type="textarea" :rows="4" :maxlength="50"></el-input>
        <span v-else> {{ formData.autDesc }}</span>
      </el-form-item>

      <el-form-item v-if="!readOnly && hasSaAud && itsMine">
        <el-button type="primary" @click="submit" v-loading="submitLoading" :disabled="submitLoading">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "EvaluationResult",
  props: {
    label: {
      type: String,
      default: () => "",
    },
    status: {
      type: [String, Number],
      default: () => false,
    },
    saAud: {
      type: Object,
      default: () => { },
    },
    clause: {
      type: Object,
      default: () => { },
    },
    readOnly: {
      require: true,
      type: Boolean,
      default: () => true,
    },
  },
  watch: {
    saAud: {
      immediate: true,
      deep: true,
      handler() {
        const { autResult = null, autDesc = null } = this.saAud || {};
        this.formData = {
          autResult,
          autDesc,
        };
        this.$refs['form'] && this.$refs['form'].clearValidate('autResult')
        this.$refs['form'] && this.$refs['form'].clearValidate('autDesc')
        this.submitLoading = false;
      },
    },
  },
  data() {
    return {
      showExaminer: false,
      formData: {
        autResult: "",
        autDesc: "",
      },

      agreeOrNotOptions: [],
      entry: 0,
      leader: 0,
      showRisk: false,
      submitLoading: false,
    };
  },
  computed: {
    rules() {
      return {
        autResult: [
          {
            required: true,
            message: `请选择${this.label}结果`,
            trigger: "change",
          },
        ],
        autDesc: [
          {
            required: this.formData.autResult != 1,
            message: `请输入${this.formData.autResult != 1 ? '原因' : `${this.label}描述`}`,
            trigger: ["change", "blur"],
          },
        ],
      };
    },
    examiner() {
      return `${this.label}员`;
    },
    reviewResult() {
      return `${this.label}结果`;
    },
    reviewDesc() {
      return this.formData.autResult != 1 ? '原因' : `${this.label}描述`;
    },
    hasSaAud() {
      return !this.saAud || !!this.saAud.isReject || !!this.saAud.toReject;
    },
    itsMine() {
      // 用户类型：0医院 1审查员 2审查组长 3评审员 4评审组长
      const types = [
        this.AutSaAudSubmitTypeEnum.AUD_FIRST_TRIAL_REVIEW_PROC,
        this.AutSaAudSubmitTypeEnum.AUD_SECOND_TRIAL_REVIEW_PROC,
      ];

      const statuses = [
        this.AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL_REVIEW,
        this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_REVIEW_PROC,
        this.AutSaAudStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW,
        this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC,
      ];
      if (statuses.includes(Number(this.status))) {
        if (this.leader != 1) return false;
        if (this.entry == 1 || this.entry == 3) return false;
        return true;
      } else {
        return false;
      }
    },

    isSaS() {
      return (
        this.status == this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_REVIEW_PROC ||
        this.status == this.AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL_REVIEW
      );
    },

    isAudS() {
      return (
        this.status == this.AutSaAudStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW ||
        this.status == this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC ||
        this.status == this.AutSaAudStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW
      );
    },
    getResultTitle() {
      return (value) => {
        return (
          this.agreeOrNotOptions.find((option) => option.dictValue == value) || {}
        ).dictLabel;
      };
    },
  },

  mounted() {
    this.initDicts();
    this.entry = this.$route.query.entry || 0;
    this.leader = this.$route.query.isLeader || 0;

    let statues = [
      this.AutSaAudStatusEnum.WAIT_AUD_FIRST_TRIAL_REVIEW,
      this.AutSaAudStatusEnum.AUD_FIRST_TRIAL_REVIEW_PROC,
      this.AutSaAudStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW,
      this.AutSaAudStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC,
    ];
    if (
      !this.readOnly &&
      this.saAud &&
      this.itsMine &&
      statues.includes(Number(this.status))
      && this.saAud.autResult != '1'
    ) {
      this.formData.autDesc = "";
    }
  },

  methods: {
    initDicts() {
      this.getDicts("agree_or_not").then((res) => {
        this.agreeOrNotOptions = res.data;
      });
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.submitLoading = true;
          let type = this.AutSaAudSubmitTypeEnum.AUD_FIRST_TRIAL_REVIEW_PROC;
          if (this.isSaS) {
            type = this.AutSaAudSubmitTypeEnum.AUD_FIRST_TRIAL_REVIEW_PROC;
          } else if (this.isAudS) {
            type = this.AutSaAudSubmitTypeEnum.AUD_SECOND_TRIAL_REVIEW_PROC;
          }
          let clauseId;
          if (this.saAud) {
            clauseId = this.saAud.clauseId;
          } else {
            clauseId = this.clause.clauseId;
          }
          this.$emit("onComplete", null, this.formData, clauseId, type);
          // 提交超时3s后重置按钮loading
          setTimeout(() => {
            this.submitLoading = false;
          }, 5000);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.evaluation-autResult {
  ::v-deep .el-divider {
    margin-bottom: 10px;
  }
  ::v-deep .el-form-item {
    margin-bottom: 24px;
    .el-form-item__content {
      overflow: visible;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
