
<!DOCTYPE html>
<html
  lang="en"
  xmlns:o="urn:schemas-microsoft-com:office:office"
  xmlns:w="urn:schemas-microsoft-com:office:word"
  xmlns:dt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882"
  xmlns="http://www.w3.org/TR/REC-html40"
>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="ProgId" content="Word.Document" />
    <meta name="Generator" content="Microsoft Word 14" />
    <meta name="Originator" content="Microsoft Word 14" />
    <title></title>
    <style>
      @font-face {
        font-family: "SimSun";
      }
      @page {
        @top-left {
          content: element(header);
        }
        size: A4;
      }
      body {
        font-family: "SimSun";
      }
    </style>
    <style>
       td {
        max-width: 80px;
        word-break: break-all;
        word-wrap: break-word;
      }
      #reviewer-list {
        width: 100%;
      }
      #reviewer-list td,
      #reviewer-list th {
        height: 30px;
      }
      .mb10 {
        margin-bottom: 10px;
      }
      .mb30 {
        margin-bottom: 30px;
      }
      .left-td {
        width: 30%;
        text-align: right;
        font-weight: bold;
        background-color: #c0c0c0;
      }
      .mt80 {
        margin-top: 60px;
      }
      .mt30 {
        margin-top: 30px;
      }
	    .mt50 {
        margin-top: 50px;
      }
      #reviewer-list .first-th {
        background-color: #2f5496;
        text-align: center;
        color: #ffffff;
      }
      #reviewer-list td>.all {
        color: #ff4949;
      }
    </style>
  </head>

  <body>
    <div>
      <table id="reviewer-list" cellpadding="0" cellspacing="0" border="1">
        <tr>
          <td colspan="2" class="first-th">评审员名单确认表</td>
        </tr>
        <tbody>
          <tr>
            <td class="left-td" style="background-color: #8eaadb;">医院</td>
            <td style="background-color: #8eaadb;">${hospitalName}</td>
          </tr>
          <tr>
            <td class="left-td">评审日期</td>
            <td>${siteCycle}</td>
          </tr>
          <#list reviewerLeaderList as reviewerLeader>
            <tr>
              <td class="left-td">${reviewerLeader.groupName}评审员（组长）</td>
              <td>${reviewerLeader.name}</td>
            </tr>
          </#list>
          <#list ordinaryReviewList as ordinaryReviewer>
            <tr>
              <td class="left-td">${ordinaryReviewer.groupName}号评审员</td>
              <td>${ordinaryReviewer.name}</td>
            </tr>
          </#list>
          <#list traineesReviewList as traineesReviewer>
            <tr>
              <td class="left-td">${traineesReviewer_index + 1}号学员</td>
              <td>${traineesReviewer.name}</td>
            </tr>
          </#list>
        </tbody>
      </table>
      <p style="word-break: break-all;word-wrap: break-word;">上述评审员将于上述评审日期对<span style="text-decoration: underline;"> ${hospitalName} </span>开展评审工作，他们的简介已发送到贵单位“医院评审评价管理平台”账号中。 请于<span style="text-decoration: underline;"> ${beforeCycleDateStr} </span>之前完成下表，并上传至“医院评审评价管理平台”。</p>
      <table id="reviewer-list" class="mb10" cellpadding="0" cellspacing="0" border="1">
        <tr>
          <th style="background-color: #d9e2f3;">√</th>
          <th style="background-color: #d9e2f3;" align="left">意见1</th>
        </tr>
        <tbody>
          <tr>
            <td>
              <#if rejectReviewerList?? && (rejectReviewerList?size > 0)>
                <span></span>
              <#else>
                <span>√</span>
              </#if>
            </td>
            <td>①本单位接受上述<span class="all">全部</span>评审员在我院开展评审工作。</td>
          </tr>
          <tr>
            <td>
              <#if rejectReviewerList?? && (rejectReviewerList?size > 0)>
                <span>√</span>
              <#else>
                <span></span>
              </#if>
            </td>
            <td>
              <span>②本单位拒绝<#list rejectReviewerList as rejectReviewer><#if rejectReviewer_index != rejectReviewerList?size - 1 ><span>${rejectReviewer.name}、</span></#if><#if rejectReviewer_index == rejectReviewerList?size - 1><span>${rejectReviewer.name}</span></#if></#list>，接受其他评审员在我院开展评审工作。</span>
            </td>
          </tr>
          <tr>
            <td colspan="2" style="font-weight: bold;background-color: #d9e2f3;">拒绝的理由</td>
          </tr>
          <tr>
            <td colspan="2">
              <#if rejectReviewerList?? && (rejectReviewerList?size > 0)>
                <div style="width: 680px;">${rejectReason}</div>
              <#else>
              </#if>
            </td>
          </tr>
        </tbody>
      </table>

      <table id="reviewer-list" cellpadding="0" cellspacing="0" border="1">
        <tr>
          <th style="background-color: #d9e2f3;">√</th>
          <th style="background-color: #d9e2f3;" align="left">意见2</th>
        </tr>
        <tbody>
          <tr>
            <td>
              <#if traineesReviewList?? && (traineesReviewList?size > 0)>
                <#if rejectTraineesReviewList?? && (rejectTraineesReviewList?size > 0)>
                <#else>
                <span>√</span>
                </#if>
              <#else>
              <span></span>
              </#if>
            </td>
            <td>①本单位接受上述<span class="all">全部</span>评审学员在我院评审工作期间观摩学习。</td>
          </tr>
          <tr>
            <td>
              <#if traineesReviewList?? && (traineesReviewList?size > 0) && rejectTraineesReviewList?? && (rejectTraineesReviewList?size > 0)>
                <span>√</span>
              <#else>
                <span></span>
              </#if>
            </td>
            <td>
              <span>②本单位拒绝<#list rejectTraineesReviewList as rejectTraineesReviewer><#if rejectTraineesReviewer_index != rejectTraineesReviewList?size - 1><span>${rejectTraineesReviewer.name}、</span></#if><#if rejectTraineesReviewer_index == rejectTraineesReviewList?size - 1><span>${rejectTraineesReviewer.name}</span></#if></#list>，接受其他评审学员在我院评审工作期间观摩学习。</span>
            </td>
          </tr>
          <tr>
            <td colspan="2" style="font-weight: bold;background-color: #d9e2f3;">拒绝的理由</td>
          </tr>
          <tr>
            <td colspan="2">
              <#if rejectTraineesReviewList?? && (rejectTraineesReviewList?size > 0)>
                <div style="width: 680px;">${refuseStudentReason}</div>
              <#else>
              </#if>
            </td>
          </tr>
        </tbody>
      </table>
      <p>请注意，拒绝的原因应是基于任何已知的困难或利益冲突，并详细说明。</p>
      <p class="mt50">单位（盖公章）：</p>
      <p class="mt30">法定代表人（签字）：</p>
      <p class="mt30">日期：</p>
    </div>
  </body>
</html>
