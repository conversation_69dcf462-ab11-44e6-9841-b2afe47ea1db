<template>
  <div class="review-plan-allot-list">
    <el-form ref="queryForm" :model="queryData" inline label-width="130px">
      <el-form-item label="医院名称：" :maxlength="20" prop="hospitalName">
        <el-input :maxlength="50" v-model.trim="queryData.hospitalName"></el-input>
      </el-form-item>
      <el-form-item label="">
        <el-button type="primary" v-loading="tableLoading" size="small" @click="query">
          查 询
        </el-button>
        <el-button type="primary" size="small" @click="resetForm">
          重 置
        </el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataSource" border stripe v-loading="tableLoading" :row-class-name="getRowClassName" height="670">
      <el-table-column type="expand">
        <template slot-scope="{row}">
          <el-form label-position="left" class="demo-table-expand" size="mini" style="margin-left: 48px;">
            <el-form-item label="医院用户名">
              <span>{{ (row.autRecords || {}).userName || '--' }}</span>
            </el-form-item>
            <el-form-item label="自评编码">
              <span>{{ (row.autRecords || {}).autCode || '--' }}</span>
            </el-form-item>
            <el-form-item label="评审标准版本名">
              <span>{{ (row.autRecords || {}).versionName || '--' }}</span>
            </el-form-item>
            <el-form-item label="更新时间">
              <span>{{ (row.autRecords || {}).updateTime || '--' }}</span>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="150">
        <template slot-scope="scope">
          <div class="table-btn">
            <el-button type="text" v-if="allotTime(scope)" @click="allotReviewCycle(scope.row, 1)">分配评审安排与流程</el-button>
            <el-button type="text" v-if="canResetCycle(scope.row)"
              @click="allotReviewCycle(scope.row, 2)">修改评审安排与流程</el-button>
            <!--  -->

            <el-button type="text" v-if="allotExamine(scope)" @click="toExamine(scope.row)">分配审查员</el-button>
            <el-button type="text" v-if="canAllotReviewer(scope.row)" @click="toReview(scope.row)">{{
              isNotAllot(scope.row) ? '分配评审员' : '评审员分配确认'
            }}</el-button>
            <el-button type="text" v-if="canAllotReviewer(scope.row) || onlyRjRTrainees(scope.row)"
              @click="allotTraineeCycle(scope.row)">分配评审学员</el-button>
            <el-button type="text" v-if="canAllotSeniorReviewer(scope.row)"
              @click="allotSeniorCycle(scope.row)">分配验证评审员</el-button>

            <el-button type="text" v-if="showDetail1(scope)" @click="toDetail1(scope.row)">审查分配详情</el-button>
            <el-button type="text" v-if="showDetail2(scope)" @click="toDetail2(scope.row)">评审分配详情</el-button>

            <!-- <el-button type="text" :disabled="uploading" @click="uploadMeetingFile(scope.row)">上传评审会议</el-button> -->

            <!--  -->
            <template>
              <el-divider v-if="isReportConfirm(scope.row)"></el-divider>
              <el-button type="text" v-if="isReportConfirm(scope.row)" @click="finalCheck(scope.row)">转给审查组长</el-button>

              <template v-if="isFinalProcess(scope.row)">
                <!-- <el-button type="text" v-if="!showMeetingFileId(scope.row)" :disabled="uploading"
                  @click="uploadMeetingFile(scope.row)">上传会议纪要</el-button> -->
                <template v-if="showMeetingFileId(scope.row)">
                  <el-button type="text" @click="finalCheck(scope.row)">驳回给审查组长</el-button>
                  <el-button type="text" @click="processFinish(scope.row)">结束流程</el-button>
                </template>
              </template>
            </template>

            <template v-if="showReviewFileId(scope.row) && showReviewFileUrl(scope.row)">
              <el-divider></el-divider>
              <el-button type="text" @click="previewFile(scope.row.autRecords.reviewReportPdfUrl)">评审报告预览</el-button>
              <el-button type="text"
                @click="downloadFile(scope.row.autRecords.reviewReportPdfFileId, scope.row.autRecords.reviewReportFileName)">评审报告下载</el-button>
            </template>

            <template v-if="showMeetingFileId(scope.row)">
              <el-button type="text" @click="previewFile(meetingUrl(scope.row))">会议纪要预览</el-button>
              <el-button type="text" @click="downloadFile(...meetingIdName(scope.row))">会议纪要下载</el-button>
            </template>

            <template v-if="showVerifyReport(scope.row)">
              <el-button type="text" @click="previewFile(showVerifyReport(scope.row).url)">验证报告预览</el-button>
              <el-button type="text"
                @click="downloadFile(showVerifyReport(scope.row).fileId, showVerifyReport(scope.row).downLoadFileName)">验证报告下载</el-button>
            </template>
          </div>
        </template>
      </el-table-column>
      <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
      <el-table-column prop="hospitalName" label="医院名称" align="center" width="140"></el-table-column>
      <el-table-column prop="" label="审查人员" align="center" width="125">
        <template slot-scope="scope">
          <span v-if="!scope.row.preExamList || !scope.row.preExamList.length">
            --
          </span>
          <span v-else>
            <span v-for="(pre, index) in preExamList(scope.row.preExamList)" :key="index">
              {{ pre.name }} {{ pre.leaderIs == 1 ? "(组长)" : "" }}
              <br />
            </span>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="评审员" align="center" width="185">
        <template slot-scope="scope">
          <span v-if="!scope.row.reviewerList || !scope.row.reviewerList.length">
            --
          </span>
          <span v-else>
            <span v-for="(pre, index) in reviewerList(scope.row.reviewerList)" :key="index">
              {{ pre.name }}{{ pre.firstBatch == 1 ? "(首批评审员)" : ""
              }}{{ pre.leaderIs == 1 ? "(组长)" : "" }}
              <br />
            </span>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="评审学员" align="center" width="100">
        <template slot-scope="scope">
          <span v-if="!scope.row.traineesList || !scope.row.traineesList.length">
            --
          </span>
          <span v-else>
            <span v-for="(pre, index) in traineesListF(scope.row.traineesList)" :key="index">
              {{ pre.name }}
              <br />
            </span>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="验证评审员" align="center" width="100">
        <template slot-scope="scope">
          <span v-if="!scope.row.seniorReviewerUser ||
            !scope.row.seniorReviewerUser.length
            ">
            --
          </span>
          <span v-else>
            <span v-for="(pre, index) in scope.row.seniorReviewerUser" :key="index">
              {{ pre.name }}
              <br />
            </span>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="评审安排与流程" align="center" min-width="375">
        <template slot-scope="scope">
          <div v-if="(scope.row.cycleStatus == 1 || scope.row.cycleStatus == 0) &&
            scope.row.hospitalReviewCycleList &&
            scope.row.hospitalReviewCycleList.length != 0
            " style="text-align: right;">
            <span v-for="(cycle, index) in scope.row.hospitalReviewCycleList" :key="index"
              style="font-size: 12px !important">
              <dict-span dictType="review_cycle" :value="cycle.stageValue" label-width="196px"
                v-if="hasCycle(cycle.stageValue)" :style="{ lineHight: cycle.stageValue == 4 ? '18px' : '' }"
                style="line-height: 20px">
                <span> : {{ cycle.cycle.split(",").join(" ～ ") }}</span>
              </dict-span>
            </span>
          </div>
          <span v-else> -- </span>
        </template>
      </el-table-column>
      <el-table-column prop="creator" label="评审计划操作人员" align="center" width="125"></el-table-column>
      <el-table-column prop="planTime" label="分配计划时间" align="center" width="160"></el-table-column>
      <el-table-column prop="confirmTime" label="确认计划时间" align="center" width="160"></el-table-column>
      <el-table-column prop="" label="状态" align="center" width="150" fixed="right">
        <template slot-scope="scope">
          <div>
            <span v-if="unAllot(scope.row) || allotting(scope.row) || !scope.row.hosStatus
              ">
              <span v-if="unAllot(scope.row)">待分配</span>
              <span class="opt-btn" v-else-if="scope.row.reviewDisComplete == 3" @click="showStatusDialog(scope.row)">评审员上传利益冲突表</span>
              <span class="opt-btn" v-else-if="scope.row.reviewDisComplete == 4" @click="showStatusDialog(scope.row)">待管理员确认评审员</span>
              <span v-else-if="allotting(scope.row)">
                分配中
                <br />
                <br />
                <span v-if="scope.row.cycleStatus == 0">待医院审核评审安排与流程</span>
                <span v-else-if="scope.row.cycleStatus == 2">
                  医院已拒绝当前评审安排与流程
                </span>
                <span class="opt-btn" v-else-if="scope.row.hosStatus != '1' && scope.row.reviewDisComplete == 1" @click="showStatusDialog(scope.row)">
                  <span v-if="scope.row.anuStatus ==
                    AutSaAudCurrentStatusEnum.ADMIN_CONFIRM_ASSESSOR
                    ">
                    待管理员确认评审员(待重新分配评审学员)
                  </span>
                  <span v-else>医院审核评审员</span>
                </span>
              </span>
              <span v-else class="opt-btn" @click="showStatusDialog(scope.row)">
                <span v-if="scope.row.hosStatus != '1' && scope.row.reviewDisComplete == 1" >
                  医院审核评审员
                </span>
                <span v-else>待审核</span>
              </span>
            </span>
            <span v-else-if="scope.row.hosStatus == 2" @click="showStatusDialog(scope.row)">
              <span v-if="scope.row.anuStatus ==
                AutSaAudCurrentStatusEnum.ADMIN_CONFIRM_ASSESSOR
                ">
                分配中
                <br />
                <br />
                <span class="opt-btn">待管理员确认评审员(有评审人员被医院拒绝)</span>
              </span>
              <span v-else class="opt-btn">被拒绝</span>
              <br />
            </span>
          </div>

          <div class="opt-btn" v-if="showStatusText(scope.row)" @click="showStatusDialog(scope.row)">
            {{ AutSaAudCurrentStatusEnum[scope.row.anuStatus] }}
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize"
      @pagination="query" />

    <el-dialog title="分配验证评审员" :visible="seniorVisible" width="40%" destroy-on-close :close-on-click-modal="false"
      @close="handlerClose">
      <div>
        <span>选择验证评审员：</span>
        <el-select v-model.trim="senior" clearable filterable>
          <el-option v-for="(senior, index) in seniorReviewUsers" :key="index" :label="senior.name"
            :value="senior.accountId"></el-option>
        </el-select>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="seniorVisible = false">取 消</el-button>
        <el-button type="primary" @click="seniorSubmit">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="分配评审学员" :visible="traineeVisible" width="40%" destroy-on-close :close-on-click-modal="false"
      @close="handlerClose">
      <div>
        <span>选择评审学员：</span>
        <el-select v-model.trim="traineeId" multiple clearable>
          <el-option v-for="(user, index) in traineesList" :key="index" :label="user.name"
            :value="user.accountId"></el-option>
        </el-select>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="traineeVisible = false">取 消</el-button>
        <el-button v-if="!isOnlyRjByHosp" type="primary" @click="traineeSubmit">确 定</el-button>
        <el-button v-else type="primary" @click="rjTraineeSubmit">确定并提交医院审核</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="cycleTitle" :visible="cycleVisible" width="70%" destroy-on-close :close-on-click-modal="false"
      class="cycleVisible" @close="handlerClose">
      <review-cycle v-if="cycleVisible" ref="reviewCycle" :key="applyNo" :applyNo="applyNo" :time="time" :authStatus="authStatus"
        :cycleStatus="cycleStatus" :progressOptions="progressOptions"></review-cycle>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cycleVisible = false">取 消</el-button>
        <el-button @click="cycleToday"> 今 天(测试) </el-button>
        <el-button type="primary" @click="reviewCycleCheckAndSubmit">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="节点信息" :visible="isShowStatus" width="60%" destroy-on-close :close-on-click-modal="false"
      @close="isShowStatus = false">
      <div class="timeline">
        <div v-for="item in progressOptions" :key="item.currentStatus" :class="['li', {'complete': item.id <= currentId}]" :ref="item.id === currentId? 'complete' : ''">
          <div class="status">
            <h4>{{ item.pageDesc }}</h4>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShowStatus = false">关 闭</el-button>
      </span>
    </el-dialog>

    <upload style="display: none;" ref="managerUpload" accept=".pdf" :files="meetingFiles"
      :data="{ type: '1' }" @fileAdd="fileAdd" @uploadSuccess="onSuccess" @fileRemove="onError" @uploadError="onError"></upload>
  </div>
</template>

<script>
import request, { download } from "@/utils/request";
import { statusListConfig } from "@/api/system/config";
import ReviewCycle from "./reviewCycle.vue";
import DictSpan from "@/components/DetailMessage/dictSpan.vue";

export default {
  name: "ReviewPlanAllotList",
  components: {
    ReviewCycle,
    DictSpan,
  },
  data() {
    return {
      url: {
        list: "/hospital/review/plan/list",
        insert: "/system/cycle/insertHospitalReviewCycleList",
        update: "/system/cycle/updateHospitalReviewCycleList",
      },
      queryData: {
        pageNum: 1,
        pageSize: 10,
        hospitalName: "",
      },
      dataSource: [],
      total: 0,
      cycleVisible: false,
      applyNo: "",
      autCode: "",
      time: [],
      cycleOptions: [],
      seniorVisible: false,
      senior: "",
      seniorReviewUsers: [],
      tableLoading: false,
      authStatus: null,
      cycleTitle: "分配评审安排与流程",

      traineeVisible: false,
      traineesList: [],
      traineeId: [],
      isOnlyRjByHosp: false,

      meetingFiles: [],
      uploading: false,
      wait4UploadProcess: null,
      isShowStatus: false,
      progressOptions: [],
      currentStatus: '',
      currentId: ''
    };
  },

  computed: {
    preDisComplete() {
      return (status) => status && status == "2";
    },
    reviewDisComplete() {
      return (status) => status == 2 || !status;
    },
    seniorReviewDisComplete() {
      return (status) => status == 2 || !status;
    },
    hasCycle() {
      return (value) => {
        return this.cycleOptions.some((option) => option.dictValue == value);
      };
    },

    preExamList() {
      return list => {
        if (list.length === 1 || list.length === 0) return list;
        let list2 = [...list]
        list2.sort((a, b) => {
          return a.leaderIs - b.leaderIs
        })
        return list2
      }
    },

    reviewerList() {
      return list => {
        if (list.length === 1 || list.length === 0) return list;
        let list2 = [...list]
        list2.sort((a, b) => {
          return a.leaderIs - b.leaderIs
        })
        return list2
      }
    },

    traineesListF() {
      return list => {
        return list
        // if (list.length === 1 || list.length === 0) return list;
        // let list2 = [...list]
        // list2.sort((a, b) => {
        //   return a.leaderIs - b.leaderIs
        // })
        // return list2
      }
    },

    showReviewFileId() {
      return row => {
        return row.autRecords?.reviewReportPdfFileId
      }
    },

    showReviewFileUrl() {
      return row => {
        return row.autRecords?.reviewReportPdfUrl
      }
    },

    showMeetingFileId() {
      return row => {
        return row.autRecords?.meetReportFileId
      }
    },

    // 获取queryList接口返回的列表中fileDetailMap中的验证报告
    showVerifyReport() {
      return row => {
        let fileMap = row.fileDetailMap || {};
        let fileMapList = Object.values(fileMap);
        let files = fileMapList.find(files => {
          let file = files.find(file => {
            return file.fileType === 'verify_review_report'
          })
          return file
        })
        let file = (files || [])[0];
        return file;
      }
    },

    meetingUrl() {
      return row => {
        let fileId = this.showMeetingFileId(row)
        let { fileDetailMap } = row
        let files = fileDetailMap[fileId]
        if (!files) return ''
        let file = files[0]
        if (!file) return ''
        return file.url
      }
    },

    meetingIdName() {
      return row => {
        let fileId = this.showMeetingFileId(row)
        let { fileDetailMap } = row
        let files = fileDetailMap[fileId]
        if (!files) return ''
        let file = files[0]
        if (!file) return ''
        return [file.fileId, file.fileName]
      }
    },

    allotTime() {
      return scope => {
        return (scope.row.cycleDisComplete != 1 ||
          scope.row.cycleStatus == 2) &&
          (!scope.row.anuStatus ||
            scope.row.anuStatus == this.AutSaAudCurrentStatusEnum.WAIT_TERM_SA)
      }
    },

    allotExamine() {
      return scope => {
        return scope.row.cycleDisComplete &&
          scope.row.cycleDisComplete == 1 &&
          scope.row.cycleStatus == 1 &&
          scope.row.preDisComplete &&
          scope.row.preDisComplete == '2'
      }
    },

    showDetail1() {
      return scope => {
        return scope.row.cycleDisComplete &&
          scope.row.cycleDisComplete == 1 &&
          scope.row.cycleStatus == 1 &&
          scope.row.preDisComplete &&
          scope.row.preDisComplete == '1'
      }
    },

    showDetail2() {
      return scope => {
        return scope.row.preDisComplete &&
          scope.row.preDisComplete == '1' &&
          scope.row.reviewDisComplete &&
          scope.row.reviewDisComplete == '1'
      }
    },

    onlyRjRTrainees() {
      return (row) => {
        // 仅学员被驳回的情况
        return (
          this.AutSaAudCurrentStatusEnum.ADMIN_CONFIRM_ASSESSOR ===
          row.anuStatus && row.reviewDisComplete == "1"
        );
      };
    },

    userNames() {
      return names => {
        return names.split(',').join(`
        `)
      }
    },

    isNotAllot() {
      return (row) => {
        return row.anuStatus === this.AutSaAudCurrentStatusEnum.ADMIN_ALLOT_ASSESSOR
      }
    },

    canAllotReviewer() {
      return (row) => {
        let statuses = [
          this.AutSaAudCurrentStatusEnum.ADMIN_ALLOT_ASSESSOR,
          this.AutSaAudCurrentStatusEnum.ASSESSOR_WRITE_SHEET,
          this.AutSaAudCurrentStatusEnum.ADMIN_CONFIRM_ASSESSOR,
          this.AutSaAudCurrentStatusEnum.HOSPITAL_AUDIT_ASSESSOR,
        ];
        // 正常分配时 分配学员的按钮展示
        return (
          row.preDisComplete &&
          row.preDisComplete == "1" &&
          row.reviewDisComplete &&
          row.reviewDisComplete != "1" &&
          statuses.includes(row.anuStatus)
        );
      };
    },

    canAllotSeniorReviewer() {
      return (row) => {
        return (
          row.seniorReviewDisComplete &&
          row.seniorReviewDisComplete == "2" &&
          row.cycleStatus == 1
        );
      };
    },

    canResetCycle() {
      return (row) => {
        let {
          cycleDisComplete,
          preDisComplete,
          reviewDisComplete,
          seniorReviewDisComplete
        } = row
        let list = [
          cycleDisComplete,
          preDisComplete,
          reviewDisComplete,
          seniorReviewDisComplete
        ]
        const result = list.every((li) => li !== '1')
        return !Boolean(result) && this.AutSaAudCurrentStatusEnum[row.anuStatus] !== '评审流程结束' && row.cycleStatus !== 2
      }
      // return ({ cycleDisComplete, anuStatus, cycleStatus }) => {
      //   let status_review = [
      //     this.AutSaAudCurrentStatusEnum.WAIT_TERM_SA,
      //     this.AutSaAudCurrentStatusEnum.TERM_SA_PROC,
      //     this.AutSaAudCurrentStatusEnum.WAIT_CONFIRM_SA_SUM,
      //     this.AutSaAudCurrentStatusEnum.WAIT_TERM_SA_SUM,
      //     this.AutSaAudCurrentStatusEnum.TERM_SA_SUM,
      //     this.AutSaAudCurrentStatusEnum.WAIT_AUD_FIRST_TRIAL,
      //     this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_INQUIRE_PROC,
      //     this.AutSaAudCurrentStatusEnum.WAIT_CONFIRM_FIRST_TRIAL,
      //     this.AutSaAudCurrentStatusEnum.WAIT_AUD_FIRST_TRIAL_SUM,
      //     this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_SUM_REJECT,
      //   ];
      //   return (
      //     cycleDisComplete == "1" &&
      //     (anuStatus == null || status_review.includes(anuStatus)) &&
      //     cycleStatus == 1
      //   ) || (
      //       cycleDisComplete == "1" && cycleStatus == 0
      //     );
      // };
    },
    unAllot() {
      return (row) => {
        let {
          cycleDisComplete,
          preDisComplete,
          reviewDisComplete,
          seniorReviewDisComplete,
        } = row;
        let list = [
          cycleDisComplete,
          preDisComplete,
          reviewDisComplete,
          seniorReviewDisComplete,
        ];
        return list.every((li) => li != "1");
      };
    },

    allotting() {
      return (row) => {
        let {
          cycleDisComplete,
          preDisComplete,
          reviewDisComplete,
          seniorReviewDisComplete,
        } = row;
        let list = [
          cycleDisComplete,
          preDisComplete,
          reviewDisComplete,
          seniorReviewDisComplete,
        ];
        return list.some((li) => li != "1");
      };
    },

    showStatusText() {
      return row => {
        if (!row.anuStatus || row.cycleStatus == 2) return false;
        let states = [
          this.AutSaAudCurrentStatusEnum.ASSESSOR_WRITE_SHEET,
          this.AutSaAudCurrentStatusEnum.ADMIN_CONFIRM_ASSESSOR,
          this.AutSaAudCurrentStatusEnum.HOSPITAL_AUDIT_ASSESSOR,
        ]

        if (states.includes(row.anuStatus)) {
          return false
        }

        let examine = row.preDisComplete == 2 && row.anuStatus === '020101'
        let review = row.reviewDisComplete == 2 && row.anuStatus === '030201'
        let senior = row.seniorReviewDisComplete == 2 && row.anuStatus === '040101'

        if (examine || review || senior) {
          return false
        }

        return true;
      }
    },

    isReportConfirm() {
      return ({ anuStatus }) => {
        return anuStatus === this.AutSaAudCurrentStatusEnum.AUD_REPORT_CONFIRM
      }
    },
    isFinalProcess() {
      return ({ anuStatus }) => {
        return anuStatus === this.AutSaAudCurrentStatusEnum.WAIT_ADMIN_PROCESS_FINISH
      }
    },

    // showMeetingUpload() {
    //   return this.isFinalProcess && this.showMeetingFileId
    // }
  },

  mounted() {
    this.initDicts();
    this.query();
    this.getProgressOptions();
    // this.seniorQuery();
  },

  methods: {
    getProgressOptions() {
      statusListConfig().then(response => {
        this.progressOptions = response.rows.filter(item => item.currentStatus !== this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_SUM_REJECT && item.currentStatus !== this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_SUM_CONDITIONAL_PASS ) || [];
      }
      );
    },
    initDicts() {
      this.getDicts("review_cycle").then((res) => {
        // console.log(this.cycleOptions);
        this.cycleOptions = res.data;
      });
    },
    handlerClose() {
      this.cycleVisible = false;
      this.seniorVisible = false;
      this.traineeVisible = false;
      this.senior = "";
      this.traineeId = [];
    },
    query() {
      if (this.tableLoading) return;
      this.tableLoading = true;
      request({
        url:
          this.url.list +
          `?pageNum=${this.queryData.pageNum}&pageSize=${this.queryData.pageSize}`,
        method: "post",
        data: this.queryData,
      })
        .then((res) => {
          if (res.code == 200) {
            let fields = ["creator", "planTime", "confirmTime"];
            this.total = res.total;
            this.dataSource = res.rows.map((row) => {
              for (const key in row) {
                if (
                  Object.hasOwnProperty.call(row, key) &&
                  fields.includes(key) &&
                  !row[key] &&
                  row[key] != 0
                ) {
                  row[key] = "--";
                }
              }
              this.$set(row, 'autRecords', (row?.autSaRelationList || [])[0] || {})
              return row;
            });
          }
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },

    seniorQuery() {
      request({
        // url: "/system/user/list?roleId=106",
        // method: "get",
        url: "/hospital/hos/plan/detail",
        method: "post",
        data: {
          applyNo: this.applyNo,
          versionId: this.versionId,
          personForm: "0",
        },
      }).then((res) => {
        this.seniorReviewUsers = res.seniorAssessorList || [];
        // let fields = ["creator", "planTime", "confirmTime"];
        // this.seniorReviewUsers = res.rows.map((row) => {
        //   for (const key in row) {
        //     if (
        //       Object.hasOwnProperty.call(row, key) &&
        //       fields.includes(key) &&
        //       !row[key] &&
        //       row[key] != 0
        //     ) {
        //       row[key] = "--";
        //     }
        //   }
        //   return row;
        // });
      });
    },

    getTrainees() {
      request({
        url: "/hospital/hos/plan/detail",
        method: "post",
        data: {
          applyNo: this.applyNo,
          versionId: this.versionId,
          personForm: "3",
        },
      }).then((res) => {
        this.traineesList = res.traineesList || [];
      });
    },

    traineesPromise() {
      let requests = []
      if (this.traineeId.length === 0) {
        requests = [
          request({
            url: "/hospital/plan/submit",
            method: "post",
            data: {
              applyNo: this.applyNo,
              personForm: "2",
              fieldIdList: "TRAINEES_REVIEW",
              autCode: this.autCode,
            },
          })
        ]
      } else {
        requests = this.traineeId.map((id) => {
          return request({
            url: "/hospital/plan/submit",
            method: "post",
            data: {
              accountId: id,
              applyNo: this.applyNo,
              personForm: "2",
              fieldIdList: "TRAINEES_REVIEW",
              autCode: this.autCode,
            },
          })
        });
      }


      return Promise.all(requests);
    },

    traineeSubmit() {
      if (this.traineeId.length === 0) {
        return this.$message({
          type: "warning",
          message: "请选择学员！",
        });
      }

      this.traineesPromise().then(() => {
        this.$message({
          type: "success",
          message: "分配评审学员成功",
        });
        this.traineeVisible = false;
        this.traineeId = [];
        this.query();
      });
    },

    async rjTraineeSubmit() {
      if (this.traineeId.length === 0) {
        let result = await new Promise((resolve) => {
          this.$confirm("是否不再分配评审学员?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              resolve(true);
            })
            .catch(() => {
              resolve(false);
            });
        });
        if (!result) return;
      }

      this.traineesPromise().then(() => {
        this.$message({
          type: "success",
          message: "分配评审学员成功",
        });
        this.traineeVisible = false;
        this.traineeId = [];
        this.updateAutStatus(1);
      });
    },

    updateAutStatus(autResult, callback) {
      request({
        url: "/aut/sa/aud/submitAutSaAud",
        method: "post",
        data: {
          time: Date.now(),
          autCode: this.autCode,
          accountId: `${this.$store.getters.userId}`,
          submitType: this.AutSaAudCurrentStatusEnum.NODE_FILP,
          statusProcess: autResult == 1 ? "PS" : "RJ",
          autSaAudLists: [
            {
              autResult,
              autDesc: "--",
            },
          ],
        },
      }).finally(() => {
        this.query();
        this.autCode = "";
        this.applyNo = "";
      });
    },

    seniorSubmit() {
      if (!this.senior) {
        return this.$message({
          type: "warning",
          message: "请选择验证评审员！",
        });
      }

      request({
        url: "/hospital/plan/submit",
        method: "post",
        data: {
          accountId: this.senior,
          applyNo: this.applyNo,
          personForm: "2",
          fieldIdList: "SENIOR_REVIEW",
          autCode: this.autCode,
        },
      }).then((res) => {
        this.$message({
          type: "success",
          message: "分配验证评审员成功",
        });
        this.seniorVisible = false;
        this.query();
      });
    },

    cycleToday() {
      this.$refs.reviewCycle.cycleToday();
    },

    async reviewCycleCheckAndSubmit() {
      const data = await this.$refs.reviewCycle.getValue();
      if (!data) return;
      let url = "";
      if (this.cycleDisComplete == 1 && this.cycleStatus == 1) {
        url = this.url.update;
      } else {
        url = this.url.insert;
      }
      request({
        url: url,
        method: "post",
        data,
      }).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: "success",
            message: "分配评审安排与流程成功",
          });
          this.applyNo = "";
          this.cycleVisible = false;
          this.query();
        }
      });
    },

    resetForm() {
      this.$refs["queryForm"].resetFields();
      this.query();
    },

    allotReviewCycle(data, type) {
      this.cycleTitle = type == 1 ? "分配评审安排与流程" : "修改评审安排与流程";
      this.applyNo = data.applyNo;
      this.time = data.hospitalReviewCycleList || [];
      this.cycleVisible = true;
      this.authStatus = data.anuStatus;
      this.cycleDisComplete = data.cycleDisComplete;
      this.cycleStatus = data.cycleStatus;
    },

    async allotTraineeCycle(data) {
      this.applyNo = data.applyNo;
      this.versionId = data.applyNo;
      this.autCode = data.autCode;

      this.traineeId = "";
      this.getTrainees();
      this.traineeVisible = true;
      this.isOnlyRjByHosp = this.onlyRjRTrainees(data);
    },

    async allotSeniorCycle(data) {
      this.applyNo = data.applyNo;
      this.autCode = data.autCode;
      request({
        url: "/hospital/hos/plan/detail",
        method: "post",
        data: {
          applyNo: this.applyNo,
          versionId: this.versionId,
          personForm: "0",
        },
      }).then((res) => {
        if (!res.code) {
          this.seniorReviewUsers = res.seniorAssessorList || [];
          this.seniorVisible = true;
        } else {
          this.seniorReviewUsers = [];
        }
      });
    },

    toDetail1(allot) {
      this.$router.push({
        path: "/reviewPlanAllot/reviewPlanAllotExamineDetail",
        // path: "/reviewPlanAllot/reviewPlanAllotDetail",
        query: {
          applyNo: allot.applyNo,
          hospitalName: allot.hospitalName,
          personForm: 1,
          anuStatus: allot.anuStatus,
        },
      });
    },

    toDetail2(allot) {
      this.$router.push({
        path: "/reviewPlanAllot/reviewPlanAllotReviewDetail",
        // path: "/reviewPlanAllot/reviewPlanAllotDetail",
        query: {
          applyNo: allot.applyNo,
          hospitalName: allot.hospitalName,
          personForm: 2,
          anuStatus: allot.anuStatus,
          autCode: allot.autCode
        },
      });
    },

    toExamine(allot) {
      this.$router.push({
        path: "/reviewPlanAllot/reviewPlanAllotExamine",
        query: {
          applyNo: allot.applyNo,
        },
      });
    },

    toReview(allot) {
      let path = allot.anuStatus === this.AutSaAudCurrentStatusEnum.ADMIN_ALLOT_ASSESSOR ? "/reviewPlanAllot/reviewPlanAllotReview" : "/reviewPlanAllot/reviewPlanAllotReviewConfirm"
      this.$router.push({
        path,
        query: {
          applyNo: allot.applyNo,
          hospitalName: allot.hospitalName,
          reviewDisComplete: allot.reviewDisComplete
        },
      });
    },

    getRowClassName({ row }) {
      if (row.autSaRelationList && row.autSaRelationList.length > 0) {
        return ''
      } else {
        return 'hide-expand-icon'

      }
    },

    finalCheck({ autCode, anuStatus }) {
      this.$confirm('是否将流程转至审查组长?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        request({
          url: "/aut/sa/aud/submitAutSaAud",
          method: "post",
          data: {
            submitType: anuStatus == this.AutSaAudCurrentStatusEnum.WAIT_ADMIN_PROCESS_FINISH ?
              this.AutSaAudCurrentStatusEnum.SR_E_REPORT_CONFIRM_MEET : this.AutSaAudCurrentStatusEnum.SR_E_REPORT_CONFIRM,
            autCode: autCode,
            accountId: this.$store.getters.userId + "",
            autSaAudLists: [
              {
                autResult: "2",
                autDesc: "--",
              },
            ],
          },
        }).then(() => {
          this.query();
        })
      })
    },

    processFinish({ autCode }) {
      this.$confirm('是否要结束当前评审流程？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        request({
          url: "/aut/sa/aud/submitAutSaAud",
          method: "post",
          data: {
            submitType: this.AutSaAudCurrentStatusEnum.SR_E_REPORT_CONFIRM_MEET,
            autCode: autCode,
            accountId: this.$store.getters.userId + "",
            autSaAudLists: [
              {
                autResult: "1",
                autDesc: "--",
              },
            ],
          },
        }).then(() => {
          this.query();
        })
      })
    },

    uploadMeetingFile(row) {
      this.wait4UploadProcess = row;
      this.$refs.managerUpload.$el.getElementsByClassName('upload-content')[0].click();
    },

    fileAdd() {
      this.uploading = true;
    },

    onSuccess(res) {
      if (this.wait4UploadProcess) {
        this.uploading = false;
        let fileId = res.data.fileId;
        // 审查员上传
        this.shareCreate([fileId], this.$store.getters.userId, 'reviewer_evaluation_party')

        if (fileId) {
          request({
            url: "/system/aut-business/insert",
            method: "post",
            data: {
              autCode: this.wait4UploadProcess.autCode,
              businessCode: 'MEET_REPORT',
              data: fileId
            },
          }).then(() => {
            this.query();
          })
        }
      }
    },

    onError() {
      this.uploading = false;
      this.wait4UploadProcess = null;
    },

    previewFile(url) {
      window.open(url, '_blank')
    },
    downloadFile(fileId, fileName) {
      download("/common/downloadFile", { fileId }, fileName);
    },
    showStatusDialog(row) {
      // 合并相同意义的三个节点
      let statusCode = '';
      if (row.anuStatus === this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_SUM_REJECT || row.anuStatus === this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_SUM_CONDITIONAL_PASS) {
        statusCode = this.AutSaAudCurrentStatusEnum.AUD_PASS
      } else {
        statusCode = row.anuStatus
      }
      let currentProgress = this.progressOptions.filter(item => statusCode === item.currentStatus) || [];
      this.currentId = currentProgress[0] && currentProgress[0].id;
      this.isShowStatus = true;
      this.$nextTick(() => {
        const element = this.$refs['complete'];
        if (element) {
          element[0].scrollIntoView({ behavior: 'smooth', inline: 'center' });
        }
      })
    }
  },
};
</script>

<style lang="scss" scoped>
.review-plan-allot-list {
  background: #fff;
  padding: 24px;

  .table-btn .el-button+.el-button {
    margin-left: 0px;
  }
  .cycleVisible {
    ::v-deep .el-dialog__body {
      padding-bottom: 0;
    }
  }
  .opt-btn {
    color: #1890ff;
    cursor: pointer;
  }
  .timeline {
    list-style-type: none;
    display: flex;
    align-items: center;
    overflow-x: scroll;
    height: 150px;
  }
  .li {
    display: flex;
    flex-direction: column;
    height: 60px;
    &:first-child {
      .status {
        &:before {
          content: "";
          position: absolute;
          top: -2px;
          left: 0;
          width: 50%;
          height: 2px;
          background-color: #fff;
        }
      }
    }
    &:last-child {
      .status {
        &:before {
          content: "";
          position: absolute;
          top: -2px;
          right: 0;
          width: 50%;
          height: 2px;
          background-color: #fff;
        }
      }
    }
  }
  .status {
    width: 150px;
    padding: 0px 10px;
    display: flex;
    justify-content: center;
    border-top: 2px solid #D6DCE0;
    position: relative;
    h4 {
      font-weight: 500;
      line-height: 1.2;
      &:before {
        content: '';
        width: 15px;
        height: 15px;
        background-color: white;
        border-radius: 15px;
        border: 1px solid #ddd;
        position: absolute;
        top: -9px;
        left: 47%;
      }
    }
  }
  .li.complete {
    .status {
      border-top: 2px solid #165dff;
      h4 {
        color: #165dff;
        &:before {
          background-color: #165dff;
          border: none;
        }
      }
    }
  }
  ::v-deep .demo-table-expand .el-form-item--mini.el-form-item {
    margin-bottom: 5px;
  }

  ::v-deep .el-divider--horizontal {
    margin: 5px 0;
  }

  ::v-deep .hide-expand-icon .el-table__expand-icon {
    display: none;
  }
}
</style>
