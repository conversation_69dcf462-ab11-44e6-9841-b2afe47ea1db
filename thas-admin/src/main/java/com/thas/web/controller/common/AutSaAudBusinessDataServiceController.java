package com.thas.web.controller.common;

import com.thas.common.core.domain.AjaxResult;
import com.thas.web.domain.AutSaAudBusinessData;
import com.thas.web.service.IAutSaAudBusinessDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2023/3/27
 */
@RestController
@RequestMapping(value = "/system/aut-business")
public class AutSaAudBusinessDataServiceController {

    @Autowired
    private IAutSaAudBusinessDataService autSaAudBusinessDataService;

    @PostMapping(value = "insert")
    public AjaxResult insert(@RequestBody AutSaAudBusinessData autSaAudBusinessData) {
        return AjaxResult.success(autSaAudBusinessDataService.saveAutSaAudBusinessData(autSaAudBusinessData));
    }
}
