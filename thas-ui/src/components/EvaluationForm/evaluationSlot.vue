<!--  -->
<template>
    <div class='evaluation-audit evaluation' v-loading="submitLoading"  >
        <el-form :label-width="labelWidth"  class="form-item">
            <slot></slot>
        </el-form>
    </div>
</template>
  
<script>
import mixin from "./mixin";

export default {
    name: 'EvaluationSlot',
    components: {},
    mixins: [mixin],
    props: {},
    data() {
        return {

        }
    }
}

</script>
<style lang='scss'></style>