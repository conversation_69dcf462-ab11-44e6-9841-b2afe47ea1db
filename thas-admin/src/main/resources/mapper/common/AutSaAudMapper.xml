<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.AutSaAudMapper">

    <resultMap type="com.thas.web.domain.AutSaAud" id="AutSaAudResult">
        <result property="id" column="id"/>
        <result property="autCode" column="aut_code"/>
        <result property="submitType" column="submit_type"/>
        <result property="clauseId" column="clause_id"/>
        <result property="accountId" column="account_id"/>
        <result property="autResult" column="aut_result"/>
        <result property="beforeAutResult" column="before_aut_result"/>
        <result property="autDesc" column="aut_desc"/>
        <result property="beforeAutDesc" column="before_aut_desc"/>
        <result property="fileIds" column="file_ids"/>
        <result property="submitDate" column="submit_date"/>
        <result property="isShareUpdate"    column="is_share_update"    />
        <result property="proposerIds"    column="proposer_ids"    />
        <result property="shareDesc"    column="share_desc"    />
        <result property="status" column="status"/>
        <result property="createId" column="create_id"/>
        <result property="createBy" column="create_nick_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_nick_name"/>
        <result property="updateId" column="update_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="riskImpact" column="risk_impact"/>
        <result property="riskPossibility" column="risk_possibility"/>
        <result property="reviewerRespond" column="reviewer_respond"/>
    </resultMap>

    <sql id="SQL_AES_ENCRYPT">'${@com.thas.web.config.SqlAesEncryptConfig@SQL_AES_ENCRYPT}'</sql>

    <sql id="selectAutSaAudVo">
        select t.aut_code,
               t.submit_type,
               t.clause_id,
               t.account_id,
               t.aut_result,
               t.before_aut_result,
               t.aut_desc,
               t.before_aut_desc,
               t.file_ids,
               t.submit_date,
               t.is_share_update,
               t.proposer_ids,
               t.share_desc,
               t.status,
               t.risk_impact,
               t.risk_possibility,
               t.reviewer_respond,
               AES_DECRYPT(unhex(tt.nick_name),<include refid="SQL_AES_ENCRYPT"/>) create_nick_name,
               t.create_id cId,
               t.create_time,
               t.update_time
        from aut_sa_aud t left join sys_user tt on t.account_id = tt.user_id
    </sql>

    <select id="selectAutSaAudList" parameterType="com.thas.web.domain.AutSaAud" resultMap="AutSaAudResult">
        <include refid="selectAutSaAudVo"/>
        <where>
            <if test="autCode != null  and autCode != ''">and t.aut_code = #{autCode}</if>
            <if test="submitType != null and submitType != ''">
                AND t.submit_type in
                <foreach collection="submitType.split(',')" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="clauseId != null and clauseId != ''">
                AND t.clause_id in
                <foreach collection="clauseId.split(',')" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="accountId != null  and accountId != ''">and t.account_id = #{accountId}</if>
            <if test="isShareUpdate != null "> and is_share_update = #{isShareUpdate}</if>
            <if test="proposerIds != null "> and proposer_ids = #{proposerIds}</if>
            <if test="status != null">and t.status = #{status}</if>
            <if test="updateTime != null">and t.update_time <![CDATA[>=]]> #{updateTime}</if>
            <if test="crewIds != null and crewIds != ''">
            AND t.account_id in
            <foreach collection="crewIds.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            </if>
        </where>
        ORDER BY t.create_time DESC
    </select>

    <insert id="batchInsertAutSaAud" parameterType="java.util.List">
        INSERT INTO aut_sa_aud (`aut_code`, `submit_type`, `clause_id`, `account_id`, `aut_result`,`before_aut_result`,
        `aut_desc`,`before_aut_desc`,`file_ids`,`status`,`risk_impact`,`risk_possibility`,`reviewer_respond`, `create_id`, `update_id`) VALUES
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.autCode, jdbcType=VARCHAR},
            #{item.submitType, jdbcType=INTEGER},
            #{item.clauseId, jdbcType=VARCHAR},
            #{item.accountId, jdbcType=VARCHAR},
            ifnull(#{item.autResult, jdbcType=VARCHAR}, ''),
            ifnull(#{item.beforeAutResult, jdbcType=VARCHAR}, ''),
            ifnull(#{item.autDesc, jdbcType=VARCHAR}, ''),
            ifnull(#{item.beforeAutDesc, jdbcType=VARCHAR}, ''),
            ifnull(#{item.fileIds, jdbcType=VARCHAR}, ''),
            1,
            ifnull(#{item.riskImpact, jdbcType=VARCHAR}, '0'),
            ifnull(#{item.riskPossibility, jdbcType=VARCHAR}, '0'),
            ifnull(#{item.reviewerRespond, jdbcType=VARCHAR}, ''),
            #{item.accountId, jdbcType=VARCHAR},
            #{item.accountId, jdbcType=VARCHAR})
        </foreach>
    </insert>

    <update id="batchUpdateAutSaAud" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update aut_sa_aud
            <set>
                <if test="item.autResult != null">
                    aut_result = #{item.autResult, jdbcType=VARCHAR},
                </if>
                <if test="item.beforeAutResult != null">
                    before_aut_result = #{item.beforeAutResult, jdbcType=VARCHAR},
                </if>
                <if test="item.autDesc != null">
                    aut_desc = #{item.autDesc, jdbcType=VARCHAR},
                </if>
                <if test="item.beforeAutDesc != null">
                    before_aut_desc = #{item.beforeAutDesc, jdbcType=VARCHAR},
                </if>
                <if test="item.fileIds != null">
                    file_ids = #{item.fileIds, jdbcType=VARCHAR},
                </if>
                <if test="item.riskImpact != null">
                    risk_impact = #{item.riskImpact, jdbcType=VARCHAR},
                </if>
                <if test="item.riskPossibility != null">
                    risk_possibility = #{item.riskPossibility, jdbcType=VARCHAR},
                </if>
                <if test="item.reviewerRespond != null">
                    reviewer_respond = #{item.reviewerRespond, jdbcType=VARCHAR},
                </if>
                <if test="item.status != null">
                    status = #{item.status, jdbcType=TINYINT},
                </if>
                <if test="item.accountId != null">
                    account_id = #{item.accountId, jdbcType=VARCHAR},
                    update_id  = #{item.accountId, jdbcType=VARCHAR},
                </if>
                <if test="item.isShareUpdate != null">is_share_update = #{item.isShareUpdate, jdbcType=TINYINT},</if>
                <if test="item.proposerIds != null">proposer_ids = #{item.proposerIds, jdbcType=VARCHAR},</if>
                <if test="item.shareDesc != null">share_desc = #{item.shareDesc, jdbcType=VARCHAR},</if>
                update_time = sysdate()
            </set>
            where aut_code = #{item.autCode, jdbcType=VARCHAR}
            <if test="item.submitType != null"> and submit_type = #{item.submitType, jdbcType=VARCHAR} </if>
            <if test="item.clauseId != null"> and clause_id = #{item.clauseId, jdbcType=VARCHAR} </if>
            and status = 1
        </foreach>
    </update>

    <update id="batchInvalidAutSaAudByClauseIds" parameterType="java.lang.String">
        update aut_sa_aud set status = 0
        where aut_code = #{autCode}
        <if test="submitTypes != null and submitTypes != ''">
            and submit_type in
            <foreach collection="submitTypes.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="clauseIds != null and clauseIds != ''">
            AND clause_id in
            <foreach collection="clauseIds.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and status = 1
    </update>

    <select id="selectSaAudByAutCode" parameterType="string" resultMap="AutSaAudResult">
        <include refid="selectAutSaAudVo"/>
        where t.aut_code=#{autCode} and t.status = 1 and t.submit_type in(1,4,7)
        order by t.create_time desc
    </select>


    <resultMap type="com.thas.web.domain.vo.AutSaAudDetailListVO" id="AutSaAudDetailListVOResult">
        <result property="applyNo" column="apply_no"/>
        <result property="autCode" column="aut_code"/>
        <result property="hospitalName" column="hospital_name"/>
        <result property="lastStageStatus" column="aut_status"/>
        <result property="isOverdue" column="status"/>
        <result property="autCsId" column="aut_cs_id"/>
        <collection property="autSaAudVoList" javaType="java.util.List"  resultMap="AutSaAudVoResult"/>
    </resultMap>
    <resultMap type="com.thas.web.domain.vo.AutSaAudVo" id="AutSaAudVoResult">
        <result property="submitType" column="submit_type"/>
        <result property="autCode" column="aut_code"/>
        <result property="clauseId" column="clause_id"/>
        <result property="accountId" column="account_id"/>
        <result property="autResult" column="aut_result"/>
        <result property="autDesc" column="aut_desc"/>
        <result property="fileIds" column="file_ids"/>
        <result property="submitDate" column="submit_date"/>
        <result property="status" column="status"/>
    </resultMap>
    <select id="selectAutSaAudDetailListByApplyNo" parameterType="string" resultMap="AutSaAudDetailListVOResult">
        select aar.aut_status,aar.hospital_apply_no apply_no,aar.aut_code,hbi.hospital_name,aaa.submit_type,aaa.clause_id,aaa.account_id,aaa.aut_result,aaa.aut_desc,aaa.file_ids,aaa.submit_date,aar.status
        ,aar.aut_cs_id
        from aut_sa_relation aar
        left join hospital_base_info hbi on aar.hospital_apply_no = hbi.apply_no
        left join aut_sa_aud aaa  on aar.aut_code = aaa.aut_code and aaa.submit_type in('sa_summary','fr_summary','sr_summary','fr_report_r_summary','far_summary','tr_summary')
        where aar.hospital_apply_no = #{applyNo}
        <if test="autCode != null and autCode != ''">
            AND aar.aut_code = #{autCode}
        </if>
        order by aaa.status asc,aaa.submit_type asc,aaa.create_time desc
    </select>

    <update id="invalidAutSaAud" parameterType="java.lang.String">
        update aut_sa_aud
        set status = 0
        where aut_code = #{autCode}
          and status = 1
    </update>

    <select id="selectAutSaAudListByAutCodeAndTypes" parameterType="string" resultMap="AutSaAudResult">
        select t.aut_code, t.submit_type, t.clause_id, t.account_id, t.aut_result,t.before_aut_result, t.aut_desc,t.before_aut_desc, t.file_ids, t.submit_date,t.update_time,
        t.status,t.risk_impact,t.risk_possibility,t.reviewer_respond,t.update_time,
        AES_DECRYPT(unhex(tt.nick_name),<include refid="SQL_AES_ENCRYPT"/>) create_nick_name,
        AES_DECRYPT(unhex(ttt.nick_name),<include refid="SQL_AES_ENCRYPT"/>) update_nick_name,
        t.is_share_update ,t.proposer_ids ,t.share_desc
        from aut_sa_aud t
            left join sys_user tt on t.create_id = tt.user_id
            left join sys_user ttt on t.update_id = ttt.user_id
        where t.aut_code = #{autCode}
        and t.status = 1
        <if test="submitTypes != null and submitTypes != ''">
            AND t.submit_type in
            <foreach collection="submitTypes.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectLatestSaAudListByAutCode" parameterType="string" resultMap="AutSaAudResult">
        select t.aut_code,
               a.submit_type,
               a.clause_id,
               t.account_id,
               t.aut_result,
               t.before_aut_result,
               t.aut_desc,
               t.before_aut_desc,
               t.file_ids,
               t.submit_date,
               t.status,
               t.risk_impact,
               t.risk_possibility,
               t.reviewer_respond,
               t.update_time
        FROM (select submit_type, clause_id, max(id) id from aut_sa_aud where aut_code = #{autCode} GROUP BY submit_type, clause_id) a
                 LEFT JOIN aut_sa_aud t ON t.id = a.id
    </select>

    <select id="selectAutSaAudListByAutCodeAndTypesAndAccountId" parameterType="string" resultMap="AutSaAudResult">
        select t.aut_code, t.submit_type, t.clause_id, t.account_id, t.aut_result,t.before_aut_result, t.aut_desc,t.before_aut_desc, t.file_ids, t.submit_date,t.update_time,
        t.status,t.risk_impact,t.risk_possibility,t.reviewer_respond,t.update_time,
        AES_DECRYPT(unhex(tt.nick_name),<include refid="SQL_AES_ENCRYPT"/>) create_nick_name,
        AES_DECRYPT(unhex(ttt.nick_name),<include refid="SQL_AES_ENCRYPT"/>) update_nick_name,
        t.is_share_update ,t.proposer_ids ,t.share_desc
        from aut_sa_aud t
        left join sys_user tt on t.create_id = tt.user_id
        left join sys_user ttt on t.update_id = ttt.user_id
        where t.aut_code = #{autCode}
        and t.status = 1
        <if test="submitTypes != null and submitTypes != ''">
            AND t.submit_type in
            <foreach collection="submitTypes.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="accountId != null and accountId != ''"> AND t.account_id = #{accountId} </if>

    </select>

</mapper>
