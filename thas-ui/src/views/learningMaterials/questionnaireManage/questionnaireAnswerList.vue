<template>
  <el-card v-loading="loading" class="questionnaire-answer-list">
    <el-row :gutter="24">
      <el-col :span="24" :xs="24">
        <el-form ref="queryForm" :model="queryData" size="small" inline>
          <el-form-item label="问卷：" prop="questionnaireId">
            <el-select v-model.trim="queryData.questionnaireId" placeholder="请选择问卷" @change="paperChange" clearable filterable>
              <el-option v-for="(item, index) in questionnaireOptions" :key="index" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item>
            <el-button size="small" @click="back">返回</el-button>
          </el-form-item> -->
        </el-form>
      </el-col>
    </el-row>
    <el-row :gutter="24" style="margin-top: 24px">
      <el-col :span="24">
        <el-table :data="dataSource" border>
          <el-table-column align="center" label="操作" width="200">
            <template slot-scope="scope">
              <el-button v-if="scope.row.type && !isNormal(scope.row.type)" type="text" @click="toDetail(scope.row)">答卷详情</el-button>
              <el-button v-if="scope.row.status == 0 && isNormal(scope.row.type)" type="text" @click="option(scope.row)">还原</el-button>
              <el-button v-else-if="scope.row.status == 1 && isNormal(scope.row.type)" type="text" @click="option(scope.row)">删除</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" label="序号" type="index" :index="indexMethod"></el-table-column>
          <el-table-column align="center" label="答题人" prop="createBy"></el-table-column>
          <el-table-column align="center" label="状态" prop="status">
            <template slot-scope="scope">
              <span v-if="!isNormal(scope.row.type)">
                <span v-if=" scope.row.status == 1" style="color: #ff0000;">无效</span>
                <span v-else-if="scope.row.status == 0" style="color: #00ae22;">有效</span>
              </span>
              <span v-else>
                <span v-if=" scope.row.status == 0" style="color: #ff0000;">无效</span>
                <span v-else-if="scope.row.status == 1" style="color: #00ae22;">有效</span>
              </span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="提交时间" prop="createTime"></el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query" />
      </el-col>
    </el-row>
    <el-dialog title="提示" :visible.sync="dialogVisible" width="60%">
      <div style="margin-bottom: 26px; margin-top: 10px; text-align: center;">您确定将该答卷{{ current && current.status == 0 ? '恢复为有效' : '标记为无效' }}答卷吗？</div>
      <div slot="footer" class="dialog-footer" style="text-align: center;">
        <el-button type="primary" @click="toggleStatus">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="答卷详情" :visible.sync="detailDialog" width="60%">
      <div class="detail" style="height: 500px; overflow-y: scroll;">
        <div class="detail-content">
          <div v-if="isNormal(answerDetail_type)">
            <div class="detail-top">
              <div class="top-item">
                <span>序号：</span>
                <span>{{ answerDetail.id }}</span>
              </div>
              <div class="top-item">
                <span>答题人：</span>
                <span>{{ answerDetail.createBy }}</span>
              </div>
              <div class="top-item">
                <span>状态：</span>
                <span>{{ answerDetail.status == 1 ? "有效" : "无效" }}</span>
              </div>
              <div class="top-item">
                <span>填写时间：</span>
                <span>{{ answerDetail.createTime }}</span>
              </div>
            </div>
            <div v-for="(item, index) in answerDetail.data" :key="index" class="content-item">
              <div class="title">
                <span v-if="item.hasRequired == 1" class="required">*</span>
                <span class="questionnaire">{{ item.question }}</span>
              </div>
              <div v-if="item.type != 2" class="content">{{ item.answer }}</div>
              <div v-else class="content">
                <div v-for="(answerItem, i) in JSON.parse(item.answer)" :key="i">{{ answerItem }}</div>
              </div>
            </div>
          </div>
          <div v-else>
            <FeedbackDetail :key="answerDetail.id" :detail="answerDetail"></FeedbackDetail>
          </div>
        </div>
      </div>
      <el-divider></el-divider>
      <div slot="footer" class="dialog-footer" style="margin-top: 40px; text-align: left;">
        <el-button @click="detailDialog = false">返回</el-button>
        <!-- <el-button @click="option(answerDetail)">删除</el-button> -->
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import request from '@/utils/request'
import FeedbackDetail from './feedback/feedbackDetail.vue'
export default {
  name: "QuestionnaireAnswerList",
  components: {
    FeedbackDetail
  },
  data() {
    return {
      dataSource: [],
      queryData: {
        questionnaireId: null,
        pageSize: 10,
        pageNum: 1
      },
      total: 0,
      props: { multiple: true },
      questionnaireOptions: [],
      loading: false,
      dialogVisible: false,
      current: null,
      detailDialog: false,
      answerDetail: {},
      questionnaireClassifyOptions: [],
      feedbackClassifyOptions: [],
      answerDetail_type: '',
    }
  },
  computed: {
    isNormal() {
      return type => {
        if (!type) return null;
        return this.questionnaireClassifyOptions.some(({ dictValue }) => dictValue == type);
      }
    },

    classifyOption() {
      return [...this.questionnaireClassifyOptions, ...this.feedbackClassifyOptions]
    }
  },
  created() {
    this.initDicts()
  },
  methods: {
    initDicts() {
      request({
        url: '/system/questionnaire/list',
        method: "get"
      }).then((res) => {
        if (res.code != 200) return

        this.questionnaireOptions = res.rows.map((item) => {
          return {
            value: item.id,
            label: item.title,
            type: item.type
          }
        })
      })

      this.getDicts("questionnaire_classify").then((response) => {
        this.questionnaireClassifyOptions = response.data;
      });
      this.getDicts("feedback_classify").then((response) => {
        this.feedbackClassifyOptions = response.data;
      });
    },

    search() {
      this.queryData.pageNum = 1
      this.query()
    },

    query(type) {
      this.loading = true
      request({
        url: '/system/answer/list',
        method: 'get',
        params: { ...this.queryData, type }
      }).then((res) => {
        if (res.code != 200) return
        this.dataSource = res.rows
        this.total = res.total
      }).finally(() => {
        this.loading = false
      })

    },

    resetForm() {
      this.$refs["queryForm"].resetFields();
      this.query();
    },

    toDetail(data) {
      try {
        this.current = data
        this.answerDetail_type = data.type

        if (this.isNormal(data.type)) {
          request({
            url: '/system/answer/detail',
            method: 'get',
            params: { questionnaireId: '', answerId: data.id }
          }).then((res) => {
            if (res.code != 200) return
            this.detailDialog = true
            this.answerDetail = res.data
          }).catch((error) => {
            // console.log(error)
          })
        } else {
          this.answerDetail = data;
          this.detailDialog = true
        }
      } catch (error) {
        // console.log(error);
      }
    },

    option(data) {
      this.current = data
      this.dialogVisible = true
    },

    toggleStatus() {
      request({
        url: '/system/answer/status',
        method: 'get',
        params: { id: this.current.id, status: this.current.status == 0 ? 1 : 0 }
      }).then((res) => {
        if (res.code != 200) return
        this.$message({
          type: 'success',
          message: this.current.status == 0 ? '恢复成功！' : '标记成功！'
        })
        this.dialogVisible = false
        this.detailDialog = false
        this.query()
      }).catch((error) => {
        // console.log(error)
      })
    },

    paperChange(questionnaireId) {
      this.queryData.questionnaireId = questionnaireId;
      let questionnaire = this.questionnaireOptions.find(({ value }) => value == questionnaireId)
      this.query(questionnaire?.type)
    },

    back() {
      this.$store.dispatch('tagsView/delView', this.$route) &&
        this.$router.push({ path: '/questionnaireManage/questionnaireList' })
    },

    indexMethod(index) {
      return (this.queryData.pageNum - 1) * this.queryData.pageSize + index + 1
    }
  }
}
</script>

<style lang="scss" scoped>
.questionnaire-answer-list {
  padding: 24px;
  ::v-deep .el-dialog {
    .el-dialog__header {
      background-color: #3c81f5;
      padding-top: 10px;
      .el-dialog__title {
        color: white;
      }
      .el-dialog__headerbtn {
        top: 15px;
        .el-dialog__close {
          color: white;
        }
      }
    }
    .el-dialog__body {
      padding-bottom: 0;
      & > span {
        color: black;
      }
    }
    .el-divider {
      margin-bottom: 10px;
    }
  }
  .detail {
    .detail-top {
      display: flex;
      border-bottom: 3px solid #58a6fe;
      background-color: #f4f4f4;
      .top-item {
        padding: 0 26px;
        line-height: 48px;
      }
    }
    .detail-content {
      .content-item {
        padding-bottom: 20px;
        border-bottom: 1px dashed #d2d4d6;
        .title {
          margin: 0;
          font-size: 16px;
          font-weight: bold;
          line-height: 48px;
          text-align: left;
          .required {
            margin-right: 5px;
            color: #ff0000;
          }
          .questionnaire {
            color: #000;
          }
        }
        .content {
          font-size: 16px;
          color: #666666;
        }
      }
    }
  }
}
</style>
