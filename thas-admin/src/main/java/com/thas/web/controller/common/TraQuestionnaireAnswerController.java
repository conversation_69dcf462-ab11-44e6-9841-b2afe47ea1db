package com.thas.web.controller.common;

import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.page.TableDataInfo;
import com.thas.common.enums.BusinessType;
import com.thas.web.domain.TraQuestionnaireAnswer;
import com.thas.web.domain.vo.TraQuestionnaireAnswerRes;
import com.thas.web.service.ITraQuestionnaireAnswerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * 调查问卷答卷Controller
 *
 * <AUTHOR>
 * @date 2022-02-14
 */
@RestController
@RequestMapping("/system/answer")
@Api(value = "API - TraQuestionnaireAnswerController", tags = "培训 - 调查问卷答卷")
public class TraQuestionnaireAnswerController extends BaseController {
    @Autowired
    private ITraQuestionnaireAnswerService traQuestionnaireAnswerService;

    /**
     * 查询调查问卷答卷列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TraQuestionnaireAnswerRes traQuestionnaireAnswer) {
        startPage();
        List<TraQuestionnaireAnswerRes> list = traQuestionnaireAnswerService.selectTraQuestionnaireAnswerList(traQuestionnaireAnswer);
        return getDataTable(list);
    }

    /**
     * 获取调查问卷答卷详细信息
     */
    @GetMapping(value = "/detail")
    @ApiOperation(httpMethod = "GET", value = "获取调查问卷答卷详细信息")
    public AjaxResult getInfo(@ApiParam(value = "问卷id", name = "questionnaireId") @RequestParam("questionnaireId") String questionnaireId, @ApiParam(value = "答卷id", name = "answerId") @RequestParam("answerId") String answerId) {
        if (Objects.isNull(questionnaireId) && Objects.isNull(answerId)) {
            throw new IllegalArgumentException("参数异常,请检查后提交");
        }
        return AjaxResult.success(traQuestionnaireAnswerService.selectTraQuestionnaireAnswerById(questionnaireId, answerId));
    }

    /**
     * 新增调查问卷答卷
     */
    @Log(title = "新增调查问卷答卷", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(httpMethod = "POST", value = "新增调查问卷答卷")
    public AjaxResult add(@RequestBody TraQuestionnaireAnswer traQuestionnaireAnswer) {
        return toAjax(traQuestionnaireAnswerService.insertTraQuestionnaireAnswer(traQuestionnaireAnswer));
    }

    /**
     * 修改调查问卷答卷状态
     */
    @Log(title = "修改调查问卷答卷状态", businessType = BusinessType.INSERT)
    @GetMapping("/status")
    @ApiOperation(httpMethod = "GET", value = "修改调查问卷答卷状态")
    public AjaxResult updateStatus(@ApiParam(value = "0:删除, 1:还原", name = "status") @RequestParam Integer status, @ApiParam(value = "问卷id", name = "id") @RequestParam Long id) {
        return toAjax(traQuestionnaireAnswerService.updateStatus(status, id));
    }

    /**
     * 删除调查问卷答卷
     */
    @Log(title = "删除调查问卷答卷", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    @ApiOperation(httpMethod = "DELETE", value = "删除调查问卷答卷")
    public AjaxResult delete(@ApiParam(value = "问卷id", name = "id") @PathVariable("id") Long id) {
        return toAjax(traQuestionnaireAnswerService.delete(id));
    }
}
