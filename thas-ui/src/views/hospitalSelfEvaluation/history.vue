<template>
  <div class="hospital-self-evaluation-history">
    <el-table :data="dataSource" stripe border>``
      <el-table-column label="操作" align="center" width="120">
        <template slot-scope="scope">
          <el-button v-if="scope.row.isOverdue != 1" type="text" @click="toDetail(scope.row.autCode)">查看详情</el-button>
          <template v-else-if="scope.row.isOverdue == 1">
            <el-button v-if="scope.row.autSaAudReport" type="text"
            @click="toReport(scope.row)">查看报告结果</el-button>
            <el-button v-if="showReport(scope.row)" type="text"
              @click="previewReport(scope.row)">预览评审报告</el-button>
            <el-button v-if="showReport(scope.row)" type="text"
              @click="downloadReport(scope.row)">下载评审报告</el-button>
          </template>
          <span v-else> -- </span>
        </template>
      </el-table-column>
      <el-table-column label="评审机构" width="160" align="center" prop="hospitalName"></el-table-column>
      <el-table-column label="自评完成时间" align="center" prop="hospitalName">
        <template slot-scope="scope">
          {{ getTime(scope.row, AutSaAudCurrentStatusEnum.SA_SUMMARY) }}
        </template>
      </el-table-column>
      <el-table-column label="审查完成时间" align="center" prop="hospitalName">
        <template slot-scope="scope">
          {{ getTime(scope.row, AutSaAudCurrentStatusEnum.FR_REPORT_SUBMIT) }}
        </template>
      </el-table-column>
      <el-table-column label="评审完成时间" align="center" prop="hospitalName">
        <template slot-scope="scope">
          {{ getTime(scope.row, AutSaAudCurrentStatusEnum.SR_SUMMARY) }}
        </template>
      </el-table-column>
      <el-table-column label="审查评审报告完成时间" align="center">
        <template slot-scope="scope">{{ getTime(scope.row, AutSaAudCurrentStatusEnum.FR_REPORT_R_SUMMARY) }} </template>
      </el-table-column>
      <el-table-column label="事实准确性确认完成时间" align="center">
        <template slot-scope="scope">{{ getTime(scope.row, AutSaAudCurrentStatusEnum.FAR_SUMMARY) }} </template>
      </el-table-column>
      <el-table-column label="验证评审完成时间" align="center">
        <template slot-scope="scope"> {{ getTime(scope.row, AutSaAudCurrentStatusEnum.TR_SUMMARY) }} </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="200">
        <template slot-scope="scope">
          <span v-if="scope.row.isOverdue == 1">
            {{ AutSaAudCurrentStatusEnum[scope.row.lastStageStatus] }}
          </span>
          <span v-else>
            <p style="color:red;margin:0">评审过期</p>
            （ 终止于：<span style="color:red">
              {{ AutSaAudCurrentStatusEnum[scope.row.lastStageStatus] }}
            </span>
            ）
          </span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import request, { download } from "@/utils/request";

export default {
  name: "HospitalSelfEvaluationHistory",
  data() {
    return {
      url: {
        list: "/aut/sa/aud/selectAutSaAudDetailList",
      },
      dataSource: [],
    };
  },

  components: {},

  computed: {
    getTime() {
      return ({ autSaAudVoList }, type) => {
        if (!autSaAudVoList) return '--';
        let data = autSaAudVoList.find(vo => vo.submitType == type);
        return data ? data.submitDate : '--'
      }
    },
    // 获取queryList接口返回的列表中fileDetailMap中评审报告
    showReport() {
      return row => {
        let fileMap = row.fileDetailMap || {};
        let fileMapList = Object.values(fileMap);
        let files = fileMapList.find(files => {
          let file = files.find(file => {
            return file.fileType === 'review_report'
          })
          return file
        })
        let file = (files || [])[0];
        return file;
      }
    },
  },

  mounted() {
    this.query();
  },

  methods: {
    query() {
      request({
        url: this.url.list,
        method: "get",
      }).then((res) => {
        if (res.code == 200) {
          this.dataSource = res.data.map((item) => {
            const list = item.autSaAudVoList || [];
            const timeMap = {};
            list.forEach((vo) => {
              item[vo.type] = vo.submitDate;
            });
            return item;
          });
        }
      });
    },
    toDetail(autCode) {
      this.$router.push({
        path: "/hospitalSelfEvaluation/history",
        query: {
          autCode,
        },
      });
    },

    toReport({ autCode, autSaAudReport }) {
      let name =
        autSaAudReport.autSaAudResult == 1
          ? "BaseClausePass"
          : "BaseClauseReject";
      this.$router.push({
        name,
        query: {
          autCode,
          isHospital: true
        },
      });
    },
    previewReport(row) {
      let file = this.showReport(row);
      window.open(file.url, '_blank')
    },

    downloadReport(row) {
      let file = this.showReport(row);
      download("/common/downloadFile", { fileId: file.fileId },  file.reviewReportPdfFileName);
    },
  },
};
</script>
<style lang='scss' scoped>
.hospital-self-evaluation-history {
  padding: 24px;
  background-color: #fff;
}
.el-button + .el-button {
  margin-left: 0px;
}
</style>
