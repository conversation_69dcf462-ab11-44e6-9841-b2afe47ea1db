<!DOCTYPE html>
<html lang="en" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word"
	xmlns:dt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882" xmlns="http://www.w3.org/TR/REC-html40">

	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<meta name="ProgId" content="Word.Document" />
		<meta name="Generator" content="Microsoft Word 14" />
		<meta name="Originator" content="Microsoft Word 14" />
		<title></title>
		<style>
			@font-face {
				font-family: "SimSun";
			}

			@page {
				@top-left {
					content: element(header);
				}

				size: A4;
			}

			body {
				font-family: "SimSun";
			}

			table {
				page-break-inside: auto;
				-fs-table-paginate: paginate;
				border-spacing: 0;
				table-layout: fixed;
				word-break: break- strict;
			}

			table tr {
				page-break-inside: avoid;
				page-break-after: auto;
			}
		</style>
		<style>
			* {
				margin: 0;
				padding: 0;
				box-sizing: border-box;
			}

			h3 {
				margin-top: 20px;
				margin-bottom: 20px;
			}

			.w80p {
				font-size: 12px;
			}

			.font14 {
				font-size: 14px;
			}

			.font12 {
				font-size: 12px;
			}

			.lh28 {
				line-height: 28px;
			}

			table {
				width: 100%;
			}

			.m0 {
				margin: 0;
			}

			.ml20 {
				margin-left: 20px;
			}

			#content {
				overflow: hidden;
			}

			#header>span:first-child {
				margin-left: 20px;
				float: left;
			}

			#header>span:last-child {
				margin-right: 20px;
				float: right;
			}

			#image img {
				width: 40%;
			}

			#title {
				padding: 10px 0;
				margin: 0 auto;
				margin-top: 40px;
				color: white;
				background-color: #31849b;
				overflow: hidden;
			}

			#title p {
				text-align: center;
				margin: 5px;
				color: white;
				padding: 0 10px;
				font-size: 30px;
			}

			#title p:first-child {
				margin-left: 10px;
				margin-right: 10px;
			}

			td {
				max-width: 80px;
				word-break: break-all;
				word-wrap: break-word;
				padding: 5px;
				box-sizing: border-box;
				min-height: 30px;
				font-size: 12px;
			}

			#institutional-info-table td,
			#clinical-services-table td,
			#medical-technology-service-table td,
			#filling-instructions-table td {
				box-sizing: border-box;
				padding: 5px;
				font-size: 12px;
			}

			#filling-instructions-table td {
				min-height:20px;
				padding-left: 5px;
				border-top: 1px dashed #c8c8c8;
				border-right: 1px dashed #c8c8c8;
				word-break: break-all;
			}

			#filling-instructions-table td:nth-child(1) {
				border-left: 1px dashed #c8c8c8;
			}

			#filling-instructions-table tr:last-child td {
				border-bottom: 1px dashed #c8c8c8;
			}

			#filling-instructions-table tr:first-child td {
				border-top: 1px solid #000;
			}

			sup {
				font-size: 14px;
			}

			#institutional-info-table {
				table-layout: fixed;
			}

			#institutional-info-table .hide-tr td {
				height: 0;
				padding: 0;
				border: none;
			}
		</style>
	</head>

	<body>
		<div id="content">
			<div id="image">
				<img src="aud_sa_report_image/aud_sa_report_image.png" />
			</div>
			<div id="title">
				<p>深圳市卫健医院评审评价研究中心</p>
				<p>《认证评审申请表》</p>
			</div>
			<table id="institutional-info-table" class="m0" cellpadding="0" cellspacing="0" border="0">
				<tr>
					<td style="text-indent:20px">
						医院认证评审申请表所提供信息必须真实、准确。医院提交申请表后，如表中所记录的信息发生变化时，医院必须在发生变更后30天内或者于预定现场评审日期前30天内通知深圳市卫健医院评审评价研究中心（下述简称“中心”）。
					</td>
				</tr>
				<tr>
					<td style="text-indent:20px">
						医院在申请及评审认证过程中，如有虚假和伪造与认证有关信息的，一经核实，终止评审认证；如已授予认证的，将撤回认证证书，并在一年内失去参加评审认证资格。
					</td>
				</tr>
			</table>

			<div class="institutional-info w80p">
				<table id="institutional-info-table" class="m0" cellpadding="0" cellspacing="0" border="1">
					<tbody>
						<tr>
							<td colspan="24" class="font14" style="font-weight: 600;">1. 机构信息</td>
						</tr>
						<tr>
							<td colspan="5">医疗机构名称</td>
							<td colspan="13">${hospitalBaseInfo.hospitalName}</td>
							<td colspan="6">
								<div style="white-space: nowrap;">开业/院日期：${hospitalBaseInfo.businessTime}</div>
							</td>
						</tr>
						<tr>
							<td colspan="24">医疗机构执业许可证登记号（如适用）：${hospitalBaseInfo.practiceLicenseNo}</td>
						</tr>
						<tr>
							<td colspan="24">地址： ${hospitalBaseInfo.hospitalAddress}</td>
						</tr>
						<tr>
							<td colspan="2">电话</td>
							<td colspan="3">
                ${hospitalBaseInfo.hospitalPhone}<#if hospitalBaseInfo.hospitalExtPhone??>-${hospitalBaseInfo.hospitalExtPhone}</#if>
              </td>
							<td colspan="3">传真</td>
							<td colspan="3">${hospitalBaseInfo.hospitalFax}</td>
							<td colspan="3">医院网址</td>
							<td colspan="3">${hospitalBaseInfo.hospitalOfficialWebsite}</td>
							<td colspan="3">邮政编码</td>
							<td colspan="4">${hospitalBaseInfo.postalOde}</td>
						</tr>
						<tr>
							<td colspan="2">联系人</td>
							<td colspan="3">${hospitalBaseInfo.hospitalContacts}</td>
							<td colspan="3">联系电话</td>
							<td colspan="3">${hospitalBaseInfo.contactsPhone}</td>
							<td colspan="3">联系邮箱</td>
							<td colspan="3">${hospitalBaseInfo.contactsEmail}</td>
							<td colspan="3">通讯地址</td>
							<td colspan="4">${hospitalBaseInfo.postalAddress}</td>
						</tr>
						<tr>
							<td colspan="5" rowspan="3">
								<#if (hospitalLegalPerson.legalPersonType==1)> √法定代表人 □院长 ： </#if>
								<#if (hospitalLegalPerson.legalPersonType==2)> □法定代表人 √院长 ： </#if>
							</td>
							<td colspan="19">
								<span>姓名：</span>
								<span style="text-decoration: underline;">${hospitalLegalPerson.legalPersonName}</span>
								<span>性别：</span>
								<#if hospitalLegalPerson.gender==0>
									<span style="text-decoration: underline;">男</span>
								</#if>
								<#if hospitalLegalPerson.gender==1>
									<span style="text-decoration: underline;">女</span>
								</#if>
								<#if hospitalLegalPerson.gender==2>
									<span style="text-decoration: underline;">未知</span>
								</#if>
								<span>职务：</span>
								<span style="text-decoration: underline;">${hospitalLegalPerson.legalPersonPost}</span>
								<span>职称：</span>
								<span style="text-decoration: underline;">${hospitalLegalPerson.legalPersonTitle}</span>
							</td>
						</tr>
						<tr>
							<td colspan="19">
								<span>证件类型：</span>
								<span style="text-decoration: underline;">
									<#if (hospitalLegalPerson.certificateType==1)>
										身份证
									</#if>
									<#if (hospitalLegalPerson.certificateType==2)>
										护照
									</#if>
									<#if (hospitalLegalPerson.certificateType==3)>
										回乡证
									</#if>
									<#if (hospitalLegalPerson.certificateType==4)>
										香港身份证
									</#if>
									<#if (hospitalLegalPerson.certificateType==5)>
										澳门身份证
									</#if>
									<#if (hospitalLegalPerson.certificateType==6)>
										台湾身份证
									</#if>
								</span>
								<span> 证件号：</span>
								<span
									style="text-decoration: underline;">${hospitalLegalPerson.certificateNumber}</span>
							</td>
						</tr>
						<tr>
							<td colspan="19">
								<span>电话：</span>
								<span style="text-decoration: underline;">
                  ${hospitalLegalPerson.legalPersonPhone}<#if hospitalLegalPerson.legalPersonExtPhone??>-${hospitalLegalPerson.legalPersonExtPhone}</#if>
                </span>
								<span>传真：</span>
								<span style="text-decoration: underline;">${hospitalLegalPerson.legalPersonFax}</span>
								<span>手机：</span>
								<span
									style="text-decoration: underline;">${hospitalLegalPerson.legalPersonMobile}</span>
								<span>邮箱：</span>
								<span style="text-decoration: underline;">${hospitalLegalPerson.legalPersonEmail}</span>
							</td>
						</tr>
						<tr>
							<td colspan="5">医疗机构类别</td>
							<td colspan="19">
								<#if hospitalBaseInfo.hospitalType==1> √综合医院 □专科医院 ： </#if>
								<#if hospitalBaseInfo.hospitalType==2> □综合医院 √专科医院 ： </#if>
							</td>
						</tr>
						<tr>
							<td colspan="5">所有制形式</td>
							<td colspan="19">
								<div>
									<#if hospitalBaseInfo.formOwnership==1> √全民 □集体 □私人 □股份 □中外合资（合作）
										<br /><br />□其他：_____
									</#if>
									<#if hospitalBaseInfo.formOwnership==2> □全民 √集体 □私人 □股份 □中外合资（合作）
										<br /><br />□其他：_____
									</#if>
									<#if hospitalBaseInfo.formOwnership==3> □全民 □集体 √私人 □股份 □中外合资（合作）
										<br /><br />□其他：_____
									</#if>
									<#if hospitalBaseInfo.formOwnership==4> □全民 □集体 □私人 √股份 □中外合资（合作）
										<br /><br />□其他：_____
									</#if>
									<#if hospitalBaseInfo.formOwnership==5> □全民 □集体 □私人 □股份 √中外合资（合作）
										<br /><br />□其他：_____
									</#if>
									<#if hospitalBaseInfo.formOwnership==6> □全民 □集体 □私人 □股份 □中外合资（合作）
										√其他：${hospitalBaseInfo.formOwnershipOther}</#if>
								</div>
							</td>
						</tr>
						<tr>
							<td colspan="5">经营性质</td>
							<td colspan="19">
								<#if hospitalBaseInfo.natureOperation==1> √政府举办非营利性 □非政府举办非营利性 □营利性</#if>
								<#if hospitalBaseInfo.natureOperation==2> □政府举办非营利性 √非政府举办非营利性 □营利性</#if>
								<#if hospitalBaseInfo.natureOperation==3> □政府举办非营利性 □非政府举办非营利性 √营利性</#if>
							</td>
						</tr>
						<tr>
							<td colspan="5">管理隶属关系</td>
							<td colspan="19">
								<#if hospitalBaseInfo.managementAffiliation==1> √部属（管） □省自治区、直辖市属 □省辖市区、地辖市属
									<br /><br />□县(旗)属 □其他：
								</#if>
								<#if hospitalBaseInfo.managementAffiliation==2> □部属（管） √省自治区、直辖市属 □省辖市区、地辖市属
									<br /><br />□县(旗)属 □其他：
								</#if>
								<#if hospitalBaseInfo.managementAffiliation==3> □部属（管） □省自治区、直辖市属 √省辖市区、地辖市属
									<br /><br />□县(旗)属 □其他：
								</#if>
								<#if hospitalBaseInfo.managementAffiliation==4> □部属（管） □省自治区、直辖市属 □省辖市区、地辖市属
									<br /><br />√县(旗)属 □其他：
								</#if>
								<#if hospitalBaseInfo.managementAffiliation==5> □部属（管） □省自治区、直辖市属 □省辖市区、地辖市属
									<br /><br />□县(旗)属
									√其他：<span
										style="text-decoration: underline;">${hospitalBaseInfo.managementAffiliationOther}</span>
								</#if>
							</td>
						</tr>
						<tr>
							<td colspan="5">主管单位名称</td>
							<td colspan="19">${hospitalBaseInfo.competentAuthorityName}</td>
						</tr>
						<tr>
							<td colspan="5">医院目前级别</td>
							<td colspan="19">
								<div>
									<#if hospitalBaseInfo.hospitalLevel==3> √三级医院</#if>
									<#if hospitalBaseInfo.hospitalLevel!=3> □三级医院</#if>
								</div>
								<br />
								<div>
									<#if hospitalBaseInfo.hospitalLevel==2> √二级医院</#if>
									<#if hospitalBaseInfo.hospitalLevel!=2> □二级医院</#if>
								</div>
								<br />
								<div>
									<#if hospitalBaseInfo.hospitalLevel==1> √一级医院</#if>
									<#if hospitalBaseInfo.hospitalLevel!=1> □一级医院</#if>
								</div>
								<br />
								<div>
									<#if hospitalBaseInfo.hospitalLevel==4> √未分级/不适用</#if>
									<#if hospitalBaseInfo.hospitalLevel!=4> □未分级/不适用</#if>
								</div>
							</td>
						</tr>
						<tr>
							<td colspan="5">教学类别</td>
							<td colspan="19">
								<div>
									<#if hospitalBaseInfo.teachingCategory==1>√大学直属附属医院 □大学非直属附属医院 □教学医院 □非教学医院 <br /><br />
										□其他（请写出）：__________</#if>
									<#if hospitalBaseInfo.teachingCategory==2>□大学直属附属医院 √大学非直属附属医院 □教学医院 □非教学医院 <br /><br />
										□其他（请写出）：__________</#if>
									<#if hospitalBaseInfo.teachingCategory==3>□大学直属附属医院 □大学非直属附属医院 √教学医院 □非教学医院 <br /><br />
										□其他（请写出）：__________</#if>
									<#if hospitalBaseInfo.teachingCategory==5>□大学直属附属医院 □大学非直属附属医院 □教学医院 √非教学医院 <br /><br />
										□其他（请写出）：__________</#if>
									<#if hospitalBaseInfo.teachingCategory==4>□大学直属附属医院 □大学非直属附属医院 □教学医院 □非教学医院 <br /><br />
										√其他（请写出）：<span
											style="text-decoration: underline;">${hospitalBaseInfo.teachingCategoryOther}</span>
									</#if>
								</div>
							</td>
						</tr>
						<tr>
							<td colspan="8">临床科室总数 ${hospitalBaseInfo.clinicalDepartmentNum}（个）</td>
							<td colspan="8">病区总数 ${hospitalBaseInfo.inpatientAreaNum}（个）</td>
							<td colspan="8">医技科室总数 ${hospitalBaseInfo.medicalTechnologyNum}（个）</td>
						</tr>
						<tr>
							<td colspan="8">占地面积：${hospitalBaseInfo.areaCovered}m<sup>2</sup></td>
							<td colspan="8">总建筑面积：${hospitalBaseInfo.areaArchitecture}m<sup>2</sup></td>
							<td colspan="8">业务用房建筑面积：${hospitalBaseInfo.areaBusinessArchitecture}m<sup>2</sup></td>
						</tr>
						<tr>
							<td colspan="8">编制床位（《医疗机构执业许可证》时核准的床位数：${hospitalBaseInfo.preparationBed}（个）</td>
							<td colspan="8">实际开放总床位数：${hospitalBaseInfo.actualBed}（个）</td>
							<td colspan="8">病床使用率：${hospitalBaseInfo.bedUtilization}%</td>
						</tr>
						<tr>
							<td colspan="12">
								<span>全院在岗人员：${hospitalBaseInfo.onDutyNum}（人）</span>
							</td>
							<td colspan="12">
								<span>卫生技术人员：${hospitalBaseInfo.healthTechnologyNum}（人）</span>
							</td>
						</tr>
					</tbody>
					<tbody>
						<tr style="background-color:#eaf1dd;">
							<td colspan="6">临床服务</td>
							<td colspan="6">是否提供门诊服务</td>
							<td colspan="6">是否提供住院服务</td>
							<td colspan="6">备注（开设少于3年的科室请注明）</td>
						</tr>
						<#list clinicalServiceDepartmentList as hospitalDepartment>
							<tr>
								<td colspan="6">${hospitalDepartment.departmentName}</td>
								<td colspan="6">
									<#if hospitalDepartment.outpatientService==1>√是 □否</#if>
									<#if hospitalDepartment.outpatientService==2>□是 √否</#if>
								</td>
								<td colspan="6">
									<#if hospitalDepartment.inpatientService==1>√是 □否</#if>
									<#if hospitalDepartment.inpatientService==2>□是 √否</#if>
								</td>
								<td colspan="6">${hospitalDepartment.departmentDesc}</td>
							</tr>
						</#list>
					</tbody>
					<tbody>
						<tr style="background-color:#eaf1dd;">
							<td colspan="6">医技服务</td>
							<td colspan="6">是否提供</td>
							<td colspan="12">备注（开设少于3年的科室请注明）</td>
						</tr>
						<#list medicalServiceDepartmentList as hospitalDepartment>
							<tr>
								<td colspan="6">${hospitalDepartment.departmentName}</td>
								<td colspan="6">
									<#if hospitalDepartment.outpatientService==1>√是 □否</#if>
									<#if hospitalDepartment.outpatientService==2>□是 √否</#if>
								</td>
								<td colspan="12">${hospitalDepartment.departmentDesc}</td>
							</tr>
						</#list>
					</tbody>
				</table>
			</div>
			<br />
			<br />
			<div class="review-info w80p lh28">
				<table class="m0" cellpadding="0" cellspacing="0" border="1">
					<tbody>
						<tr>
							<td class="font14" style="font-weight: 600;"> 2. 评审信息（请勾选√）</td>
						</tr>
						<tr>
							<#if hospitalBaseInfo.first==1>
								<td> 2.1 是否参加过等级医院评审： □是 √否 </td>
							</#if>
							<#if hospitalBaseInfo.first==2>
								<td> 2.1 是否参加过等级医院评审： √是 □否 </td>
							</#if>
						</tr>
						<#if hospitalBaseInfo.first==2>
							<tr>
								<td>
									2.1.1 参加等级医院评审情况（如有）<br />
									<span style="font-weight: 600;">最近一次等级医院评审时间</span>：<#if hospitalBaseInfo.first==2>
										${hospitalBaseInfo.beforeReviewerDate}
										<#else>
											_____年____月
									</#if> <br />
									<span style="font-weight: 600;">评审结论</span>（请勾选√）：<br />
									<#if hospitalBaseInfo.first==2>
										<#if hospitalBaseInfo.beforeReviewerConclusionGrade==1>
											<#if hospitalBaseInfo.beforeReviewerConclusionLevel==1>
												<div>· 一级医院：√甲等 □乙等 □丙等 □未定等</div>
												<div>· 二级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 三级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 未分级/不适用：□甲等 □乙等 □丙等 □未定等</div>
											</#if>
											<#if hospitalBaseInfo.beforeReviewerConclusionLevel==2>
												<div>· 一级医院：□甲等 √乙等 □丙等 □未定等</div>
												<div>· 二级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 三级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 未分级/不适用：□甲等 □乙等 □丙等 □未定等</div>
											</#if>
											<#if hospitalBaseInfo.beforeReviewerConclusionLevel==3>
												<div>· 一级医院：□甲等 □乙等 √丙等 □未定等</div>
												<div>· 二级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 三级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 未分级/不适用：□甲等 □乙等 □丙等 □未定等</div>
											</#if>
											<#if hospitalBaseInfo.beforeReviewerConclusionLevel==4>
												<div>· 一级医院：□甲等 □乙等 □丙等 √未定等</div>
												<div>· 二级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 三级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 未分级/不适用：□甲等 □乙等 □丙等 □未定等</div>
											</#if>
										</#if>
										<#if hospitalBaseInfo.beforeReviewerConclusionGrade==2>
											<#if hospitalBaseInfo.beforeReviewerConclusionLevel==1>
												<div>· 一级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 二级医院：√甲等 □乙等 □丙等 □未定等</div>
												<div>· 三级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 未分级/不适用：□甲等 □乙等 □丙等 □未定等</div>
											</#if>
											<#if hospitalBaseInfo.beforeReviewerConclusionLevel==2>
												<div>· 一级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 二级医院：□甲等 √乙等 □丙等 □未定等</div>
												<div>· 三级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 未分级/不适用：□甲等 □乙等 □丙等 □未定等</div>
											</#if>
											<#if hospitalBaseInfo.beforeReviewerConclusionLevel==3>
												<div>· 一级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 二级医院：□甲等 □乙等 √丙等 □未定等</div>
												<div>· 三级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 未分级/不适用：□甲等 □乙等 □丙等 □未定等</div>
											</#if>
											<#if hospitalBaseInfo.beforeReviewerConclusionLevel==4>
												<div>· 一级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 二级医院：□甲等 □乙等 □丙等 √未定等</div>
												<div>· 三级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 未分级/不适用：□甲等 □乙等 □丙等 □未定等</div>
											</#if>
										</#if>
										<#if hospitalBaseInfo.beforeReviewerConclusionGrade==3>
											<#if hospitalBaseInfo.beforeReviewerConclusionLevel==1>
												<div>· 一级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 二级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 三级医院：√甲等 □乙等 □丙等 □未定等</div>
												<div>· 未分级/不适用：□甲等 □乙等 □丙等 □未定等</div>
											</#if>
											<#if hospitalBaseInfo.beforeReviewerConclusionLevel==2>
												<div>· 一级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 二级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 三级医院：□甲等 √乙等 □丙等 □未定等</div>
												<div>· 未分级/不适用：□甲等 □乙等 □丙等 □未定等</div>
											</#if>
											<#if hospitalBaseInfo.beforeReviewerConclusionLevel==3>
												<div>· 一级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 二级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 三级医院：□甲等 □乙等 √丙等 □未定等</div>
												<div>· 未分级/不适用：□甲等 □乙等 □丙等 □未定等</div>
											</#if>
											<#if hospitalBaseInfo.beforeReviewerConclusionLevel==4>
												<div>· 一级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 二级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 三级医院：□甲等 □乙等 □丙等 √未定等</div>
												<div>· 未分级/不适用：□甲等 □乙等 □丙等 □未定等</div>
											</#if>
										</#if>
										<#if hospitalBaseInfo.beforeReviewerConclusionGrade==4>
											<#if hospitalBaseInfo.beforeReviewerConclusionLevel==1>
												<div>· 一级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 二级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 三级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 未分级/不适用：√甲等 □乙等 □丙等 □未定等</div>
											</#if>
											<#if hospitalBaseInfo.beforeReviewerConclusionLevel==2>
												<div>· 一级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 二级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 三级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 未分级/不适用：□甲等 √乙等 □丙等 □未定等</div>
											</#if>
											<#if hospitalBaseInfo.beforeReviewerConclusionLevel==3>
												<div>· 一级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 二级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 三级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 未分级/不适用：□甲等 □乙等 √丙等 □未定等</div>
											</#if>
											<#if hospitalBaseInfo.beforeReviewerConclusionLevel==4>
												<div>· 一级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 二级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 三级医院：□甲等 □乙等 □丙等 □未定等</div>
												<div>· 未分级/不适用：□甲等 □乙等 □丙等 √未定等</div>
											</#if>
										</#if>
									<#else>
										<div>· 一级医院：□甲等 □乙等 □丙等 □未定等</div>
										<div>· 二级医院：□甲等 □乙等 □丙等 □未定等</div>
										<div>· 三级医院：□甲等 □乙等 □丙等 □未定等</div>
										<div>· 未分级/不适用：□甲等 □乙等 □丙等 □未定等</div>
									</#if>
								</td>
							</tr>
						</#if>
						<tr>
							<td>
								<#if hospitalBaseInfo.first==1>
									2.2是否参加过国际认证项目：□是 □否
								<#elseif (hospitalBaseInfo.hasIntnPro==1)>
									2.2是否参加过国际认证项目：√是 □否
								<#else>
									2.2是否参加过国际认证项目：□是 √否
								</#if>
							</td>
						</tr>
						<#if (hospitalBaseInfo.hasIntnPro==1)>
							<tr>
								<td>
									2.2.1 参加国际认证情况<br />
									<div class="ml20">
										<#if (allCertificateAbilityList?size>0)>
											<#list allCertificateAbilityList as aCer>
												<div>
													<#if hospitalBaseInfo.first==2 && aCer.expireDate??>
														√
													<#else>
														□
													</#if>
													<#if aCer.abilityDictValue=='otherAuthDueDate'>
														其他（请写出）
													<#elseif aCer.abilityDictValue=='fail' && !aCer.abilityName?has_content>
														_____年____月参加__________国际认证项目，未通过
													<#elseif aCer.abilityDictValue=='fail' && !aCer.expireDate?has_content>
														_____年____月参加__________国际认证项目，未通过
													<#elseif aCer.abilityDictValue=='fail' && aCer.abilityName?has_content>
													<#else>
														${aCer.abilityName}
													</#if>
													<#if aCer.abilityDictValue=='otherAuthDueDate'>
														<#if hospitalBaseInfo.first==2 && aCer.expireDate??>
															${aCer.abilityName}，${aCer.expireDate}到期
														<#else>
															__________，_____年____月到期
														</#if>
													<#elseif aCer.abilityDictValue=='fail' && aCer.abilityName?? && aCer.expireDate??>
														${aCer.expireDate}参加${aCer.abilityName}国际认证项目，未通过
													<#elseif aCer.abilityDictValue=='fail'>
													<#elseif aCer.abilityDictValue!='0' && aCer.abilityDictValue!='fail'>
														，<#if hospitalBaseInfo.first==2 && aCer.expireDate??>
															${aCer.expireDate}到期
														<#else>
															_____年____月到期
														</#if>
													</#if>
												</div>
											</#list>
										</#if>
									</div>
								</td>
							</tr>
						</#if>
					</tbody>
				</table>
			</div>
			<br />
			<br />
			<div class="promise w80p">
				<table class="m0" cellpadding="0" cellspacing="0" border="1">
					<tbody>
						<tr>
							<td class="font14" style="font-weight: 600;">
								3. 确认申请
							</td>
						</tr>
						<tr>
							<td>
								<br />
								<span style="text-decoration: underline;">${hospitalBaseInfo.hospitalName}</span>
								[申请机构名称]承诺所提供的所有信息、数据及各类资料真实可靠，无瞒报、漏报，并可提供实地考察与复核，特申请国际医疗质量认证。
								<br />
								<br />
								期望评审日期为 <span
									style="text-decoration: underline;">${hospitalBaseInfo.expectReviewDate}</span>。
								<br />
							</td>
						</tr>
						<tr>
							<td>
								<br />
								<br />
								申请单位（盖公章）：
								<br />
								<br />
								<p>
									<span>法定代表人（签字）：</span>
									<span style="float:right;margin-right:100px"> 日期：</span>
								</p>
							</td>
						</tr>
					</tbody>
				</table>
			</div>

			<br />
			<br />
			<div class="approval w80p">
				<table class="m0" cellpadding="0" cellspacing="0" border="1">
					<tbody>
						<tr>
							<td colspan="24" style="font-weight: 600;text-align:center;">
								“中心”审批情况
							</td>
						</tr>
						<tr>
							<td colspan="6" style="text-align:center;font-weight: 600;">
								标准与评价管理部<br />审批意见
							</td>
							<td colspan="18">
								<br />
								<br />
								<br />
								<p style="text-align:center;">签字/盖章：</p>
								<br />
								<br />
								<p style="text-align:center;"> 日期：</p>
								<br />
								<br />
								<br />
							</td>
						</tr>
						<tr>
							<td colspan="6" style="text-align:center;font-weight: 600;">
								中心主任<br />审批意见
							</td>
							<td colspan="18">
								<br />
								<br />
								<br />
								<p style="text-align:center;">签字/盖章：</p>
								<br />
								<br />
								<p style="text-align:center;"> 日期：</p>
								<br />
								<br />
								<br />
							</td>
						</tr>
						<tr>
							<td colspan="24" style="font-weight: 600;text-align:center;">
								归档情况
							</td>
						</tr>
						<tr>
							<td colspan="24">
								<br />
								<br />
								<p>
									<span> 归档日期：</span>
									<span style="float:right;margin-right:200px"> 归档人：</span>
								</p>
								<br />
								<br />
							</td>
						</tr>
					</tbody>
				</table>
				<br />
				备注:填写说明请见附表
				<br />
			</div>
			<br />
			<div style="page-break-after:always;"></div>
			<div class="filling-instructions w80p">
				<h3>附表：填写说明</h3>
				<table id="filling-instructions-table" cellpadding="0" cellspacing="0" style="table-layout: fixed;">
					<tbody>
						<tr>
							<td colspan="1">
								<span style="font-weight: bold;">信息项目</span>
							</td>
							<td colspan="4">
								<span style="font-weight: bold;">填写说明</span>
							</td>
						</tr>
						<tr>
							<td colspan="1">执业许可证代码</td>
							<td colspan="4">有效的执业许可证所显示的代码。</td>
						</tr>
						<tr>
							<td colspan="1">医院目前级别</td>
							<td colspan="4">本省上一周期医院评审中所确定的级别，目前执业许可证上标注的级别，如三、二、一级。</td>
						</tr>
						<tr>
							<td colspan="1">医院目前等级</td>
							<td colspan="4">上一周期医院评审中所确定的等别，目前执业许可证上标注的等别，如甲、乙、其他（具体说明），如果仅有级别未确定等别，则填写：未定等。</td>
						</tr>
						<tr>
							<td colspan="1">医院隶属关系</td>
							<td colspan="4">医院直接与卫生行政部门有行政直接隶属关系，分为：卫生部属（管）含卫生部与教育部共管单位、省卫生厅、直辖市卫生局等。</td>
						</tr>
						<tr>
							<td colspan="1">医院所有制形式</td>
							<td colspan="4">选择相应项目填报：（只能填一个）a、全民 b、集体 c、私人 d、中外合资（合作）e、其他。</td>
						</tr>
						<tr>
							<td colspan="1">医院经营性质</td>
							<td colspan="4">政府举办非营利性、非政府举办非营利性、营利性。</td>
						</tr>
						<tr>
							<td colspan="1">医院教学类别</td>
							<td colspan="4">
								<div>附属医院：即医院与大学有直接行政管理隶属关系。</div>
								<div>教学医院：即医院与大学没有直接行政隶属关系，仅承担教学任务。</div>
							</td>
						</tr>
						<tr>
							<td colspan="1">医院执业地址</td>
							<td colspan="4">医疗机构所在地的详细地址，含所有执业地点地址和邮政编码。执业地址与执业执照一致。如执业时间3年以下的执业地点请标明具体时间。</td>
						</tr>
						<tr>
							<td colspan="1">编制床位</td>
							<td colspan="4">《医疗机构执业许可证》时核准的床位数。</td>
						</tr>
						<tr>
							<td colspan="1">实际开放总床位数</td>
							<td colspan="4">
								报告期内医院各科每日夜晚12时开放病床数之总和。不论该病床是否被人占用，都应计算在内。包括因故（如消毒、小修理等）暂时停用的病床，不包括因医院扩建、大修理、搬迁或粉刷而停用的病床及临时增设的病床。
							</td>
						</tr>
						<tr>
							<td colspan="1">病床使用率</td>
							<td colspan="4">反映每天使用床位与实有床位的比率，即实际占用总床日数/同期实际开放总床日数*100%。</td>
						</tr>
						<tr>
							<td colspan="1">医技科室</td>
							<td colspan="4">填写全院开设的所有医技科室，开设少于3年的科室请注明。</td>
						</tr>
						<tr>
							<td colspan="1">缺失科室/专业</td>
							<td colspan="4">
								<div>卫生部《医疗机构基本标准（试行）》（卫医发（1994）第30号）和卫生部后续颁发的各个增加诊疗科目通知规定医院科室设置标准：</div>
								<div>（一）临床科室：至少设有急诊科、内科、外科、妇产科、儿科、中医科、耳鼻喉科、口腔科、眼科、皮肤科、麻醉科、康复科、预防保健科（13个科室）</div>
								<div>
									（二）医技科室：至少设有药剂科、检验科、放射科、手术室、病理科、输血科、核医学科、理疗科（可与康复科合设）、消毒供应室、病案室、营养部和相应的临床功能检查室（12个医技科室）。
								</div>
								<div>对缺失的科室或专业填上名称。</div>
							</td>
						</tr>
						<tr>
							<td colspan="1">执业＜3年</td>
							<td colspan="4">对照上述科室，临床科室及医技科室执业时间少于3年填上其名称。</td>
						</tr>
						<tr>
							<td colspan="1">卫生技术人员</td>
							<td colspan="4">指年末单位在职的执业（助理）医师、护师、注册护士、药剂人员、检验和影像人员等卫生专业人员。不包括专职从事管理工作的卫生技术人员。</td>
						</tr>
						<tr>
							<td colspan="1">全院在岗人员数</td>
							<td colspan="4">包括在编人员、延聘、返聘人员、合同聘用人员（包括临床博士后、规培医生等）</td>
						</tr>
						<tr>
							<td colspan="1">联系电话</td>
							<td colspan="4">申报单位联系人电话。</td>
						</tr>
						<tr>
							<td colspan="1">联系人</td>
							<td colspan="4">申报单位负责申报资料人员。</td>
						</tr>
						<tr>
							<td colspan="1">传真电话</td>
							<td colspan="4">申报单位联系人传真电话。</td>
						</tr>
						<tr>
							<td colspan="1">通讯地址</td>
							<td colspan="4">申报单位联系人通讯地址。</td>
						</tr>
						<tr>
							<td colspan="1">法定代表人</td>
							<td colspan="4">与执业执照上一致。</td>
						</tr>
					</tbody>
				</table>


				<table cellpadding="0" cellspacing="0" border="0" style="table-layout: fixed;">
					<tbody>
						<br />
						<tr>
							<td style="border-top: 1px solid black">
								<span>深圳市卫健医院评审评价研究中心</span>
								<span style="float:right;">2022年12月2日印发</span>
							</td>
						</tr>

						<tr>
							<td style="border-top: 1px solid black">
								校对人：刘梦丹
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</body>

</html>
