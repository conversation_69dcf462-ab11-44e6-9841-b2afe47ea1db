<template>
  <div class="answer">
    <el-card class="box-card">
      <div class="paper-name">{{answerDetail.paperName}}</div>
      <div class="learn-resource">{{answerDetail.learnResourceTitle}}</div>
      <div class="paper-description">{{answerDetail.paperDiscription}}</div>
      <div class="statistics">
        <div class="item">
          <div class="total">总分{{ totalScore || 0 }}分</div>
        </div>
        <div class="item">
          <div class="total">共{{answerDetail.details && answerDetail.details.length || 0}}题</div>
        </div>
      </div>
      <div v-for="(item, index) in answerDetail.details" :key="index" class="exam-question-item">
        <el-form :model="form" size="medium" label-width="0px">
          <el-form-item label="">
            <div style="font-size: 16px; font-weight: bold;">
              <span>{{index + 1 + "、" + item.examQuestion}}</span>
            </div>
            <el-radio-group v-if="item.type == 1" v-model.trim="item.answer">
              <el-radio v-for="(question, i) in JSON.parse(item.questionOptions)" :key="i" :label="question.label">
                {{question.label}}
              </el-radio>
            </el-radio-group>
            <el-checkbox-group v-else-if="item.type == 2" v-model.trim="item.answer">
              <el-checkbox v-for="(question, i) in JSON.parse(item.questionOptions)" :key="i" :label="question.label">
                {{question.label}}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>
      <div style="text-align: center;">
        <el-button type="primary" @click="submit">提交答案</el-button>
        <el-button plain @click="back">返回</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import request from "@/utils/request"
export default {
  name: 'Answer',
  data() {
    return {
      answerDetail: {
        paperName: '',
        learnResourceTitle: '',
        paperDiscription: '',
        details: []
      },
      form: {},
      totalCorrect: 0,
      totalScore: 0,
      from: null
    }
  },
  beforeRouteEnter: (to, from, next) => {
  // console.log(from);
    next((vm) => {
      vm.from = from.path
    })
  },
  created() {
    this.getAnswerDetail()
  },
  methods: {
    getAnswerDetail() {
      request({
        url: '/system/paper/' + this.$route.query.examPaperId,
        method: 'get'
      }).then((res) => {
        for (let index = 0; index < res.data.details.length; index++) {
          this.totalScore += (+res.data.details[index].score)
          if (res.data.details[index].type === 2) {
            res.data.details[index].answer = []
          } else if (res.data.details[index].type === 1) {
            res.data.details[index].answer = null
          }
        }
        this.answerDetail = res.data
      }).catch((error) => {
        throw error
      })
    },
    submit() {
      let details = []
      for (let index = 0; index < this.answerDetail.details.length; index++) {
        if (!this.answerDetail.details[index].answer || !this.answerDetail.details[index].answer.length) {
          this.$message({
            type: 'warning',
            message: '您还有题目未完成哦~'
          })
          return
        }
        let sortArray = []
        if (this.answerDetail.details[index].type == 2) {
          // 多选
          let questionOptions = JSON.parse(this.answerDetail.details[index].questionOptions)
          for (let i = 0; i < questionOptions.length; i++) {
            for (let j = 0; j < this.answerDetail.details[index].answer.length; j++) {
              if (this.answerDetail.details[index].answer[j] == questionOptions[i].label) {
                sortArray.push({
                  label: this.answerDetail.details[index].answer[j],
                  sort: i
                })
                break
              }
            }
          }
          sortArray.sort((a, b) => {
            if (a.sort < b.sort) {
              return -1
            } else if (a.sort > b.sort) {
              return 1
            }
            return 0
          })
        }
        details.push({
          answer: this.answerDetail.details[index].type == 2 ? JSON.stringify(sortArray.map((item) => item.label)) : this.answerDetail.details[index].answer,
          examDetailsId: this.answerDetail.details[index].id
        })
      }
      request({
        url: '/system/sheet',
        method: 'post',
        data: {
          details,
          examPaperId: +this.$route.query.examPaperId,
          learnResourceId: this.answerDetail.learnResourceId
        },
      }).then((res) => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '提交答案成功！'
          })
          if (this.from == "/learningMaterials/courseDetail") {
            this.$store.dispatch("tagsView/delView", this.$route) &&
            this.$router.push({
              path: "/learningMaterials/courseDetail",
              query: { id: this.$route.query.id }
            })
          } else {
            this.$store.dispatch("tagsView/delView", this.$route) &&
              this.$router.push({ path: "/learningMaterials/onlineLearning" })
          }
            // this.$router.push({
            //   path: "/learningMaterials/courseDetail",
            //   query: {
            //     id: this.$route.query.id
            //   }
            // })
        } else {
          this.$message({
            type: 'error',
            message: '提交答案失败！'
          })
        }
      }).catch((error) => { })
    },
    back() {
      this.$store.dispatch("tagsView/delView", this.$route) &&
        this.$router.push({
          path: "/learningMaterials/courseDetail",
          query: { id: this.$route.query.id }
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.answer {
  .el-radio {
    display: block;
    padding: 10px 0 0 0;
    line-height: 36px;
    &:nth-child(1) {
      padding: 0;
    }
  }
  .el-checkbox {
    padding: 10px 0 0 0;
    display: block;
    &:nth-child(1) {
      padding: 0;
    }
  }
  .paper-name {
    margin-top: 40px;
    text-align: center;
    font-size: 24px;
    font-weight: bold;
  }
  .learn-resource {
    margin: 10px 0;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
  }
  .paper-description {
    text-align: center;
    font-size: 12px;
    font-weight: bold;
    color: #747774;
  }
  .statistics {
    display: flex;
    justify-content: center;
    margin-top: 14px;
    margin-bottom: 60px;
    text-align: center;
    .item {
      width: 440px;
      border: 1px solid #ccc;
      &:nth-child(2) {
        border-left: 0;
      }
      .total {
        line-height: 40px;
        background-color: #f8f8f8;
      }
    }
  }
  .exam-question-item {
    width: 880px;
    margin: 0 auto;
  }
}
</style>
