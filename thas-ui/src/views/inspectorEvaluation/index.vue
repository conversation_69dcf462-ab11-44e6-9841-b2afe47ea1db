<template>
  <div class="inspector-evaluation">
    <el-form ref="form" :model="queryData" inline label-width="130px">
      <el-form-item label="医院名称：" prop="distributeHospitalName">
        <el-input v-model.trim="queryData.distributeHospitalName" :maxlength="20" placeholder="请输入医院名称"></el-input>
      </el-form-item>
      <el-form-item label="状态：" prop="distributeStatus">
        <el-select v-model.trim="queryData.distributeStatus" placeholder="请选择状态" clearable>
          <el-option v-for="item in progressOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="">
        <el-button type="primary" size="small" @click="query">
          查 询
        </el-button>
        <el-button type="primary" size="small" @click="reset"> 重 置 </el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataSource" border stripe v-loading="loading">
      <el-table-column label="操作" align="center" width="160">
        <template slot-scope="scope">
          <div class="table-btn">
            <!-- <el-button type="text" @click="toShowHospital(scope.row.autCode)" v-if="showDetailBtn(scope.row)">自评详情</el-button> -->
            <div v-if="preliminary">
              <el-button type="text" @click="
                  toCheck(
                    scope.row.autCode,
                    scope.row.distributeClauseIdList,
                    scope.row.autCsId
                  )
                " v-if="showExamineBtn(scope.row)">形式审查</el-button>
              <el-button v-if="scope.row.isLeader == 1" type="text" @click="showGroup(scope.row)">小组进度</el-button>
            </div>
            <div v-else>
              <el-button type="text" @click="
                  toCheck2(
                    scope.row.autCode,
                    scope.row.distributeClauseIdList,
                    scope.row.autCsId,
                    1
                  )
                " v-if="showSDetailBtn(scope.row)">审查审核</el-button>
              <el-button v-if="showFinally(scope.row)" type="text" @click="selfFinalConfirm(scope.row)">提交自评报告终稿</el-button>
              <!-- <el-button v-if="showFinally2(scope.row)" type="text" @click="finalCheck(scope.row)">最终修改</el-button> -->
              <el-button v-if="scope.row.isLeader == 1" type="text" @click="showGroup(scope.row)">小组管理</el-button>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="医院名称" align="center" prop="hospitalName">
      </el-table-column>
      <!--  -->
      <el-table-column label="自评完成时间" align="center" prop="saAudSubmitTime">
      </el-table-column>
      <!-- <el-table-column v-if="preliminary" label="待组长审核" align="center" prop="waitLeaderAudNum">
      </el-table-column> -->
      <el-table-column v-if="preliminary" label="组长" align="center" prop="leaderName">
      </el-table-column>
      <!--  -->
      <el-table-column v-if="!preliminary" label="审查员" align="center" prop="memberName">
      </el-table-column>
      <!-- <el-table-column v-if="!preliminary" label="待复查" align="center" prop="waitLeaderAudNum">
      </el-table-column> -->
      <!--  -->
      <el-table-column label="审查完成时间" align="center" prop="audSubmitTime">
      </el-table-column>
      <el-table-column label="审查周期" align="center" prop="audCycle">
        <template slot-scope="scope">
          {{ scope.row.audCycle.replace(',', ' ~ ') || "--" }}
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="hospitalName">
        <template slot-scope="scope">
          {{ AutSaAudCurrentStatusEnum[scope.row.status] }}
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query" />
    <dict-span :dictType="processDictType" value="0" v-show="false"></dict-span>

    <el-dialog :title="groupTitle" :visible.sync="groupDialogVisible" width="80%">
      <el-form :inline="true" :model="groupForm" class="demo-form-inline" label-width="100px">
        <el-form-item label="审查医院：">
          {{ groupForm.hospitalName || "--" }}
        </el-form-item>
        <el-form-item label="审查时间：" style="margin-left: 100px">
          {{ groupForm.audSubmitTime || "--" }}
        </el-form-item>
      </el-form>
      <el-table :data="groupDataSource" border stripe>
        <el-table-column label="审查员" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.name
              }}{{ scope.row.leaderIs == 1 ? "(组长)" : "" }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="groupForm.hasRj ? '待审查组长审查':'待审查员审查'" align="center">
          <template slot-scope="scope">
            <span> {{ scope.row.pendingReviewCount || 0 }}款 </span>
          </template>
        </el-table-column>
        <el-table-column label="待审查组长审核" align="center">
          <template slot-scope="scope">
            <span> {{ scope.row.leaderPendingReviewCount || 0 }}款 </span>
          </template>
        </el-table-column>
        <el-table-column label="审查驳回/待医院修改" align="center">
          <template slot-scope="scope">
            <span> {{ scope.row.pendingHospitalUpdateCount || 0 }}款 </span>
          </template>
        </el-table-column>
        <el-table-column label="共计审查" align="center">
          <template slot-scope="scope">
            <span> {{ (scope.row.pendingReviewCount || 0) + (scope.row.censoredCount || 0) + (scope.row.pendingHospitalUpdateCount || 0) }}款 </span>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="groupDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="groupDialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import DictSpan from "@/components/DetailMessage/dictSpan.vue";
import store from "@/store";
import request from "@/utils/request";

export default {
  name: "inspectorEvaluationList",
  data() {
    return {
      queryData: {
        distributeHospitalName: "",
        distributeStatus: "",
        pageSize: 10,
        pageNum: 1,
      },
      url: {
        list: "/aut/sa/aud/queryList",
      },
      total: 0,
      dataSource: [],
      preliminary: true,
      groupDialogVisible: false,
      groupDataSource: [],
      groupForm: {},
      groupTitle: "小组进度",
      loading: false
    };
  },

  components: {
    DictSpan,
  },

  computed: {
    processDictType() {
      return this.preliminary
        ? "inspector_first_progress_status"
        : "inspector_second_progress_status";
    },
    showDetailBtn() {
      return (row) =>
        row.autCode &&
        row.status >= this.AutSaAudCurrentStatusEnum.WAIT_AUD_FIRST_TRIAL;
    },

    showExamineBtn() {
      return (row) => {
        let statuses = [
          this.AutSaAudCurrentStatusEnum.WAIT_AUD_FIRST_TRIAL,
          this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_INQUIRE_PROC,
          this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_REVIEW_REJECT
        ];
        return row.autCode && statuses.includes(row.status) && row.waitAudNum;
      };
    },
    showSDetailBtn() {
      return (row) => {
        let statuses = [
          this.AutSaAudCurrentStatusEnum.WAIT_CONFIRM_FIRST_TRIAL,
        ];
        return row.autCode && statuses.includes(row.status);
      };
    },

    progressOptions() {
      return this.AutSaAudCurrentStatusEnum.getStatus()
    },
    showFinally() {
      return ({ status }) => status === this.AutSaAudCurrentStatusEnum.WAIT_AUD_FIRST_TRIAL_SUM
    },
    showFinally2() {
      return ({ status }) => status === this.AutSaAudCurrentStatusEnum.WAIT_REPORT || status === this.AutSaAudCurrentStatusEnum.REPORT_FINISH
    }
  },

  mounted() {
    this.preliminary = this.$route.query.preliminary != 0;
    this.groupTitle = this.preliminary ? "小组进度" : "小组管理";
    this.initDicts();
    this.query();
  },

  methods: {
    initDicts() { },

    reset() {
      this.$refs['form'].resetFields();
      this.query();
    },

    query() {
      this.loading = true;
      let data = {
        accountId: store.getters.userId + "",
        ...this.queryData,
        // type: this.preliminary ? 2 : 3,
        pageType: this.preliminary ? 'fr_clause' : 'fr_v_clause'
      };
      request({
        url: this.url.list,
        method: "post",
        data,
      }).then((res) => {
        if (res.code == 200) {
          let fields = [
            "hospitalName",
            "waitAudNum",
            "waitLeaderAudNum",
            "leaderName",
            "memberName",
            "waitLeaderAudNum",
            "saAudSubmitTime",
            "audSubmitTime",
            "audCycle",
          ];
          this.dataSource = res.rows.map((row) => {
            for (const key in row) {
              if (Object.hasOwnProperty.call(row, key)) {
                if (!row[key] && row[key] != 0 && fields.includes(key)) {
                  row[key] = '--'
                }

              }
            }
            return row;
          });
        }
      }).catch(() => {
        this.dataSource = []
      }).finally(() => {
        this.loading = false;
      });
    },
    toShowHospital(autCode) {
      this.$router.push({
        path: "/fInspector/fInspectorIndex",
        query: {
          readOnly: true,
          isLeader: 0,
          showOnlyHospital: true,
          autCode,
          type: this.AutSaAudSubmitTypeEnum.TERM_SA_SUM,
          type: this.preliminary ? 2 : 3,
        },
      });
    },

    toCheck(autCode, clauseIds, versionId, isLeader) {
      this.$router.push({
        path: "/fInspector/fInspectorIndex",
        query: {
          autCode,
          type: 'fr_clause'
        },
      });
    },

    toCheck2(autCode, clauseIds, versionId, isLeader) {
      this.$router.push({
        path: "/fInspector/sInspectorIndex",
        query: {
          autCode,
          type: 'fr_v_clause'
        },
      });
    },

    showGroup(row) {
      if (row) {
        this.$set(this, "groupDataSource", row.groupProgressList);
        this.$set(this, "groupForm", {
          hasRj: !!row.rejectIds && row.status == this.AutSaAudCurrentStatusEnum.WAIT_CONFIRM_FIRST_TRIAL,
          hospitalName: row.hospitalName,
          audSubmitTime: row.audSubmitTime,
        });
      }
      this.groupDialogVisible = true;
    },

    finalCheck({ autCode }) {
      this.$router.push({
        path: "/fInspector/lInspectorIndex",
        query: {
          autCode,
          type: 'fr_v_clause'
        },
      });
    },

    async selfFinalConfirm(row) {
      if (row.status !== this.AutSaAudCurrentStatusEnum.WAIT_AUD_FIRST_TRIAL_SUM) return;
      let data = {
        autCode: row.autCode,
        accountId: this.$store.getters.userId + "",
        submitType: this.AutSaAudCurrentStatusEnum.FR_REPORT_SUBMIT,
        autSaAudLists: []
      }
      try {
        await request({
          url: "/aut/sa/aud/submitAutSaAud",
          method: "post",
          data,
        });
        this.$message({
          type: 'success',
          message: "提交自评报告终稿成功"
        });
      } finally {
        this.query();
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.inspector-evaluation {
  padding: 12px;
  background-color: #fff;
  min-height: inherit;
}
</style>
