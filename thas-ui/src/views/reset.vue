<template>
  <div class="forgot">
    <h1 class="headTitle">深圳卫健医院评审评价研究中心
      <span v-if="isFirstLogin || token"  class="logout" @click="logout">退出登录</span>
    </h1>
    <div class="register">
      <el-form ref="passwordForm" :model="passwordForm" :rules="registerRules" class="register-form">
        <h1 class="title">
          <p>设置新密码</p>
          <span>新密码不得少于8位，且不可与之前设置过的密码重复</span>
        </h1>
        <el-form-item v-if="isFirstLogin" prop="oldPassword" :rules="oldPasswordRule" style="margin-bottom: 30px;">
          <el-input v-model.trim="passwordForm.oldPassword" type="password" auto-complete="off" placeholder="请输入旧密码"
            maxlength="16" @keyup.enter.native="handleRegister">
            <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
          </el-input>
        </el-form-item>
        <el-form-item prop="password" :rules="passwordRule" style="margin-bottom:30px;">
          <el-input v-model.trim="passwordForm.password" type="password" auto-complete="off" placeholder="请输入新密码"
            maxlength="16" @keyup.enter.native="handleRegister">
            <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
          </el-input>
        </el-form-item>
        <el-form-item prop="confirmPwd" :rules="confirmPwdRule" style="margin-bottom:30px;">
          <el-input v-model.trim="passwordForm.confirmPwd" type="password" auto-complete="off" placeholder="重复新密码"
            maxlength="16" @keyup.enter.native="handleRegister">
            <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
          </el-input>
        </el-form-item>
        <el-form-item style="width: 100%">
          <el-button v-loading="loading" :disabled="loading" size="medium" type="primary" style="width: 100%"
            @click.native.prevent="handleRegister">
            <span v-if="isFirstLogin">提交</span>
            <span v-else>重置密码</span>
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import { getCodeImg, register } from "@/api/login";
import request from "@/utils/request";
import { updateUserPwd } from "@/api/system/user";
export default {
  name: "Register",
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.passwordForm.password !== value) {
        callback(new Error("两次输入的密码不一致"));
      } else {
        callback();
      }
    };
    return {
      token:getToken(),
      codeUrl: "",
      url: {
        emailCode: "system/user/sendEmailCode",
        verifyEmailCode: "system/user/verifyEmailCode",
        updatePwd: "system/user/updatePwdByEmail",
        bingUpdatePwd:'/system/user/bing/updatePwdByEmail',
      },
      passwordForm: {
        oldPassword: "",
        password: "",
        confirmPwd: "",
      },
      registerRules: {
        password: [
          { required: true, trigger: "blur", message: "请输入您的新密码" },
          {
            min: 8,
            message: "用户密码长度不得小于8位",
            trigger: "blur",
          },
        ],
        confirmPwd: [
          { required: true, trigger: "blur", message: "请再次输入您的新密码" },
          { required: true, validator: equalToPassword, trigger: "blur" },
        ],
      },
      oldPasswordRule: [{ required: true, trigger: "blur", message: "请输入您的旧密码" }],
      loading: false,
      captchaOnOff: true,
      regPassword: '',
      isSetPsw: false
    };
  },

  beforeRouteLeave(to, from, next) {
    console.log(this.isSetPsw);
    if (this.isSetPsw) {
      next(true)
    } else {
      next(false)
    }
  },
  computed: {
    passwordRule() {
      return [{
        required: true,
        validator: this.passwordValid,
        trigger: ['blur', 'change']
      }]
    },
    confirmPwdRule() {
      return [{
        required: true,
        validator: this.confirmPwdValid,
        trigger: ['blur', 'change']
      }]
    },
    isFirstLogin() {
      const { toEmailNo } = this.$route.params
      const isFirstLogin = localStorage.getItem('isFirstLogin')
      return  !toEmailNo && isFirstLogin && isFirstLogin === '1'
    }
  },
  created() {
    if (getToken()) {
      this.getCode();
      this.getConfigKey("reg.password").then(response => {
        this.regPassword = response.msg.trim();
      });
    } else {
      this.getBingConfigKey("reg.password").then(response => {
        this.regPassword = response.msg.trim();
      });
    }

  },
  methods: {
    passwordValid(rule, value, callback) {
      if (!value) callback('请输入您的新密码')
      if (this.regPassword && !new RegExp(this.regPassword).test(value)) {
        callback('密码由大小写英文字母、数字以及特殊字符(#?!@$%^&*+-)组成，长度为8 ～ 16位')
      }
      callback();
    },
    confirmPwdValid(rule, value, callback) {
      if (!value) callback('请输入您的新密码')
      if (this.regPassword && !new RegExp(this.regPassword).test(value)) {
        callback('密码由大小写英文字母、数字以及特殊字符(#?!@$%^&*+-)组成，长度为8 ～ 16位')
      }
      if (this.passwordForm.password !== this.passwordForm.confirmPwd) {
        callback(new Error("两次输入的密码不一致"));
      }
      callback();
    },
    getCode() {
      getCodeImg().then((res) => {
        this.captchaOnOff =
          res.captchaOnOff === undefined ? true : res.captchaOnOff;
        if (this.captchaOnOff) {
          this.codeUrl = "data:image/gif;base64," + res.img;
          this.passwordForm.uuid = res.uuid;
        }
      });
    },
    handleRegister() {
      this.$refs.passwordForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          if (this.isFirstLogin || getToken()) {
            const sm3 = require('sm-crypto').sm3
            const oldPassword = sm3(this.passwordForm.oldPassword)
            const newPassword = sm3(this.passwordForm.password)
            updateUserPwd(oldPassword, newPassword).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.$tab.closePage()
              this.isSetPsw = true;
              localStorage.setItem('isFirstLogin', undefined)
              this.$router.push({
                path: '/index'
              })
            }).finally(() => {
              this.loading = false
            })
          } else {
            const { toEmailNo } = this.$route.params
            const sm3 = require('sm-crypto').sm3
            const password = sm3(this.passwordForm.password)
            const confirmPwd = sm3(this.passwordForm.confirmPwd)
            request({
              url: this.url.bingUpdatePwd,
              method: "post",
              data: {
                password,
                confirmPwd,
                emailNo: toEmailNo
              },
            }).then(res => {
              if (res.code === 200) {
                this.isSetPsw = true;
                localStorage.setItem('isFirstLogin', undefined)
                this.$router.push({
                  path: '/'
                })
              }
            }).finally(() => {
              this.loading = false
            })
          }
        }
      });
    },

    logout() {
      this.$confirm('确定退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          location.href = '/index';
          localStorage.setItem('isFirstLogin', '')
        })
      }).catch(() => {});
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.forgot {
  height: 100%;
}

.headTitle {
  background-color: #3d81f4;
  color: white;
  margin: 0;
  padding: 20px 0;
  font-size: 18px;
  font-weight: bold;
  text-indent: 100px;
  letter-spacing: 1px;
}

.register {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100% - 60px);
  // background-image: url("../assets/images/login-background.jpg");
  background-size: cover;
}

.title {
  margin: 0px auto 30px auto;
  color: #707070;

  p {
    font-weight: bold;
    margin-bottom: 0;
  }

  span {
    font-size: 14px;
  }
}

.register-form {
  border-radius: 6px;
  background: #ffffff;
  width: 450px;
  padding: 0 25px 25px 25px;

  .el-input {
    height: 38px;

    input {
      height: 38px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}

.register-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.register-code {
  width: 33%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-register-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.register-code-img {
  height: 38px;
}

.logout {
  float: right;
  font-size: 12px;
  margin-right: 24px;
  cursor: pointer;
  vertical-align: middle;
  text-decoration: underline;
}
</style>
