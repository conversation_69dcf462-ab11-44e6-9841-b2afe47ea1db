<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.HospitalAuthContactMapper">

    <resultMap type="com.thas.web.domain.HospitalAuthContact" id="HospitalAuthContactResult">
        <result property="id"    column="id"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="authContactName"    column="auth_contact_name"    />
        <result property="authContactMobile"    column="auth_contact_mobile"    />
        <result property="certificateType"    column="certificate_type"    />
        <result property="certificateNumber"    column="certificate_number"    />
        <result property="authContactEmail"    column="auth_contact_email"    />
        <result property="contactAddress"    column="contact_address"    />
        <result property="authContactPhoto"    column="auth_contact_photo"    />
        <result property="hospitalCertificateAuth"    column="hospital_certificate_auth"    />
        <result property="status"    column="status"    />
        <result property="creator"    column="creator"    />
        <result property="createTime"    column="create_time"    />
        <result property="updater"    column="updater"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectHospitalAuthContactVo">
        select id, apply_no, certificate_type, auth_contact_photo, hospital_certificate_auth, status, creator, create_time, updater, update_time,
        <include refid="SELECT_ENCRYPT_FIELD"/> from hospital_auth_contact
    </sql>

    <sql id="SQL_AES_ENCRYPT">'${@com.thas.web.config.SqlAesEncryptConfig@SQL_AES_ENCRYPT}'</sql>

    <sql id="SELECT_ENCRYPT_FIELD">
        AES_DECRYPT(unhex(auth_contact_name),<include refid="SQL_AES_ENCRYPT"/>) auth_contact_name,
        AES_DECRYPT(unhex(auth_contact_mobile),<include refid="SQL_AES_ENCRYPT"/>) auth_contact_mobile,
        AES_DECRYPT(unhex(certificate_number),<include refid="SQL_AES_ENCRYPT"/>) certificate_number,
        AES_DECRYPT(unhex(auth_contact_email),<include refid="SQL_AES_ENCRYPT"/>) auth_contact_email,
        AES_DECRYPT(unhex(contact_address),<include refid="SQL_AES_ENCRYPT"/>) contact_address
    </sql>

    <select id="selectHospitalAuthContactList" parameterType="HospitalAuthContact" resultMap="HospitalAuthContactResult">
        select * from (
        <include refid="selectHospitalAuthContactVo"/>
        <where>  
            <if test="applyNo != null  and applyNo != ''"> and apply_no = #{applyNo}</if>

            <if test="authContactMobile != null  and authContactMobile != ''"> and auth_contact_mobile = HEX(AES_ENCRYPT(#{authContactMobile},<include refid="SQL_AES_ENCRYPT"/>))</if>
            <if test="certificateNumber != null  and certificateNumber != ''"> and certificate_number = HEX(AES_ENCRYPT(#{certificateNumber},<include refid="SQL_AES_ENCRYPT"/>))</if>
            <if test="authContactEmail != null  and authContactEmail != ''"> and auth_contact_email = HEX(AES_ENCRYPT(#{authContactEmail},<include refid="SQL_AES_ENCRYPT"/>))</if>
            <if test="contactAddress != null  and contactAddress != ''"> and contact_address = HEX(AES_ENCRYPT(#{contactAddress},<include refid="SQL_AES_ENCRYPT"/>))</if>
            <if test="certificateType != null "> and certificate_type = #{certificateType}</if>
            <if test="authContactPhoto != null "> and auth_contact_photo = #{authContactPhoto}</if>
            <if test="hospitalCertificateAuth != null "> and hospital_certificate_auth = #{hospitalCertificateAuth}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="creator != null  and creator != ''"> and creator = #{creator}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
        </where>
        ) aa
        where
        1=1
        <if test="authContactName != null  and authContactName != ''"> and auth_contact_name like concat('%', #{authContactName} , '%')</if>
    </select>
    
    <select id="selectHospitalAuthContactById" parameterType="Long" resultMap="HospitalAuthContactResult">
        <include refid="selectHospitalAuthContactVo"/>
        where id = #{id}
    </select>

    <select id="selectHospitalAuthContactByApplyNo" parameterType="String" resultMap="HospitalAuthContactResult">
        <include refid="selectHospitalAuthContactVo"/>
        where apply_no = #{applyNo} and status = 1
    </select>
        
    <insert id="insertHospitalAuthContact" parameterType="HospitalAuthContact" useGeneratedKeys="true" keyProperty="id">
        insert into hospital_auth_contact
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">apply_no,</if>
            <if test="authContactName != null and authContactName != ''">auth_contact_name,</if>
            <if test="authContactMobile != null and authContactMobile != ''">auth_contact_mobile,</if>
            <if test="certificateType != null">certificate_type,</if>
            <if test="certificateNumber != null and certificateNumber != ''">certificate_number,</if>
            <if test="authContactEmail != null and authContactEmail != ''">auth_contact_email,</if>
            <if test="contactAddress != null and contactAddress != ''">contact_address,</if>
            <if test="authContactPhoto != null and authContactPhoto != ''">auth_contact_photo,</if>
            <if test="hospitalCertificateAuth != null and hospitalCertificateAuth != ''">hospital_certificate_auth,</if>
            <if test="status != null">status,</if>
            <if test="creator != null and creator != ''">creator,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updater != null and updater != ''">updater,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">#{applyNo},</if>
            <if test="authContactName != null and authContactName != ''"> HEX(AES_ENCRYPT(#{authContactName},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="authContactMobile != null and authContactMobile != ''">HEX(AES_ENCRYPT(#{authContactMobile},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="certificateType != null">#{certificateType},</if>
            <if test="certificateNumber != null and certificateNumber != ''">HEX(AES_ENCRYPT(#{certificateNumber},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="authContactEmail != null and authContactEmail != ''">HEX(AES_ENCRYPT(#{authContactEmail},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="contactAddress != null and contactAddress != ''">HEX(AES_ENCRYPT(#{contactAddress},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="authContactPhoto != null and authContactPhoto != ''">#{authContactPhoto},</if>
            <if test="hospitalCertificateAuth != null  and hospitalCertificateAuth != ''">#{hospitalCertificateAuth},</if>
            <if test="status != null">#{status},</if>
            <if test="creator != null and creator != ''">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updater != null and updater != ''">#{updater},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateHospitalAuthContact" parameterType="HospitalAuthContact">
        update hospital_auth_contact
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">apply_no = #{applyNo},</if>
            <if test="authContactName != null and authContactName != ''">auth_contact_name = HEX(AES_ENCRYPT(#{authContactName},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="authContactMobile != null and authContactMobile != ''">auth_contact_mobile = HEX(AES_ENCRYPT(#{authContactMobile},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="certificateType != null">certificate_type = #{certificateType},</if>
            <if test="certificateNumber != null and certificateNumber != ''">certificate_number = HEX(AES_ENCRYPT(#{certificateNumber},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="authContactEmail != null and authContactEmail != ''">auth_contact_email = HEX(AES_ENCRYPT(#{authContactEmail},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="contactAddress != null and contactAddress != ''">contact_address = HEX(AES_ENCRYPT(#{contactAddress},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="authContactPhoto != null and authContactPhoto != ''">auth_contact_photo = #{authContactPhoto},</if>
            <if test="hospitalCertificateAuth != null and hospitalCertificateAuth != ''">hospital_certificate_auth = #{hospitalCertificateAuth},</if>
            <if test="status != null">status = #{status},</if>
            <if test="creator != null and creator != ''">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where
        1=1
        <if test="id != null ">AND id = #{id}</if>
        <if test="applyNo != null and applyNo != ''">AND apply_no = #{applyNo}</if>
    </update>

    <delete id="deleteHospitalAuthContactById" parameterType="Long">
        delete from hospital_auth_contact where id = #{id}
    </delete>

    <delete id="deleteHospitalAuthContactByApplyNo" parameterType="String">
        delete from hospital_auth_contact where apply_no = #{applyNo}
    </delete>

    <delete id="deleteHospitalAuthContactByIds" parameterType="String">
        delete from hospital_auth_contact where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>