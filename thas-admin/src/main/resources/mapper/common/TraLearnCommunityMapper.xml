<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.TraLearnCommunityMapper">

    <resultMap type="TraLearnCommunityVO" id="TraLearnCommunityResult">
        <result property="id" column="id"/>
        <result property="pid" column="pid"/>
        <result property="title" column="title"/>
        <result property="cover" column="cover"/>
        <result property="content" column="content"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectTraLearnCommunityVo">
        select *
        from tra_learn_community
    </sql>

    <select id="selectTraLearnCommunityList" parameterType="TraLearnCommunity" resultMap="TraLearnCommunityResult">
        <include refid="selectTraLearnCommunityVo"/>
        <where>
            <if test="pid != null">and pid = #{pid}</if>
            <if test="title != null  and title != ''">and title = #{title}</if>
            <if test="cover != null  and cover != ''">and cover = #{cover}</if>
            <if test="content != null  and content != ''">and content = #{content}</if>
            <if test="status != null ">and status = #{status}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectTraLearnCommunityById" parameterType="Long" resultMap="TraLearnCommunityResult">
        <include refid="selectTraLearnCommunityVo"/>
        where id = #{id}
    </select>
    <select id="getCountByCommunityId" resultType="java.lang.Integer">
        select count(1)
        from tra_learn_community
        where pid = #{communityId}
    </select>

    <insert id="insertTraLearnCommunity" parameterType="TraLearnCommunity" useGeneratedKeys="true" keyProperty="id">
        insert into tra_learn_community
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pid != null">pid,</if>
            <if test="title != null">title,</if>
            <if test="cover != null">cover,</if>
            <if test="content != null">content,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pid != null">#{pid},</if>
            <if test="title != null">#{title},</if>
            <if test="cover != null">#{cover},</if>
            <if test="content != null">#{content},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateTraLearnCommunity" parameterType="TraLearnCommunity">
        update tra_learn_community
        <trim prefix="SET" suffixOverrides=",">
            <if test="pid != null">pid = #{pid},</if>
            <if test="title != null">title = #{title},</if>
            <if test="cover != null">cover = #{cover},</if>
            <if test="content != null">content = #{content},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTraLearnCommunityById" parameterType="Long">
        delete
        from tra_learn_community
        where id = #{id}
    </delete>

    <delete id="deleteTraLearnCommunityByIds" parameterType="String">
        delete from tra_learn_community where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
        or pid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
