<template>
  <el-dialog class="standards-normal-dialog" title="修改信息" :visible.sync="showDialog" destroy-on-close :close-on-click-modal="false" width="80%">
    <el-form ref="form" :model="form" label-width="80px" :rules="rules">
      <el-form-item label="类型：">
        {{ form.type }}
      </el-form-item>
      <el-form-item label="标题：" v-if="isDetail" prop="title">
        <!-- <el-input :maxlength="50" :rows="6" v-model.trim="form.title"></el-input> -->
        {{form.title}}
      </el-form-item>
      <el-form-item label="描述：" prop="description">
        <el-input v-if="maxlength" type="textarea" :rows="20" v-model.trim="form.description" :maxlength="maxlength"></el-input>
        <el-input v-else type="textarea" :rows="20" v-model.trim="form.description"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="back">返 回</el-button>
      <el-button type="primary" size="small" @click="save">保 存</el-button>
    </span>
  </el-dialog>
</template>

<script>
const defaultForm = {
  type: "",
  title: "",
  description: "",
};
export default {
  name: "NormalDialog",
  props: {
    isDetail: {
      type: Boolean,
      default: () => true,
    },
  },
  data() {
    return {
      showDialog: false,
      key: 0,
      form: {},
      maxlength: 0,
      rules: {
        title: {
          required: true,
          message: "请输入标题",
        },
        description: {
          required: true,
          message: "请输入描述",
        },
      },
    };
  },
  methods: {
    save() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.$emit("modify", this.form);
          this.back();
        }
      });
    },
    show(form, maxlength) {
      this.$set(this, "form", { ...form });
      this.maxlength = maxlength;
      this.showDialog = true;
      this.$nextTick(() => {
        this.$refs.form.clearValidate('description');
      })
    },

    back() {
      this.showDialog = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.standards-normal-dialog {
  ::v-deep .el-dialog {
    &:not(.is-fullscreen) {
      margin-top: 8vh !important;
    }
    .el-dialog__header {
      background-color: #3d81f5;
      .el-dialog__title {
        color: white;
        font-weight: bold;
      }
      .el-dialog__headerbtn i {
        color: white;
      }
    }
    .el-dialog__body {
      padding-bottom: 0;
      .el-form {
        // height: 500px;
      }
    }
  }
}
</style>
