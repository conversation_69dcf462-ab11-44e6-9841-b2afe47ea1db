package com.thas.web.dto;

import com.thas.common.validate.ValidGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


/**
 * 评审共享描述DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ShareDescDTO", description = "评审共享-共享描述DTO（提出共享修改建议的评审员相关信息）")
public class ShareDescDTO implements Serializable {

    private static final long serialVersionUID = 7612345822902025970L;
    @ApiModelProperty(value = "用户id")
    private String userId;
    @ApiModelProperty(value = "用户名称")
    private String userName;
    @ApiModelProperty(value = "描述")
    private String desc;
    @ApiModelProperty(value = "提交时间")
    private String dateTime;

}
