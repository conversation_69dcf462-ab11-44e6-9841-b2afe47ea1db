import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getStandard } from "@/api/system/standard";
import { getToken } from '@/utils/auth'

NProgress.configure({ showSpinner: false })

const whiteList = ['/login', '/auth-redirect', '/bind', '/register', '/forgot', '/reset', '/application', '/reviewerApplication', 'reviewPlanAllot/reviewPlanAllotExamine', '/reviewPlanAllot/reviewPlanAllotReview', '/reviewPlanAllot/reviewPlanAllotDetail', '/conflictOfInterest', '/re-application/:code', '/re-reviewerApplication/:code']

router.beforeEach((to, from, next) => {
  NProgress.start()
  if (getToken()) {
    to.meta.title && store.dispatch('settings/setTitle', to.meta.title)

    if (localStorage.isFirstLogin == 1 && localStorage.whetherMdyPsw == 1 && to.path != '/reset') {
      next({ path: '/reset' })
    }
    /* has token*/
    if (to.path === '/login') {
      // 处理pdf超链接直接跳转的情况
      let path = to.query?.redirect || '/'
      next({ path })
      NProgress.done()
    } else {
      if (store.getters.roles.length === 0) {
        // 判断当前用户是否已拉取完user_info信息
        store.dispatch('GetInfo').then(() => {
          store.dispatch('GenerateRoutes').then(accessRoutes => {
            // 根据roles权限生成可访问的路由表
            router.addRoutes(accessRoutes) // 动态添加可访问路由表
            next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
          })
        }).catch(err => {
          store.dispatch('LogOut').then(() => {
            // Message.error(err)
            // next({ path: '/' })
            // 处理pdf超链接直接跳转的情况
            // 对原始路径进行 URL 编码
            const encodedPath = encodeURIComponent(to.fullPath);
            next(`/login?redirect=${encodedPath}`) // 否则全部重定向到登录页
          })
        })
      } else {
        next()
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else if ((/^\/re-application\/[0-9]{6}$/g.test(to.path)) || /^\/re-reviewerApplication\/[0-9]{6}$/g.test(to.path)) {
      next()
    } else {
      const encodedPath = encodeURIComponent(to.fullPath);
      next(`/login?redirect=${encodedPath}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
