<!--  -->
<template>
  <div class='evaluation-check evaluation' v-loading="submitLoading"  >
    <el-button v-if="showForm && !readonly" class="objection" size="small" type="text" @click="showForm = false">取消审核</el-button>
    <el-form ref="evaluationForm" :model="formData" :label-width="labelWidth" :rules="rules">
      <el-form-item v-if="!showForm && !readonly" class="form-item" label=" " style="margin:0;">
        <el-button type="danger" size="mini" @click="showFormChange" style="margin:auto;">有异议</el-button>
      </el-form-item>
      <el-form-item v-if="showForm || readonly" class="form-item" :class="{readonly: readonly}" label="审核结果：" prop="autResult">
        <dict-span :noTag='true'  :key="checkSummaryOptions.length+ Number(formData.autResult)" :value="formData.autResult" :options="agreeOrNotOptions" type="default"></dict-span>
      </el-form-item>
      <el-form-item v-if="showForm || readonly" class="form-item" :class="{readonly: !modifying || readonly}" :label="`${labelLabel}：`" prop="autDesc">
        <el-input v-if="modifying && !readonly" type="textarea" v-model="formData.autDesc" :placeholder="`请填写${labelLabel}`" :rows="4" :maxlength="descMaxLength"></el-input>
        <span v-else>{{ formData.autDesc || '--' }}</span>
      </el-form-item>
      <el-form-item class="form-item" label=" " v-if="showForm || readonly">
        <el-button v-if="modifying && !readonly" type="primary" @click="submit">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import mixin from "./mixin";

const defaultForm = {
  autResult: '',
  autDesc: ''
}
export default {
  name: 'EvaluationCheck',
  components: {},
  mixins: [mixin],
  props: {

  },
  watch: {
    showForm() {
      if (!this.showForm) {
        this.reply();
      }
    }
  },
  data() {
    return {
      showForm: false
    };
  },

  computed: {
    labelLabel() {
      let statuses = [
        this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_SUM_PASS,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_INQUIRE_PROC,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_REJECT,
        this.AutSaAudCurrentStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC,
      ];
      if (statuses.includes(this.status)) {
        return this.formData.autResult == 2 ? '原因' : '审核描述'
      }
      return '审核描述'
    },
  },

  created() {
    this.autResultMessage = '请选择审核结果'
    this.autDescMessage = `请填写${this.labelLabel}`
    if (this.formData.autResult != 1) {
      this.formData.autResult = 2
    }
  },

  mounted() {
    if (this.AutSaAudCurrentStatusEnum.WAIT_CONFIRM_FIRST_TRIAL == this.status) {
      if ((!this.aut || this.aut.rejected)) {
        this.modifying = true;
      }
      if (this.aut && this.aut.rejected) {
        this.formData.autResult = undefined;
        this.formData.autDesc = '';
        this.$refs.evaluationForm && this.$refs.evaluationForm.clearValidate();
      }
    }
  },

  methods: {
    submit() {
      const evaluationForm = this.$refs.evaluationForm;
      if (evaluationForm && evaluationForm.validate) {
        evaluationForm.validate(valid => {
          if (!valid) return;
          this.submitEmit()
        })
      }
    },

    showFormChange() {
      this.clearDesc();
      this.showForm = true;
      this.bus.$emit('heightChange')
    },

    clearDesc() {
      this.formData.autDesc = '';
    }
  },
}

</script>
<style lang='scss'>
</style>
