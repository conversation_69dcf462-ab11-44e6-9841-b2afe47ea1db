<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.HospitalBaseInfoMapper">

    <resultMap type="com.thas.web.domain.HospitalBaseInfo" id="HospitalBaseInfoResult">
        <result property="id" column="id"/>
        <result property="applyNo" column="apply_no"/>
        <result property="hospitalName" column="hospital_name"/>
        <result property="businessTime" column="business_time"/>
        <result property="practiceLicenseNo" column="practice_license_no"/>
        <result property="hospitalAddress" column="hospital_address"/>
        <result property="hospitalPhone" column="hospital_phone"/>
        <result property="hospitalExtPhone" column="hospital_ext_phone"/>
        <result property="hospitalFax" column="hospital_fax"/>
        <result property="hospitalOfficialWebsite" column="hospital_official_website"/>
        <result property="postalOde" column="postal_ode"/>
        <result property="hospitalContacts" column="hospital_contacts"/>
        <result property="contactsPhone" column="contacts_phone"/>
        <result property="contactsEmail" column="contacts_email"/>
        <result property="postalAddress" column="postal_address"/>
        <result property="hospitalType" column="hospital_type"/>
        <result property="formOwnership" column="form_ownership"/>
        <result property="formOwnershipOther" column="form_ownership_other"/>
        <result property="natureOperation" column="nature_operation"/>
        <result property="managementAffiliation" column="management_affiliation"/>
        <result property="managementAffiliationOther" column="management_affiliation_other"/>
        <result property="competentAuthorityName" column="competent_authority_name"/>
        <result property="hospitalLevel" column="hospital_level"/>
        <result property="teachingCategory" column="teaching_category"/>
        <result property="teachingCategoryOther" column="teaching_category_other"/>
        <result property="clinicalDepartmentNum" column="clinical_department_num"/>
        <result property="inpatientAreaNum" column="inpatient_area_num"/>
        <result property="medicalTechnologyNum" column="medical_technology_num"/>
        <result property="areaCovered" column="area_covered"/>
        <result property="areaArchitecture" column="area_architecture"/>
        <result property="areaBusinessArchitecture" column="area_business_architecture"/>
        <result property="preparationBed" column="preparation_bed"/>
        <result property="actualBed" column="actual_bed"/>
        <result property="bedUtilization" column="bed_utilization"/>
        <result property="onDutyNum" column="on_duty_num"/>
        <result property="healthTechnologyNum" column="health_technology_num"/>
        <result property="businessLicenseId" column="business_license_id"/>
        <result property="hospitalCertificate" column="hospital_certificate"/>
        <result property="first" column="first"/>
        <result property="hasIntnPro" column="has_intn_pro"/>
        <result property="beforeReviewerDate" column="before_reviewer_date"/>
        <result property="beforeReviewerConclusionGrade" column="before_reviewer_conclusion_grade"/>
        <result property="beforeReviewerConclusionLevel" column="before_reviewer_conclusion_level"/>
        <result property="signConfirmStatus" column="sign_confirm_status"/>
        <result property="signFileId" column="sign_file_id"/>
        <result property="authStatus" column="auth_status"/>
        <result property="authDesc" column="auth_desc"/>
        <result property="authPersonId" column="auth_person_id"/>
        <result property="authPersonName" column="auth_person_name"/>
        <result property="authDate" column="auth_date"/>
        <result property="planArea" column="plan_area"/>
        <result property="expectReviewDate" column="expect_review_date"/>
        <result property="status" column="status"/>
        <result property="creator" column="creator"/>
        <result property="createTime" column="create_time"/>
        <result property="updater" column="updater"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectHospitalBaseInfoVo">
        select id,
               apply_no,
               hospital_name,
               business_time,
               practice_license_no,
               hospital_address,
               hospital_phone,
               hospital_ext_phone,
               hospital_fax,
               hospital_official_website,
               postal_ode,
               hospital_type,
               form_ownership,
               form_ownership_other,
               nature_operation,
               management_affiliation,
               management_affiliation_other,
               competent_authority_name,
               hospital_level,
               teaching_category,
               teaching_category_other,
               clinical_department_num,
               inpatient_area_num,
               medical_technology_num,
               area_covered,
               area_architecture,
               area_business_architecture,
               preparation_bed,
               actual_bed,
               bed_utilization,
               on_duty_num,
               health_technology_num,
               business_license_id,
               hospital_certificate,
               first,
               has_intn_pro,
               before_reviewer_date,
               before_reviewer_conclusion_grade,
               sign_confirm_status,
               sign_file_id,
               before_reviewer_conclusion_level,
               auth_status, auth_desc,
               auth_person_id,
               auth_date,
               plan_area,
               expect_review_date,
               status,
               creator,
               create_time,
               updater,
               update_time,
               <include refid="SELECT_ENCRYPT_FIELD"/>
        from hospital_base_info
    </sql>

    <sql id="SQL_AES_ENCRYPT">'${@com.thas.web.config.SqlAesEncryptConfig@SQL_AES_ENCRYPT}'</sql>

    <sql id="SELECT_ENCRYPT_FIELD">
        AES_DECRYPT(unhex(hospital_contacts),<include refid="SQL_AES_ENCRYPT"/>) hospital_contacts,
        AES_DECRYPT(unhex(contacts_phone),<include refid="SQL_AES_ENCRYPT"/>) contacts_phone,
        AES_DECRYPT(unhex(contacts_email),<include refid="SQL_AES_ENCRYPT"/>) contacts_email,
        AES_DECRYPT(unhex(postal_address),<include refid="SQL_AES_ENCRYPT"/>) postal_address,
        AES_DECRYPT(unhex(auth_person_name),<include refid="SQL_AES_ENCRYPT"/>) auth_person_name
    </sql>

    <sql id="selectHospitalBaseInfoDTO">
        select hbi.id id,
               hbi.apply_no applyNo,
               hbi.hospital_name hospitalName,
               hbi.business_time businessTime,
               hbi.practice_license_no practiceLicenseNo,
               hbi.hospital_address hospitalAddress,
               hbi.hospital_phone hospitalPhone,
               hbi.hospital_ext_phone hospitalExtPhone,
               hbi.hospital_fax hospitalFax,
               hbi.hospital_official_website hospitalOfficialWebsite,
               hbi.postal_ode postalOde,
               AES_DECRYPT(unhex(hbi.hospital_contacts),<include refid="SQL_AES_ENCRYPT"/>) hospitalContacts,
               AES_DECRYPT(unhex(hbi.contacts_phone),<include refid="SQL_AES_ENCRYPT"/>) contactsPhone,
               AES_DECRYPT(unhex(hbi.contacts_email),<include refid="SQL_AES_ENCRYPT"/>) contactsEmail,
               AES_DECRYPT(unhex(hbi.postal_address),<include refid="SQL_AES_ENCRYPT"/>) postalAddress,
               AES_DECRYPT(unhex(hbi.auth_person_name),<include refid="SQL_AES_ENCRYPT"/>) authPersonName,
               hbi.hospital_type hospitalType,
               hbi.form_ownership formOwnership,
               hbi.form_ownership_other formOwnershipOther,
               hbi.nature_operation natureOperation,
               hbi.management_affiliation managementAffiliation,
               hbi.management_affiliation_other managementAffiliationOther,
               hbi.competent_authority_name competentAuthorityName,
               hbi.hospital_level hospitalLevel,
               hbi.teaching_category teachingCategory,
               hbi.teaching_category_other teachingCategoryOther,
               hbi.clinical_department_num clinicalDepartmentNum,
               hbi.inpatient_area_num inpatientAreaNum,
               hbi.medical_technology_num medicalTechnologyNum,
               hbi.area_covered areaCovered,
               hbi.area_architecture areaArchitecture,
               hbi.area_business_architecture areaBusinessArchitecture,
               hbi.preparation_bed preparationBed,
               hbi.actual_bed actualBed,
               hbi.bed_utilization bedUtilization,
               hbi.on_duty_num onDutyNum,
               hbi.health_technology_num healthTechnologyNum,
               hbi.business_license_id businessLicenseId,
               hbi.hospital_certificate hospitalCertificate,
               hbi.first first,
               hbi.has_intn_pro hasIntnPro,
               hbi.before_reviewer_date beforeReviewerDate,
               hbi.before_reviewer_conclusion_grade beforeReviewerConclusionGrade,
               hbi.sign_confirm_status beforeReviewerConclusionLevel,
               hbi.sign_file_id signConfirmStatus,
               hbi.before_reviewer_conclusion_level signFileId,
               hbi.auth_status authStatus, auth_desc authDesc,
               hbi.auth_person_id authPersonId,
               hbi.auth_date authDate,
               hbi.status status,
               hbi.creator creator,
               hbi.create_time createTime,
               hbi.updater updater,
               hbi.update_time updateTime,

               AES_DECRYPT(unhex(hlp.legal_person_name),<include refid="SQL_AES_ENCRYPT"/>) legalPersonName,
               AES_DECRYPT(unhex(hlp.legal_person_phone),<include refid="SQL_AES_ENCRYPT"/>) legalPersonPhone,
               AES_DECRYPT(unhex(hlp.legal_person_mobile),<include refid="SQL_AES_ENCRYPT"/>) legalPersonMobile,
               AES_DECRYPT(unhex(hlp.legal_person_email),<include refid="SQL_AES_ENCRYPT"/>) legalPersonEmail,

               hlp.gender gender,
               hlp.legal_person_post legalpersonpost,
               hlp.legal_person_title legalPersonTitle,
               hlp.legal_person_fax legalPersonFax,

               AES_DECRYPT(unhex(hac.auth_contact_name),<include refid="SQL_AES_ENCRYPT"/>) authContactName,
               AES_DECRYPT(unhex(hac.auth_contact_mobile),<include refid="SQL_AES_ENCRYPT"/>) authContactMobile,
               AES_DECRYPT(unhex(hac.auth_contact_email),<include refid="SQL_AES_ENCRYPT"/>) authContactEmail,
               AES_DECRYPT(unhex(hac.contact_address),<include refid="SQL_AES_ENCRYPT"/>) contactAddress
        FROM hospital_base_info hbi
        LEFT JOIN hospital_legal_person hlp ON hlp.apply_no = hbi.apply_no
		LEFT JOIN hospital_auth_contact hac ON hac.apply_no = hbi.apply_no
    </sql>



    <select id="queryHospitalList" parameterType="com.thas.web.dto.QueryHospitalListDTO"
            resultType="com.thas.web.dto.QueryHospitalListDTO">
        select * from (
        SELECT
        hbi.apply_no applyNo,
        hbi.hospital_name hospitalName,
        AES_DECRYPT(unhex(hac.auth_contact_name),<include refid="SQL_AES_ENCRYPT"/>) authContactName,
        AES_DECRYPT(unhex(hac.auth_contact_mobile),<include refid="SQL_AES_ENCRYPT"/>) authContactMobile,
        AES_DECRYPT(unhex(hac.auth_contact_email),<include refid="SQL_AES_ENCRYPT"/>) authContactEmail,
        hbi.auth_status authStatus,
        AES_DECRYPT(unhex(hbi.auth_person_name),<include refid="SQL_AES_ENCRYPT"/>) authPersonName,
        hbi.create_time createTime,
        hbi.auth_date authDate,
        hbi.practice_license_no practiceLicenseNo
        FROM
        hospital_base_info hbi
        LEFT JOIN hospital_auth_contact hac ON hbi.apply_no = hac.apply_no
        <where>
            hbi.status = 1 and hbi.sign_confirm_status = 1
            <if test="hospitalName != null  and hospitalName != ''">and hbi.hospital_name like concat('%', #{hospitalName}, '%')</if>
            <if test="authContactMobile != null  and authContactMobile != ''"> and hac.auth_contact_mobile = HEX(AES_ENCRYPT(#{authContactMobile},<include refid="SQL_AES_ENCRYPT"/>))</if>
            <if test="authStatus != null"> and hbi.auth_status =#{authStatus} </if>
        </where>
        order by hbi.create_time desc
        ) aa
        where
        1=1
        <if test="authContactName != null  and authContactName != ''"> and authContactName like concat('%', #{authContactName} , '%')</if>

    </select>

    <select id="selectHosPlanList" parameterType="com.thas.web.dto.QueryHosReviewPlanDTO"
            resultType="com.thas.web.dto.HosReviewPlanVO">
        SELECT
        hbi.apply_no applyNo,
        hbi.hospital_name hospitalName,
        AES_DECRYPT(unhex(su.nick_name),<include refid="SQL_AES_ENCRYPT"/>) creator,
        hpd.hos_status hosStatus,
        hpd.create_time planTime,
        hpd.confirm_time confirmTime,
        hpd.pre_dis_complete preDisComplete,
        hpd.review_dis_complete reviewDisComplete,
        hpd.senior_review_dis_complete seniorReviewDisComplete,
        hpd.cycle_status cycleStatus,
        hpd.rev_file_id revFileId
        FROM
        hospital_base_info hbi
        LEFT JOIN hospital_planned_distribution hpd ON hbi.apply_no = hpd.apply_no AND hpd.status = 1
        LEFT JOIN sys_user su ON hpd.creator = su.user_id
        <where>
            hbi.auth_status = 2 and hbi.status = 1
            <if test="hospitalName != null  and hospitalName != ''">and hbi.hospital_name like concat('%',
                #{hospitalName}, '%')
            </if>
            <if test="applyNo != null  and applyNo != ''">and hbi.apply_no = #{applyNo}</if>
            <if test="hosStatus != null ">and hpd.hos_status = #{hosStatus}</if>
        </where>
        ORDER BY hpd.update_time DESC
    </select>

    <select id="selectHospitalBaseInfoList" parameterType="HospitalBaseInfo" resultMap="HospitalBaseInfoResult">
        <include refid="selectHospitalBaseInfoVo"/>
        <where>
            <if test="applyNo != null  and applyNo != ''">and apply_no = #{applyNo}</if>
            <if test="hospitalName != null  and hospitalName != ''">and hospital_name like concat('%', #{hospitalName},
                '%')
            </if>
            <if test="businessTime != null  and businessTime != ''">and business_time = #{businessTime}</if>
            <if test="practiceLicenseNo != null  and practiceLicenseNo != ''">and practice_license_no =
                #{practiceLicenseNo}
            </if>
            <if test="hospitalAddress != null  and hospitalAddress != ''">and hospital_address = #{hospitalAddress}</if>
            <if test="hospitalPhone != null  and hospitalPhone != ''">and hospital_phone = #{hospitalPhone}</if>
            <if test="hospitalExtPhone != null  and hospitalExtPhone != ''">and hospital_ext_phone = #{hospitalExtPhone}</if>
            <if test="hospitalFax != null  and hospitalFax != ''">and hospital_fax = #{hospitalFax}</if>
            <if test="hospitalOfficialWebsite != null  and hospitalOfficialWebsite != ''">and hospital_official_website
                = #{hospitalOfficialWebsite}
            </if>
            <if test="postalOde != null  and postalOde != ''">and postal_ode = #{postalOde}</if>

            <if test="hospitalContacts != null  and hospitalContacts != ''">and hospital_contacts = HEX(AES_ENCRYPT(#{hospitalContacts},<include refid="SQL_AES_ENCRYPT"/>))</if>
            <if test="contactsPhone != null  and contactsPhone != ''">and contacts_phone = HEX(AES_ENCRYPT(#{contactsPhone},<include refid="SQL_AES_ENCRYPT"/>))</if>
            <if test="contactsEmail != null  and contactsEmail != ''">and contacts_email = HEX(AES_ENCRYPT(#{contactsEmail},<include refid="SQL_AES_ENCRYPT"/>))</if>
            <if test="postalAddress != null  and postalAddress != ''"> and postal_address = HEX(AES_ENCRYPT(#{postalAddress},<include refid="SQL_AES_ENCRYPT"/>))</if>
            <if test="authPersonName != null and authPersonName != ''">and auth_person_name = HEX(AES_ENCRYPT(#{authPersonName},<include refid="SQL_AES_ENCRYPT"/>))</if>

            <if test="hospitalType != null ">and hospital_type = #{hospitalType}</if>
            <if test="formOwnership != null ">and form_ownership = #{formOwnership}</if>
            <if test="formOwnershipOther != null  and formOwnershipOther != ''">and form_ownership_other =
                #{formOwnershipOther}
            </if>
            <if test="natureOperation != null ">and nature_operation = #{natureOperation}</if>
            <if test="managementAffiliation != null ">and management_affiliation = #{managementAffiliation}</if>
            <if test="managementAffiliationOther != null  and managementAffiliationOther != ''">and
                management_affiliation_other = #{managementAffiliationOther}
            </if>
            <if test="competentAuthorityName != null  and competentAuthorityName != ''">and competent_authority_name
                like concat('%', #{competentAuthorityName}, '%')
            </if>
            <if test="hospitalLevel != null ">and hospital_level = #{hospitalLevel}</if>
            <if test="teachingCategory != null ">and teaching_category = #{teachingCategory}</if>
            <if test="teachingCategoryOther != null  and teachingCategoryOther != ''">and teaching_category_other =
                #{teachingCategoryOther}
            </if>
            <if test="clinicalDepartmentNum != null ">and clinical_department_num = #{clinicalDepartmentNum}</if>
            <if test="inpatientAreaNum != null ">and inpatient_area_num = #{inpatientAreaNum}</if>
            <if test="medicalTechnologyNum != null ">and medical_technology_num = #{medicalTechnologyNum}</if>
            <if test="areaCovered != null ">and area_covered = #{areaCovered}</if>
            <if test="areaArchitecture != null ">and area_architecture = #{areaArchitecture}</if>
            <if test="areaBusinessArchitecture != null ">and area_business_architecture = #{areaBusinessArchitecture}
            </if>
            <if test="preparationBed != null ">and preparation_bed = #{preparationBed}</if>
            <if test="actualBed != null ">and actual_bed = #{actualBed}</if>
            <if test="bedUtilization != null  and bedUtilization != ''">and bed_utilization = #{bedUtilization}</if>
            <if test="onDutyNum != null ">and on_duty_num = #{onDutyNum}</if>
            <if test="healthTechnologyNum != null ">and health_technology_num = #{healthTechnologyNum}</if>
            <if test="businessLicenseId != null and businessLicenseId != ''">and business_license_id = #{businessLicenseId}</if>
            <if test="hospitalCertificate != null ">and hospital_certificate = #{hospitalCertificate}</if>
            <if test="first != null ">and first = #{first}</if>
            <if test="beforeReviewerDate != null  and beforeReviewerDate != ''">and before_reviewer_date =
                #{beforeReviewerDate}
            </if>
            <if test="beforeReviewerConclusionGrade != null ">and before_reviewer_conclusion_grade =
                #{beforeReviewerConclusionGrade}
            </if>
            <if test="beforeReviewerConclusionLevel != null ">and before_reviewer_conclusion_level =
                #{beforeReviewerConclusionLevel}
            </if>
            <if test="authStatus != null ">and auth_status = #{authStatus}</if>
            <if test="authDesc != null and authDesc != ''">and auth_desc = #{authDesc}</if>
            <if test="authPersonId != null and authPersonId != ''">and auth_person_id = #{authPersonId}</if>
            <if test="authDate != null and authDate != ''">and auth_Date = #{authDate}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="creator != null  and creator != ''">and creator = #{creator}</if>
            <if test="updater != null  and updater != ''">and updater = #{updater}</if>
        </where>
    </select>

    <select id="selectHospitalBaseInfoById" parameterType="Long" resultMap="HospitalBaseInfoResult">
        <include refid="selectHospitalBaseInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectHospitalByApplyNo" parameterType="com.thas.web.dto.QueryBaseConditionDTO"
            resultMap="HospitalBaseInfoResult">
        <include refid="selectHospitalBaseInfoVo"/>
        <where>
            status = 1
            <if test="commonId != null  and commonId != ''">and apply_no = #{commonId}</if>
            <if test="practiceLicenseNo != null  and practiceLicenseNo != ''">and practice_license_no =
                #{practiceLicenseNo}
            </if>
        </where>
    </select>

    <select id="qryHosBaseInfoByEmail" resultMap="HospitalBaseInfoResult">
        <include refid="selectHospitalBaseInfoVo"/>
        where contacts_email = HEX(AES_ENCRYPT(#{email},<include refid="SQL_AES_ENCRYPT"/>)) and status = 1
    </select>

    <select id="queryUnassignedHos" parameterType="com.thas.web.dto.QueryBaseConditionDTO" resultType="com.thas.web.dto.QueryUnassignedHosDTO">
        SELECT
        apply_no applyNo,
        hospital_name hospitalName
        FROM
        hospital_base_info
        WHERE
        STATUS = 1 AND auth_status = 2
        <if test="commonId != null  and commonId != ''">
          and apply_no NOT IN ( SELECT hospital_apply_no FROM sys_user_hospital )
        </if>
        <if test="hospitalName != null  and hospitalName != ''">
            and hospital_name like concat('%',#{hospitalName},'%')
        </if>
    </select>

    <select id="selectApplyNoByUserId" parameterType="String" resultType="String">
        SELECT apply_no
        FROM hospital_base_info
        WHERE apply_no IN (SELECT hospital_apply_no FROM sys_user_hospital WHERE user_id = #{userId})
          AND STATUS = 1;
    </select>

    <select id="qryPassHosInfo" resultType="com.thas.web.domain.HospitalBaseInfo">
        SELECT
            hbi.apply_no applyNo,
            hbi.hospital_name hospitalName
        FROM
            sys_user_hospital suh
                LEFT JOIN hospital_base_info hbi ON suh.hospital_apply_no = hbi.apply_no
        WHERE
            hbi.`status` = 1
          AND hbi.auth_status = 2;
    </select>

    <insert id="insertHospitalBaseInfo" parameterType="HospitalBaseInfo" useGeneratedKeys="true" keyProperty="id">
        insert into hospital_base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">apply_no,</if>
            <if test="hospitalName != null and hospitalName != ''">hospital_name,</if>
            <if test="businessTime != null and businessTime != ''">business_time,</if>
            <if test="practiceLicenseNo != null and practiceLicenseNo != ''">practice_license_no,</if>
            <if test="hospitalAddress != null and hospitalAddress != ''">hospital_address,</if>
            <if test="hospitalPhone != null and hospitalPhone != ''">hospital_phone,</if>
            <if test="hospitalExtPhone != null and hospitalExtPhone != ''">hospital_ext_phone,</if>
            <if test="hospitalFax != null">hospital_fax,</if>
            <if test="hospitalOfficialWebsite != null">hospital_official_website,</if>
            <if test="postalOde != null">postal_ode,</if>
            <if test="hospitalContacts != null">hospital_contacts,</if>
            <if test="contactsPhone != null">contacts_phone,</if>
            <if test="contactsEmail != null">contacts_email,</if>
            <if test="postalAddress != null">postal_address,</if>
            <if test="hospitalType != null">hospital_type,</if>
            <if test="formOwnership != null">form_ownership,</if>
            <if test="formOwnershipOther != null">form_ownership_other,</if>
            <if test="natureOperation != null">nature_operation,</if>
            <if test="managementAffiliation != null">management_affiliation,</if>
            <if test="managementAffiliationOther != null">management_affiliation_other,</if>
            <if test="competentAuthorityName != null and competentAuthorityName != ''">competent_authority_name,</if>
            <if test="hospitalLevel != null">hospital_level,</if>
            <if test="teachingCategory != null">teaching_category,</if>
            <if test="teachingCategoryOther != null and teachingCategoryOther != ''">teaching_category_other,</if>
            <if test="clinicalDepartmentNum != null">clinical_department_num,</if>
            <if test="inpatientAreaNum != null">inpatient_area_num,</if>
            <if test="medicalTechnologyNum != null">medical_technology_num,</if>
            <if test="areaCovered != null">area_covered,</if>
            <if test="areaArchitecture != null">area_architecture,</if>
            <if test="areaBusinessArchitecture != null">area_business_architecture,</if>
            <if test="preparationBed != null">preparation_bed,</if>
            <if test="actualBed != null">actual_bed,</if>
            <if test="bedUtilization != null and bedUtilization != ''">bed_utilization,</if>
            <if test="onDutyNum != null">on_duty_num,</if>
            <if test="healthTechnologyNum != null">health_technology_num,</if>
            <if test="businessLicenseId != null and businessLicenseId != ''">business_license_id,</if>
            <if test="hospitalCertificate != null">hospital_certificate,</if>
            <if test="first != null">first,</if>
            <if test="hasIntnPro != null">has_intn_pro,</if>
            <if test="beforeReviewerDate != null and beforeReviewerDate != ''">before_reviewer_date,</if>
            <if test="beforeReviewerConclusionGrade != null">before_reviewer_conclusion_grade,</if>
            <if test="beforeReviewerConclusionLevel != null">before_reviewer_conclusion_level,</if>
            <if test="authStatus != null">auth_status,</if>
            <if test="authDesc != null and authDesc != ''">auth_desc,</if>
            <if test="authPersonId != null and authPersonId != ''">auth_person_id,</if>
            <if test="authPersonName != null and authPersonName != ''">auth_person_name,</if>
            <if test="authDate != null and authDate != ''">auth_date,</if>
            <if test="planArea != null and planArea != ''">plan_area,</if>
            <if test="expectReviewDate != null and expectReviewDate != ''">expect_review_date,</if>
            <if test="status != null">status,</if>
            <if test="creator != null and creator != ''">creator,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updater != null and updater != ''">updater,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">#{applyNo},</if>
            <if test="hospitalName != null and hospitalName != ''">#{hospitalName},</if>
            <if test="businessTime != null and businessTime != ''">#{businessTime},</if>
            <if test="practiceLicenseNo != null and practiceLicenseNo != ''">#{practiceLicenseNo},</if>
            <if test="hospitalAddress != null and hospitalAddress != ''">#{hospitalAddress},</if>
            <if test="hospitalPhone != null and hospitalPhone != ''">#{hospitalPhone},</if>
            <if test="hospitalExtPhone != null and hospitalExtPhone != ''">#{hospitalExtPhone},</if>
            <if test="hospitalFax != null">#{hospitalFax},</if>
            <if test="hospitalOfficialWebsite != null">#{hospitalOfficialWebsite},</if>
            <if test="postalOde != null">#{postalOde},</if>

            <if test="hospitalContacts != null">HEX(AES_ENCRYPT(#{hospitalContacts},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="contactsPhone != null">HEX(AES_ENCRYPT(#{contactsPhone},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="contactsEmail != null">HEX(AES_ENCRYPT(#{contactsEmail},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="postalAddress != null">HEX(AES_ENCRYPT(#{postalAddress},<include refid="SQL_AES_ENCRYPT"/>)),</if>

            <if test="hospitalType != null">#{hospitalType},</if>
            <if test="formOwnership != null">#{formOwnership},</if>
            <if test="formOwnershipOther != null">#{formOwnershipOther},</if>
            <if test="natureOperation != null">#{natureOperation},</if>
            <if test="managementAffiliation != null">#{managementAffiliation},</if>
            <if test="managementAffiliationOther != null">#{managementAffiliationOther},</if>
            <if test="competentAuthorityName != null and competentAuthorityName != ''">#{competentAuthorityName},</if>
            <if test="hospitalLevel != null">#{hospitalLevel},</if>
            <if test="teachingCategory != null">#{teachingCategory},</if>
            <if test="teachingCategoryOther != null and teachingCategoryOther != ''">#{teachingCategoryOther},</if>
            <if test="clinicalDepartmentNum != null">#{clinicalDepartmentNum},</if>
            <if test="inpatientAreaNum != null">#{inpatientAreaNum},</if>
            <if test="medicalTechnologyNum != null">#{medicalTechnologyNum},</if>
            <if test="areaCovered != null">#{areaCovered},</if>
            <if test="areaArchitecture != null">#{areaArchitecture},</if>
            <if test="areaBusinessArchitecture != null">#{areaBusinessArchitecture},</if>
            <if test="preparationBed != null">#{preparationBed},</if>
            <if test="actualBed != null">#{actualBed},</if>
            <if test="bedUtilization != null and bedUtilization != ''">#{bedUtilization},</if>
            <if test="onDutyNum != null">#{onDutyNum},</if>
            <if test="healthTechnologyNum != null">#{healthTechnologyNum},</if>
            <if test="businessLicenseId != null and businessLicenseId != ''">#{businessLicenseId},</if>
            <if test="hospitalCertificate != null">#{hospitalCertificate},</if>
            <if test="first != null">#{first},</if>
            <if test="hasIntnPro != null">#{hasIntnPro},</if>
            <if test="beforeReviewerDate != null and beforeReviewerDate != ''">#{beforeReviewerDate},</if>
            <if test="beforeReviewerConclusionGrade != null">#{beforeReviewerConclusionGrade},</if>
            <if test="beforeReviewerConclusionLevel != null">#{beforeReviewerConclusionLevel},</if>
            <if test="authStatus != null">#{authStatus},</if>
            <if test="authDesc != null and authDesc !=''">#{authDesc},</if>
            <if test="authPersonId != null and authPersonId != ''">#{authPersonId},</if>

            <if test="authPersonName != null and authPersonName != ''">HEX(AES_ENCRYPT(#{authPersonName},<include refid="SQL_AES_ENCRYPT"/>)),</if>

            <if test="authDate != null and authDate != ''">#{authDate},</if>
            <if test="planArea != null and planArea != ''">#{planArea},</if>
            <if test="expectReviewDate != null and expectReviewDate != ''">#{expectReviewDate},</if>
            <if test="status != null">#{status},</if>
            <if test="creator != null and creator != ''">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updater != null and updater != ''">#{updater},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateHospitalBaseInfo" parameterType="HospitalBaseInfo">
        update hospital_base_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">apply_no = #{applyNo},</if>
            <if test="hospitalName != null and hospitalName != ''">hospital_name = #{hospitalName},</if>
            <if test="businessTime != null and businessTime != ''">business_time = #{businessTime},</if>
            <if test="practiceLicenseNo != null and practiceLicenseNo != ''">practice_license_no =
                #{practiceLicenseNo},
            </if>
            <if test="hospitalAddress != null and hospitalAddress != ''">hospital_address = #{hospitalAddress},</if>
            <if test="hospitalPhone != null and hospitalPhone != ''">hospital_phone = #{hospitalPhone},</if>
            <if test="hospitalExtPhone != null and hospitalExtPhone != ''">hospital_ext_phone = #{hospitalExtPhone},</if>
            <if test="hospitalFax != null">hospital_fax = #{hospitalFax},</if>
            <if test="hospitalOfficialWebsite != null">hospital_official_website = #{hospitalOfficialWebsite},</if>
            <if test="postalOde != null">postal_ode = #{postalOde},</if>

            <if test="hospitalContacts != null">hospital_contacts = HEX(AES_ENCRYPT(#{hospitalContacts},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="contactsPhone != null">contacts_phone = HEX(AES_ENCRYPT(#{contactsPhone},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="contactsEmail != null">contacts_email = HEX(AES_ENCRYPT(#{contactsEmail},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="postalAddress != null">postal_address = HEX(AES_ENCRYPT(#{postalAddress},<include refid="SQL_AES_ENCRYPT"/>)),</if>

            <if test="hospitalType != null">hospital_type = #{hospitalType},</if>
            <if test="formOwnership != null">form_ownership = #{formOwnership},</if>
            <if test="formOwnershipOther != null">form_ownership_other = #{formOwnershipOther},</if>
            <if test="natureOperation != null">nature_operation = #{natureOperation},</if>
            <if test="managementAffiliation != null">management_affiliation = #{managementAffiliation},</if>
            <if test="managementAffiliationOther != null">management_affiliation_other =
                #{managementAffiliationOther},
            </if>
            <if test="competentAuthorityName != null and competentAuthorityName != ''">competent_authority_name =
                #{competentAuthorityName},
            </if>
            <if test="hospitalLevel != null">hospital_level = #{hospitalLevel},</if>
            <if test="teachingCategory != null">teaching_category = #{teachingCategory},</if>
            <if test="teachingCategoryOther != null and teachingCategoryOther != ''">teaching_category_other =
                #{teachingCategoryOther},
            </if>
            <if test="clinicalDepartmentNum != null">clinical_department_num = #{clinicalDepartmentNum},</if>
            <if test="inpatientAreaNum != null">inpatient_area_num = #{inpatientAreaNum},</if>
            <if test="medicalTechnologyNum != null">medical_technology_num = #{medicalTechnologyNum},</if>
            <if test="areaCovered != null">area_covered = #{areaCovered},</if>
            <if test="areaArchitecture != null">area_architecture = #{areaArchitecture},</if>
            <if test="areaBusinessArchitecture != null">area_business_architecture = #{areaBusinessArchitecture},</if>
            <if test="preparationBed != null">preparation_bed = #{preparationBed},</if>
            <if test="actualBed != null">actual_bed = #{actualBed},</if>
            <if test="bedUtilization != null and bedUtilization != ''">bed_utilization = #{bedUtilization},</if>
            <if test="onDutyNum != null">on_duty_num = #{onDutyNum},</if>
            <if test="healthTechnologyNum != null">health_technology_num = #{healthTechnologyNum},</if>
            <if test="businessLicenseId != null and businessLicenseId != ''">business_license_id = #{businessLicenseId},</if>
            <if test="hospitalCertificate != null">hospital_certificate = #{hospitalCertificate},</if>
            <if test="first != null">first = #{first},</if>
            <if test="hasIntnPro != null">has_intn_pro = #{hasIntnPro},</if>
            <if test="beforeReviewerDate != null and beforeReviewerDate != ''">before_reviewer_date =
                #{beforeReviewerDate},
            </if>
            <if test="beforeReviewerConclusionGrade != null">before_reviewer_conclusion_grade =
                #{beforeReviewerConclusionGrade},
            </if>
            <if test="beforeReviewerConclusionLevel != null">before_reviewer_conclusion_level =
                #{beforeReviewerConclusionLevel},
            </if>
            <if test="authStatus != null">auth_status = #{authStatus},</if>
            <if test="authDesc != null and authDesc != ''">auth_Desc = #{authDesc},</if>
            <if test="authPersonId != null and authPersonId != ''">auth_person_id = #{authPersonId},</if>

            <if test="authPersonName != null and authPersonName != ''">auth_person_name = HEX(AES_ENCRYPT(#{authPersonName},<include refid="SQL_AES_ENCRYPT"/>)),</if>

            <if test="authDate != null and authDate != ''">auth_Date = #{authDate},</if>
            <if test="planArea != null and planArea != ''">plan_area = #{planArea},</if>
            <if test="status != null">status = #{status},</if>
            <if test="creator != null and creator != ''">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="signConfirmStatus != null">sign_confirm_status = #{signConfirmStatus},</if>
            <if test="signFileId != null and signFileId != ''">sign_file_id = #{signFileId},</if>
            <if test="expectReviewDate != null and expectReviewDate != ''">expect_review_date = #{expectReviewDate},</if>
        </trim>
        where apply_no = #{applyNo}
    </update>

    <delete id="deleteHospitalBaseInfoById" parameterType="Long">
        delete
        from hospital_base_info
        where id = #{id}
    </delete>

    <delete id="deleteHospitalBaseInfoByApplyNo" parameterType="String">
        delete
        from hospital_base_info
        where apply_no = #{applyNo}
    </delete>

    <delete id="deleteHospitalBaseInfoByIds" parameterType="String">
        delete from hospital_base_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectCurrentYearApplyCountByApplyNO" parameterType="String" resultType="map">
        select YEAR(ttt.create_time) "year", count(1) "count"
        from hospital_base_info tt,
             (select t.create_time from hospital_base_info t where t.apply_no = #{applyNo}) ttt
        where YEAR (tt.create_time) = YEAR (ttt.create_time)
          and tt.create_time <![CDATA[ <= ]]> ttt.create_time
    </select>

    <select id="selectBatchHospitalBaseInfoList" parameterType="HospitalBaseInfo" resultType="com.thas.web.domain.dto.HospitalBaseInfosDTO">
        <include refid="selectHospitalBaseInfoDTO"/>
        where hbi.apply_no in
        <foreach item="applyNos" collection="list" open="(" separator="," close=")">
            #{applyNos}
        </foreach>
        and hbi.status = 1
        and hlp.status = 1
        and hac.status = 1
    </select>


    <select id="getApplyNoAndCycleList"
            resultType="com.thas.web.domain.dto.ApplyAndCycleListDTO">
        <![CDATA[
        select ttt.apply_no applyNo, ttt.aut_code autCode, ttt.convert_aut_status convertAutStatus, ttt.old_aut_status oldAutStatus, ttt.cycle_stage cycleStage, aa.cycle cycle
        from (
                 select tt.apply_no, tt.aut_code, tt.convert_aut_status, tt.old_aut_status, a.cycle_stage
                 from (
                          select t.apply_no,
                                 asr.aut_status                   old_aut_status,
                                 ifnull(asr.aut_status, '010101') convert_aut_status,
                                 asr.aut_code
                          from (
                                   select hbi.apply_no
                                   from hospital_base_info hbi
                                   where hbi.status = 1
                                     and hbi.sign_confirm_status = 1
                                     and hbi.auth_status = 2
                                     and hbi.apply_no in (
                                       select DISTINCT hrc.apply_no
                                       from hospital_review_cycle hrc
                                       where hrc.`status` = '1'
                                   )) t
                                   left JOIN aut_sa_relation asr
                                             ON t.apply_no = asr.hospital_apply_no and asr.status = 1
                      ) tt
                          left JOIN asa_status_config a ON a.current_status = tt.convert_aut_status
             ) ttt
                 left JOIN hospital_review_cycle aa
                           ON aa.apply_no = ttt.apply_no and aa.stage_value = ttt.cycle_stage and aa.`status` = '1' and
                              str_to_date(SUBSTRING_INDEX(aa.cycle, ',', -1), '%Y-%m-%d') < CURDATE()
        where aa.cycle is not null
        ]]>
    </select>

</mapper>
