<!--  -->
<template>
  <div class='concerns-response'>
    <el-table :data="dataSource" border stripe>
      <el-table-column label="款号" width="120" prop="clauseNo">
        <template slot-scope="{ row }">
          <span>
            {{row.isStar == 1 ? "★" : ""}}
            {{row.clauseNo}}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="评审员评分结果" width="120"></el-table-column>
      <el-table-column label="评审员报告"></el-table-column>
      <el-table-column>
        <template slot="header">
          <span>验证评审员提出疑问</span>
          <el-tooltip class="item" effect="dark" content="请提供验证审查时发现报告中可能的评分错误/需要注意/澄清的地方及修改意见" placement="top-start">
            <i class="el-icon-info" style="color:#409EFF;margin-left:5px;"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column>
        <template slot="header">
          <span>评审员回应</span>
          <el-tooltip class="item" effect="dark" content="请回应验证评审员提问及修改报告和评分 如无需修改需青楚说明原因" placement="top-start">
            <i class="el-icon-info" style="color:#409EFF;margin-left:5px;"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'Concerns',
  components: {},
  props: {},
  watch: {},
  data() {
    return {
      dataSource: [],
      reviewResultOptions: [],
    };
  },

  computed: {
    autResultText() {
      return autResult => {
        let resultOption = this.reviewResultOptions.find(option => option.dictValue == autResult);
        return (resultOption || {}).dictLabel;
      }
    },
  },

  async created() {
    try {
      this.reviewResultOptions = await this.getDicts("review_result").data;
    } catch (error) {
      // console.log(error);
    }
  },

  mounted() { },

  methods: {

  }
}

</script>
<style lang='scss' scoped>
</style>