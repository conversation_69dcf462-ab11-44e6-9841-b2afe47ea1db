#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffde4292683, pid=8400, tid=15604
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# Problematic frame:
# V  [jvm.dll+0x1e2683]
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://github.com/adoptium/adoptium-support/issues
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\5d08634aeff5bf03a8d363bf8e64864a\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\5d08634aeff5bf03a8d363bf8e64864a\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-4b4201e3d8a82eb8e9f0157cb487105a-sock

Host: Intel(R) Core(TM) i5-10300H CPU @ 2.50GHz, 8 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4484)
Time: Wed Jul 30 19:50:59 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.4484) elapsed time: 22.362039 seconds (0d 0h 0m 22s)

---------------  T H R E A D  ---------------

Current thread (0x00000188efb04550):  JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=15604, stack(0x0000004f90b00000,0x0000004f90c00000) (1024K)]


Current CompileTask:
C2:22362 10104   !   4       org.eclipse.jdt.internal.core.search.indexing.BinaryIndexer::indexDocument (1168 bytes)

Stack: [0x0000004f90b00000,0x0000004f90c00000],  sp=0x0000004f90bfca90,  free space=1010k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x1e2683]
V  [jvm.dll+0x382ac6]
V  [jvm.dll+0x381f0a]
V  [jvm.dll+0x247af0]
V  [jvm.dll+0x2470cf]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


siginfo: EXCEPTION_ACCESS_VIOLATION (0xc0000005), reading address 0x00000188d5bb8d00


Registers:
RAX=0x00000188f9732780, RBX=0x0000000000000000, RCX=0x00000188f7121d20, RDX=0xfffffffff7121960
RSP=0x0000004f90bfca90, RBP=0x00000188f6865780, RSI=0x0000004f90bfcc10, RDI=0xffffffffffffffff
R8 =0x00000000ffffffff, R9 =0x0000000000000000, R10=0x0000000000000000, R11=0x3333333333333333
R12=0x0000000000000029, R13=0x0000000000000148, R14=0x00000188fb0fcf40, R15=0x00007ffde4cd5b00
RIP=0x00007ffde4292683, EFLAGS=0x0000000000010287

XMM[0]=0x0000000000000000 0x3ec0000702538863
XMM[1]=0x0000000000000000 0x408dcd6f177dcbd2
XMM[2]=0x0000000000000000 0x0000000000000000
XMM[3]=0x0000000000000000 0x0000000000000000
XMM[4]=0x0000000000000000 0x0000000000000000
XMM[5]=0x0000000000000000 0x0000000000000000
XMM[6]=0x0000000000000000 0x408f400000000000
XMM[7]=0x0000000000000000 0x0000000000000000
XMM[8]=0x0000000000000000 0x0000000000000000
XMM[9]=0x0000000000000000 0x0000000000000000
XMM[10]=0x0000000000000000 0x0000000000000000
XMM[11]=0x0000000000000000 0x0000000000000000
XMM[12]=0x0000000000000000 0x0000000000000000
XMM[13]=0x0000000000000000 0x0000000000000000
XMM[14]=0x0000000000000000 0x0000000000000000
XMM[15]=0x0000000000000000 0x0000000000000000
  MXCSR=0x00001fb2


Register to memory mapping:

RAX=0x00000188f9732780 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
RBX=0x0 is null
RCX=0x00000188f7121d20 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
RDX=0xfffffffff7121960 is an unknown value
RSP=0x0000004f90bfca90 is pointing into the stack for thread: 0x00000188efb04550
RBP=0x00000188f6865780 points into unknown readable memory: 0x00007ffde4a9a788 | 88 a7 a9 e4 fd 7f 00 00
RSI=0x0000004f90bfcc10 is pointing into the stack for thread: 0x00000188efb04550
RDI=0xffffffffffffffff is an unknown value
R8 =0x00000000ffffffff is an unallocated location in the heap
R9 =0x0 is null
R10=0x0 is null
R11=0x3333333333333333 is an unknown value
R12=0x0000000000000029 is an unknown value
R13=0x0000000000000148 is an unknown value
R14=0x00000188fb0fcf40 points into unknown readable memory: 0x00007ffde4b25538 | 38 55 b2 e4 fd 7f 00 00
R15=0x00007ffde4cd5b00 jvm.dll

Top of Stack: (sp=0x0000004f90bfca90)
0x0000004f90bfca90:   00000188f8704080 00000188f7892500
0x0000004f90bfcaa0:   0000004f90bfcc10 00000188f6867960
0x0000004f90bfcab0:   000000bd00000006 00007ffd00000069
0x0000004f90bfcac0:   00000188f6865780 0000000000000000
0x0000004f90bfcad0:   0000000000000000 0000000000000000
0x0000004f90bfcae0:   00000000001ea880 00000188ef7340c0
0x0000004f90bfcaf0:   0000000000000000 00000188ef7344a8
0x0000004f90bfcb00:   00000188ef7340d0 0000004f90bfcff0
0x0000004f90bfcb10:   00000000000003d8 0000004f90bfcc30
0x0000004f90bfcb20:   0000000000002c99 00007ffde4432ac6
0x0000004f90bfcb30:   0000004f90bfcc10 0000000000002c00
0x0000004f90bfcb40:   0000004f90bfcc00 00000188ffffffff
0x0000004f90bfcb50:   000000007fffff01 000000000000005c
0x0000004f90bfcb60:   00000188f71681a0 00007ffde443185f
0x0000004f90bfcb70:   3febaba2d34abe14 0000000000000007
0x0000004f90bfcb80:   00000188ef7340c0 00000188ef378dc0
0x0000004f90bfcb90:   00000188ef37fa18 00000188ef380da8
0x0000004f90bfcba0:   000000000079f8d0 00000188f9888aa0
0x0000004f90bfcbb0:   00000188f9893d80 00000188f9888ab0
0x0000004f90bfcbc0:   00000188efb66050 3e9baba200000200
0x0000004f90bfcbd0:   00000188f5c35a50 00007ffd00000200
0x0000004f90bfcbe0:   00000188efb66050 00000188f78ab0a0
0x0000004f90bfcbf0:   00000000001d1b90 00007ffde443477e
0x0000004f90bfcc00:   00000188efe25cc8 00007ffde44319d4
0x0000004f90bfcc10:   00007ffde4aaee80 0000000000000009
0x0000004f90bfcc20:   0000004f90bfe570 0000000000000000
0x0000004f90bfcc30:   3ff0000000000000 0000000000000002
0x0000004f90bfcc40:   00000188f6122b20 0000000000000002
0x0000004f90bfcc50:   00000188efb66050 0000004f90bfcff0
0x0000004f90bfcc60:   0000004fdeadbeef 0000004f90bfd200
0x0000004f90bfcc70:   0000000000000001 0000004f90bfcdc0
0x0000004f90bfcc80:   0000004f90bfcd80 0000000000000002 

Instructions: (pc=0x00007ffde4292683)
0x00007ffde4292583:   a0 00 00 00 48 8b e8 66 c7 83 9c 00 00 00 08 00
0x00007ffde4292593:   66 44 89 ab a4 00 00 00 eb 55 48 8b b4 24 a0 00
0x00007ffde42925a3:   00 00 48 8b e8 66 c7 83 9c 00 00 00 10 00 66 44
0x00007ffde42925b3:   89 ab a4 00 00 00 eb 37 49 8b cf e8 8d 8f 53 00
0x00007ffde42925c3:   48 8b b4 24 a0 00 00 00 48 8b 6c 24 30 66 89 83
0x00007ffde42925d3:   9c 00 00 00 66 89 83 a4 00 00 00 81 8b a8 00 00
0x00007ffde42925e3:   00 00 04 00 00 81 8b a8 00 00 00 80 00 00 00 44
0x00007ffde42925f3:   0f b6 8c 24 a8 00 00 00 41 b8 ff ff ff ff 41 8b
0x00007ffde4292603:   5e 18 89 5c 24 28 44 89 84 24 b8 00 00 00 45 84
0x00007ffde4292613:   c9 74 38 49 8b 06 49 8b ce ff 90 b8 00 00 00 89
0x00007ffde4292623:   84 24 b8 00 00 00 44 8b c0 83 f8 ff 74 14 8b d0
0x00007ffde4292633:   49 8b ce e8 85 ef 42 00 44 8b c0 89 84 24 b8 00
0x00007ffde4292643:   00 00 44 0f b6 8c 24 a8 00 00 00 44 3b e3 0f 83
0x00007ffde4292653:   2c 02 00 00 41 8b c4 4c 8d 2c c5 00 00 00 00 0f
0x00007ffde4292663:   1f 40 00 66 66 0f 1f 84 00 00 00 00 00 49 8b 46
0x00007ffde4292673:   08 4a 8b 0c 28 48 8b 86 08 01 00 00 48 63 51 28
0x00007ffde4292683:   8b 1c 90 85 db 0f 84 cc 01 00 00 45 84 c9 74 11
0x00007ffde4292693:   45 3b c4 75 0c 49 8b 06 49 8b ce ff 90 e0 00 00
0x00007ffde42926a3:   00 80 bc 24 b0 00 00 00 00 74 17 80 be 18 01 00
0x00007ffde42926b3:   00 00 74 0e 41 0f b7 46 3a 44 3b e0 0f 83 95 01
0x00007ffde42926c3:   00 00 48 8b 46 70 41 8b d4 48 69 fb b0 00 00 00
0x00007ffde42926d3:   49 8b ce 48 03 78 20 49 8b 06 ff 90 88 00 00 00
0x00007ffde42926e3:   80 bc 24 a8 00 00 00 00 4c 8b f8 75 2c 49 8b 4e
0x00007ffde42926f3:   08 f2 0f 10 4d 08 f2 0f 59 ce 4a 8b 14 29 48 8b
0x00007ffde4292703:   4e 48 44 8b 42 28 48 8b 51 78 4a 8b 0c c2 f2 0f
0x00007ffde4292713:   10 41 08 66 0f 2f c1 77 4b 8b 97 90 00 00 00 8b
0x00007ffde4292723:   87 94 00 00 00 3b d0 77 1b 0f 1f 40 00 8b ca ff
0x00007ffde4292733:   c2 49 8b 04 cf 48 21 44 cf 38 8b 87 94 00 00 00
0x00007ffde4292743:   3b d0 76 e9 41 8b 4f 58 39 8f 90 00 00 00 73 06
0x00007ffde4292753:   89 8f 90 00 00 00 41 8b 4f 5c 3b c1 76 06 89 8f
0x00007ffde4292763:   94 00 00 00 49 8b 46 08 4a 8b 0c 28 48 8b 01 ff
0x00007ffde4292773:   50 70 8b c8 8b d8 e8 62 93 53 00 8b d3 48 8d 4f 


Stack slot to memory mapping:

stack at sp + 0 slots: 0x00000188f8704080 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
stack at sp + 1 slots: 0x00000188f7892500 points into unknown readable memory: 0x00007ffde4a9a788 | 88 a7 a9 e4 fd 7f 00 00
stack at sp + 2 slots: 0x0000004f90bfcc10 is pointing into the stack for thread: 0x00000188efb04550
stack at sp + 3 slots: 0x00000188f6867960 points into unknown readable memory: 0x00007ffde4a9a788 | 88 a7 a9 e4 fd 7f 00 00
stack at sp + 4 slots: 0x000000bd00000006 is an unknown value
stack at sp + 5 slots: 0x00007ffd00000069 is an unknown value
stack at sp + 6 slots: 0x00000188f6865780 points into unknown readable memory: 0x00007ffde4a9a788 | 88 a7 a9 e4 fd 7f 00 00
stack at sp + 7 slots: 0x0 is null


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000188f813a3a0, length=61, elements={
0x00000188941829f0, 0x000001889423bd20, 0x000001889423e2f0, 0x00000188ac795300,
0x00000188ac795f50, 0x00000188ac796ba0, 0x00000188ac799700, 0x00000188ac79a530,
0x00000188ef273820, 0x00000188941f1120, 0x00000188ef552de0, 0x00000188efde8100,
0x00000188efb4f900, 0x00000188efac42e0, 0x00000188efae32b0, 0x00000188efb04550,
0x00000188f44aff10, 0x00000188f44ca9c0, 0x00000188f47da160, 0x00000188f47dae80,
0x00000188f47d9ad0, 0x00000188f47da7f0, 0x00000188f47db510, 0x00000188f47dbba0,
0x00000188f47d8db0, 0x00000188f7443bd0, 0x00000188f7447050, 0x00000188f74469c0,
0x00000188f7444f80, 0x00000188f74476e0, 0x00000188f7445610, 0x00000188f7444260,
0x00000188f7447d70, 0x00000188f74448f0, 0x00000188f7448400, 0x00000188f7448a90,
0x00000188f74497b0, 0x00000188f7446330, 0x00000188f7449e40, 0x00000188f7ab7960,
0x00000188f744a4d0, 0x00000188f744ab60, 0x00000188f47d9440, 0x00000188f7a63700,
0x00000188f7a63070, 0x00000188f7a64420, 0x00000188f7a64ab0, 0x00000188f7a63d90,
0x00000188f7a65140, 0x00000188f7a61cc0, 0x00000188f7a657d0, 0x00000188f7a62350,
0x00000188f7a65e60, 0x00000188f7a629e0, 0x00000188f7a68c50, 0x00000188f7a692e0,
0x00000188f7a67f30, 0x00000188f7a67210, 0x00000188f7a678a0, 0x00000188f5ee07c0,
0x00000188f5edfaa0
}

Java Threads: ( => current thread )
  0x00000188941829f0 JavaThread "main"                              [_thread_blocked, id=1584, stack(0x0000004f8f200000,0x0000004f8f300000) (1024K)]
  0x000001889423bd20 JavaThread "Reference Handler"          daemon [_thread_blocked, id=4828, stack(0x0000004f8f600000,0x0000004f8f700000) (1024K)]
  0x000001889423e2f0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=19300, stack(0x0000004f8f700000,0x0000004f8f800000) (1024K)]
  0x00000188ac795300 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=18740, stack(0x0000004f8f800000,0x0000004f8f900000) (1024K)]
  0x00000188ac795f50 JavaThread "Attach Listener"            daemon [_thread_blocked, id=19928, stack(0x0000004f8f900000,0x0000004f8fa00000) (1024K)]
  0x00000188ac796ba0 JavaThread "Service Thread"             daemon [_thread_blocked, id=20148, stack(0x0000004f8fa00000,0x0000004f8fb00000) (1024K)]
  0x00000188ac799700 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=1760, stack(0x0000004f8fb00000,0x0000004f8fc00000) (1024K)]
  0x00000188ac79a530 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=14860, stack(0x0000004f8fc00000,0x0000004f8fd00000) (1024K)]
  0x00000188ef273820 JavaThread "C1 CompilerThread0"         daemon [_thread_in_vm, id=7124, stack(0x0000004f8fd00000,0x0000004f8fe00000) (1024K)]
  0x00000188941f1120 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=20000, stack(0x0000004f8fe00000,0x0000004f8ff00000) (1024K)]
  0x00000188ef552de0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=13460, stack(0x0000004f90000000,0x0000004f90100000) (1024K)]
  0x00000188efde8100 JavaThread "Active Thread: Equinox Container: a7125d17-5b71-4efe-ac5e-9438219a8591"        [_thread_blocked, id=13716, stack(0x0000004f90100000,0x0000004f90200000) (1024K)]
  0x00000188efb4f900 JavaThread "Refresh Thread: Equinox Container: a7125d17-5b71-4efe-ac5e-9438219a8591" daemon [_thread_blocked, id=21180, stack(0x0000004f90800000,0x0000004f90900000) (1024K)]
  0x00000188efac42e0 JavaThread "Framework Event Dispatcher: Equinox Container: a7125d17-5b71-4efe-ac5e-9438219a8591" daemon [_thread_blocked, id=17972, stack(0x0000004f90900000,0x0000004f90a00000) (1024K)]
  0x00000188efae32b0 JavaThread "Start Level: Equinox Container: a7125d17-5b71-4efe-ac5e-9438219a8591" daemon [_thread_blocked, id=988, stack(0x0000004f90a00000,0x0000004f90b00000) (1024K)]
=>0x00000188efb04550 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=15604, stack(0x0000004f90b00000,0x0000004f90c00000) (1024K)]
  0x00000188f44aff10 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=20876, stack(0x0000004f90c00000,0x0000004f90d00000) (1024K)]
  0x00000188f44ca9c0 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=13348, stack(0x0000004f90d00000,0x0000004f90e00000) (1024K)]
  0x00000188f47da160 JavaThread "Worker-JM"                         [_thread_blocked, id=16136, stack(0x0000004f90f00000,0x0000004f91000000) (1024K)]
  0x00000188f47dae80 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=10116, stack(0x0000004f91000000,0x0000004f91100000) (1024K)]
  0x00000188f47d9ad0 JavaThread "Worker-0"                          [_thread_blocked, id=8104, stack(0x0000004f91100000,0x0000004f91200000) (1024K)]
  0x00000188f47da7f0 JavaThread "Worker-1: Java indexing... "        [_thread_blocked, id=13692, stack(0x0000004f91200000,0x0000004f91300000) (1024K)]
  0x00000188f47db510 JavaThread "Java indexing"              daemon [_thread_in_Java, id=16540, stack(0x0000004f91300000,0x0000004f91400000) (1024K)]
  0x00000188f47dbba0 JavaThread "Worker-2"                          [_thread_blocked, id=20452, stack(0x0000004f91400000,0x0000004f91500000) (1024K)]
  0x00000188f47d8db0 JavaThread "Worker-3"                          [_thread_blocked, id=19364, stack(0x0000004f91800000,0x0000004f91900000) (1024K)]
  0x00000188f7443bd0 JavaThread "Worker-4: Building"                [_thread_blocked, id=21488, stack(0x0000004f91900000,0x0000004f91a00000) (1024K)]
  0x00000188f7447050 JavaThread "Thread-2"                   daemon [_thread_in_native, id=16252, stack(0x0000004f91a00000,0x0000004f91b00000) (1024K)]
  0x00000188f74469c0 JavaThread "Thread-3"                   daemon [_thread_in_native, id=15988, stack(0x0000004f91b00000,0x0000004f91c00000) (1024K)]
  0x00000188f7444f80 JavaThread "Thread-4"                   daemon [_thread_in_native, id=9764, stack(0x0000004f91c00000,0x0000004f91d00000) (1024K)]
  0x00000188f74476e0 JavaThread "Thread-5"                   daemon [_thread_in_native, id=8764, stack(0x0000004f91d00000,0x0000004f91e00000) (1024K)]
  0x00000188f7445610 JavaThread "Thread-6"                   daemon [_thread_in_native, id=9012, stack(0x0000004f91e00000,0x0000004f91f00000) (1024K)]
  0x00000188f7444260 JavaThread "Thread-7"                   daemon [_thread_in_native, id=20124, stack(0x0000004f91f00000,0x0000004f92000000) (1024K)]
  0x00000188f7447d70 JavaThread "Thread-8"                   daemon [_thread_in_native, id=17380, stack(0x0000004f92000000,0x0000004f92100000) (1024K)]
  0x00000188f74448f0 JavaThread "Thread-9"                   daemon [_thread_in_native, id=20420, stack(0x0000004f92100000,0x0000004f92200000) (1024K)]
  0x00000188f7448400 JavaThread "Thread-10"                  daemon [_thread_in_native, id=18904, stack(0x0000004f92200000,0x0000004f92300000) (1024K)]
  0x00000188f7448a90 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=11380, stack(0x0000004f92300000,0x0000004f92400000) (1024K)]
  0x00000188f74497b0 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=2500, stack(0x0000004f92400000,0x0000004f92500000) (1024K)]
  0x00000188f7446330 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=18560, stack(0x0000004f92500000,0x0000004f92600000) (1024K)]
  0x00000188f7449e40 JavaThread "Worker-5"                          [_thread_blocked, id=3808, stack(0x0000004f92600000,0x0000004f92700000) (1024K)]
  0x00000188f7ab7960 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=6888, stack(0x0000004f90e00000,0x0000004f90f00000) (1024K)]
  0x00000188f744a4d0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=19156, stack(0x0000004f91500000,0x0000004f91600000) (1024K)]
  0x00000188f744ab60 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=16908, stack(0x0000004f92700000,0x0000004f92800000) (1024K)]
  0x00000188f47d9440 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=18208, stack(0x0000004f92800000,0x0000004f92900000) (1024K)]
  0x00000188f7a63700 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=20940, stack(0x0000004f92900000,0x0000004f92a00000) (1024K)]
  0x00000188f7a63070 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=7856, stack(0x0000004f92a00000,0x0000004f92b00000) (1024K)]
  0x00000188f7a64420 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=5520, stack(0x0000004f92b00000,0x0000004f92c00000) (1024K)]
  0x00000188f7a64ab0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=9600, stack(0x0000004f92c00000,0x0000004f92d00000) (1024K)]
  0x00000188f7a63d90 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=13144, stack(0x0000004f92d00000,0x0000004f92e00000) (1024K)]
  0x00000188f7a65140 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=14048, stack(0x0000004f92e00000,0x0000004f92f00000) (1024K)]
  0x00000188f7a61cc0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=2328, stack(0x0000004f92f00000,0x0000004f93000000) (1024K)]
  0x00000188f7a657d0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=19532, stack(0x0000004f93000000,0x0000004f93100000) (1024K)]
  0x00000188f7a62350 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=13640, stack(0x0000004f93100000,0x0000004f93200000) (1024K)]
  0x00000188f7a65e60 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=10044, stack(0x0000004f93200000,0x0000004f93300000) (1024K)]
  0x00000188f7a629e0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=18448, stack(0x0000004f93300000,0x0000004f93400000) (1024K)]
  0x00000188f7a68c50 JavaThread "Compiler Processing Task"   daemon [_thread_in_vm, id=8652, stack(0x0000004f93400000,0x0000004f93500000) (1024K)]
  0x00000188f7a692e0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=7276, stack(0x0000004f93500000,0x0000004f93600000) (1024K)]
  0x00000188f7a67f30 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=5072, stack(0x0000004f93600000,0x0000004f93700000) (1024K)]
  0x00000188f7a67210 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=19452, stack(0x0000004f93700000,0x0000004f93800000) (1024K)]
  0x00000188f7a678a0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=21256, stack(0x0000004f93800000,0x0000004f93900000) (1024K)]
  0x00000188f5ee07c0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=13472, stack(0x0000004f93900000,0x0000004f93a00000) (1024K)]
  0x00000188f5edfaa0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=5124, stack(0x0000004f93a00000,0x0000004f93b00000) (1024K)]
Total: 61

Other Threads:
  0x0000018893ddea80 VMThread "VM Thread"                           [id=10996, stack(0x0000004f8f500000,0x0000004f8f600000) (1024K)]
  0x00000188ac6aca00 WatcherThread "VM Periodic Task Thread"        [id=17860, stack(0x0000004f8f400000,0x0000004f8f500000) (1024K)]
  0x00000188941a0c20 WorkerThread "GC Thread#0"                     [id=19620, stack(0x0000004f8f300000,0x0000004f8f400000) (1024K)]
  0x00000188efcaa440 WorkerThread "GC Thread#1"                     [id=4840, stack(0x0000004f90200000,0x0000004f90300000) (1024K)]
  0x00000188efcaa7e0 WorkerThread "GC Thread#2"                     [id=15824, stack(0x0000004f90300000,0x0000004f90400000) (1024K)]
  0x00000188efcaab80 WorkerThread "GC Thread#3"                     [id=18224, stack(0x0000004f90400000,0x0000004f90500000) (1024K)]
  0x00000188efcaaf20 WorkerThread "GC Thread#4"                     [id=21264, stack(0x0000004f90500000,0x0000004f90600000) (1024K)]
  0x00000188efcab2c0 WorkerThread "GC Thread#5"                     [id=19832, stack(0x0000004f90600000,0x0000004f90700000) (1024K)]
  0x00000188efa68e80 WorkerThread "GC Thread#6"                     [id=11392, stack(0x0000004f90700000,0x0000004f90800000) (1024K)]
  0x00000188efc3f8a0 WorkerThread "GC Thread#7"                     [id=204, stack(0x0000004f8ff00000,0x0000004f90000000) (1024K)]
Total: 10

Threads with active compile tasks:
C2 CompilerThread0  22420 10174       4       java.lang.reflect.Field::get (39 bytes)
C1 CompilerThread0  22421 10747       2       java.lang.invoke.LambdaForm$MH/0x00000188ae61c000::invoke (42 bytes)
C2 CompilerThread1  22421 10104   !   4       org.eclipse.jdt.internal.core.search.indexing.BinaryIndexer::indexDocument (1168 bytes)
C2 CompilerThread2  22421 10001   !   4       lombok.core.AST::buildWithField0 (121 bytes)
Total: 4

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffde4d5ce88] MethodCompileQueue_lock - owner thread: 0x00000188ef273820
[0x00007ffde4d5e208] CodeCache_lock - owner thread: 0x00000188ef273820

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x00000188ad000000-0x00000188adba0000-0x00000188adba0000), size 12189696, SharedBaseAddress: 0x00000188ad000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x00000188ae000000-0x00000188ee000000, reserved size: 1073741824
Narrow klass base: 0x00000188ad000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 16207M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 7680K, used 5782K [0x00000000d5580000, 0x00000000d6180000, 0x0000000100000000)
  eden space 6144K, 69% used [0x00000000d5580000,0x00000000d59a5dc0,0x00000000d5b80000)
  from space 1536K, 99% used [0x00000000d5b80000,0x00000000d5cffbd0,0x00000000d5d00000)
  to   space 3072K, 0% used [0x00000000d5e80000,0x00000000d5e80000,0x00000000d6180000)
 ParOldGen       total 128000K, used 114867K [0x0000000080000000, 0x0000000087d00000, 0x00000000d5580000)
  object space 128000K, 89% used [0x0000000080000000,0x000000008702ccb0,0x0000000087d00000)
 Metaspace       used 58649K, committed 60032K, reserved 1114112K
  class space    used 5897K, committed 6528K, reserved 1048576K

Card table byte_map: [0x00000188a7b10000,0x00000188a7f20000] _byte_map_base: 0x00000188a7710000

Marking Bits: (ParMarkBitMap*) 0x00007ffde4d631f0
 Begin Bits: [0x00000188a81d0000, 0x00000188aa1d0000)
 End Bits:   [0x00000188aa1d0000, 0x00000188ac1d0000)

Polling page: 0x0000018893ed0000

Metaspace:

Usage:
  Non-class:     51.52 MB used.
      Class:      5.76 MB used.
       Both:     57.27 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      52.25 MB ( 82%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       6.38 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      58.62 MB (  5%) committed. 

Chunk freelists:
   Non-Class:  11.47 MB
       Class:  9.67 MB
        Both:  21.13 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 97.31 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 1184.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 938.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 23.
num_chunks_taken_from_freelist: 3929.
num_chunk_merges: 12.
num_chunk_splits: 2418.
num_chunks_enlarged: 1369.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=7656Kb max_used=7788Kb free=112343Kb
 bounds [0x00000188a0400000, 0x00000188a0ba0000, 0x00000188a7930000]
CodeHeap 'profiled nmethods': size=120000Kb used=23812Kb max_used=24185Kb free=96187Kb
 bounds [0x0000018898930000, 0x000001889a0d0000, 0x000001889fe60000]
CodeHeap 'non-nmethods': size=5760Kb used=1415Kb max_used=1542Kb free=4344Kb
 bounds [0x000001889fe60000, 0x00000188a00d0000, 0x00000188a0400000]
 total_blobs=10566 nmethods=9838 adapters=633
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 22.337 Thread 0x00000188ef273820 nmethod 10683 0x0000018898973410 code [0x00000188989737a0, 0x0000018898974590]
Event: 22.337 Thread 0x00000188ef273820 10684       1       org.eclipse.core.internal.resources.MarkerSet::size (5 bytes)
Event: 22.337 Thread 0x00000188ef273820 nmethod 10684 0x00000188a05b7510 code [0x00000188a05b76a0, 0x00000188a05b7768]
Event: 22.337 Thread 0x00000188ef273820 10685       1       org.eclipse.core.internal.resources.Workspace::isOpen (5 bytes)
Event: 22.337 Thread 0x00000188ef273820 nmethod 10685 0x00000188a05b7010 code [0x00000188a05b71a0, 0x00000188a05b7270]
Event: 22.337 Thread 0x00000188ef273820 10686       2       java.nio.MappedByteBuffer::flip (5 bytes)
Event: 22.337 Thread 0x00000188ef273820 nmethod 10686 0x00000188992e4c10 code [0x00000188992e4da0, 0x00000188992e4ec0]
Event: 22.337 Thread 0x00000188ef273820 10687       2       sun.nio.ch.IOStatus::normalize (10 bytes)
Event: 22.337 Thread 0x00000188ef273820 nmethod 10687 0x0000018898fee010 code [0x0000018898fee1a0, 0x0000018898fee2c0]
Event: 22.340 Thread 0x00000188ef273820 10688       2       org.eclipse.jdt.internal.compiler.ast.Argument::bind (279 bytes)
Event: 22.342 Thread 0x00000188ef273820 nmethod 10688 0x0000018899143690 code [0x0000018899143920, 0x00000188991442c8]
Event: 22.342 Thread 0x00000188ef273820 10689       2       java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject::doSignal (62 bytes)
Event: 22.342 Thread 0x00000188ef273820 nmethod 10689 0x0000018898d0ba90 code [0x0000018898d0bc40, 0x0000018898d0beb0]
Event: 22.344 Thread 0x00000188ef273820 10690       2       sun.nio.ch.IOUtil::bufferAddress (10 bytes)
Event: 22.344 Thread 0x00000188ef273820 nmethod 10690 0x000001889920bd90 code [0x000001889920bf20, 0x000001889920c010]
Event: 22.345 Thread 0x00000188ef273820 10691       2       com.sun.jna.internal.Cleaner::getCleaner (4 bytes)
Event: 22.345 Thread 0x00000188ef273820 nmethod 10691 0x0000018898dad010 code [0x0000018898dad1a0, 0x0000018898dad290]
Event: 22.345 Thread 0x00000188ef273820 10692  s    2       com.sun.jna.internal.Cleaner::register (19 bytes)
Event: 22.346 Thread 0x00000188ef273820 nmethod 10692 0x0000018899272b90 code [0x0000018899272d40, 0x0000018899273020]
Event: 22.348 Thread 0x00000188ef273820 10693       2       java.util.concurrent.ConcurrentHashMap::<init> (73 bytes)

GC Heap History (20 events):
Event: 21.072 GC heap after
{Heap after GC invocations=62 (full 2):
 PSYoungGen      total 9216K, used 1558K [0x00000000d5580000, 0x00000000d6100000, 0x0000000100000000)
  eden space 7168K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5c80000)
  from space 2048K, 76% used [0x00000000d5f00000,0x00000000d6085ba0,0x00000000d6100000)
  to   space 2048K, 0% used [0x00000000d5d00000,0x00000000d5d00000,0x00000000d5f00000)
 ParOldGen       total 118784K, used 118482K [0x0000000080000000, 0x0000000087400000, 0x00000000d5580000)
  object space 118784K, 99% used [0x0000000080000000,0x00000000873b49f8,0x0000000087400000)
 Metaspace       used 57269K, committed 58688K, reserved 1114112K
  class space    used 5832K, committed 6464K, reserved 1048576K
}
Event: 21.478 GC heap before
{Heap before GC invocations=63 (full 2):
 PSYoungGen      total 9216K, used 8726K [0x00000000d5580000, 0x00000000d6100000, 0x0000000100000000)
  eden space 7168K, 100% used [0x00000000d5580000,0x00000000d5c80000,0x00000000d5c80000)
  from space 2048K, 76% used [0x00000000d5f00000,0x00000000d6085ba0,0x00000000d6100000)
  to   space 2048K, 0% used [0x00000000d5d00000,0x00000000d5d00000,0x00000000d5f00000)
 ParOldGen       total 118784K, used 118482K [0x0000000080000000, 0x0000000087400000, 0x00000000d5580000)
  object space 118784K, 99% used [0x0000000080000000,0x00000000873b49f8,0x0000000087400000)
 Metaspace       used 57450K, committed 58880K, reserved 1114112K
  class space    used 5852K, committed 6464K, reserved 1048576K
}
Event: 21.483 GC heap after
{Heap after GC invocations=63 (full 2):
 PSYoungGen      total 9216K, used 1590K [0x00000000d5580000, 0x00000000d6080000, 0x0000000100000000)
  eden space 7168K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5c80000)
  from space 2048K, 77% used [0x00000000d5d00000,0x00000000d5e8d8f0,0x00000000d5f00000)
  to   space 1536K, 0% used [0x00000000d5f00000,0x00000000d5f00000,0x00000000d6080000)
 ParOldGen       total 120320K, used 119880K [0x0000000080000000, 0x0000000087580000, 0x00000000d5580000)
  object space 120320K, 99% used [0x0000000080000000,0x0000000087512000,0x0000000087580000)
 Metaspace       used 57450K, committed 58880K, reserved 1114112K
  class space    used 5852K, committed 6464K, reserved 1048576K
}
Event: 21.597 GC heap before
{Heap before GC invocations=64 (full 2):
 PSYoungGen      total 9216K, used 8758K [0x00000000d5580000, 0x00000000d6080000, 0x0000000100000000)
  eden space 7168K, 100% used [0x00000000d5580000,0x00000000d5c80000,0x00000000d5c80000)
  from space 2048K, 77% used [0x00000000d5d00000,0x00000000d5e8d8f0,0x00000000d5f00000)
  to   space 1536K, 0% used [0x00000000d5f00000,0x00000000d5f00000,0x00000000d6080000)
 ParOldGen       total 120320K, used 119880K [0x0000000080000000, 0x0000000087580000, 0x00000000d5580000)
  object space 120320K, 99% used [0x0000000080000000,0x0000000087512000,0x0000000087580000)
 Metaspace       used 57631K, committed 59008K, reserved 1114112K
  class space    used 5858K, committed 6464K, reserved 1048576K
}
Event: 21.601 GC heap after
{Heap after GC invocations=64 (full 2):
 PSYoungGen      total 7680K, used 1505K [0x00000000d5580000, 0x00000000d6380000, 0x0000000100000000)
  eden space 6144K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5b80000)
  from space 1536K, 98% used [0x00000000d5f00000,0x00000000d60785b8,0x00000000d6080000)
  to   space 3584K, 0% used [0x00000000d5b80000,0x00000000d5b80000,0x00000000d5f00000)
 ParOldGen       total 121856K, used 121427K [0x0000000080000000, 0x0000000087700000, 0x00000000d5580000)
  object space 121856K, 99% used [0x0000000080000000,0x0000000087694c40,0x0000000087700000)
 Metaspace       used 57631K, committed 59008K, reserved 1114112K
  class space    used 5858K, committed 6464K, reserved 1048576K
}
Event: 21.764 GC heap before
{Heap before GC invocations=65 (full 2):
 PSYoungGen      total 7680K, used 7649K [0x00000000d5580000, 0x00000000d6380000, 0x0000000100000000)
  eden space 6144K, 100% used [0x00000000d5580000,0x00000000d5b80000,0x00000000d5b80000)
  from space 1536K, 98% used [0x00000000d5f00000,0x00000000d60785b8,0x00000000d6080000)
  to   space 3584K, 0% used [0x00000000d5b80000,0x00000000d5b80000,0x00000000d5f00000)
 ParOldGen       total 121856K, used 121427K [0x0000000080000000, 0x0000000087700000, 0x00000000d5580000)
  object space 121856K, 99% used [0x0000000080000000,0x0000000087694c40,0x0000000087700000)
 Metaspace       used 57959K, committed 59328K, reserved 1114112K
  class space    used 5863K, committed 6464K, reserved 1048576K
}
Event: 21.770 GC heap after
{Heap after GC invocations=65 (full 2):
 PSYoungGen      total 7680K, used 1321K [0x00000000d5580000, 0x00000000d5f80000, 0x0000000100000000)
  eden space 6144K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5b80000)
  from space 1536K, 86% used [0x00000000d5b80000,0x00000000d5cca7f8,0x00000000d5d00000)
  to   space 2048K, 0% used [0x00000000d5d80000,0x00000000d5d80000,0x00000000d5f80000)
 ParOldGen       total 122880K, used 122820K [0x0000000080000000, 0x0000000087800000, 0x00000000d5580000)
  object space 122880K, 99% used [0x0000000080000000,0x00000000877f1288,0x0000000087800000)
 Metaspace       used 57959K, committed 59328K, reserved 1114112K
  class space    used 5863K, committed 6464K, reserved 1048576K
}
Event: 21.817 GC heap before
{Heap before GC invocations=66 (full 2):
 PSYoungGen      total 7680K, used 7465K [0x00000000d5580000, 0x00000000d5f80000, 0x0000000100000000)
  eden space 6144K, 100% used [0x00000000d5580000,0x00000000d5b80000,0x00000000d5b80000)
  from space 1536K, 86% used [0x00000000d5b80000,0x00000000d5cca7f8,0x00000000d5d00000)
  to   space 2048K, 0% used [0x00000000d5d80000,0x00000000d5d80000,0x00000000d5f80000)
 ParOldGen       total 122880K, used 122820K [0x0000000080000000, 0x0000000087800000, 0x00000000d5580000)
  object space 122880K, 99% used [0x0000000080000000,0x00000000877f1288,0x0000000087800000)
 Metaspace       used 58022K, committed 59392K, reserved 1114112K
  class space    used 5863K, committed 6464K, reserved 1048576K
}
Event: 21.821 GC heap after
{Heap after GC invocations=66 (full 2):
 PSYoungGen      total 7680K, used 1454K [0x00000000d5580000, 0x00000000d5f00000, 0x0000000100000000)
  eden space 6144K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5b80000)
  from space 1536K, 94% used [0x00000000d5d80000,0x00000000d5eeb920,0x00000000d5f00000)
  to   space 1536K, 0% used [0x00000000d5b80000,0x00000000d5b80000,0x00000000d5d00000)
 ParOldGen       total 124416K, used 124003K [0x0000000080000000, 0x0000000087980000, 0x00000000d5580000)
  object space 124416K, 99% used [0x0000000080000000,0x0000000087918e70,0x0000000087980000)
 Metaspace       used 58022K, committed 59392K, reserved 1114112K
  class space    used 5863K, committed 6464K, reserved 1048576K
}
Event: 21.919 GC heap before
{Heap before GC invocations=67 (full 2):
 PSYoungGen      total 7680K, used 7597K [0x00000000d5580000, 0x00000000d5f00000, 0x0000000100000000)
  eden space 6144K, 99% used [0x00000000d5580000,0x00000000d5b7fb48,0x00000000d5b80000)
  from space 1536K, 94% used [0x00000000d5d80000,0x00000000d5eeb920,0x00000000d5f00000)
  to   space 1536K, 0% used [0x00000000d5b80000,0x00000000d5b80000,0x00000000d5d00000)
 ParOldGen       total 124416K, used 124003K [0x0000000080000000, 0x0000000087980000, 0x00000000d5580000)
  object space 124416K, 99% used [0x0000000080000000,0x0000000087918e70,0x0000000087980000)
 Metaspace       used 58243K, committed 59584K, reserved 1114112K
  class space    used 5875K, committed 6464K, reserved 1048576K
}
Event: 21.923 GC heap after
{Heap after GC invocations=67 (full 2):
 PSYoungGen      total 7680K, used 1219K [0x00000000d5580000, 0x00000000d5e80000, 0x0000000100000000)
  eden space 6144K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5b80000)
  from space 1536K, 79% used [0x00000000d5b80000,0x00000000d5cb0cf0,0x00000000d5d00000)
  to   space 1536K, 0% used [0x00000000d5d00000,0x00000000d5d00000,0x00000000d5e80000)
 ParOldGen       total 125440K, used 125325K [0x0000000080000000, 0x0000000087a80000, 0x00000000d5580000)
  object space 125440K, 99% used [0x0000000080000000,0x0000000087a634b0,0x0000000087a80000)
 Metaspace       used 58243K, committed 59584K, reserved 1114112K
  class space    used 5875K, committed 6464K, reserved 1048576K
}
Event: 22.007 GC heap before
{Heap before GC invocations=68 (full 2):
 PSYoungGen      total 7680K, used 7363K [0x00000000d5580000, 0x00000000d5e80000, 0x0000000100000000)
  eden space 6144K, 100% used [0x00000000d5580000,0x00000000d5b80000,0x00000000d5b80000)
  from space 1536K, 79% used [0x00000000d5b80000,0x00000000d5cb0cf0,0x00000000d5d00000)
  to   space 1536K, 0% used [0x00000000d5d00000,0x00000000d5d00000,0x00000000d5e80000)
 ParOldGen       total 125440K, used 125325K [0x0000000080000000, 0x0000000087a80000, 0x00000000d5580000)
  object space 125440K, 99% used [0x0000000080000000,0x0000000087a634b0,0x0000000087a80000)
 Metaspace       used 58379K, committed 59712K, reserved 1114112K
  class space    used 5886K, committed 6464K, reserved 1048576K
}
Event: 22.011 GC heap after
{Heap after GC invocations=68 (full 2):
 PSYoungGen      total 7680K, used 1440K [0x00000000d5580000, 0x00000000d5e80000, 0x0000000100000000)
  eden space 6144K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5b80000)
  from space 1536K, 93% used [0x00000000d5d00000,0x00000000d5e68020,0x00000000d5e80000)
  to   space 1536K, 0% used [0x00000000d5b80000,0x00000000d5b80000,0x00000000d5d00000)
 ParOldGen       total 126464K, used 126445K [0x0000000080000000, 0x0000000087b80000, 0x00000000d5580000)
  object space 126464K, 99% used [0x0000000080000000,0x0000000087b7b670,0x0000000087b80000)
 Metaspace       used 58379K, committed 59712K, reserved 1114112K
  class space    used 5886K, committed 6464K, reserved 1048576K
}
Event: 22.078 GC heap before
{Heap before GC invocations=69 (full 2):
 PSYoungGen      total 7680K, used 6782K [0x00000000d5580000, 0x00000000d5e80000, 0x0000000100000000)
  eden space 6144K, 86% used [0x00000000d5580000,0x00000000d5ab7ab0,0x00000000d5b80000)
  from space 1536K, 93% used [0x00000000d5d00000,0x00000000d5e68020,0x00000000d5e80000)
  to   space 1536K, 0% used [0x00000000d5b80000,0x00000000d5b80000,0x00000000d5d00000)
 ParOldGen       total 126464K, used 126445K [0x0000000080000000, 0x0000000087b80000, 0x00000000d5580000)
  object space 126464K, 99% used [0x0000000080000000,0x0000000087b7b670,0x0000000087b80000)
 Metaspace       used 58468K, committed 59776K, reserved 1114112K
  class space    used 5888K, committed 6464K, reserved 1048576K
}
Event: 22.080 GC heap after
{Heap after GC invocations=69 (full 2):
 PSYoungGen      total 7680K, used 1259K [0x00000000d5580000, 0x00000000d5e80000, 0x0000000100000000)
  eden space 6144K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5b80000)
  from space 1536K, 81% used [0x00000000d5b80000,0x00000000d5cbad50,0x00000000d5d00000)
  to   space 1536K, 0% used [0x00000000d5d00000,0x00000000d5d00000,0x00000000d5e80000)
 ParOldGen       total 128000K, used 127741K [0x0000000080000000, 0x0000000087d00000, 0x00000000d5580000)
  object space 128000K, 99% used [0x0000000080000000,0x0000000087cbf468,0x0000000087d00000)
 Metaspace       used 58468K, committed 59776K, reserved 1114112K
  class space    used 5888K, committed 6464K, reserved 1048576K
}
Event: 22.080 GC heap before
{Heap before GC invocations=70 (full 3):
 PSYoungGen      total 7680K, used 1259K [0x00000000d5580000, 0x00000000d5e80000, 0x0000000100000000)
  eden space 6144K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5b80000)
  from space 1536K, 81% used [0x00000000d5b80000,0x00000000d5cbad50,0x00000000d5d00000)
  to   space 1536K, 0% used [0x00000000d5d00000,0x00000000d5d00000,0x00000000d5e80000)
 ParOldGen       total 128000K, used 127741K [0x0000000080000000, 0x0000000087d00000, 0x00000000d5580000)
  object space 128000K, 99% used [0x0000000080000000,0x0000000087cbf468,0x0000000087d00000)
 Metaspace       used 58468K, committed 59776K, reserved 1114112K
  class space    used 5888K, committed 6464K, reserved 1048576K
}
Event: 22.229 GC heap after
{Heap after GC invocations=70 (full 3):
 PSYoungGen      total 7680K, used 0K [0x00000000d5580000, 0x00000000d5e80000, 0x0000000100000000)
  eden space 6144K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5b80000)
  from space 1536K, 0% used [0x00000000d5b80000,0x00000000d5b80000,0x00000000d5d00000)
  to   space 1536K, 0% used [0x00000000d5d00000,0x00000000d5d00000,0x00000000d5e80000)
 ParOldGen       total 128000K, used 113613K [0x0000000080000000, 0x0000000087d00000, 0x00000000d5580000)
  object space 128000K, 88% used [0x0000000080000000,0x0000000086ef37b8,0x0000000087d00000)
 Metaspace       used 58468K, committed 59776K, reserved 1114112K
  class space    used 5888K, committed 6464K, reserved 1048576K
}
Event: 22.301 GC heap before
{Heap before GC invocations=71 (full 3):
 PSYoungGen      total 7680K, used 6144K [0x00000000d5580000, 0x00000000d5e80000, 0x0000000100000000)
  eden space 6144K, 100% used [0x00000000d5580000,0x00000000d5b80000,0x00000000d5b80000)
  from space 1536K, 0% used [0x00000000d5b80000,0x00000000d5b80000,0x00000000d5d00000)
  to   space 1536K, 0% used [0x00000000d5d00000,0x00000000d5d00000,0x00000000d5e80000)
 ParOldGen       total 128000K, used 113613K [0x0000000080000000, 0x0000000087d00000, 0x00000000d5580000)
  object space 128000K, 88% used [0x0000000080000000,0x0000000086ef37b8,0x0000000087d00000)
 Metaspace       used 58527K, committed 59904K, reserved 1114112K
  class space    used 5893K, committed 6528K, reserved 1048576K
}
Event: 22.303 GC heap after
{Heap after GC invocations=71 (full 3):
 PSYoungGen      total 7680K, used 1369K [0x00000000d5580000, 0x00000000d5e80000, 0x0000000100000000)
  eden space 6144K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5b80000)
  from space 1536K, 89% used [0x00000000d5d00000,0x00000000d5e56738,0x00000000d5e80000)
  to   space 1536K, 0% used [0x00000000d5b80000,0x00000000d5b80000,0x00000000d5d00000)
 ParOldGen       total 128000K, used 113621K [0x0000000080000000, 0x0000000087d00000, 0x00000000d5580000)
  object space 128000K, 88% used [0x0000000080000000,0x0000000086ef57b8,0x0000000087d00000)
 Metaspace       used 58527K, committed 59904K, reserved 1114112K
  class space    used 5893K, committed 6528K, reserved 1048576K
}
Event: 22.348 GC heap before
{Heap before GC invocations=72 (full 3):
 PSYoungGen      total 7680K, used 7513K [0x00000000d5580000, 0x00000000d5e80000, 0x0000000100000000)
  eden space 6144K, 100% used [0x00000000d5580000,0x00000000d5b80000,0x00000000d5b80000)
  from space 1536K, 89% used [0x00000000d5d00000,0x00000000d5e56738,0x00000000d5e80000)
  to   space 1536K, 0% used [0x00000000d5b80000,0x00000000d5b80000,0x00000000d5d00000)
 ParOldGen       total 128000K, used 113621K [0x0000000080000000, 0x0000000087d00000, 0x00000000d5580000)
  object space 128000K, 88% used [0x0000000080000000,0x0000000086ef57b8,0x0000000087d00000)
 Metaspace       used 58583K, committed 59968K, reserved 1114112K
  class space    used 5897K, committed 6528K, reserved 1048576K
}

Dll operation events (10 events):
Event: 0.013 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.064 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.176 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.181 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.183 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.191 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.215 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.309 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 2.938 Loaded shared library C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 5.951 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-52394423\jna1033839967679530717.dll

Deoptimization events (20 events):
Event: 21.983 Thread 0x00000188f7a68c50 DEOPT PACKING pc=0x00000188a0b4717c sp=0x0000004f934fe010
Event: 21.983 Thread 0x00000188f7a68c50 DEOPT UNPACKING pc=0x000001889feb3aa2 sp=0x0000004f934fdfd8 mode 2
Event: 21.988 Thread 0x00000188f7a68c50 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000188a0b70500 relative=0x0000000000000e40
Event: 21.988 Thread 0x00000188f7a68c50 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000188a0b70500 method=lombok.eclipse.EclipseNode.traverse(Llombok/eclipse/EclipseASTVisitor;)V @ 357 c2
Event: 21.988 Thread 0x00000188f7a68c50 DEOPT PACKING pc=0x00000188a0b70500 sp=0x0000004f934fe670
Event: 21.988 Thread 0x00000188f7a68c50 DEOPT UNPACKING pc=0x000001889feb3aa2 sp=0x0000004f934fe608 mode 2
Event: 21.988 Thread 0x00000188f7a68c50 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000188a0b70500 relative=0x0000000000000e40
Event: 21.988 Thread 0x00000188f7a68c50 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000188a0b70500 method=lombok.eclipse.EclipseNode.traverse(Llombok/eclipse/EclipseASTVisitor;)V @ 357 c2
Event: 21.988 Thread 0x00000188f7a68c50 DEOPT PACKING pc=0x00000188a0b70500 sp=0x0000004f934fe670
Event: 21.988 Thread 0x00000188f7a68c50 DEOPT UNPACKING pc=0x000001889feb3aa2 sp=0x0000004f934fe608 mode 2
Event: 22.041 Thread 0x00000188f7a68c50 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000188a0b95fd4 relative=0x00000000000000b4
Event: 22.041 Thread 0x00000188f7a68c50 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000188a0b95fd4 method=org.eclipse.jdt.internal.compiler.lookup.TypeBinding.equalsEquals(Lorg/eclipse/jdt/internal/compiler/lookup/TypeBinding;Lorg/eclipse/jdt/internal/compiler/lookup/TypeBinding
Event: 22.041 Thread 0x00000188f7a68c50 DEOPT PACKING pc=0x00000188a0b95fd4 sp=0x0000004f934fe3d0
Event: 22.041 Thread 0x00000188f7a68c50 DEOPT UNPACKING pc=0x000001889feb3aa2 sp=0x0000004f934fe370 mode 2
Event: 22.295 Thread 0x00000188f7a68c50 Uncommon trap: trap_request=0xffffff6e fr.pc=0x00000188a0b8c1ac relative=0x000000000000018c
Event: 22.295 Thread 0x00000188f7a68c50 Uncommon trap: reason=loop_limit_check action=maybe_recompile pc=0x00000188a0b8c1ac method=org.eclipse.jdt.internal.compiler.lookup.ReferenceBinding.compare([C[CII)I @ 46 c2
Event: 22.295 Thread 0x00000188f7a68c50 DEOPT PACKING pc=0x00000188a0b8c1ac sp=0x0000004f934fdb50
Event: 22.295 Thread 0x00000188f7a68c50 DEOPT UNPACKING pc=0x000001889feb3aa2 sp=0x0000004f934fdac0 mode 2
Event: 22.335 Thread 0x00000188f7443bd0 DEOPT PACKING pc=0x00000188992d7c5b sp=0x0000004f919fdc50
Event: 22.335 Thread 0x00000188f7443bd0 DEOPT UNPACKING pc=0x000001889feb4242 sp=0x0000004f919fd0d8 mode 0

Classes loaded (20 events):
Event: 20.585 Loading class jdk/internal/module/IllegalAccessLogger
Event: 20.585 Loading class jdk/internal/module/IllegalAccessLogger done
Event: 20.616 Loading class java/util/concurrent/ArrayBlockingQueue
Event: 20.617 Loading class java/util/concurrent/ArrayBlockingQueue done
Event: 20.713 Loading class java/util/Hashtable$ValueCollection
Event: 20.713 Loading class java/util/Hashtable$ValueCollection done
Event: 20.717 Loading class java/util/concurrent/ConcurrentHashMap$KeySpliterator
Event: 20.717 Loading class java/util/concurrent/ConcurrentHashMap$KeySpliterator done
Event: 20.737 Loading class sun/nio/ch/ChannelOutputStream
Event: 20.737 Loading class sun/nio/ch/ChannelOutputStream done
Event: 21.859 Loading class java/util/stream/ReferencePipeline$4
Event: 21.860 Loading class java/util/stream/ReferencePipeline$4 done
Event: 21.860 Loading class java/util/stream/Nodes$IntFixedNodeBuilder
Event: 21.860 Loading class java/util/stream/Node$Builder$OfInt
Event: 21.860 Loading class java/util/stream/Node$Builder$OfInt done
Event: 21.860 Loading class java/util/stream/Nodes$IntArrayNode
Event: 21.861 Loading class java/util/stream/Nodes$IntArrayNode done
Event: 21.861 Loading class java/util/stream/Nodes$IntFixedNodeBuilder done
Event: 21.861 Loading class java/util/stream/ReferencePipeline$4$1
Event: 21.862 Loading class java/util/stream/ReferencePipeline$4$1 done

Classes unloaded (7 events):
Event: 6.904 Thread 0x0000018893ddea80 Unloading class 0x00000188ae1a4c00 'java/lang/invoke/LambdaForm$MH+0x00000188ae1a4c00'
Event: 6.904 Thread 0x0000018893ddea80 Unloading class 0x00000188ae1a4800 'java/lang/invoke/LambdaForm$MH+0x00000188ae1a4800'
Event: 6.904 Thread 0x0000018893ddea80 Unloading class 0x00000188ae1a4400 'java/lang/invoke/LambdaForm$MH+0x00000188ae1a4400'
Event: 6.904 Thread 0x0000018893ddea80 Unloading class 0x00000188ae1a4000 'java/lang/invoke/LambdaForm$MH+0x00000188ae1a4000'
Event: 6.904 Thread 0x0000018893ddea80 Unloading class 0x00000188ae1a3c00 'java/lang/invoke/LambdaForm$BMH+0x00000188ae1a3c00'
Event: 6.904 Thread 0x0000018893ddea80 Unloading class 0x00000188ae1a3800 'java/lang/invoke/LambdaForm$DMH+0x00000188ae1a3800'
Event: 6.904 Thread 0x0000018893ddea80 Unloading class 0x00000188ae1a2800 'java/lang/invoke/LambdaForm$DMH+0x00000188ae1a2800'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 15.670 Thread 0x00000188f7a63700 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5d40d18}> (0x00000000d5d40d18) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 15.671 Thread 0x00000188f7a64420 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5cda058}> (0x00000000d5cda058) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 15.672 Thread 0x00000188f7a64ab0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5d518a0}> (0x00000000d5d518a0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 15.677 Thread 0x00000188f7a63070 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5cf97e8}> (0x00000000d5cf97e8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 15.677 Thread 0x00000188f744a4d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5d14fd0}> (0x00000000d5d14fd0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 15.678 Thread 0x00000188f47d9440 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5d06718}> (0x00000000d5d06718) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 15.678 Thread 0x00000188f7a63070 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5cfb460}> (0x00000000d5cfb460) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 15.679 Thread 0x00000188f7a64ab0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5d52fd0}> (0x00000000d5d52fd0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 15.679 Thread 0x00000188f7a64420 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5cdd900}> (0x00000000d5cdd900) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 15.679 Thread 0x00000188f744ab60 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5d23e50}> (0x00000000d5d23e50) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 15.771 Thread 0x00000188f7443bd0 Implicit null exception at 0x00000188a08d4460 to 0x00000188a08d4477
Event: 17.147 Thread 0x00000188f47db510 Implicit null exception at 0x00000188a094c02c to 0x00000188a0950574
Event: 18.687 Thread 0x00000188f7443bd0 Implicit null exception at 0x00000188a09d4968 to 0x00000188a09d4b3d
Event: 18.733 Thread 0x00000188f7443bd0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d55fc910}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, int)'> (0x00000000d55fc910) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 18.735 Thread 0x00000188f7443bd0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5600c20}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, int)'> (0x00000000d5600c20) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.504 Thread 0x00000188f7443bd0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5e529d8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d5e529d8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 20.725 Thread 0x00000188f7443bd0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5e7cb58}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int, int, java.lang.Object)'> (0x00000000d5e7cb58) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 22.041 Thread 0x00000188f7a68c50 Implicit null exception at 0x00000188a0b95f49 to 0x00000188a0b95fc6
Event: 22.309 Thread 0x00000188f7a68c50 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5595668}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, int, int, java.lang.Object)'> (0x00000000d5595668) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 22.310 Thread 0x00000188f7a68c50 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d55ee1c0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, int, java.lang.Object)'> (0x00000000d55ee1c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 21.770 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 21.778 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 21.778 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 21.817 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 21.821 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 21.916 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 21.923 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 21.924 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 21.924 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 21.947 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 21.948 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 22.007 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 22.011 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 22.021 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 22.021 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 22.078 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold)
Event: 22.229 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold) done
Event: 22.301 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 22.303 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 22.348 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 22.131 Thread 0x0000018893ddea80 flushing  nmethod 0x0000018899332c90
Event: 22.131 Thread 0x0000018893ddea80 flushing  nmethod 0x0000018899339190
Event: 22.131 Thread 0x0000018893ddea80 flushing  nmethod 0x0000018899339710
Event: 22.131 Thread 0x0000018893ddea80 flushing  nmethod 0x0000018899339d10
Event: 22.131 Thread 0x0000018893ddea80 flushing  nmethod 0x000001889933a110
Event: 22.131 Thread 0x0000018893ddea80 flushing  nmethod 0x000001889933b690
Event: 22.131 Thread 0x0000018893ddea80 flushing  nmethod 0x000001889936f310
Event: 22.131 Thread 0x0000018893ddea80 flushing  nmethod 0x0000018899379c90
Event: 22.131 Thread 0x0000018893ddea80 flushing  nmethod 0x000001889937bd10
Event: 22.131 Thread 0x0000018893ddea80 flushing  nmethod 0x000001889937c510
Event: 22.131 Thread 0x0000018893ddea80 flushing  nmethod 0x000001889937ce90
Event: 22.131 Thread 0x0000018893ddea80 flushing  nmethod 0x000001889937ed10
Event: 22.131 Thread 0x0000018893ddea80 flushing  nmethod 0x0000018899385890
Event: 22.131 Thread 0x0000018893ddea80 flushing  nmethod 0x0000018899386190
Event: 22.131 Thread 0x0000018893ddea80 flushing  nmethod 0x0000018899387190
Event: 22.131 Thread 0x0000018893ddea80 flushing  nmethod 0x0000018899388090
Event: 22.131 Thread 0x0000018893ddea80 flushing  nmethod 0x0000018899388490
Event: 22.131 Thread 0x0000018893ddea80 flushing osr nmethod 0x000001889938e010
Event: 22.131 Thread 0x0000018893ddea80 flushing  nmethod 0x0000018899391a90
Event: 22.131 Thread 0x0000018893ddea80 flushing  nmethod 0x0000018899394290

Events (20 events):
Event: 15.645 Thread 0x00000188f744a4d0 Thread added: 0x00000188f744ab60
Event: 15.646 Thread 0x00000188f744a4d0 Thread added: 0x00000188f47d9440
Event: 15.647 Thread 0x00000188f744ab60 Thread added: 0x00000188f7a63700
Event: 15.647 Thread 0x00000188f7a63700 Thread added: 0x00000188f7a63070
Event: 15.648 Thread 0x00000188f744ab60 Thread added: 0x00000188f7a64420
Event: 15.648 Thread 0x00000188f744ab60 Thread added: 0x00000188f7a64ab0
Event: 15.648 Thread 0x00000188f7a64ab0 Thread added: 0x00000188f7a63d90
Event: 16.154 Thread 0x00000188f7443bd0 Thread added: 0x00000188f7a65140
Event: 16.155 Thread 0x00000188f7443bd0 Thread added: 0x00000188f7a61cc0
Event: 16.155 Thread 0x00000188f7443bd0 Thread added: 0x00000188f7a657d0
Event: 16.159 Thread 0x00000188f7a61cc0 Thread added: 0x00000188f7a62350
Event: 16.164 Thread 0x00000188f7a62350 Thread added: 0x00000188f7a65e60
Event: 16.164 Thread 0x00000188f7a65140 Thread added: 0x00000188f7a629e0
Event: 20.621 Thread 0x00000188f7443bd0 Thread added: 0x00000188f7a68c50
Event: 20.726 Thread 0x00000188f7443bd0 Thread added: 0x00000188f7a692e0
Event: 20.745 Thread 0x00000188f7443bd0 Thread added: 0x00000188f7a67f30
Event: 20.852 Thread 0x00000188f7443bd0 Thread added: 0x00000188f7a67210
Event: 20.852 Thread 0x00000188f7443bd0 Thread added: 0x00000188f7a678a0
Event: 20.852 Thread 0x00000188f7443bd0 Thread added: 0x00000188f5ee07c0
Event: 20.853 Thread 0x00000188f7443bd0 Thread added: 0x00000188f5edfaa0


Dynamic libraries:
0x00007ff6832e0000 - 0x00007ff6832ee000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ffeb1100000 - 0x00007ffeb1368000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffeaf340000 - 0x00007ffeaf409000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffeae440000 - 0x00007ffeae82d000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffeaed50000 - 0x00007ffeaee9b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffe4f3a0000 - 0x00007ffe4f3b8000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ffeb05c0000 - 0x00007ffeb078c000 	C:\WINDOWS\System32\USER32.dll
0x00007ffeae830000 - 0x00007ffeae857000 	C:\WINDOWS\System32\win32u.dll
0x00007ffeb0c80000 - 0x00007ffeb0cab000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffeaec10000 - 0x00007ffeaed47000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffeaeb60000 - 0x00007ffeaec03000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffe70be0000 - 0x00007ffe70bfe000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffe8add0000 - 0x00007ffe8b06a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4484_none_3e0e6d4ce32ef3b3\COMCTL32.dll
0x00007ffeaf120000 - 0x00007ffeaf1c9000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffeb0320000 - 0x00007ffeb034f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffe928f0000 - 0x00007ffe928fc000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffe1cfa0000 - 0x00007ffe1d02d000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007ffde40b0000 - 0x00007ffde4e40000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ffeb0500000 - 0x00007ffeb05b4000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffeaf900000 - 0x00007ffeaf9a6000 	C:\WINDOWS\System32\sechost.dll
0x00007ffeb0900000 - 0x00007ffeb0a18000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffeb0a20000 - 0x00007ffeb0a94000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffea3b00000 - 0x00007ffea3b35000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffeae0a0000 - 0x00007ffeae0fe000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffea6ca0000 - 0x00007ffea6cab000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffeae080000 - 0x00007ffeae094000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffeacfb0000 - 0x00007ffeacfcb000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffe82780000 - 0x00007ffe8278a000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ffe95440000 - 0x00007ffe95681000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffeb0cb0000 - 0x00007ffeb1036000 	C:\WINDOWS\System32\combase.dll
0x00007ffeaefe0000 - 0x00007ffeaf0c0000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffe93d80000 - 0x00007ffe93dc3000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffeae250000 - 0x00007ffeae2e9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffe79a40000 - 0x00007ffe79a4f000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ffe4d960000 - 0x00007ffe4d97f000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ffeafb60000 - 0x00007ffeb02aa000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffeae9e0000 - 0x00007ffeaeb54000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffeabe70000 - 0x00007ffeac6cb000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffeaeec0000 - 0x00007ffeaefb5000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffeb02b0000 - 0x00007ffeb031a000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffeae160000 - 0x00007ffeae18f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffe4b5e0000 - 0x00007ffe4b5f8000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ffe79890000 - 0x00007ffe798a0000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ffea67b0000 - 0x00007ffea68ce000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffead530000 - 0x00007ffead59a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffe4b100000 - 0x00007ffe4b116000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ffe744b0000 - 0x00007ffe744c0000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007ffe1d960000 - 0x00007ffe1d9a5000 	C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ffeaf9b0000 - 0x00007ffeafb4e000 	C:\WINDOWS\System32\ole32.dll
0x00007ffead830000 - 0x00007ffead84b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffeacf10000 - 0x00007ffeacf4b000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffead5d0000 - 0x00007ffead5fb000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffeae130000 - 0x00007ffeae156000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffead850000 - 0x00007ffead85c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffeac9c0000 - 0x00007ffeac9f3000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffeaefd0000 - 0x00007ffeaefda000 	C:\WINDOWS\System32\NSI.dll
0x00007ffe30cc0000 - 0x00007ffe30d09000 	C:\Users\<USER>\AppData\Local\Temp\jna-52394423\jna1033839967679530717.dll
0x00007ffeaf250000 - 0x00007ffeaf258000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffea6560000 - 0x00007ffea657f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffea64e0000 - 0x00007ffea6505000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4484_none_3e0e6d4ce32ef3b3;c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-52394423

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\5d08634aeff5bf03a8d363bf8e64864a\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\5d08634aeff5bf03a8d363bf8e64864a\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-4b4201e3d8a82eb8e9f0157cb487105a-sock
java_class_path (initial): c:\Users\<USER>\.cursor\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\5d08634aeff5bf03a8d363bf8e64864a\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME="C:\Users\<USER>\.jdks\ms-17.0.15"
PATH=d:\zm\cursor\resources\app\bin;d:\zm\cursor\resources\app\bin;d:\zm\cursor\resources\app\bin;d:\zm\cursor\resources\app\bin;C:\Program Files (x86)\Microsoft\Edge\Application;d:\zm\cursor\resources\app\bin;d:\zm\cursor\resources\app\bin;d:\zm\cursor\resources\app\bin;C:\Program Files (x86)\Microsoft\Edge\Application;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\.jdks\openjdk-24\bin;D:\MySQL\MySQL Server 8.0\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;D:\MySQL\MySQL Shell 8.0\bin\;D:\zm\cursor\resources\app\bin;D:\zm\IntelliJ IDEA 2024.3.5\bin;d:\zm\cursor\resources\app\bin;D:\MySQL\MySQL Shell 8.0\bin\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\zm\cursor\resources\app\bin;D:\zm\IntelliJ IDEA 2024.3.5\bin;d:\zm\cursor\resources\app\bin;D:\MySQL\MySQL Shell 8.0\bin;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;D:\NodeJS\;C:\Users\<USER>\.jdks\ms-17.0.15\bin;D:\zm\apache-maven-3.9.9\bin;D:\Git\cmd;D:\MySQL\MySQL Shell 8.0\bin\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\zm\cursor\resources\app\bin;D:\zm\IntelliJ IDEA 2024.3.5\bin;;D:\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm
USERNAME=74444
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 2, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4484)
OS uptime: 0 days 4:11 hours
Hyper-V role detected

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 165 stepping 2 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 2496, Current Mhz: 2496, Mhz Limit: 2496

Memory: 4k page, system-wide physical 16207M (2627M free)
TotalPageFile size 21839M (AvailPageFile size 1967M)
current process WorkingSet (physical memory assigned to process): 436M, peak: 436M
current process commit charge ("private bytes"): 480M, peak: 482M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
