<template>
  <div class="fill-in-questionnaire">
    <el-card class="box-card">
      <div class="paper-name">{{answerDetail.title}}</div>
      <div class="paper-description">{{answerDetail.description}}</div>
      <div v-for="(item, index) in answerDetail.data" :key="index" class="exam-question-item">
        <el-form :model="form" size="medium" label-width="0px">
          <el-form-item label="">
            <div style="font-size: 16px; font-weight: bold; word-break: break-all;">
              <span v-if="item.hasRequired == 1" style="color: red;">*&nbsp;&nbsp;&nbsp;</span>
              <span>{{ index + 1 + "、" + item.question }}</span>
            </div>
            <el-radio-group v-if="item.type == 1" v-model.trim="item.answer">
              <el-radio v-for="(question, i) in JSON.parse(item.questionOptions)" :key="i" :label="question.label">
                <span style="word-break: break-all;">{{ question.label }}</span>
              </el-radio>
            </el-radio-group>
            <el-checkbox-group v-else-if="item.type == 2" v-model.trim="item.answer">
              <el-checkbox v-for="(question, i) in JSON.parse(item.questionOptions)" :key="i" :label="question.label">
                {{question.label}}
              </el-checkbox>
            </el-checkbox-group>
            <el-input :maxlength="50" v-else-if="item.type == 3" v-model.trim="item.answer" placeholder="请输入"></el-input>
            <el-input :maxlength="50" v-else-if="item.type == 4" v-model.trim="item.answer" type="textarea" placeholder="请输入"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div style="text-align: center;">
        <el-button type="primary" @click="submit">提交答案</el-button>
        <el-button plain @click="back">返回</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import request from "@/utils/request"
export default {
  name: 'FillInQuestionnaire',
  data() {
    return {
      answerDetail: {
        title: '',
        description: '',
        data: []
      },
      form: {},
      totalCorrect: 0,
      totalScore: 0
    }
  },
  created() {
    this.getAnswerDetail()
  },
  methods: {
    getAnswerDetail() {
      request({
        url: '/system/questionnaire/' + this.$route.query.id,
        method: 'get'
      }).then((res) => {
        for (let index = 0; index < res.data.data.length; index++) {
          this.totalScore += (+res.data.data[index].score)
          if (res.data.data[index].type === 2) {
            res.data.data[index].answer = []
          } else if (res.data.data[index].type === 1) {
            res.data.data[index].answer = null
          }
        }
        this.answerDetail = res.data
      }).catch((error) => {
        throw error
      })
    },
    submit() {
      let data = []
      for (let index = 0; index < this.answerDetail.data.length; index++) {
        if (this.answerDetail.data[index].hasRequired == 1 && (!this.answerDetail.data[index].answer || !this.answerDetail.data[index].answer.length)) {
          this.$message({
            type: 'warning',
            message: '您还有题目未完成哦~'
          })
          return
        }
        let sortArray = []
        if (this.answerDetail.data[index].type == 2) {
          // 多选
          let questionOptions = JSON.parse(this.answerDetail.data[index].questionOptions)
          for (let i = 0; i < questionOptions.length; i++) {
            if (this.answerDetail.data[index].answer && this.answerDetail.data[index].answer.length) {
              for (let j = 0; j < this.answerDetail.data[index].answer.length; j++) {
                if (this.answerDetail.data[index].answer[j] == questionOptions[i].label) {
                  sortArray.push({
                    label: this.answerDetail.data[index].answer[j],
                    sort: i
                  })
                  break
                }
              }
            }
          }
          sortArray.sort((a, b) => {
            if (a.sort < b.sort) {
              return -1
            } else if (a.sort > b.sort) {
              return 1
            }
            return 0
          })
        }
        data.push({
          answer: this.answerDetail.data[index].type == 2 ? JSON.stringify(sortArray.map((item) => item.label)) : this.answerDetail.data[index].answer,
          questionnaireDetailsId: this.answerDetail.data[index].id
        })
      }
      request({
        url: '/system/answer',
        method: 'post',
        data: {
          data,
          questionnaireId: this.$route.query.id,
          status: 1
        },
      }).then((res) => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '提交答案成功！'
          })
          this.$store.dispatch("tagsView/delView", this.$route) &&
            this.$router.push({ path: "/questionnaireList" })
        } else {
          this.$message({
            type: 'error',
            message: '提交答案失败！'
          })
        }
      }).catch((error) => { })
    },
    back() {
      this.$store.dispatch("tagsView/delView", this.$route) &&
        this.$router.push({ path: "/questionnaireList" })
    }
  }
}
</script>

<style lang="scss" scoped>
.fill-in-questionnaire {
  .el-radio {
    display: block;
    padding: 10px 0 0 0;
    line-height: 36px;
    &:nth-child(1) {
      padding: 0;
    }
    ::v-deep .el-radio__label {
      display: inline-block;
      word-break: break-all;
      white-space: normal;
      vertical-align: top;
    }
  }
  .el-checkbox {
    padding: 10px 0 0 0;
    display: block;
    &:nth-child(1) {
      padding: 0;
    }
    ::v-deep .el-checkbox__label {
      display: inline-block;
      word-break: break-all;
      white-space: normal;
      vertical-align: top;
      line-height: 36px;
    }
  }
  .paper-name {
    margin-top: 40px;
    text-align: center;
    font-size: 24px;
    font-weight: bold;
    word-break: break-all;
  }
  .learn-resource {
    margin: 10px 0;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
  }
  .paper-description {
    padding: 10px 0;
    text-align: center;
    font-size: 12px;
    font-weight: bold;
    color: #747774;
    word-break: break-all;
  }
  .statistics {
    display: flex;
    justify-content: center;
    margin-top: 14px;
    margin-bottom: 60px;
    text-align: center;
    .item {
      width: 440px;
      border: 1px solid #ccc;
      &:nth-child(2) {
        border-left: 0;
      }
      .total {
        line-height: 40px;
        background-color: #f8f8f8;
      }
    }
  }
  .exam-question-item {
    width: 880px;
    margin: 0 auto;
  }
}
</style>
