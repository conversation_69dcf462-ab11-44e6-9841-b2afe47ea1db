<template>
  <el-card v-loading="loading" class="offline-training-manage">
    <el-row :gutter="24">
      <el-col :span="24" :xs="24">
        <el-form ref="queryForm" :model="queryData" size="small" label-width="80px" inline>
          <el-form-item label="培训名称" prop="trainingName">
            <el-input v-model.trim="queryData.trainingName" maxlength="128" placeholder="请输入培训名称" clearable :style="{ width: '100%' }"></el-input>
          </el-form-item>
          <el-form-item label="状态" prop="trainingStatus">
            <el-select v-model.trim="queryData.trainingStatus" placeholder="请选择状态" clearable filterable>
              <el-option v-for="cate in statusOptions" :key="cate.dictValue" :value="cate.dictValue" :label="cate.dictLabel">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="search">
              查询
            </el-button>
            <el-button type="primary" size="small" @click="resetForm">
              重 置
            </el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="openDialog(1)">添加</el-button>
      </el-col>
    </el-row>
    <el-row :gutter="24" style="margin-top: 24px">
      <el-col :span="24">
        <el-table :data="dataSource" border>
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="applyDetail(scope.row)">报名情况</el-button>
              <el-button type="text" @click="openDialog(2, scope.row)">编辑</el-button>
              <el-button v-if="scope.row.trainingStatus == 2" type="text" @click="openPublishDialog(scope.row)">发布</el-button>
              <el-button v-if="scope.row.trainingStatus == 1" type="text" @click="openPublishDialog(scope.row)">停止</el-button>
              <el-button type="text" @click="openDelDialog(scope.row)">删除</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" label="培训名称" prop="trainingName">
            <template slot-scope="scope">
              {{ scope.row.trainingName ? scope.row.trainingName : "--" }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="举办时间" prop="trainingTime">
            <template slot-scope="scope">
              {{ scope.row.trainingTime ? scope.row.trainingTime : "--" }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="举办地点" prop="trainingLocation">
            <template slot-scope="scope">
              {{ scope.row.trainingLocation ? scope.row.trainingLocation : "--" }}
            </template>
          </el-table-column>
          <!-- <el-table-column align="center" label="培训类型" prop="trainingType">
            <template slot-scope="scope">
              {{ scope.row.trainingType | trainingTypeFilter }}
            </template>
          </el-table-column> -->
          <el-table-column align="center" label="举办方" prop="organizers">
            <template slot-scope="scope">
              {{ scope.row.organizers ? scope.row.organizers : "--" }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="状态" prop="trainingStatus">
            <template slot-scope="scope">
              {{ trainingStatus(`${scope.row.trainingStatus}`) }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="创建时间" prop="createTime">
            <template slot-scope="scope">
              {{ scope.row.createTime ? scope.row.createTime : "--" }}
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query" />
      </el-col>
    </el-row>
    <el-dialog :title="trainingFormTitle" :visible.sync="formDialogVisible" width="60%" @close="close" :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="培训名称" prop="trainingName">
          <el-input :maxlength="50" v-model.trim="form.trainingName" placeholder="请输入培训名称"></el-input>
        </el-form-item>
        <!-- <el-form-item label="培训类型" prop="trainingType">
          <el-select v-model.trim="form.trainingType" placeholder="请选择培训类型">
            <el-option value="assessor" label="评审员培训"></el-option>
            <el-option value="inspector" label="审查员培训"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="活动时间" prop="trainingTime">
          <el-date-picker v-model.trim="form.trainingTime" type="datetime" placeholder="选择时间" value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%;"></el-date-picker>
        </el-form-item>
        <el-form-item label="举办地点" prop="trainingLocation">
          <el-input :maxlength="50" v-model.trim="form.trainingLocation" placeholder="请输入举办地点"></el-input>
        </el-form-item>
        <el-form-item label="举办方" prop="organizers">
          <el-input :maxlength="50" v-model.trim="form.organizers" placeholder="请输入举办方"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="trainingStatus">
          <el-radio-group v-model.trim="form.trainingStatus">
            <el-radio label="1">已发布</el-radio>
            <el-radio label="2">未发布</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="formDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="createTraining">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="提示" :visible.sync="publishDialog" width="400px" :close-on-click-modal="false">
      <div v-if="current.trainingStatus == '2'">确定发布{{ current.trainingName }}吗？</div>
      <div v-if="current.trainingStatus == '1'">状态设为“停止”后，客户端将无法查看，是否继续？</div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="publishDialog = false">取 消</el-button>
        <el-button type="primary" @click="publish">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="提示" :visible.sync="delDialog" width="400px" :close-on-click-modal="false">
      <div>确定删除{{ current.trainingName }}吗？</div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="delDialog = false">取 消</el-button>
        <el-button type="primary" @click="del">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="报名详情" :visible.sync="applyDialog" width="60%" :close-on-click-modal="false" destroy-on-close>
      <el-table :key="applyKeys" :data="applySource" border stripe height="600">
        <el-table-column label="报名人员" prop="nickName" align="center"></el-table-column>
        <el-table-column label="报名时间" prop="registrationTime" align="center"></el-table-column>
        <el-table-column label="签到表" align="center">
          <template slot-scope="scope">
            {{scope.row.signFileName || scope.row.fileName || '--'}}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="text" v-loading="uploading && trainer && trainer.userId == scope.row.userId" @click="uploadFile(scope.row)">上传签到表</el-button>
            <el-button type="text" v-if="scope.row.signFileName || scope.row.fileName" @click="download(scope.row)">下载</el-button>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="applyDialog = false">关 闭</el-button>
        <el-button type="primary" @click="training">确 定</el-button>

      </span>
    </el-dialog>

    <el-upload ref="upload" :action="action" :before-upload="beforeUpload" :on-success="onSuccess" :on-error="onError" :with-credentials="true" :headers="{
        Authorization: 'Bearer ' + getToken(),
      }" :data='{type : 1}' :accept="accept" style="display: none">
      <span id="signUpload" slot="trigger">
        上传签到表
      </span>
    </el-upload>
  </el-card>
</template>

<script>
import MaterialForm from "./components/materialForm.vue";
import MaterialDetail from "./components/materialDetail.vue";
import { getToken } from "@/utils/auth";
import request, { download } from "@/utils/request";

export default {
  name: "OfflineTrainingManage",
  components: {
    MaterialForm,
    MaterialDetail,
  },
  data() {
    return {
      dataSource: [],
      queryData: {
        trainingName: "",
        trainingType: null,
        status: 1,
        trainingStatus: null,
        pageSize: 10,
        pageNum: 1,
      },
      total: 0,
      formDialogVisible: false,
      dialogVisible: false,
      trainingFormTitle: "",
      form: {
        trainingName: '',
        trainingType: null,
        trainingTime: '',
        trainingLocation: '',
        organizers: '',
        trainingStatus: null
      },
      rules: {
        trainingName: [{ required: true, message: '请输入培训名称', trigger: 'blur' }]
      },
      statusOptions: [],
      current: {},
      publishDialog: false,
      delDialog: false,
      loading: false,
      applySource: [],
      applyDialog: false,
      trainingId: null,
      trainer: null,
      uploadDom: null,
      applyKeys: 0,
      uploading: false
    }
  },
  computed: {
    trainingStatus() {
      return (value) => {
        const status = this.statusOptions.find((status) => status.dictValue == value)
        return status ? status.dictLabel : '--'
      }
    },
    action() {
      return `${localStorage.baseUrl || process.env.VUE_APP_BASE_API
        }/common/uploadFile`;
    },
    accept() {
      return '.JPG,.JPEG,.PNG,.PDF,.XLS,.XLSX'
    }
  },
  filters: {
    trainingTypeFilter(value) {
      if (value == 'assessor') {
        return '评审员培训'
      } else if (value == 'inspector') {
        return '审查员培训'
      } else {
        return value || '--'
      }
    }
  },
  created() {
    this.initDicts()
    this.query()
  },
  methods: {
    getToken,
    initDicts() {
      this.getDicts("offline_training_status").then((response) => {
        this.statusOptions = response.data
      })
    },
    beforeUpload(file) {
      const acceptList = this.accept.split(',').map(accept => accept.toLocaleUpperCase());;
      const suffix = '.' + file.name.split('.').pop().toLocaleUpperCase();
      if (!acceptList.includes(suffix)) {
        this.$message({
          type: 'warning',
          message: '请上传指定格式的文件！'
        })
        return false;
      }
      // 当fileSize大于0时才检查文件大小是否符合，为0或者小于0则不校验文件大小
      if (file.size / 1024 / 1024 > 100) {
        this.$message({
          type: 'warning',
          message: `上传的文件大小不得超过100M`
        })
        return false;
      }
      this.uploading = true;
      return true;
    },
    download(row) {
      download("/common/downloadFile", { fileId: row.signFileId }, row.fileName || row.signFileName);
    },
    onSuccess(response, file, fileList) {
      if (response.code != 200) return this.onError(response);
      if (this.trainer) {
        if (this.trainer?.signFileId) {
          this.shareDelete([this.trainer.signFileId])
        }

        this.$set(this.trainer, 'signFileId', response.data.fileId)
        this.$set(this.trainer, 'signFileName', file.name)
        this.trainer = null
        this.applyKeys++;
        this.uploading = false;
      }
    },
    onError(error) {
      this.uploading = false;
      this.student = null
      this.$message({
        type: 'error',
        message: error.msg
      })
    },
    search() {
      this.queryData.pageNum = 1
      this.query()
    },
    query() {
      this.loading = true
      request({
        url: '/system/management/list',
        method: "get",
        params: this.queryData,
      }).then((res) => {
        this.loading = false
        if (res.code != 200) return
        this.dataSource = res.rows
        this.total = res.total
      }).catch((error) => {
        // console.log(error)
        this.loading = false
      })
    },
    training() {
      if (!this.applySource.length) {
        this.applyDialog = true;
        return;
      }
      let list = this.applySource.filter(({ signFileId }) => signFileId);
      if (!list.length) {
        this.applyDialog = true;
        return;
      }

      request({
        url: '/reviewer/rev/offline/training',
        method: 'post',
        data: {
          trainingId: this.trainingId,
          reviewerIdMapList: list.map(data => {
            return {
              reviewerId: data.userId,
              signFileId: data.signFileId
            }
          }),
        }
      }).then(() => {
        let ids = [...new Set(list.map(({ signFileId }) => signFileId))]
        if (ids.length) {
          // 管理员上传
          this.shareCreate(ids, this.$store.getters.userId, 'reviewer_initial_training');
        }

        this.trainingId = '';
        this.applyDialog = false;
      })
    },
    resetForm() {
      this.$refs["queryForm"].resetFields()
      this.queryData.pageSize = 10
      this.queryData.pageNum = 1
      this.query()
    },
    close() {
      this.form = {
        trainingName: '',
        trainingType: null,
        trainingTime: '',
        trainingLocation: '',
        organizers: '',
        trainingStatus: null
      }
      this.$refs.form.resetFields()
    },

    openDialog(type, data) {
      if (type == 2) {
        this.trainingFormTitle = '编辑培训'
        this.form = JSON.parse(JSON.stringify(data))
      } else if (type == 1) {
        this.trainingFormTitle = '新增培训'
      }
      this.formDialogVisible = true
    },
    applyDetail({ id }) {
      this.trainingId = id;
      request({
        url: '/reviewer/offline/training/registered?trainingId=' + id,
        method: 'get',
      }).then(res => {
        this.applySource = res.rows || []
        this.applyDialog = true;
      })
    },
    createTraining() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          request({
            url: '/system/management',
            method: this.trainingFormTitle == '新增培训' ? 'post' : 'put',
            data: this.form,
          }).then((res) => {
            if (res.code != 200) return
            this.$refs.form.resetFields()
            this.formDialogVisible = false
            this.query()
          })
        } else {
          return false
        }
      })
    },
    openPublishDialog(data) {
      this.current = data
      this.publishDialog = true
    },
    uploadFile(row) {
      this.trainer = row;
      this.trainer.signFileId = ''
      this.trainer.signFileName = ''
      if (!this.uploadDom) {
        this.uploadDom = document.getElementById("signUpload");
      }
      this.uploadDom.click();
    },
    delSignFile(row) {
      row.signFileId = ''
      row.signFileName = ''
      this.applyKeys++
    },
    publish() {
      request({
        url: '/system/management',
        method: 'put',
        data: {
          ...this.current,
          trainingStatus: this.current.trainingStatus == '2' ? '1' : '2'
        },
      }).then((res) => {
        if (res.code != 200) return
        this.$message({
          type: 'success',
          message: this.current.trainingStatus == '2' ? '发布' : '停止' + '成功！'
        })
        this.publishDialog = false
        this.query()
      })
    },
    openDelDialog(data) {
      this.current = data
      this.delDialog = true
    },
    del() {
      request({
        url: '/system/management',
        method: 'put',
        data: {
          ...this.current,
          status: 0
        }
      }).then((res) => {
        if (res.code != 200) return
        this.$message({
          type: 'success',
          message: '删除成功！'
        })
        this.delDialog = false
        this.query()
      })
    }
  }
}
</script>

<style lang='scss' scoped>
.offline-training-manage {
  padding: 24px;
  ::v-deep .el-dialog {
    .el-dialog__header {
      background-color: #3c81f5;
      padding-top: 10px;
      .el-dialog__title {
        color: white;
      }
      .el-dialog__headerbtn {
        top: 15px;
        .el-dialog__close {
          color: white;
        }
      }
    }
    .el-dialog__body {
      padding-bottom: 1px;
      & > span {
        color: black;
      }
    }
  }
  .detail-item {
    display: flex;
    margin-bottom: 32px;
    font-size: 14px;
    .item-name {
      width: 100px;
      text-align: right;
      color: #666666d8;
    }
    .item-value {
      font-weight: bold;
      color: #000;
    }
  }
}
</style>
