package com.thas.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 评审报告-风险分析数据
 * <AUTHOR>
 */

@Data
@ApiModel(value = "风险分析数据")
public class RiskInfoVO {

    @ApiModelProperty(value = "基本款Id")
    private String clauseId;
    @ApiModelProperty(value = "基本款款号")
    private String clauseNo;
    @ApiModelProperty(value = "评价结果(4-部分达标,5-不达标)")
    private String autResult;
    @ApiModelProperty(value = "不足描述")
    private String autEvaluate;
    @ApiModelProperty(value = "风险等级：1,2,4,6,9分")
    private String riskLevel;


}
