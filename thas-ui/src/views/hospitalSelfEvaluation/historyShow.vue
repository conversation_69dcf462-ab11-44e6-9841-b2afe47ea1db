<!--  -->
<template>
  <div class="history-show" v-loading="loading">
    <div v-if="distributed">
      <!-- 医院名称 -->
      <el-card class="box-card" shadow="always" style="margin-bottom: 10px; margin-left: -5px">
        <h1 style="margin: 0">{{ detail.hospitalName }}</h1>
      </el-card>

      <!-- 自评报告 -->
      <summary-show v-if="showSummary" :formData="summaryData"></summary-show>
      <aut-sa-aud-sa-report style="margin-top:15px;" v-if="showReport || showSummary" :flag="showSummary" :key="headerSlotKeys" :report="detail.autSaAudSaReport"></aut-sa-aud-sa-report>

      <!-- 自评总结 -->
      <!-- <summary-edit v-if="showSum" :status="autSaAudStatus" :detail="detail" :relation="relation" @summarySubmit="summarySubmit"></summary-edit> -->

      <!-- 评价  -->
      <el-card class="box-card" shadow="never" >
        <div slot="header" class="clearfix">
          <div style="float:left;">
            <h4>医院自评</h4>
          </div>
        </div>
        <!-- 款项选择器 -->
        <standard-card ref="standardCard" :key="`base${loadingKeys}`" :clauseIds="clauseIds" :versionId="relation.autCsId" @selectChange="selectChange" @loaded="standardLoaded">
        </standard-card>

        <!-- 款项 -->
        <el-form :key="loadingKeys" label-width="100px">
          <clause-item v-for="(clause, index) in clauseList" v-show="isThisPage(index + 1)" :clause="clause" :key="clause.clauseId">
            <evaluation-self :readonly="true" :type="AutSaAudCurrentStatusEnum.SA_CLAUSE" :clause="clause" :aut="saMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-self>
            <evaluation-examine :readonly="true" v-if="showExamine" :clause="clause" :aut="frMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-examine>
            <evaluation-review :readonly="true" v-if="showReview" :clause="clause" :aut="lastMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-review>
          </clause-item>
          <pagination v-show="total > limit" :total="total" :page.sync="pageNum" :limit.sync="limit" :pageSizes="[limit]" />
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
import request from "@/utils/request";
import evaluationMixin from "@/mixins/evaluation.vue";
import AutSaAudSaReport from './evaluationResult/autSaAudSaReport.vue'
import SummaryEdit from './summaryEdit/index.vue'
import SummaryShow from './summaryShow/index2.vue'
import EvaluationReview from '../../components/ClauseItem/evaluationReview.vue';

// 组件

export default {
  name: "HistoryShow",
  mixins: [evaluationMixin],
  components: {
    AutSaAudSaReport,
    SummaryEdit,
    SummaryShow,
    EvaluationReview
  },
  props: {},
  watch: {},
  data() {
    return {
      show: false,
      loading: true,
      relation: {},
      detail: {},
      termSubmitLoading: false,
      summaryData: {
        autDesc: '',
        fileName: '',
      },
      reviewResultOptions: [],
      categoryKeys: 0,
      specialInit: true,
    };
  },

  computed: {
    showArticle() {
      return this.articleId && (this.showArtFilter['artSa'][this.articleId] || this.termMap[this.articleId]);
    },
    showArticleEdit() {
      return !this.termMap[this.articleId] || this.showReport;
    },
    showFinally() {

    },
    showReport() {
      return this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.WAIT_CONFIRM_SA_SUM
    },
    showSum() {
      return this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.WAIT_TERM_SA_SUM ||
        this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.TERM_SA_SUM
    },
    showSummary() {
      let statuses = [
        this.AutSaAudCurrentStatusEnum.WAIT_TERM_SA,
        this.AutSaAudCurrentStatusEnum.TERM_SA_PROC,
        this.AutSaAudCurrentStatusEnum.WAIT_CONFIRM_SA_SUM,
        this.AutSaAudCurrentStatusEnum.WAIT_TERM_SA_SUM,
        this.AutSaAudCurrentStatusEnum.TERM_SA_SUM,
        this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_SUM_REJECT
      ]
      return !statuses.includes(this.autSaAudStatus)
    },
    showExamine() {
      let status = [
        this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_SUM_REJECT,
      ];
      return status.includes(this.autSaAudStatus)
    },
    showReview() {
      return Object.values(this.lastMap).length != 0;
    },
    showExamineCheck() {
      // 待审核  审核中  审核驳回
      let status = [
        this.AutSaAudCurrentStatusEnum.WAIT_AUD_FIRST_TRIAL_REVIEW,
        this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_REVIEW_PROC,
        this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_REVIEW_REJECT,
      ];
      return status.includes(this.autSaAudStatus) || true;
    },

    // 除自评外的全部
    statusOut() {
      let status = [
        this.AutSaAudCurrentStatusEnum.WAIT_TERM_SA,
        this.AutSaAudCurrentStatusEnum.TERM_SA_PROC,
        this.AutSaAudCurrentStatusEnum.WAIT_CONFIRM_SA_SUM,
        this.AutSaAudCurrentStatusEnum.WAIT_TERM_SA_SUM,
        this.AutSaAudCurrentStatusEnum.TERM_SA_SUM,
      ];
      return !status.includes(this.autSaAudStatus)
    },

    rejected() {
      return this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_SUM_REJECT;
    },

    onTime2ShowArtSum() {
      if (!this.articleId) return false;
      let clauseIds = this.clauseList.map(({ clauseId }) => clauseId);
      clauseIds.reduce((flag, id) => {
        return flag && this.empty(this.saMap[id]);
      }, true)
    },

    // 是否是自评中
    isSaClauseStatus() { },

    wholeClauseIds() {
      return this.wholeClauseList.map(({ clauseId }) => Number(clauseId));
    },

    // 待自评
    showAutFilter() {
      if (this.wholeClauseList.length === 0) return [];
      const list = Object.values(this.saMap);
      let wholeClauseIds = this.wholeClauseIds;
      let saClauseIds = list.map(({ clauseId }) => Number(clauseId));
      let restOfClauseIds = wholeClauseIds.filter((id) => !saClauseIds.includes(id));
      return restOfClauseIds;
    },

    // 待小结
    showArtFilter() {
      let articleMap = this.$store.getters.standardTypeItemByVersionId(this.relation.autCsId, 'article', 'map');
      let articleSimpleMap = {}

      // 获取全部的条
      Object.values(articleMap).forEach(({ clauseVoList, articleId }) => {
        // 过滤已提交的条小结
        if (this.termMap[articleId]) return;
        articleSimpleMap[articleId] = {
          articleId,
          clauseIds: clauseVoList.map(({ clauseId }) => Number(clauseId))
        }
      })

      // 获取所有已提交的款
      let allReadySubmitClauseIds = Object.values(this.saMap).map(({ clauseId }) => Number(clauseId));
      let totalAr = [], totalSa = [], artSaFinishMap = {};
      Object.values(articleSimpleMap).forEach(({ articleId, clauseIds }) => {
        // 判断该条的所有款师傅都填充完毕
        let flag = clauseIds.reduce((flag, id) => {
          return flag && allReadySubmitClauseIds.includes(id);
        }, true)
        if (flag) {
          totalSa.push(...clauseIds);
          totalAr.push(articleId);
          artSaFinishMap[articleId] = true;
        }
      })
      return {
        art: totalAr,
        sa: totalSa,
        artSa: artSaFinishMap
      };
    },

    // 被驳回
    showAutRjFilter() {
      const list = Object.values(this.saMap);
      const rjList = list.filter(({ rejected }) => rejected).map(({ clauseId }) => clauseId);
      return rjList;
    },

    // 自评完毕分类
    showAutCategoryFilter() {
      let saList = Object.values(this.saMap);
      let list = this.reviewResultOptions.map(({ dictValue, dictLabel }) => {
        let ids = saList.filter(({ autResult }) => autResult == dictValue);
        return {
          dictValue,
          dictLabel,
          ids
        }
      }) || []
      return list.filter(({ ids }) => ids.length);
    },

    showCategory() {
      if (!this.detail.rejectAutSaAudListMap) return true;
      let saRejectMap = this.detail.rejectAutSaAudListMap['sa_clause']
      if (!saRejectMap) return true;
      const list = Object.values(saRejectMap);
      return !list.length
    },
  },

  mounted() {
    this.getDicts('review_result').then(res => {
      this.reviewResultOptions = res.data;
      this.categoryKeys++;
    })
    this.init2(true, this.$route.query.autCode);
  },

  methods: {
    queryList(autCode) {
      return request({
        url: "/aut/sa/aud/selectAutSaAudDetailList",
        method: 'get',
        params: {
          autCode,
        }
      })
    },

    async init2(force = true, autCode, pageType) {
      try {
        let { data } = await this.queryList(autCode);
        let autSaRelation = data[0];
        let { distributeClauseIdList } = autSaRelation

        this.relation = autSaRelation;
        // this.relation = data;

        this.autCode = this.relation.autCode;
        this.versionId = this.relation.autCsId;
        let res = await this.queryDetail();
        this.detail = res.data;

        this.autSaAudStatus = this.detail.autSaAudStatus;
        this.clauseIds = !this.specialIds ? distributeClauseIdList : []
        this.distributeClauseIds = !this.specialIds ? distributeClauseIdList : []

        force && this.clearClauseList()

        this.analyzeData(force);
      } catch (error) {
        // console.log(error);
        this.handleError();
      } finally {
        this.loading = false;
        this.distributed = true;
      }
    },



    initFinalBack() {

      if (this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_SUM_REJECT && this.showAutRjFilter.length) {
        this.$set(this, 'clauseIds', this.showAutRjFilter)
        this.$set(this, 'distributeClauseIds', this.showAutRjFilter)
        this.loadingKeys++;
      }

      if (!this.detail || !this.detail.autSaAudSum) {
        return;
      }
      let { autDesc, fileIds } = this.detail.autSaAudSum;
      this.summaryData = {}
      let fileNames = ''
      if (fileIds) {
        let fileIdList = fileIds.split(',') || [];
        let fileList = fileIdList.map(id => this.detail.fileDetailMap[id][0]);
        if (fileList.length) {
          fileNames = fileList.map(file => file.fileName).join(',');
        }
      }
      this.$set(this.summaryData, 'autDesc', autDesc)
      this.$set(this.summaryData, 'fileName', fileNames)
    },
    empty(value) {
      let type = typeof value;
      if (type === 'object') {
        if (Array.isArray(value)) {
          return value.length === 0
        } else {
          return Object.keys(value).length === 0;
        }
      } else if (type === 'string') {
        return value.trim() === ""
      } else if (type === 'number') {
        return Number.isNaN(value);
      }
      return true;
    },

    summarySubmit() {
      this.init();
    },

    async selfConfirm() {
      if (this.autSaAudStatus !== this.AutSaAudCurrentStatusEnum.WAIT_CONFIRM_SA_SUM) return;
      let types = this.AutSaAudCurrentStatusEnum.getTypeByStatus(this.autSaAudStatus);
      let data = {
        autCode: this.relation.autCode,
        accountId: this.$store.getters.userId + "",
        submitType: types[1],
        autSaAudLists: []
      }
      try {
        let res = await this.submit(data);
        // 报错就不提示
        if (!res) return;
        this.$message("确认成功");
        this.init();
      } catch (error) {
        if (error.code == 1000003) {
          this.init();
        }
      } finally {
        // this.termSubmitLoading = false;
      }
    },


    handleError() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push({ path: "/hospitalSelfEvaluation/hospitalSelfEvaluationHistory" });
    },

    // 自评
    submitAll() {
      let list = Object.values(this.saMap);
      if (list.length == 0) return this.$message({ type: 'warning', message: '请先提交最少一条自评！' });
      this.makeData(this.AutSaAudCurrentStatusEnum.SA_CLAUSE, 'clause', list[0].fileIds);
    },
    submitAllArticle() { this.makeArtiCle(this.AutSaAudCurrentStatusEnum.SA_ARTICLE_SUMMARY) },

    // 审查组长审查
    submitAllSaAudReport() { this.makeData(this.AutSaAudCurrentStatusEnum.FR_REPORT_R_CLAUSE) },
    // 医院认证
    submitAllHospitalAcc() { this.makeData(this.AutSaAudCurrentStatusEnum.FAR_CLAUSE) },
    // 第一次修改
    submitAllAudFirst() { this.makeData(this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_M) },
    // 验证评审员
    submitAllSAud() { this.makeData(this.AutSaAudCurrentStatusEnum.TR_CLAUSE) },
    // 第二次修改
    submitAllAudSecond() { this.makeData(this.AutSaAudCurrentStatusEnum.TR_CLAUSE_M) },



    async makeArtiCle(type) {
      this.makeData(type, 'article');
    },
  },
};
</script>
<style lang="scss" scoped>
.box-card + .box-card {
  margin-top: 15px;
}
.box-card {
  border: 0;
}
.articleSum {
  margin-top: 15px;
}
</style>

<style lang="scss">
</style>

