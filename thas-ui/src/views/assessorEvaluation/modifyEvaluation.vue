<!--  -->
<template>
  <div class="modify-evaluation-list">
    <el-form ref="form" :model="queryData" inline label-width="130px">
      <el-form-item label="医院名称：" prop="distributeHospitalName">
        <el-input v-model.trim="queryData.distributeHospitalName" :maxlength="20" placeholder="请输入医院名称"></el-input>
      </el-form-item>
      <el-form-item label="">
        <el-button type="primary" size="small" @click="query">
          查 询
        </el-button>
        <el-button type="primary" size="small" @click="reset">
          重 置
        </el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataSource" border stripe>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <div class="table-btn">
            <el-button type="text" v-if="showTrReportModal(scope.row)" @click="seniorReport(scope.row)">查看验证报告</el-button>
            <el-button type="text" v-if="showConfirm(scope.row)" @click="modify(scope.row)">查看事实准确性确认表</el-button>
            <el-button type="text" v-else-if="showModify(scope.row)" @click="toModify(scope.row)">修改</el-button>
            <!-- <el-button type="text"
              v-if="(scope.row.status == AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_REVIEW || scope.row.status == AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_REVIEW_CONFIRM) && scope.row.isLeader == 1"
              @click="toModify(scope.row)">修改</el-button> -->
            <el-button type="text" v-else-if="showModifyChange(scope.row)"
              @click="toModifyChange(scope.row)">修改审核</el-button>
            <el-button type="text" v-else-if="showModifyReport(scope.row)"
              @click="leaderModify(scope.row)">提交事实准确性确认表</el-button>
            <el-button type="text" v-else-if="showSubmitReport(scope.row)"
              @click="submitReport(scope.row)">提交评审报告</el-button>

            <el-button type="text" v-if="scope.row.reviewReportPdfFileId"
              @click="downloadReport(scope.row)">评审报告下载</el-button>
            <el-button v-if="showTemporaryReport(scope.row)" type="text"
              @click="downloadTemReport(scope.row)">预审核报告下载</el-button>
            <el-button v-if="showVerifyReport(scope.row)" type="text"
              @click="downloadVerifyReport(scope.row)">验证报告下载</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="医院名称" align="center" prop="hospitalName">
      </el-table-column>
      <el-table-column label="评审完成时间" align="center" prop="audSecondSubmitTime">
      </el-table-column>
      <el-table-column label="评审报告生成时间" align="center" prop="reportSubmitTime">
      </el-table-column>
      <el-table-column label="审查评审报告完成时间" align="center" prop="reviewReviewReportSubmitTime">
      </el-table-column>
      <el-table-column label="准确性审查完成时间" align="center" prop="accuracyReviewSubmitTime">
      </el-table-column>
      <el-table-column label="验证评审员验证完成时间" align="center" prop="seniorReviewerReviewSubmitTime">
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          {{ AutSaAudCurrentStatusEnum[scope.row.status] }}
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize"
      @pagination="query" />

    <el-dialog title="待修改款项" :visible.sync="reviewerVisible" :close-on-click-modal="false" width="80%">
      <el-table :data="modifyClauseInfoLists" border stripe>
        <el-table-column label="待修改款项" prop="clauseNo"></el-table-column>
        <el-table-column label="评审员" prop="name"></el-table-column>
        <el-table-column label="是否已修改" prop="modify">
          <template slot-scope="scope">
            {{ scope.row.modify == 'N' ? '未修改' : '已修改' }}
          </template>
        </el-table-column>
      </el-table>

      <span slot="footer" class="dialog-footer">
        <el-button @click="reviewerVisible = false">关 闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="上传验证报告" :visible.sync="visible" width="50%" :close-on-click-modal="false" destroy-on-close
      custom-class="elDialog" @close="trClose">
      <el-form :model="form" ref="verifyForm" label-width="80px">
        <el-form-item label="下载：">
          <el-button @click="downloadVerifyReport(selectRow)">下载验证报告</el-button>
        </el-form-item>
        <el-form-item label="上传：" prop="fileId" :rules="[{ required: true, message: '请上传已盖章的验证报告', trigger: ['blur'] }]">
          <upload :disabled="!form.hasDownloadVerifyReport" single class="evaluation-upload" :files="files" :data="{type: '1', downLoadFileName: `${selectRow && selectRow.hospitalName}-验证报告.pdf`}" @uploadSuccess="onSuccess" @fileRemove="onError" :text="`上传签字盖章后的验证报告`"></upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false">关 闭</el-button>
        <!-- <el-button type="primary" @click="submitVerifyReport">提交</el-button> -->
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request, { download } from "@/utils/request";
import DictSpan from "@/components/DetailMessage/dictSpan.vue";
import ModifyTable from "@/views/hospitalSelfEvaluation/components/modifyTable.vue";

export default {
  name: "ModifyEvaluation",
  components: {
    DictSpan,
    ModifyTable
  },
  props: {},
  watch: {},
  data() {
    return {
      queryData: {
        distributeHospitalName: "",
        distributeStatus: "",
        pageSize: 10,
        pageNum: 1,
      },
      url: {
        list: "/aut/sa/aud/queryList",
      },
      total: 0,
      dataSource: [],
      reviewerVisible: false,
      modifyClauseInfoLists: [],

      visible: false,
      selectRow: null,
      trReport: null,

      form: {
        hasDownloadVerifyReport: false,
        fileId: '',
        downloadLoading: false,
      },
      files: [],
      trClauseList: [],
      srClauseList: [],
      trmClauseList: [],
      trmSkipClauseList: [],
      suggestionOptions: [
        {
          dictValue: "1",
          dictLabel: "优秀",
          listClass: '',
        },
        {
          dictValue: "2",
          dictLabel: "良好",
          listClass: '',
        },
        {
          dictValue: "3",
          dictLabel: "中等",
          listClass: '',
        },
        {
          dictValue: "4",
          dictLabel: "尚可",
          listClass: '',
        },
        {
          dictValue: "5",
          dictLabel: "较差",
          listClass: '',
        },
      ],
    };
  },

  watch: {
    $route(to, from) {
      if (
        to.path == "/fAssessor/m/assessorEvaluationList" &&
        from.path == "/fInspector/CInspectorIndex"
      ) {
        this && this.query && this.query();
      }
    },
  },

  computed: {
    revision() {
      return ({ isLeader, status }) => {
        let st = [
          // this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_REJECT,
          this.AutSaAudCurrentStatusEnum.MODIFIED_TERMS_FIRST_PROC,
          this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW,
        ]
        return isLeader == 1 && st.includes(status)
      }
    },
    showModifyReport() {
      return (row) => {
        let statues = [
          this.AutSaAudCurrentStatusEnum.MODIFICATION_TERMS_SUM,
          this.AutSaAudCurrentStatusEnum.MODIFICATION_TERMS_CONFIRM,
          this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW_SUM,
        ]
        return row.isLeader == 1 && statues.includes(row.status)
      }
    },
    showSubmitReport() {
      return (row) => {
        return row.isLeader == 1 && this.AutSaAudCurrentStatusEnum.WAIT_AUD_REPORT_SUBMIT === row.status
      }
    },
    showReviewDialog() {
      return row => {
        return (this.showConfirm(row) || this.showModify(row) || this.showModifyReport(row)) && row.isLeader == 1 && Array.isArray(row.modifyClauseInfoLists)
      }
    },
    showConfirm() {
      return (row) =>
        row.isLeader == 1 && (
          row.status == this.AutSaAudCurrentStatusEnum.CHECK_MODIFIED_TERMS_FIRST
        );
    },
    showTrReportModal() {
      return (row) => {
        return row.isLeader == 1 && ((row.autSaAud && row.autSaAud.some(({ submitType }) => submitType == 'tr_clause_summary')) || row.status == this.AutSaAudCurrentStatusEnum.CHECK_MODIFIED_TERMS_SECOND)
      }
    },

    showTurnToLeader() {
      return this.selectRow?.status == this.AutSaAudCurrentStatusEnum.CHECK_MODIFIED_TERMS_SECOND
    },

    showModify() {
      return (row) => {
        let statues = [
          this.AutSaAudCurrentStatusEnum.MODIFIED_TERMS_FIRST_PROC,
          this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW
        ]
        let statues2 = [
          this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_REVIEW,
          this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_REVIEW_CONFIRM
        ]
        let hasModifyClause = (row.waitModifyClauseIdList || []).length != 0
        let isTrueStatus = statues.includes(row.status)
        let isFirstModify = statues2.includes(row.status)
        return ((hasModifyClause && isTrueStatus) || isFirstModify) && row.isLeader == 1;
      }
    },

    showModifyChange() {
      return (row) => {
        return row.isLeader == 1 && (this.AutSaAudCurrentStatusEnum.MODIFIED_TERMS_TRIAL_FIRST_PROC == row.status ||
          this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_TRIAL_SENIOR_REVIEWER_REVIEW == row.status);
      }
    },

    showModifyConfirm() {
      return (row) =>
        row.isLeader == 1 &&
        row.status == this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_REVIEW_CONFIRM
    },

    showReportGenerate() {
      return (row) =>
        row.isLeader == 1 &&
        row.status == this.AutSaAudCurrentStatusEnum.WAIT_REPORT
    },


    showTemporaryReport() {
      return ({ isLeader, status }) => {
        let statuses = [
          // 第一次复查
          this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_REVIEW_CONFIRM,
          this.AutSaAudCurrentStatusEnum.MODIFIED_TERMS_TRIAL_FIRST_PROC,
          this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_TRIAL_SENIOR_REVIEWER_REVIEW,
        ]
        return isLeader == 1 && statuses.includes(status);
      }
    },

    showVerifyReport() {
      return row => {
        let fileMap = row.fileDetailMap || {};
        let fileMapList = Object.values(fileMap);
        let files = fileMapList.find(files => {
          let file = files.find(file => {
            return file.fileType === 'verify_review_report'
          })
          return file
        })
        let file = (files || [])[0];
        return file;
      }
    },

    modifyClause() {
      return this.trClauseList.map(({ clauseId }) => clauseId)
    },

    modifyAutResult() {
      return id => {
        let aut = this.srClauseList.find(({ clauseId }) => clauseId == id)
        return aut?.autResult
      }
    },

    modifyAutDesc() {
      return (id) => {
        let aut = this.srClauseList.find(({ clauseId }) => clauseId == id)
        return this.descSubdivision(aut)
      };
    },

    showTrm() {
      return this.trmClauseList.length || this.trmSkipClauseList.length
    },

    descSubdivision() {
      return aut => {
        if (!aut) return '--'
        try {
          let autResult = aut.autResult;
          let { autAdvantage, autEvaluate, autProposal, autImprove, autDesc } = JSON.parse(aut.autDesc)
          if (autResult == 0) {
            return '拒绝原因：' + (autDesc || '--')
          }
          if (autResult == 1 || autResult == 2) {
            return '评价描述：' + (autAdvantage || '--')
          } else if (autResult == 3) {
            return '评价描述：' + (autImprove || '--')
          } else {
            if (autResult != 6) {
              let a = autEvaluate ? `不足：${autEvaluate}  \n` : '--'
              let b = autProposal ? `整改建议：${autProposal}  \n` : '--'
              let c = autImprove ? `改进机会：${autImprove}  \n` : '--'
              if (autEvaluate || autProposal || autImprove) {
                return [a, b, c].join(' ')
              }
            }
            return '评价描述：' + (autDesc || '--');
          }
        } catch (error) {
          return '评价描述：' + (aut.autDesc || '--');
        }
      }
    },

    trDesc() {
      return id => {
        return this.trClauseList.find(({ clauseId }) => clauseId == id)?.autDesc || '--'
      }
    },

    trmResult() {
      return (id) => {
        try {
          let aut = this.trmSkipClauseList.find(({ clauseId }) => clauseId == id)
          if (!aut) {
            aut = this.trmClauseList.find(({ clauseId }) => clauseId == id)
          }
          let result = aut.autResult;
          // if(result == 0) {
          //   return '评价结果：拒绝修改 \n'
          // }
          let option = this.reviewResultOptions.find(
            ({ dictValue }) => dictValue == result
          );
          return option ? '评价结果：' + option.dictLabel + '\n' : "";
        } catch (error) {
          return ''
        }
      }
    },

    trmDesc() {
      return id => {
        let aut = this.trmSkipClauseList.find(({ clauseId }) => clauseId == id)
        if (!aut) {
          aut = this.trmClauseList.find(({ clauseId }) => clauseId == id)
        }
        return this.descSubdivision(aut)
      }
    },
  },

  created() { },

  mounted() {
    this.query();
  },

  methods: {
    query() {
      request({
        url: this.url.list,
        method: "post",
        data: {
          accountId: this.$store.getters.userId + "",
          pageType: 'far_clause_w_m'
        },
      }).then((res) => {
        this.dataSource = (res.rows || []).map((row) => {
          // for (const key in row) {
          //   if (
          //     Object.hasOwnProperty.call(row, key) &&
          //     !row[key] &&
          //     row[key] != 0
          //   ) {
          //     row[key] = "--";
          //   }
          // }
          return row;
        });
        this.total = res.total;
      }).catch(() => {
        this.dataSource = []
        this.total = 0
      });
    },

    downloadReport({ hospitalName, reviewReportPdfFileId, reviewReportPdfFileName }) {
      download("/common/downloadFile", { fileId: reviewReportPdfFileId }, reviewReportPdfFileName);
    },

    leaderModify({ autCode, status }) {
      let path = status === this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW_SUM ? '/fAssessor/modifyVerifyReportResults' : '/fAssessor/CAssessorIndex';
      this.$router.push({
        path,
        query: {
          autCode,
          type: this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_W_M
        },
      });
    },

    submitReport(row) {
      this.selectRow = row;
      this.visible = true;

    },

    submitVerifyReport() {
      this.$refs.verifyForm.validate(flag => {
        if (!flag) return;
        let loading = this.$loading({
          lock: true,
          text: "提交中...",
          background: "rgba(0, 0, 0, 0.7)",
        });
        let row = this.selectRow;
        request({
          url: "/aut/sa/aud/submitAutSaAud",
          method: "post",
          data: {
            submitType: this.AutSaAudCurrentStatusEnum.SR_T_REPORT_SUBMIT,
            autCode: row.autCode,
            accountId: this.$store.getters.userId + "",
            autSaAudLists: [
              {
                autResult: "1",
                autDesc: "--",
                fileIds: this.form.fileId,
              },
            ],
          },
        })
          .then((res) => {
            this.$message({
              type: "success",
              message: "报告已生成",
            });
            this.shareCreate([this.form.fileId], this.$store.getters.userId, 'reviewer_evaluation_party', this.selectRow.hospitalName);
            this.query();
            this.visible = false;
          }).finally(() => {
            loading.close();
          })
      })

    },

    turnToLeader2() {
      if (!this.selectRow) return this.trClose();
      this.modify(this.selectRow, 'viewVerifyReport')
    },

    modify({ autCode }, type) {
      let path = type === 'viewVerifyReport' ? '/fAssessor/viewVerifyReport' : '/fAssessor/ModificationConfirm'
      this.$router.push({
        path,
        query: {
          autCode,
          type: this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_W_M
        }
      })
    },

    showReviewer(data) {
      this.modifyClauseInfoLists = data.modifyClauseInfoLists || []
      this.reviewerVisible = true;
    },

    toModify({ autCode, status }) {
      let type = this.AutSaAudCurrentStatusEnum.SR_CLAUSE_M
      if (status === this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW) {
        type = this.AutSaAudCurrentStatusEnum.TR_CLAUSE_W_M
      } else if (status === this.AutSaAudCurrentStatusEnum.MODIFIED_TERMS_FIRST_PROC) {
        type = this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_W_M
      }
      let path = status === this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_REVIEW || status === this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_REVIEW_CONFIRM ? '/fAssessor/modifyResultsReport' : status === this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW ? '/fAssessor/modifyVerifyReportResults' : '/fAssessor/CAssessorIndex';
      this.$router.push({
        path,
        query: {
          autCode,
          type
        },
      });
    },

    toModifyChange({ autCode, status }) {
      this.$router.push({
        path: "/fAssessor/CAssessorIndex",
        query: {
          autCode,
          type: status === this.AutSaAudCurrentStatusEnum.MODIFIED_TERMS_TRIAL_FIRST_PROC ? this.AutSaAudCurrentStatusEnum.FAR_V_CLAUSE_M : this.AutSaAudCurrentStatusEnum.TR_V_CLAUSE_M,
        },
      });
    },

    toRevision(autCode) {
      this.$router.push({
        path: "/fAssessor/revision",
        query: { autCode },
      });
    },

    reportGenerate({ autCode }) {
      this.$message('生成最终报告中...')
      request({
        url: "/aut/sa/aud/submitAutSaAud",
        method: "post",
        data: {
          autCode,
          accountId: this.$store.getters.userId + "",
          submitType: this.AutSaAudCurrentStatusEnum.SR_REPORT_GENERATE,
          autSaAudLists: [],
        },
      }).then((res) => {
        this.$message({
          type: 'success',
          message: '报告生成成功！'
        })
        this.query();
        this.shareCreate([this.form.fileId], this.$store.getters.userId, 'reviewer_evaluation_party');
      }).catch(error => {
        this.$message({
          type: 'error',
          message: error || error.message
        })
      });
    },

    confirm({ autCode }) {
      request({
        url: "/aut/sa/aud/submitAutSaAud",
        method: "post",
        data: {
          autCode,
          accountId: this.$store.getters.userId + "",
          type: this.AutSaAudSubmitTypeEnum.DEFAULT_TYPE_98,
          autSaAudLists: [],
        },
      }).then((res) => {
        this.query();
      });
    },

    reset() {
      this.$refs["form"].resetFields();
      this.query();
    },

    seniorReport(row) {
      this.selectRow = row;
      this.turnToLeader2();
      // this.visible = true;
      // try {
      //   let list = row.autSaAud
      //   this.trClauseList = list.filter(({ submitType }) => submitType == 'tr_clause')
      //   this.srClauseList = list.filter(({ submitType }) => submitType == 'sr_clause')
      //   this.trmClauseList = list.filter(({ submitType }) => submitType == 'tr_clause_m')
      //   this.trmSkipClauseList = list.filter(({ submitType }) => submitType == 'tr_clause_m_skip')
      //   let autSaAud = list.find(({ submitType }) => submitType == 'tr_summary')
      //   this.form = JSON.parse(autSaAud.autDesc)
      //   this.form.autCode = row.autCode
      // } catch (error) {
      //   console.log(error);
      // }
    },


    trClose() {
      this.form = {
        hasDownloadVerifyReport: false,
        fileId: '',
        downloadLoading: false,
      }
      this.trClauseList = []
      this.selectRow = null
      this.visible = null
    },

    downloadTemReport({ autCode }) {
      request({
        url: '/aut/sa/aud/getTemReviewReportFileInfo/' + autCode,
        method: 'get'
      }).then(res => {
        let { fileId, fileName } = res.data
        download("/common/downloadFile", { fileId }, fileName);
      })
    },

    downloadVerifyReport(row) {
      let file = this.showVerifyReport(row);
      download("/common/downloadFile", { fileId: file.fileId }, file.downLoadFileName, flag => {
        if (flag) {
          this.form.hasDownloadVerifyReport = true;
        }
      });
    },

    onSuccess(res, file) {
      this.form.fileId = res.data.fileId;
      this.$set(this.files, 0, file);
      this.submitVerifyReport()
    },

    onError() {
      if (this.files[0]) {
        this.files.splice(0, 1);
        this.setFileIDs();
      }
    },
  },
};
</script>

<style lang='scss' scoped>
.modify-evaluation-list {
  min-height: inherit;
  background-color: white;
  padding: 12px;

  ::v-deep .table-btn {
    .el-button+.el-button {
      display: block;
      margin: 0 auto;
    }
  }
}
</style>
