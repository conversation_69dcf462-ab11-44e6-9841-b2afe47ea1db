<template>
  <div class="panel-group">
    <div
      :class="['card-panel', { active: currentIndex === 1 }]"
      @click="handleSetTableData('1')"
    >
      <div class="card-panel-icon-wrapper icon-first">
        <svg-icon icon-class="hospital" class-name="card-panel-icon" />
      </div>
      <div class="card-panel-description">
        <div class="card-panel-text">受评医院的总数量</div>
        <count-to
          :start-val="0"
          :end-val="+data.acceptTotalNum || 0"
          :duration="2600"
          class="card-panel-num"
        />
      </div>
    </div>
    <div
      :class="['card-panel', { active: currentIndex === 2 }]"
      @click="handleSetTableData('2')"
    >
      <div class="card-panel-icon-wrapper icon-second">
        <svg-icon icon-class="hospital" class-name="card-panel-icon" />
      </div>
      <div class="card-panel-description">
        <div class="card-panel-text">受评中的医院数量</div>
        <count-to
          :start-val="0"
          :end-val="+data.acceptingNum || 0"
          :duration="3000"
          class="card-panel-num"
        />
      </div>
    </div>
    <div
      :class="['card-panel', { active: currentIndex === 3 }]"
      @click="handleSetTableData('3')"
    >
      <div class="card-panel-icon-wrapper icon-third">
        <svg-icon icon-class="hospital" class-name="card-panel-icon" />
      </div>
      <div class="card-panel-description">
        <div class="card-panel-text">已受评的医院数量</div>
        <count-to
          :start-val="0"
          :end-val="+data.acceptedNum || 0"
          :duration="3200"
          class="card-panel-num"
        />
      </div>
    </div>
  </div>
</template>

<script>
import CountTo from "vue-count-to";

export default {
  components: {
    CountTo,
  },
  props: {
    data: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      currentIndex: 1,
    };
  },
  methods: {
    handleSetTableData(type) {
      this.currentIndex = +type;
      this.$emit("handleSetTableData", type);
    },
  },
};
</script>

<style lang="scss" scoped>
.panel-group {
  display: flex;
  justify-content: space-around;
  background-color: #f2f3f5;
  padding: 10px 15px 0 15px;
  margin-bottom: 10px;

  .card-panel {
    position: relative;
    background-color: transparent;
    padding: 0px 10px;

    border-radius: 12px 12px 0 0;
    cursor: pointer;
    transition: 0.2s;

    .card-panel-icon-wrapper {
      float: left;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }
    .icon-first {
      color: #36a3f7;
    }

    .icon-second {
      color: #e6a23c;
    }

    .icon-third {
      color: #40c9c6;
    }

    .card-panel-icon {
      float: left;
      font-size: 40px;
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 14px;
      margin-left: 0px;

      .card-panel-text {
        line-height: 23px;
        font-size: 14px;
      }

      .card-panel-num {
        font-size: 16px;
      }
    }
  }
  .card-panel::before,
  .card-panel::after {
    position: absolute;
    bottom: 0;
    content: "";
    width: 20px;
    height: 20px;
    border-radius: 100%;
    box-shadow: 0 0 0 40px transparent;
    transition: 0.2s;
  }
  .card-panel::before {
    left: -20px;
    clip-path: inset(50% -10px 0 50%);
  }
  .card-panel::after {
    right: -20px;
    clip-path: inset(50% 50% 0 -10px);
  }

  .card-panel:hover {
    background-color: #fff;
  }
  .card-panel:hover::before,
  .card-panel:hover::after {
    box-shadow: 0 0 0 30px #fff;
  }
  .card-panel.active {
    background-color: #fff;
    z-index: 1;
  }
  .card-panel.active::before,
  .card-panel.active::after {
    box-shadow: 0 0 0 30px #fff;
  }
}

@media (max-width: 550px) {
  .card-panel-description {
    display: none;
  }

  .card-panel-icon-wrapper {
    float: none !important;
    width: 100%;
    height: 100%;
    margin: 0 !important;

    .svg-icon {
      display: block;
      margin: 14px auto !important;
      float: none !important;
    }
  }
}
</style>
