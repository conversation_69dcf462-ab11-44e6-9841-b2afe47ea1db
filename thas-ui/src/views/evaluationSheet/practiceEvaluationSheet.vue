<template>
  <div class='theoretical-evaluation-sheet'>
    <h2 style="text-align:center;padding-top:16px;">现场评审带教培训评估结果</h2>
    <div class="sheet">
      <el-descriptions :column="3" border :labelStyle="{ width: '100px', wordBreak: 'keep-all', fontSize: '16px' }"
        :contentStyle="{ wordBreak: 'keep-all', minWidth: '100px' }">
        <el-descriptions-item label="学员名字">{{ formData.name }}</el-descriptions-item>
        <el-descriptions-item label="性别"> {{ gender }}</el-descriptions-item>
        <el-descriptions-item label="专业">{{ major }}</el-descriptions-item>
        <el-descriptions-item label="工作单位"> {{ formData.company }}</el-descriptions-item>
        <el-descriptions-item label="现任职务"> {{ formData.companyPost }}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="1" border>
        <el-descriptions-item :labelStyle="{ width: '0px', padding: '0px', borderRight: '0px' }"
          :contentStyle="{ borderTop: '0px', borderBottom: '0px', borderLeft: '0px' }">
          <span class="required" style="color: #909399;font-size: 16px;">现场评审带教时间：</span>
          <el-date-picker v-model="formData.courseStartTime" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date"
            placeholder="请选择开始日期" @change="timeChange" style="margin-left:10px;"></el-date-picker>
          <label style="margin-left:10px;margin-right:12px;">至</label>
          <el-date-picker v-model="formData.courseEndTime" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date"
            placeholder="请选择结束日期" @change="timeChange"></el-date-picker>
          <div class="error" v-if="showError && (!formData.courseStartTime || !formData.courseEndTime)">请选择课程时间</div>
          <div class="error" v-if="showError && timeError">结束日期不得小于开始日期</div>
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="1" direction="vertical" border
        :labelStyle="{ width: '100px', padding: '0', border: '0' }">
        <el-descriptions-item :contentStyle="{ borderBottom: '0px' }">
          <div class="fontStyle required">课程形式（可勾选√）：</div>
          <el-radio-group v-model="formData.courseFormat">
            <el-radio class="course_radio" v-for="(courseFormat, index) in courseTypeOptions" :key="index"
              :label="Number(courseFormat.dictValue)">{{ courseFormat.dictLabel }}</el-radio>
          </el-radio-group>
          <div class="error" v-if="showError && !formData.courseFormat">请选择课程形式</div>
        </el-descriptions-item>
        <el-descriptions-item :contentStyle="{ borderBottom: '0px' }">
          <div class="fontStyle required">培训教员（可选多名）：</div>
          <el-select v-model="formData.trainerInfoList" multiple filterable allow-create default-first-option
            placeholder="请选择培训教员或者输入培训教员" @change="teacherSelect">
            <el-option v-for="(teacher, index) in reviewerList" :key="index" :value="`${teacher.userId}`"
              :label="teacher.nickName"></el-option>
          </el-select>
          <div class="error" v-if="showError && !formData.trainerInfoList.length">请选择培训教员</div>
        </el-descriptions-item>
        <el-descriptions-item :contentStyle="{ borderBottom: '0px' }">
          <div class="fontStyle required">结论（单选√）</div>
          <el-radio class="course_radio" v-model="formData.conclusion" :label="20">适合当评审员，可考虑培训为评审组长</el-radio>
          <el-radio class="course_radio" v-model="formData.conclusion" :label="21">适合当评审员，不考虑培训为评审组长</el-radio>
          <el-radio class="course_radio" v-model="formData.conclusion" :label="22">需再带教培训</el-radio>
          <el-radio class="course_radio" v-model="formData.conclusion" :label="23">不适合当评审员</el-radio>
          <el-radio class="course_radio" v-model="formData.conclusion" :label="0">其他（请具体列明）{{ formData.conclusion === 0 ?
            '：' : '' }}<el-input v-if="formData.conclusion === 0" v-model="formData.otherRemark" :maxlength="20"
              style="width:300px;"></el-input>
          </el-radio>
          <div class="error" v-if="showError && !formData.conclusion && formData.conclusion != 0">请选择结论</div>
          <div class="error"
            v-if="showError && formData.conclusion !== '' && formData.conclusion == 0 && !formData.otherRemark">其他结论请具体列明
          </div>
        </el-descriptions-item>
        <el-descriptions-item>
          <upload class="required" :accept='`.JPG,.JPEG,.PNG,.PDF`' :action='`${uploadUrl}/common/uploadFile`' :files="files" multiple
            :fileLimit="10" :single="false" :data="params" @uploadSuccess="uploadSuccess" @fileRemove="fileRemove" text="上传已签章的评估表">
          </upload>
          <span v-if="showError && !formData.fileIds.length" class="error">请上传已签章的评估表</span>
        </el-descriptions-item>
        <el-descriptions-item>
          <div class="fontStyle">备注</div>
          <el-input type="textarea" v-model="formData.remark" placeholder="请输入备注" :rows="3" :maxlength="250"></el-input>
        </el-descriptions-item>
      </el-descriptions>
      <div style="margin-top:25px;">
        <el-button type="primary" style="display:block;margin:0 auto;" @click="submit">提交</el-button>
      </div>
      <div style="padding:20px"></div>
    </div>

  </div>
</template>

<script>
import request from "@/utils/request";
export default {
  name: 'PracticeEvaluationSheet',
  data() {
    return {
      courseTypeOptions: [],
      sexOptions: [],
      majorOptions: [],
      formData: {
        traineesAssessorId: '',
        name: '',
        gender: '',
        major: '',
        majorDirection: '',
        company: '',
        companyPost: '',
        courseFormat: '',
        trainerInfoList: [],
        otherRemark: '',
        remark: '',
        conclusion: '',
        courseStartTime: '',
        courseEndTime: '',
        reviewResultType: 2,
        fileId: '',
        fileIds: [],
        trainerId: '',
        trainerName: '',
      },
      reviewerBaseInfo: {},
      reviewerList: [],
      showError: false,
      timeError: false,
      files: [],
      params: {
        type: "1",
      },
      disabled: false,
    };
  },

  components: {},

  computed: {
    uploadUrl() {
      return localStorage.baseUrl || process.env.VUE_APP_BASE_API;
    },

    gender() {
      return (this.sexOptions.find(({ dictValue }) => dictValue == this.formData.gender) || {}).dictLabel || '-'
    },

    major() {
      let major = (this.formData.major || '').split(',')
      let majorDir = JSON.parse(this.formData.majorDirection || '{}')
      let list = major.map(id => {
        let str = ''
        if (id != 5) {
          str = (this.majorOptions.find(({ dictValue }) => dictValue == id) || {}).dictLabel || '-'
          str += majorDir[id] ? `(${majorDir[id]})` : ''
        } else {
          str += majorDir[id] ? `${majorDir[id]}` : ''
        }
        return str
      })
      return list.join(';')
    }
  },

  mounted() {
    this.accountId = this.$route.path.split('/').pop();
    this.formData.traineesAssessorId = this.accountId;
    this.getDicts('course_form').then(res => {
      this.courseTypeOptions = res.data;
    })

    this.getDicts('sys_user_sex').then(res => {
      this.sexOptions = res.data;
    })

    this.getDicts('reviewer_major').then(res => {
      this.majorOptions = res.data;
    })

    this.getDetail()
    this.getTheoryQuery();
    this.getTeachers();
  },

  methods: {
    timeChange() {
      let { courseStartTime, courseEndTime } = this.formData;
      if (courseStartTime && courseEndTime) {
        let a = new Date(courseStartTime).getTime();
        let b = new Date(courseEndTime).getTime();
        if (a - b > 0) {
          // this.formData.courseStartTime = courseEndTime
          // this.formData.courseEndTime = courseStartTime
          this.timeError = true;
          this.showError = true
        } else {
          this.timeError = false;
        }
      }
    },

    teacherSelect(value) {
      if (Array.isArray(value) && value.length) {
        let last = value.pop();
        let has = this.reviewerList.some(({ userId }) => last == userId)
        if (!has) {
          if (isNaN(last)) {
            value.push(last)
          } else {
            this.$message({
              type: 'warning',
              message: '姓名不能为纯数字'
            })
          }
        } else {
          value.push(last)
        }
      }
    },

    getDetail() {
      request({
        url: "/reviewer/query/reviewer/detail",
        method: "post",
        data: { commonId: this.accountId },
      }).then(res => {
        let { reviewerBaseInfo = {} } = res;
        this.reviewerBaseInfo = reviewerBaseInfo
        this.formData.name = reviewerBaseInfo.reviewerName
        this.formData.gender = reviewerBaseInfo.reviewerGender
        this.formData.major = reviewerBaseInfo.majorField
        this.formData.majorDirection = reviewerBaseInfo.majorDirection
        this.formData.company = reviewerBaseInfo.company
        this.formData.companyPost = reviewerBaseInfo.companyPost
      })
    },

    getTheoryQuery() {
      request({
        url: "/training-evaluate-result/site-review-query",
        method: "post",
        data: { traineesAssessorId: this.accountId, roleName: this.$route.query.roleName, reviewResultType: 2 },
      }).then(res => {
        if (!res.data) return;
        let { fileInfoList = [], ...data } = res.data
        this.files = [...(fileInfoList || [])];
        if (this.files.length) {
          this.formData.fileIds = this.files.map(({ fileId }) => fileId)
        }
        for (const key in data) {
          if (key == 'traineesAssessorId' || key == 'reviewResultType') {
            continue;
          }
          if (Object.hasOwnProperty.call(data, key) && Object.hasOwnProperty.call(this.formData, key)) {
            if (key == 'trainerId') {
              if (data[key]) {
                this.formData.trainerInfoList.push(...(data[key].split(',')))
              }
            } else if (key == 'trainerName') {
              if (data[key]) {
                this.formData.trainerInfoList.push(...(data[key].split(',')))
              }
            } else {
              this.formData[key] = data[key]
            }
          }
        }
      }).catch(() => {
        this.$store.dispatch("tagsView/delView", this.$route);
        this.$router.push({ path: "/auditManagement/reviewerAudit" });
      })
    },

    getTeachers() {
      request({
        url: '/training-evaluate-result/trainer-valid-data-query',
        method: 'get',
        params: {
          roleName: '评审员'
        }
      }).then(res => {
        this.reviewerList = res.data.find(({ roleName }) => roleName == '评审员')?.sysUserList || []
      })
    },

    submit() {
      let fields = ['courseStartTime', 'courseEndTime', 'courseFormat', 'trainerInfoList', 'conclusion', 'otherRemark', 'fileIds']
      let flag = false;
      fields.forEach(field => {
        if ((!this.formData[field] && this.formData[field] !== 0) || this.formData[field].length == 0) {
          if (field == 'otherRemark') {
            if (this.formData.conclusion === 0) {
              flag = true
            }
          } else {
            flag = true
          }
        }
      })
      this.showError = flag;
      if (this.showError) return;

      let { trainerInfoList } = this.formData
      this.formData.trainerId = trainerInfoList.filter(iN => {
        return this.reviewerList.find(({ userId }) => iN == userId)
      }).map(userId => userId).join(',');
      this.formData.fileId = this.formData.fileIds.join(',')
      this.formData.trainerName = trainerInfoList.filter(iN => !this.formData.trainerId.includes(iN)).join(',')
      let data = {
        ...this.formData,
      }

      if (data.conclusion != 0) {
        data.otherRemark = ''
      }

      request({
        url: '/training-evaluate-result/site-review-submit',
        method: 'post',
        data,
      }).then(() => {
        this.$message({
          type: 'success',
          message: '提交成功'
        })
        if (data.fileId) {
          // 管理员上传
          this.shareCreate(data.fileIds, this.$store.getters.userId, 'reviewer_continuous_training');
        }
        if (data.conclusion == 20 || data.conclusion == 21) {
          request({
            url: "/aut/record/info/submitAutRecord",
            method: "post",
            data: {
              accountId: this.accountId,
              role: "101",
              audId: this.$store.getters.userId,
              audResult: '1',
              audDesc: data.conclusion == 20 ? '适合当评审员，可考虑培训为评审组长' : '适合当评审员，不考虑培训为评审组长',
              skipFlag: '1',
            }
          }).finally(() => {
            this.back();
          })
        } else {
          this.back();
        }

      })
    },

    back() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push({ path: "/auditManagement/reviewerAudit" });
    },

    fileRemove(file) {
      let f_id_idx = this.formData.fileIds.indexOf(file.fileId)
      this.formData.fileIds.splice(f_id_idx, 1);

      let f_idx = this.files.findIndex(({fileId}) => fileId == file.fileId);
      this.files.splice(f_idx, 1);

      // let f_idex = this.files
      // this.formData.fileId = "";
    },
    uploadSuccess(response, file, fileList) {
      if (response.code != 200) {
        return this.$message.error(response.msg || response.message);
      }
      let fileId = response.data.fileId;
      this.formData.fileIds.push(fileId)

      let allSuc = fileList.every(file => file.response || file.fileId)
      if (allSuc) {
        this.files.push(...fileList.filter(file => file.response).map(file => file.response.data));
      }
    },
  }
}

</script>

<style lang='scss' scoped>
.sheet {
  width: 85%;
  margin: 0 auto;
  // font-weight: bold;

  ::v-deep .el-descriptions {
    width: 100%;
  }

  .fontStyle {
    font-size: 16px;
    // text-align: right;
    vertical-align: middle;
    // float: left;
    color: #909399;
    line-height: 40px;
    padding: 0 12px 0 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    // font-weight: bold;
  }

  .course_radio {
    display: block;
    // padding: 10px 0;
    line-height: 32px;
    margin-bottom: 5px;
    // ::v-deep .el-radio__label {
    //   // font-weight: bold;
    //   color: black;
    //   font-size: 15px;
    // }
  }

  ::v-deep .el-select {
    width: 100%;
    padding-bottom: 5px;
  }

  .error {
    color: #ff4949;
    margin-left: 10px;
    margin-top: 5px;
  }
  .required::before {
    content: "*";
    color: #ff4949;
    font-size: 16px;
  }
}</style>
