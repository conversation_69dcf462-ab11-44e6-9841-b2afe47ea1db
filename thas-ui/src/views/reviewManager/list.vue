<!--  -->
<template>
  <div class="review-manager">
    <el-form ref="form" :model="queryData" inline label-width="130px">
      <el-form-item label="医院名称：" prop="distributeHospitalName">
        <el-input v-model.trim="queryData.distributeHospitalName" :maxlength="20" placeholder="请输入医院名称"></el-input>
      </el-form-item>
      <el-form-item label="状态：" prop="distributeStatus">
        <el-select v-model.trim="queryData.distributeStatus" placeholder="请选择状态" clearable>
          <el-option v-for="item in progressOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="">
        <el-button type="primary" size="small" @click="query">
          查 询
        </el-button>
        <el-button type="primary" size="small" @click="reset">
          重 置
        </el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataSource" border stripe>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <!-- <el-button type="text" v-if="scope.row.autStatus == AutSaAudCurrentStatusEnum.AUD_REPORT_CONFIRM" @click="reviewFinish(scope.row)">结束流程</el-button>
          <el-button type="text" v-if="scope.row.autStatus == AutSaAudCurrentStatusEnum.AUD_REPORT_CONFIRM" @click="finalCheck(scope.row)">评审报告修改</el-button> -->
          <el-button type="text" v-if="scope.row.reviewReportPdfFileId" @click="previewFile(scope.row)">评审报告预览</el-button>
          <el-button type="text" v-if="scope.row.reviewReportPdfUrl" @click="downloadFile(scope.row)">评审报告下载</el-button>
        </template>
      </el-table-column>
      <el-table-column label="序号" type="index"> </el-table-column>
      <el-table-column label="医院名称" align="center" prop="hospitalName">
      </el-table-column>

      <el-table-column label="医院用户名" align="center" prop="userName">
      </el-table-column>

      <el-table-column label="自评编码" align="center" prop="autCode">
      </el-table-column>

      <el-table-column label="评审标准版本名" align="center" prop="versionName">
      </el-table-column>

      <el-table-column label="更新时间" align="center" prop="updateTime">
      </el-table-column>

      <el-table-column label="状态" align="center" prop="autStatus">
        <template slot-scope="scope">
          <span>{{ AutSaAudCurrentStatusEnum[scope.row.autStatus] }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query" />
  </div>
</template>

<script>
import request, { download, downloadPDF } from "@/utils/request";

export default {
  name: "ReviewManagerList",
  components: {},
  props: {},
  watch: {},
  data() {
    return {
      queryData: {
        distributeHospitalName: "",
        distributeStatus: "",
        pageSize: 10,
        pageNum: 1,
      },
      url: {
        list: "/aut/sa/relation/list",
        // list: '/aut/sa/aud/queryList'
      },
      total: 0,
      dataSource: [],
    };
  },

  computed: {
    progressOptions() {
      return this.AutSaAudCurrentStatusEnum.getAutSaAudStatusOptions(true);
    },
    showTurnToBtn() {
      return (row) =>
        Number(row.autStatus) == this.AutSaAudCurrentStatusEnum.CONFIRM_REVIEW_FACTUAL;
    },

    showTurnToBtn2() {
      return (row) =>
        Number(row.autStatus) > this.AutSaAudCurrentStatusEnum.CONFIRM_REVIEW_FACTUAL;
    },

    showReportBtn() {
      return (row) => false;
    },
  },

  created() { },

  mounted() {
    this.query();
  },

  methods: {
    self(data) {
      downloadPDF({
        filename: `${data.hospitalName}-自评报告.pdf`,
        autCode: data.autCode,
        ftlTemplateCode: 'aud_sa_report',
        checkStatus: "N"
      });
    },

    hos(data) {
      downloadPDF({
        filename: `${data.hospitalName}-事实准确性查询表.pdf`,
        autCode: data.autCode,
        ftlTemplateCode: 'far_report_hos',
        checkStatus: "N"
      });
    },

    leader(data) {
      downloadPDF({
        filename: `${data.hospitalName}-事实准确性确认表.pdf`,
        autCode: data.autCode,
        ftlTemplateCode: 'far_report_reviewer',
        checkStatus: "N"
      });
    },

    inter(data) {
      downloadPDF({
        filename: `${data.hospitalName}-事实准确性确认表.pdf`,
        autCode: data.autCode,
        ftlTemplateCode: 'review_interest',
        checkStatus: "N",
        accountId: 321,
        hospitalName: data.hospitalName
      });
    },


    query() {
      request({
        url:
          this.url.list +
          `?pageSize=${this.queryData.pageSize}&pageNum=${this.queryData.pageNum}`,
        method: "post",
        data: this.queryData,
      }).then((res) => {
        this.dataSource = res.rows;
        this.total = res.total;
      }).catch(() => {
        this.dataSource = []
        this.total = 0
      });
    },
    reset() {
      this.$refs["form"].resetFields();
      this.query();
    },
    turnToLeader({ autCode }) {
      this.$router.push({
        path: '/reviewManagerDetail',
        query: {
          autCode
        }
      })
    },

    reviewFinish({ autCode }) {
      this.$confirm('此操作将结束当前评审流程，是否结束?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        request({
          url: "/aut/sa/aud/submitAutSaAud",
          method: "post",
          data: {
            submitType: this.AutSaAudCurrentStatusEnum.SR_E_REPORT_CONFIRM,
            autCode: autCode,
            accountId: this.$store.getters.userId + "",
            autSaAudLists: [
              {
                autResult: "1",
                autDesc: "--",
              },
            ],
          },
        }).then(() => {
          this.query();
        })
      }).catch(() => {

      });

    },

    finalCheck({ autCode }) {
      this.$confirm('是否将流程转至审查组长?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        request({
          url: "/aut/sa/aud/submitAutSaAud",
          method: "post",
          data: {
            submitType: this.AutSaAudCurrentStatusEnum.SR_E_REPORT_CONFIRM,
            autCode: autCode,
            accountId: this.$store.getters.userId + "",
            autSaAudLists: [
              {
                autResult: "2",
                autDesc: "--",
              },
            ],
          },
        }).then(() => {
          this.query();
        })
      }).catch(() => {

      });

    },

    previewFile({ reviewReportPdfUrl }) {
      window.open(reviewReportPdfUrl, '_blank')
    },
    downloadFile({ reviewReportPdfFileId, reviewReportPdfFileName }) {
      download("/common/downloadFile", { fileId: reviewReportPdfFileId }, reviewReportPdfFileName);

    },

    showReport({ autSaAudReport }) {
      if (!autSaAudReport) return this.$message({
        type: 'warning',
        message: '当前评审暂无报告'
      })
      let name =
        autSaAudReport.autSaAudResult == 1
          ? "BaseClausePass"
          : "BaseClauseReject";
      this.$router.push({
        name,
        params: {
          autSaAudReport,
        },
      });
    },
  },
};
</script>
<style lang='scss' scoped>
.review-manager {
  padding: 12px;
  background-color: #fff;
  min-height: inherit;
}
</style>
