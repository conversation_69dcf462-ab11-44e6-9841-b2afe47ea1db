<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.TraQuestionnaireAnswerMapper">

    <resultMap type="TraQuestionnaireAnswer" id="TraQuestionnaireAnswerResult">
        <result property="id" column="id"/>
        <result property="questionnaireId" column="questionnaire_id"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTraQuestionnaireAnswerVo">
        select id, questionnaire_id, status, create_by, create_time, update_by, update_time
        from tra_questionnaire_answer
    </sql>

    <select id="selectTraQuestionnaireAnswerList" parameterType="TraQuestionnaireAnswer"
            resultMap="TraQuestionnaireAnswerResult">
        <include refid="selectTraQuestionnaireAnswerVo"/>
        <where>
            <if test="questionnaireId != null ">and questionnaire_id = #{questionnaireId}</if>
            <if test="status != null ">and status = #{status}</if>
        </where>
    </select>

    <resultMap type="TraQuestionnaireAnswerVO" id="TraQuestionnaireAnswerDetailsResult">
        <result property="id" column="id"/>
        <result property="status" column="status"/>
        <result property="createBy" column="createBy"/>
        <result property="createTime" column="createTime"/>
        <collection property="data" ofType="com.thas.web.domain.vo.TraQuestionnaireAnswerDetailsVO">
            <result property="question" column="question"/>
            <result property="questionOptions" column="questionOptions"/>
            <result property="type" column="type"/>
            <result property="hasRequired" column="hasRequired"/>
            <result property="answer" column="answer"/>
        </collection>
    </resultMap>

    <select id="selectTraQuestionnaireAnswerById" parameterType="Long" resultMap="TraQuestionnaireAnswerDetailsResult">
        select tqa.id id,
        tqa.create_by createBy,
        tqa.create_time createTime,
        tqa.status status,
        tqd.question question,
        tqd.question_options questionOptions,
        tqd.type type,
        tqd.has_required hasRequired,
        tqad.answer answer
        from tra_questionnaire_answer tqa
        LEFT JOIN tra_questionnaire tq ON tq.id = tqa.questionnaire_id
        LEFT JOIN tra_questionnaire_details tqd on tqd.questionnaire_id = tq.id
        LEFT JOIN tra_questionnaire_answer_details tqad on tqad.questionnaire_details_id = tqd.id
        WHERE 1 = 1
        <if test="questionnaireId != null and questionnaireId != ''">and tqa.questionnaire_id = #{questionnaireId}</if>
        <if test="answerId != null and answerId != ''">and tqa.id = #{answerId}</if>
        <if test="createId != null and createId != ''">and tqa.create_id = #{createId,jdbcType=VARCHAR}</if>
        order by tqd.id
    </select>

    <select id="selectCountByQuestionnaireId" resultType="java.lang.Integer">
        select count(1)
        from tra_questionnaire_answer
        where questionnaire_id = #{questionnaireId}
    </select>

    <insert id="insertTraQuestionnaireAnswer" parameterType="TraQuestionnaireAnswer">
        insert into tra_questionnaire_answer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="questionnaireId != null">questionnaire_id,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="questionnaireId != null">#{questionnaireId},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateTraQuestionnaireAnswer" parameterType="TraQuestionnaireAnswer">
        update tra_questionnaire_answer
        <trim prefix="SET" suffixOverrides=",">
            <if test="questionnaireId != null">questionnaire_id = #{questionnaireId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateStatus">
        update tra_questionnaire_answer
        set status = #{status}
        where id = #{id}
    </update>

    <delete id="deleteTraQuestionnaireAnswerById" parameterType="Long">
        delete
        from tra_questionnaire_answer
        where id = #{id}
    </delete>

    <delete id="deleteTraQuestionnaireAnswerByIds" parameterType="String">
        delete from tra_questionnaire_answer where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="delete">
        delete from tra_questionnaire_answer where id = #{id}
        <if test="createId != null and createId != ''">and create_id = #{createId,jdbcType=VARCHAR}</if>
    </delete>
</mapper>