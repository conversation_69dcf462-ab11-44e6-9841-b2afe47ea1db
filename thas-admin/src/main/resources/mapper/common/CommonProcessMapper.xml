<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.CommonProcessMapper">

    <select id="checkSubmitPermission" parameterType="string" resultType="int">
        select case item_arr1
            when 'hospital' then
                (select count(1) from sys_user_hospital where user_id = #{userId} and hospital_apply_no = #{applyNo})
            when 'inspector' then
                (select count(1) from  hospital_pre_exam t,hospital_planned_distribution tt where t.apply_no = #{applyNo} and t.pre_exam_id = #{userId} and t.status = 1 and t.apply_no = tt.apply_no and tt.status = 1 and tt.pre_dis_complete = 1)
            when 'assessor' then
                (select count(1) from  hospital_reviewer t,hospital_planned_distribution tt where t.apply_no = #{applyNo} and t.reviewer_id = #{userId} and t.field_id_list != 'SENIOR_REVIEW' and t.status = 1 and t.apply_no = tt.apply_no and tt.status = 1 and (tt.review_dis_complete = 1 or tt.review_dis_complete = 4))
            when 'senior-assessor' then
                (select count(1) from  hospital_reviewer t,hospital_planned_distribution tt where t.apply_no = #{applyNo} and t.reviewer_id = #{userId} and t.field_id_list = 'SENIOR_REVIEW' and t.status = 1 and t.apply_no = tt.apply_no and tt.status = 1 and tt.senior_review_dis_complete = 1)
            else 0
            end
         from asa_business_config where item_type = 'status_prefix_to_role_key' and item_key = #{statusPrefix}
    </select>

    <select id="checkPdfGeneratePermission" parameterType="string" resultType="int">
        select case #{roleKey}
            when 'hospital' then
                (select count(1) from sys_user_hospital where user_id = #{userId} and hospital_apply_no = #{applyNo})
            when 'inspector' then
                (select count(1) from hospital_pre_exam where apply_no = #{applyNo} and pre_exam_id = #{userId} and status = 1)
            when 'assessor' then
                (select count(1) from hospital_reviewer where apply_no = #{applyNo} and reviewer_id = #{userId} and field_id_list != 'SENIOR_REVIEW' and status = 1)
            when 'senior-assessor' then
                (select count(1) from  hospital_reviewer where apply_no = #{applyNo} and reviewer_id = #{userId} and field_id_list = 'SENIOR_REVIEW' and status = 1)
            else 0
            end
        from dual
    </select>

    <select id="selectCountByType" parameterType="string" resultType="int">
        select count(1) from aut_sa_aud where aut_code = #{autCode} and submit_type = #{submitType} and status = 1
    </select>


    <select id="selectAutSaAudStatusConfig" parameterType="string"
            resultType="com.thas.web.domain.AutSaAudStatusConfig">
        select id,
               current_status     currentStatus,
               status_desc        statusDesc,
               next_status_config nextStatusConfig,
               service_name       serviceName,
               process_method     processMethod,
               check_method       checkMethod,
               submit_type        submitType,
               cycle_stage        cycleStage,
               create_id          createId,
               create_time        createTime,
               update_id          updateId,
               update_time        updateTime
        from asa_status_config
        where current_status = #{curStatus}
    </select>

    <select id="selectAutSaAudBusinessConfig" parameterType="string"
            resultType="com.thas.web.domain.AutSaAudBusinessConfig">
        select id,
               item_type   itemType,
               item_key    itemKey,
               item_desc   itemDesc,
               item_arr1   itemArr1,
               item_arr2   itemArr2,
               create_id   createId,
               create_time createTime,
               update_id   updateId,
               update_time updateTime
        from asa_business_config
        where item_type = #{itemType}
          and item_key = #{itemKey}
    </select>




</mapper>
