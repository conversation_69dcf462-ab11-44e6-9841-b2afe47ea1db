<template>
  <div class="inspector-evaluation">
    <el-form ref="form" :model="queryData" inline label-width="130px">
      <el-form-item label="医院名称：" prop="distributeHospitalName">
        <el-input v-model.trim="queryData.distributeHospitalName" :maxlength="20" placeholder="请输入医院名称"></el-input>
      </el-form-item>
      <!-- <el-form-item label="状态：" prop="distributeStatus">
        <el-select v-model.trim="queryData.distributeStatus" placeholder="请选择状态" clearable>
          <el-option v-for="item in progressOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue">
          </el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="">
        <el-button type="primary" size="small" @click="query">
          查 询
        </el-button>
        <el-button type="primary" size="small" @click="reset"> 重 置 </el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataSource" border stripe v-loading="loading">
      <el-table-column label="操作" align="center" width="160">
        <template slot-scope="scope">
          <div class="table-btn">
            <el-button v-if="showFinally2(scope.row)" type="text" @click="finalCheck(scope.row)">最终修改</el-button>
            <el-button v-if="showFinally3(scope.row)" type="text" :disabled="uploading" @click="uploadMeetingFile(scope.row)">上传会议纪要</el-button>

            <template v-if="showVerifyReport(scope.row)">
              <el-button type="text" @click="previewVerifyFile(scope.row)">验证报告预览</el-button>
              <el-button type="text" @click="downloadVerifyReport(scope.row)">验证报告下载</el-button>
            </template>

            <el-button type="text" v-if="scope.row.reviewReportPdfFileId"
              @click="previewFile(scope.row)">评审报告预览</el-button>
            <el-button v-if="scope.row.reviewReportPdfFileId" type="text"
              @click="downloadReport(scope.row)">评审报告下载</el-button>

          </div>
        </template>
      </el-table-column>
      <el-table-column label="医院名称" align="center" prop="hospitalName">
      </el-table-column>
      <!--  -->
      <el-table-column label="自评完成时间" align="center" prop="saAudSubmitTime">
      </el-table-column>
      <!-- <el-table-column v-if="preliminary" label="待组长审核" align="center" prop="waitLeaderAudNum">
      </el-table-column> -->
      <el-table-column v-if="preliminary" label="组长" align="center" prop="leaderName">
      </el-table-column>
      <!--  -->
      <el-table-column v-if="!preliminary" label="审查员" align="center" prop="memberName">
      </el-table-column>
      <!-- <el-table-column v-if="!preliminary" label="待复查" align="center" prop="waitLeaderAudNum">
      </el-table-column> -->
      <!--  -->
      <el-table-column label="审查完成时间" align="center" prop="audSubmitTime">
      </el-table-column>
      <el-table-column label="审查周期" align="center" prop="audCycle">
        <template slot-scope="scope">
          {{ scope.row.audCycle.replace(',', ' ~ ') || "--" }}
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="hospitalName">
        <template slot-scope="scope">
          {{ AutSaAudCurrentStatusEnum[scope.row.status] }}
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize"
      @pagination="query" />

    <dict-span :dictType="processDictType" value="0" v-show="false"></dict-span>

    <upload style="display: none;" ref="managerUpload" accept=".pdf" :files="meetingFiles"
      :data="{ type: '1' }" @fileAdd="fileAdd" @uploadSuccess="onSuccess" @fileRemove="onError" @uploadError="onError"></upload>
  </div>
</template>

<script>
import DictSpan from "@/components/DetailMessage/dictSpan.vue";
import store from "@/store";
import request, { download } from "@/utils/request";

export default {
  name: "inspectorEvaluationFinalList",
  data() {
    return {
      queryData: {
        distributeHospitalName: "",
        distributeStatus: "020401,020402,050202,020403",
        pageSize: 10,
        pageNum: 1,
      },
      url: {
        list: "/aut/sa/aud/queryList",
      },
      total: 0,
      dataSource: [],
      preliminary: true,
      groupDialogVisible: false,
      groupDataSource: [],
      groupForm: {},
      groupTitle: "小组进度",
      loading: false,
      // 上传会议纪要
      meetingFiles: [],
      uploading: false,
      wait4UploadProcess: null,
    };
  },

  components: {
    DictSpan,
  },

  computed: {
    processDictType() {
      return this.preliminary
        ? "inspector_first_progress_status"
        : "inspector_second_progress_status";
    },
    showDetailBtn() {
      return (row) =>
        row.autCode &&
        row.status >= this.AutSaAudCurrentStatusEnum.WAIT_AUD_FIRST_TRIAL;
    },

    showExamineBtn() {
      return (row) => {
        let statuses = [
          this.AutSaAudCurrentStatusEnum.WAIT_AUD_FIRST_TRIAL,
          this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_INQUIRE_PROC,
          this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_REVIEW_REJECT
        ];
        return row.autCode && statuses.includes(row.status) && row.waitAudNum;
      };
    },
    showSDetailBtn() {
      return (row) => {
        let statuses = [
          this.AutSaAudCurrentStatusEnum.WAIT_CONFIRM_FIRST_TRIAL,
        ];
        return row.autCode && statuses.includes(row.status);
      };
    },

    progressOptions() {
      return this.AutSaAudCurrentStatusEnum.getStatus()
    },
    showFinally() {
      return ({ status }) => status === this.AutSaAudCurrentStatusEnum.WAIT_AUD_FIRST_TRIAL_SUM
    },
    showFinally2() {
      return ({ status }) => status === this.AutSaAudCurrentStatusEnum.WAIT_REPORT || status === this.AutSaAudCurrentStatusEnum.REPORT_FINISH
    },
    showFinally3() {
      return ({ status, meetReportFileId }) => {
        return status === this.AutSaAudCurrentStatusEnum.UPLOAD_MEETING_RECORD_FILE && !meetReportFileId
      }
    },


    showVerifyReport() {
      return row => {
        let fileMap = row.fileDetailMap || {};
        let fileMapList = Object.values(fileMap);
        let files = fileMapList.find(files => {
          let file = files.find(file => {
            return file.fileType === 'verify_review_report'
          })
          return file
        })
        let file = (files || [])[0];
        return file;
      }
    },
  },

  mounted() {
    this.preliminary = this.$route.query.preliminary != 0;
    this.groupTitle = this.preliminary ? "小组进度" : "小组管理";
    this.initDicts();
    this.query();
  },

  methods: {
    initDicts() { },

    reset() {
      this.$refs['form'].resetFields();
      this.query();
    },

    query() {
      this.loading = true;
      let data = {
        accountId: store.getters.userId + "",
        ...this.queryData,
        // type: this.preliminary ? 2 : 3,
        pageType: 'fr_v_clause'
      };
      request({
        url: this.url.list,
        method: "post",
        data,
      }).then((res) => {
        if (res.code == 200) {
          let fields = [
            "hospitalName",
            "waitAudNum",
            "waitLeaderAudNum",
            "leaderName",
            "memberName",
            "waitLeaderAudNum",
            "saAudSubmitTime",
            "audSubmitTime",
            "audCycle",
          ];
          this.dataSource = res.rows.map((row) => {
            for (const key in row) {
              if (Object.hasOwnProperty.call(row, key)) {
                if (!row[key] && row[key] != 0 && fields.includes(key)) {
                  row[key] = '--'
                }

              }
            }
            return row;
          });
        }
      }).catch(() => {
        this.dataSource = []
      }).finally(() => {
        this.loading = false;
      });
    },
    toShowHospital(autCode) {
      this.$router.push({
        path: "/fInspector/fInspectorIndex",
        query: {
          readOnly: true,
          isLeader: 0,
          showOnlyHospital: true,
          autCode,
          type: this.AutSaAudSubmitTypeEnum.TERM_SA_SUM,
          type: this.preliminary ? 2 : 3,
        },
      });
    },


    downloadReport({ reviewReportPdfFileId, reviewReportPdfFileName }) {
      download("/common/downloadFile", { fileId: reviewReportPdfFileId }, reviewReportPdfFileName);
    },

    previewFile({ reviewReportPdfUrl }) {
      window.open(reviewReportPdfUrl, '_blank')
    },

    previewVerifyFile(row) {
      let file = this.showVerifyReport(row);
      window.open(file.url, '_blank')
    },

    downloadVerifyReport(row) {
      let file = this.showVerifyReport(row);
      download("/common/downloadFile", { fileId: file.fileId },  file.downLoadFileName);
    },

    toCheck(autCode, clauseIds, versionId, isLeader) {
      this.$router.push({
        path: "/fInspector/fInspectorIndex",
        query: {
          autCode,
          type: 'fr_clause'
        },
      });
    },

    toCheck2(autCode, clauseIds, versionId, isLeader) {
      this.$router.push({
        path: "/fInspector/sInspectorIndex",
        query: {
          autCode,
          type: 'fr_v_clause'
        },
      });
    },

    showGroup(row) {
      if (row) {
        this.$set(this, "groupDataSource", row.groupProgressList);
        this.$set(this, "groupForm", {
          hospitalName: row.hospitalName,
          audSubmitTime: row.audSubmitTime,
        });
      }
      this.groupDialogVisible = true;
    },

    finalCheck({ autCode }) {
      this.$router.push({
        path: "/fInspector/lInspectorIndex",
        query: {
          autCode,
          type: 'fr_v_clause'
        },
      });
    },
    uploadMeetingFile(row) {
      this.wait4UploadProcess = row;
      this.$refs.managerUpload.$el.getElementsByClassName('upload-content')[0].click();
    },

    fileAdd() {
      this.uploading = true;
    },

    onSuccess(res) {
      if (this.wait4UploadProcess) {
        this.uploading = false;
        let fileId = res.data.fileId;
        // 审查员上传
        this.shareCreate([fileId], store.getters.userId, 'reviewer_evaluation_party')

        if (fileId) {
          request({
            url: "/aut/sa/aud/submitAutSaAud",
            method: "post",
            data: {
              submitType: this.AutSaAudCurrentStatusEnum.SR_REPORT_M_MEET,
              autCode: this.wait4UploadProcess.autCode,
              accountId: store.getters.userId + "",
              autSaAudBusinessData: {
                autCode: this.wait4UploadProcess.autCode,
                businessCode: 'MEET_REPORT',
                data: fileId
              }
            },
          }).then(() => {
            this.query();
          })
        }
      }
    },

    onError() {
      this.uploading = false;
      this.wait4UploadProcess = null;
    },
  },
};
</script>
<style lang="scss" scoped>
.inspector-evaluation {
  padding: 12px;
  background-color: #fff;
  min-height: inherit;
}
::v-deep .table-btn {
  .el-button+.el-button {
    display: block;
    margin: 0 auto;
  }
}
</style>
