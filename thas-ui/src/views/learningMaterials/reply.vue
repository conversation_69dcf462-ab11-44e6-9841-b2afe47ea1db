<template>
  <el-card v-loading="loading" class="learning-materials-list">
    <el-row :gutter="24">
      <el-col :span="24" :xs="24">
        <el-form ref="queryForm" :model="queryData" size="small" label-width="80px" inline>
          <el-form-item label="留言用户" prop="createId">
            <el-input v-model.trim="queryData.createId" maxlength="64" placeholder="请输入用户名" clearable :style="{ width: '100%' }">
            </el-input>
          </el-form-item>
          <el-form-item label="留言来源" prop="learnResourceName">
            <el-input v-model.trim="queryData.learnResourceName" maxlength="128" placeholder="请输入来源" clearable :style="{ width: '100%' }">
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="search"> 查询 </el-button>
            <el-button type="primary" size="small" @click="resetForm"> 重 置 </el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-row :gutter="24" style="margin-top: 24px">
      <el-col :span="24">
        <el-table :data="tableData" border>
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="deleteReply(scope.row)">删除</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" label="留言内容" prop="messageContent"></el-table-column>
          <el-table-column align="center" label="留言来源" prop="title"></el-table-column>
          <el-table-column align="center" label="留言用户" prop="createId"></el-table-column>
          <el-table-column align="center" label="点赞数" prop="likeNum"></el-table-column>
          <!-- <el-table-column align="center" label="回复数" prop="replyNum"></el-table-column> -->
          <el-table-column align="center" label="留言时间" prop="createTime"></el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query" />
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
import request from "@/utils/request"
export default {
  name: "LearningMaterialsList",
  components: {
    // MarterialsForm,
  },
  data() {
    return {
      tableData: [],
      queryData: {
        createId: "",
        learnResourceName: "",
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false
    };
  },
  created() {
    this.query()
  },
  mounted() { },
  methods: {
    deleteReply(arg) {
      this.$confirm('此操作将永久删除该留言, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        request({
          url: '/system/message/' + arg.id,
          method: "delete",
        }).then((res) => {
          if (res.code != 200) return;
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.search()
        })
      }).catch(() => { })
    },
    search() {
      this.queryData.pageNum = 1
      this.query()
    },
    query() {
      this.loading = true
      request({
        url: '/system/message/list',
        method: "get",
        params: this.queryData,
      }).then((res) => {
        this.loading = false
        if (res.code != 200) return;
        this.tableData = res.rows;
        this.total = res.total;
      }).catch((error) => {
        // console.log(error)
        this.loading = false
      })
    },
    resetForm() {
      this.$refs["queryForm"].resetFields();
      this.query();
    }
  }
}
</script>

<style lang='scss' scoped>
.learning-materials-list {
  padding: 24px;
  ::v-deep .el-dialog {
    .el-dialog__header {
      background-color: #3c81f5;
      padding-top: 10px;
      .el-dialog__title {
        color: white;
      }
      .el-dialog__headerbtn {
        top: 15px;
        .el-dialog__close {
          color: white;
        }
      }
    }
    .el-dialog__body {
      padding-bottom: 0;
      & > span {
        color: black;
      }
    }
  }
}
</style>