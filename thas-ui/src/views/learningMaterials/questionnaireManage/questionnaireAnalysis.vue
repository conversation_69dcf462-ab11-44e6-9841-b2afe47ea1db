<template>
  <el-card class="questionnaire-analysis">
    <div class="top">
      <el-select v-model.trim="value" placeholder="请选择" popper-class="questionnaire-popper" @change="handleChange" clearable filterable>
        <el-option v-for="item in options" :key="item.value" :label="item.title" :value="item.id">
        </el-option>
      </el-select>
      <el-button @click="back">返回</el-button>
    </div>
    <div class="content">
      <div v-for="(item, index) in questionnaireList" :key="index">
        <div v-if="item.type == 1" class="item">
          <div class="title">
            <span class="questionnaire">{{ item.question }}</span>
            <span class="questionnaire-type">[单选题]</span>
          </div>
          <div>
            <el-table :data="item.options" border>
              <el-table-column align="center" prop="option" label="选项">
              </el-table-column>
              <el-table-column align="center" prop="subtotal" label="小计" width="120">
              </el-table-column>
              <el-table-column align="center" prop="proportion" label="比例">
                <template v-if="scope.row.proportion || scope.row.proportion == 0" slot-scope="scope">
                  <el-progress :percentage="scope.row.proportion"></el-progress>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div v-else-if="item.type == 2" class="item">
          <div class="title">
            <span class="questionnaire">{{ item.question }}</span>
            <span class="questionnaire-type">[多选题]</span>
          </div>
          <div>
            <el-table :data="item.options" border>
              <el-table-column align="center" prop="option" label="选项"></el-table-column>
              <el-table-column align="center" prop="subtotal" label="小计" width="120"></el-table-column>
              <el-table-column align="center" prop="proportion" label="比例">
                <template v-if="scope.row.proportion || scope.row.proportion == 0" slot-scope="scope">
                  <el-progress :percentage="scope.row.proportion"></el-progress>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <!-- <div v-else-if="item.type == 3 || item.type == 4" class="item">
          <div class="title">
            <span class="questionnaire">{{ item.question }}</span>
            <span class="questionnaire-type">[填空题]</span>
          </div>
          <div>
            <el-button plain>查看详细信息</el-button>
          </div>
        </div> -->
      </div>
    </div>
  </el-card>
</template>

<script>
import request from "@/utils/request"
export default {
  name: 'questionnaireAnalysis',
  data() {
    return {
      options: [
        {
          value: '调查问卷标题1',
          label: '调查问卷标题1'
        },
        {
          value: '调查问卷标题2',
          label: '调查问卷标题2'
        },
        {
          value: '调查问卷标题3',
          label: '调查问卷标题3'
        },
        {
          value: '调查问卷标题4',
          label: '调查问卷标题4'
        }
      ],
      value: '',
      tableData: [
        {
          option: '培训类别1',
          subtotal: '365',
          proportion: 6.08
        },
        {
          option: '培训类别2',
          subtotal: '206',
          proportion: 3.43
        },
        {
          option: '本题有效填写人次',
          subtotal: '206'
        }
      ],
      questionnaireList: []
    }
  },
  created() {
    this.getQuestionnaireList()
    this.value = +this.$route.query.id || ''
  },
  methods: {
    getQuestionnaireList() {
      request({
        url: '/system/questionnaire/list',
        method: 'get'
      }).then((res) => {
        if (res.code != 200) return
        this.options = res.rows
        this.query()
      })
    },
    query(id) {
      let _id = id || this.$route.query.id
      request({
        url: '/system/questionnaire/' + _id,
        method: 'get'
      }).then((res) => {
        if (res.code != 200) return
        for (let index = 0; index < res.data.data.length; index++) {
          if (res.data.data[index].type == 1 || res.data.data[index].type == 2) {
            res.data.data[index].options = []
            let _options = JSON.parse(res.data.data[index].questionOptions)
            let subtotal = 0
            for (const key in res.data.data[index].answerCountMap) {
              subtotal += res.data.data[index].answerCountMap[key]
            }
            for (let i = 0; i < _options.length; i++) {
              if (res.data.data[index].answerCountMap) {
                res.data.data[index].options.push({
                  option: _options[i].label,
                  subtotal: Object.keys(res.data.data[index].answerCountMap).includes(_options[i].label) ? res.data.data[index].answerCountMap[_options[i].label] : 0,
                  proportion: Object.keys(res.data.data[index].answerCountMap).includes(_options[i].label) ? (Math.round(res.data.data[index].answerCountMap[_options[i].label] / subtotal * 10000) / 100.00) : 0
                })
              }
            }
            res.data.data[index].options.push({
              option: '本题有效填写人次',
              subtotal
            })
          }
        }
        this.questionnaireList = res.data.data
      })
    },
    handleChange(value) {
      this.query(value)
    },
    back() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push({ path: "/questionnaireManage/questionnaireList" });
    }
  }
}
</script>

<style lang="scss" scoped>
.questionnaire-analysis {
  margin-bottom: 10px;
  .top {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #e0e1e2;
    padding-bottom: 10px;
  }
  .content {
    .item {
      padding: 32px 0;
      .title {
        margin-bottom: 16px;
        font-weight: bold;
        .questionnaire {
          font-size: 16px;
          color: #000;
          word-break: break-all;
        }
        .questionnaire-type {
          margin-left: 10px;
          color: #666666;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.questionnaire-popper {
  max-width: 400px !important;
  .el-select-dropdown__item {
    span {
      text-overflow: ellipsis;
    }
  }
  .el-scrollbar__bar {
    opacity: 1;
  }
}
</style>
