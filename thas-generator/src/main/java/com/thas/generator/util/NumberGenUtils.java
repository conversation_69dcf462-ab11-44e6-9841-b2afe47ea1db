package com.thas.generator.util;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.thas.common.utils.uuid.IdUtils;

import java.io.File;
import java.security.SecureRandom;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Random;

/**
 * 编码生成器
 */
public class NumberGenUtils {

    /**
     * 医疗结构生成对应的applyNo
     * 生成规则年月日时分秒+uuid后四位
     */
    public static String hospitalApplyNoGen() {
        StringBuilder res = new StringBuilder();
        LocalDateTime now = LocalDateTime.now();
        String nowStr = DateUtil.format(now, DatePattern.PURE_DATETIME_PATTERN);
        res.append(nowStr);
        String uuid = IdUtils.simpleUUID();
        res.append(uuid, 0, 6);
        return res.toString();
    }

    public static String genFilePath() {
        return genFilePath(null);
    }

    /**
     * 文件路径生成
     *
     * @param pre 前缀
     * @return 文件路径
     */
    public static String genFilePath(String pre) {
        StringBuilder res = new StringBuilder();
        if (StrUtil.isNotEmpty(pre)) {
            if (!pre.startsWith("/")) {
                res.append(File.separator);
            }
            res.append(pre);
            if (!pre.endsWith("/")) {
                res.append("/");
            }
        } else {
            res.append("/");
        }
        LocalDateTime now = LocalDateTime.now();
        String nowStr = DateUtil.format(now, DatePattern.PURE_DATE_PATTERN);
        res.append(nowStr);
        res.append("/");
        return res.toString();
    }

    /**
     * 生成文件名
     *
     * @param suffix 文件后缀
     * @return 文件名
     */
    public static String genFileName(String suffix) {
        StringBuilder res = new StringBuilder();
        String uuid = IdUtils.simpleUUID();
        res.append(uuid, 24, 32);
        LocalDateTime now = LocalDateTime.now();
        String nowStr = DateUtil.format(now, DatePattern.PURE_TIME_PATTERN);
        res.append(nowStr);
        if (!suffix.startsWith(".")) {
            res.append(".");
        }
        res.append(suffix);
        return res.toString();
    }


    /**
     * 生成唯一id
     */
    public static String genParentId(){
        StringBuilder res = new StringBuilder();
        String u1 = IdUtils.simpleUUID();
        LocalDateTime now = LocalDateTime.now();
        String nowStr = DateUtil.format(now, DatePattern.PURE_TIME_PATTERN);
        res.append(nowStr);
        res.append(u1, 24, 32);
        return res.toString();
    }


    public static void main(String[] args) {
        String s = genFileName("txt");
        System.out.println(s);
    }

    /**
     * 获取验证码
     *
     * @return 验证码
     */
    public static String getVerificationCode() {
        final Random random = new SecureRandom();
        return String.valueOf(random.nextInt(899999) + 100000);
    }

}
