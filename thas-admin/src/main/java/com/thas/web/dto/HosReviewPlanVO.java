package com.thas.web.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.thas.web.domain.AutSaRelationList;
import com.thas.web.domain.HospitalReviewCycle;
import com.thas.web.domain.vo.FileInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 医疗机构评审计划列表详情实体
 */

@Data
@ApiModel(value = "医疗机构评审计划列表详情")
public class HosReviewPlanVO {

    @ApiModelProperty(value = "医疗机构认证编号")
    private String applyNo;

    @ApiModelProperty(value = "医疗机构名称")
    private String hospitalName;

    @ApiModelProperty(value = "初审人员列表")
    private List<HosPlanUserInfoVO> preExamList;

    @ApiModelProperty(value = "评审人员列表")
    private List<HosPlanUserInfoVO> reviewerList;

    @ApiModelProperty(value = "可选择资深评审员列表")
    private List<HosPlanUserInfoVO> seniorReviewerList;

    @ApiModelProperty(value = "资深评审员")
    private List<HosPlanUserInfoVO> seniorReviewerUser;

    @ApiModelProperty(value = "评审学员")
    private List<HosPlanUserInfoVO> traineesList;

    @ApiModelProperty(value = "评审时间周期")
    private List<HospitalReviewCycle> hospitalReviewCycleList;

    @ApiModelProperty(value = "操作人")
    private String creator;

    @ApiModelProperty(value = "分配计划审核状态0待审核 1计划通过 2被拒绝")
    private Integer hosStatus;

    @ApiModelProperty(value = "分配计划时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String planTime;

    @ApiModelProperty(value = "确认计划时间")
    private String confirmTime;

    @ApiModelProperty(value = "初审员是否分配完成 1完成 2未完成")
    private String preDisComplete;

    @ApiModelProperty(value = "评审员是否分配完成 1完成 2未完成")
    private String reviewDisComplete;

    @ApiModelProperty(value = "资深评审员是否分配完成 1完成 2未完成")
    private String seniorReviewDisComplete;

    @ApiModelProperty(value = "评审周期是否分配完成 1完成 2未完成")
    private String cycleDisComplete;

    @ApiModelProperty(value = "关联评估标准版本号")
    private String versionId;

    @ApiModelProperty(value = "医疗机构审核评审计划PDF文件id")
    private String revFileId;

    @ApiModelProperty(value = "评审周期状态")
    private Integer cycleStatus;

    @ApiModelProperty(value = "文件id对应文件详情")
    private Map<String, List<FileInfoVO>> fileDetailMap;

    @ApiModelProperty(value = "审核状态")
    private String anuStatus;

    @ApiModelProperty(value = "自评编码")
    private String autCode;

    private List<AutSaRelationList> autSaRelationList;
}
