package com.thas.web.controller.common;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.thas.common.utils.poi.ExcelUtil;
import com.thas.web.domain.CertificateAbility;
import com.thas.web.service.ICertificateAbilityService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.enums.BusinessType;
import com.thas.common.core.page.TableDataInfo;

/**
 * 对应证书关联Controller
 * 
 * <AUTHOR>
 * @date 2022-01-19
 */
@RestController
@RequestMapping("/system/ability")
public class CertificateAbilityController extends BaseController
{
    @Autowired
    private ICertificateAbilityService certificateAbilityService;

    /**
     * 查询对应证书关联列表
     */
    @PreAuthorize("@ss.hasPermi('system:ability:list')")
    @GetMapping("/list")
    public TableDataInfo list(CertificateAbility certificateAbility)
    {
        startPage();
        List<CertificateAbility> list = certificateAbilityService.selectCertificateAbilityList(certificateAbility);
        return getDataTable(list);
    }

    /**
     * 导出对应证书关联列表
     */
    @PreAuthorize("@ss.hasPermi('system:ability:export')")
    @Log(title = "对应证书关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CertificateAbility certificateAbility)
    {
        List<CertificateAbility> list = certificateAbilityService.selectCertificateAbilityList(certificateAbility);
        ExcelUtil<CertificateAbility> util = new ExcelUtil<CertificateAbility>(CertificateAbility.class);
        util.exportExcel(response, list, "对应证书关联数据");
    }

    /**
     * 获取对应证书关联详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:ability:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(certificateAbilityService.selectCertificateAbilityById(id));
    }

    /**
     * 新增对应证书关联
     */
    @PreAuthorize("@ss.hasPermi('system:ability:add')")
    @Log(title = "对应证书关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CertificateAbility certificateAbility)
    {
        return toAjax(certificateAbilityService.insertCertificateAbility(certificateAbility));
    }

    /**
     * 修改对应证书关联
     */
    @PreAuthorize("@ss.hasPermi('system:ability:edit')")
    @Log(title = "对应证书关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CertificateAbility certificateAbility)
    {
        return toAjax(certificateAbilityService.updateCertificateAbility(certificateAbility));
    }

    /**
     * 删除对应证书关联
     */
    @PreAuthorize("@ss.hasPermi('system:ability:remove')")
    @Log(title = "对应证书关联", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(certificateAbilityService.deleteCertificateAbilityByIds(ids));
    }
}
