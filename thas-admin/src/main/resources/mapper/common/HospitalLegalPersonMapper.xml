<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.HospitalLegalPersonMapper">
    
    <resultMap type="com.thas.web.domain.HospitalLegalPerson" id="HospitalLegalPersonResult">
        <result property="id"    column="id"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="legalPersonName"    column="legal_person_name"    />
        <result property="gender"    column="gender"    />
        <result property="legalPersonPost"    column="legal_person_post"    />
        <result property="legalPersonTitle"    column="legal_person_title"    />
        <result property="legalPersonPhone"    column="legal_person_phone"    />
        <result property="legalPersonExtPhone"    column="legal_person_ext_phone"    />
        <result property="legalPersonFax"    column="legal_person_fax"    />
        <result property="legalPersonMobile"    column="legal_person_mobile"    />
        <result property="legalPersonEmail"    column="legal_person_email"    />
        <result property="legalPersonPhoto"    column="legal_person_photo"    />
        <result property="certificateType"    column="certificate_type"    />
        <result property="certificateNumber"    column="certificate_number"    />
        <result property="status"    column="status"    />
        <result property="creator"    column="creator"    />
        <result property="createTime"    column="create_time"    />
        <result property="updater"    column="updater"    />
        <result property="updateTime"    column="update_time"    />
        <result property="legalPersonType"    column="legal_person_type"    />
    </resultMap>

    <sql id="selectHospitalLegalPersonVo">
        select id, apply_no, gender, legal_person_post, legal_person_title, legal_person_fax, legal_person_photo, certificate_type, status, creator, create_time, updater, update_time,legal_person_type,
        <include refid="SELECT_ENCRYPT_FIELD"/> from hospital_legal_person
    </sql>

    <sql id="SQL_AES_ENCRYPT">'${@com.thas.web.config.SqlAesEncryptConfig@SQL_AES_ENCRYPT}'</sql>

    <sql id="SELECT_ENCRYPT_FIELD">
        AES_DECRYPT(unhex(legal_person_name),<include refid="SQL_AES_ENCRYPT"/>) legal_person_name,
        AES_DECRYPT(unhex(legal_person_phone),<include refid="SQL_AES_ENCRYPT"/>) legal_person_phone,
        AES_DECRYPT(unhex(legal_person_ext_phone),<include refid="SQL_AES_ENCRYPT"/>) legal_person_ext_phone,
        AES_DECRYPT(unhex(legal_person_mobile),<include refid="SQL_AES_ENCRYPT"/>) legal_person_mobile,
        AES_DECRYPT(unhex(legal_person_email),<include refid="SQL_AES_ENCRYPT"/>) legal_person_email,
        AES_DECRYPT(unhex(certificate_number),<include refid="SQL_AES_ENCRYPT"/>) certificate_number
    </sql>


    <select id="selectHospitalLegalPersonList" parameterType="HospitalLegalPerson" resultMap="HospitalLegalPersonResult">
        select * from (
        <include refid="selectHospitalLegalPersonVo"/>
        <where>  
            <if test="applyNo != null  and applyNo != ''"> and apply_no = #{applyNo}</if>

            <if test="legalPersonPhone != null  and legalPersonPhone != ''"> and legal_person_phone = HEX(AES_ENCRYPT(#{legalPersonPhone},<include refid="SQL_AES_ENCRYPT"/>))</if>
            <if test="legalPersonExtPhone != null  and legalPersonExtPhone != ''"> and legal_person_ext_phone = HEX(AES_ENCRYPT(#{legalPersonExtPhone},<include refid="SQL_AES_ENCRYPT"/>))</if>
            <if test="legalPersonMobile != null  and legalPersonMobile != ''"> and legal_person_mobile = HEX(AES_ENCRYPT(#{legalPersonMobile},<include refid="SQL_AES_ENCRYPT"/>))</if>
            <if test="legalPersonEmail != null  and legalPersonEmail != ''"> and legal_person_email = HEX(AES_ENCRYPT(#{legalPersonEmail},<include refid="SQL_AES_ENCRYPT"/>))</if>
            <if test="certificateNumber != null  and certificateNumber != ''"> and certificate_number = HEX(AES_ENCRYPT(#{certificateNumber},<include refid="SQL_AES_ENCRYPT"/>))</if>

            <if test="gender != null "> and gender = #{gender}</if>
            <if test="legalPersonPost != null  and legalPersonPost != ''"> and legal_person_post = #{legalPersonPost}</if>
            <if test="legalPersonTitle != null  and legalPersonTitle != ''"> and legal_person_title = #{legalPersonTitle}</if>
            <if test="legalPersonFax != null  and legalPersonFax != ''"> and legal_person_fax = #{legalPersonFax}</if>
            <if test="legalPersonPhoto != null and legalPersonPhoto != ''"> and legal_person_photo = #{legalPersonPhoto}</if>
            <if test="certificateType != null "> and certificate_type = #{certificateType}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="creator != null  and creator != ''"> and creator = #{creator}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
        </where>
        ) aa
        where 1=1
        <if test="legalPersonName != null  and legalPersonName != ''"> and legal_person_name like concat('%',#{legalPersonName},'%')</if>

    </select>
    
    <select id="selectHospitalLegalPersonById" parameterType="Long" resultMap="HospitalLegalPersonResult">
        <include refid="selectHospitalLegalPersonVo"/>
        where id = #{id}
    </select>

    <select id="selectHospitalLegalPersonByApplyNo" parameterType="String" resultMap="HospitalLegalPersonResult">
        <include refid="selectHospitalLegalPersonVo"/>
        where apply_no = #{applyNo} and status = 1
    </select>
        
    <insert id="insertHospitalLegalPerson" parameterType="HospitalLegalPerson" useGeneratedKeys="true" keyProperty="id">
        insert into hospital_legal_person
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">apply_no,</if>
            <if test="legalPersonName != null and legalPersonName != ''">legal_person_name,</if>
            <if test="gender != null">gender,</if>
            <if test="legalPersonPost != null and legalPersonPost != ''">legal_person_post,</if>
            <if test="legalPersonTitle != null and legalPersonTitle != ''">legal_person_title,</if>
            <if test="legalPersonPhone != null">legal_person_phone,</if>
            <if test="legalPersonExtPhone != null">legal_person_ext_phone,</if>
            <if test="legalPersonFax != null">legal_person_fax,</if>
            <if test="legalPersonMobile != null and legalPersonMobile != ''">legal_person_mobile,</if>
            <if test="legalPersonEmail != null and legalPersonEmail != ''">legal_person_email,</if>
            <if test="legalPersonPhoto != null and legalPersonPhoto != ''">legal_person_photo,</if>
            <if test="certificateType != null">certificate_type,</if>
            <if test="certificateNumber != null and certificateNumber != ''">certificate_number,</if>
            <if test="status != null">status,</if>
            <if test="creator != null and creator != ''">creator,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updater != null and updater != ''">updater,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="legalPersonType != null">legal_person_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">#{applyNo},</if>

            <if test="legalPersonName != null and legalPersonName != ''">HEX(AES_ENCRYPT(#{legalPersonName},<include refid="SQL_AES_ENCRYPT"/>)),</if>

            <if test="gender != null">#{gender},</if>
            <if test="legalPersonPost != null and legalPersonPost != ''">#{legalPersonPost},</if>
            <if test="legalPersonTitle != null and legalPersonTitle != ''">#{legalPersonTitle},</if>

            <if test="legalPersonPhone != null">HEX(AES_ENCRYPT(#{legalPersonPhone},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="legalPersonExtPhone != null">HEX(AES_ENCRYPT(#{legalPersonExtPhone},<include refid="SQL_AES_ENCRYPT"/>)),</if>

            <if test="legalPersonFax != null">#{legalPersonFax},</if>

            <if test="legalPersonMobile != null and legalPersonMobile != ''">HEX(AES_ENCRYPT(#{legalPersonMobile},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="legalPersonEmail != null and legalPersonEmail != ''">HEX(AES_ENCRYPT(#{legalPersonEmail},<include refid="SQL_AES_ENCRYPT"/>)),</if>

            <if test="legalPersonPhoto != null and legalPersonPhoto != ''">#{legalPersonPhoto},</if>
            <if test="certificateType != null">#{certificateType},</if>

            <if test="certificateNumber != null and certificateNumber != ''">HEX(AES_ENCRYPT(#{certificateNumber},<include refid="SQL_AES_ENCRYPT"/>)),</if>

            <if test="status != null">#{status},</if>
            <if test="creator != null and creator != ''">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updater != null and updater != ''">#{updater},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="legalPersonType != null">#{legalPersonType},</if>
         </trim>
    </insert>

    <delete id="deleteHospitalLegalPersonByApplyNo" parameterType="String">
        delete from hospital_legal_person where apply_no = #{applyNo}
    </delete>

    <update id="updateHospitalLegalPerson" parameterType="HospitalLegalPerson">
        update hospital_legal_person
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">apply_no = #{applyNo},</if>

            <if test="legalPersonName != null and legalPersonName != ''">legal_person_name = HEX(AES_ENCRYPT(#{legalPersonName},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="legalPersonPhone != null">legal_person_phone = HEX(AES_ENCRYPT(#{legalPersonPhone},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="legalPersonExtPhone != null">legal_person_ext_phone = HEX(AES_ENCRYPT(#{legalPersonExtPhone},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="legalPersonMobile != null and legalPersonMobile != ''">legal_person_mobile = HEX(AES_ENCRYPT(#{legalPersonMobile},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="legalPersonEmail != null and legalPersonEmail != ''">legal_person_email = HEX(AES_ENCRYPT(#{legalPersonEmail},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="certificateNumber != null and certificateNumber != ''">certificate_number = HEX(AES_ENCRYPT(#{certificateNumber},<include refid="SQL_AES_ENCRYPT"/>)),</if>

            <if test="gender != null">gender = #{gender},</if>
            <if test="legalPersonPost != null and legalPersonPost != ''">legal_person_post = #{legalPersonPost},</if>
            <if test="legalPersonTitle != null and legalPersonTitle != ''">legal_person_title = #{legalPersonTitle},</if>
            <if test="legalPersonFax != null">legal_person_fax = #{legalPersonFax},</if>
            <if test="legalPersonPhoto != null and legalPersonPhoto != ''">legal_person_photo = #{legalPersonPhoto},</if>
            <if test="certificateType != null">certificate_type = #{certificateType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="creator != null and creator != ''">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="legalPersonType != null">legal_person_type = #{legalPersonType},</if>
        </trim>
        where
        1=1
        <if test="id != null ">AND id = #{id}</if>
        <if test="applyNo != null and applyNo != ''">AND apply_no = #{applyNo}</if>
    </update>

    <delete id="deleteHospitalLegalPersonById" parameterType="Long">
        delete from hospital_legal_person where id = #{id}
    </delete>

    <delete id="deleteHospitalLegalPersonByIds" parameterType="String">
        delete from hospital_legal_person where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>