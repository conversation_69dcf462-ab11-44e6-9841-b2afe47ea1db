<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.AutSaRelationMapper">

    <resultMap type="com.thas.web.domain.AutSaRelation" id="AutSaRelationResult">
        <result property="id" column="id"/>
        <result property="autCode" column="aut_code"/>
        <result property="autCsId" column="aut_cs_id"/>
        <result property="hospitalApplyNo" column="hospital_apply_no"/>
        <result property="autStatus" column="aut_status"/>
        <result property="status" column="status"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateId" column="update_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="evaluateFlag" column="evaluate_flag"/>
    </resultMap>

    <sql id="selectAutSaRelationVo">
        select id,
               aut_code,
               aut_cs_id,
               hospital_apply_no,
               aut_status,
               status,
               create_id,
               create_time,
               update_id,
               update_time,
               evaluate_flag
        from aut_sa_relation
    </sql>

    <sql id="SQL_AES_ENCRYPT">'${@com.thas.web.config.SqlAesEncryptConfig@SQL_AES_ENCRYPT}'</sql>

    <delete id="deleteAutSaRelationByHospitalApplyNo">
        DELETE FROM aut_sa_relation WHERE hospital_apply_no = #{hospitalApplyNo} AND status = '1'
    </delete>

    <select id="selectAutSaRelationListByCondition" parameterType="com.thas.web.domain.AutSaRelation"
            resultMap="AutSaRelationResult">
        <include refid="selectAutSaRelationVo"/>
        <where>
            <if test="autCode != null  and autCode != ''">
                and aut_code in
                <foreach collection="autCode.split(',')" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="autCsId != null  and autCsId != ''">and aut_cs_id = #{autCsId}</if>
            <if test="hospitalApplyNo != null  and hospitalApplyNo != ''">and hospital_apply_no = #{hospitalApplyNo}
            </if>
            <if test="autStatus != null">and aut_status = #{autStatus}</if>
            <if test="status != null">and status = #{status}</if>
            <if test="createId != null">and create_id = #{createId}</if>
            <if test="updateId != null">and update_id = #{updateId}</if>
            <if test="evaluateFlag != null">and evaluate_flag = #{evaluateFlag}</if>
        </where>
        order by create_time desc
    </select>

    <update id="updateAutSaRelationByCondition" parameterType="com.thas.web.domain.AutSaRelation">
        update aut_sa_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="autStatus != null">aut_status = #{autStatus},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="evaluateFlag != null">evaluate_flag = #{evaluateFlag},</if>
        </trim>
        where status = 1
        <if test="hospitalApplyNo != null and hospitalApplyNo != ''">
            and hospital_apply_no = #{hospitalApplyNo}
        </if>
        <if test="autCode != null and autCode != ''">
            and aut_code = #{autCode}
        </if>
    </update>
    <update id="updateAutSaRelationByAutCodeList">
       update aut_sa_relation set status = 0 where aut_code in
       <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
        #{item}
       </foreach>
     and status = 1;
    </update>

    <insert id="insertAutSaRelation" parameterType="com.thas.web.domain.AutSaRelation" useGeneratedKeys="true"
            keyProperty="id">
        insert into aut_sa_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="autCode != null and autCode != ''">aut_code,</if>
            <if test="autCsId != null and autCsId != ''">aut_cs_id,</if>
            <if test="hospitalApplyNo != null and hospitalApplyNo != ''">hospital_apply_no,</if>
            <if test="autStatus != null">aut_status,</if>
            <if test="status != null">status,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="evaluateFlag != null">evaluate_flag</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="autCode != null and autCode != ''">#{autCode},</if>
            <if test="autCsId != null and autCsId != ''">#{autCsId},</if>
            <if test="hospitalApplyNo != null and hospitalApplyNo != ''">#{hospitalApplyNo},</if>
            <if test="autStatus != null">#{autStatus},</if>
            <if test="status != null">#{status},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="evaluateFlag != null">#{evaluateFlag}</if>
        </trim>
    </insert>

    <select id="selectAudRelationId" resultType="string">
        SELECT auto_increment
        FROM information_schema.`TABLES`
        WHERE TABLE_NAME = 'aut_sa_relation'
          and table_schema = (select database())
    </select>

    <select id="selectAllAutSaRelationList" resultType="com.thas.web.domain.AutSaRelationList"
            parameterType="com.thas.web.domain.AutSaRelation">
        select tt.hospital_name hospitalName,
        tt.apply_no applyNo,
        GROUP_CONCAT(tttt.user_name) userName,
        t.aut_code autCode,
        t.aut_status autStatus,
        t.aut_cs_id versionId,
        ttttt.version_name versionName,
        t.create_time createTime,
        t.update_time updateTime
        from aut_sa_relation t
        left join hospital_base_info tt on t.hospital_apply_no = tt.apply_no
        left join sys_user_hospital ttt on t.hospital_apply_no = ttt.hospital_apply_no
        left join sys_user tttt on ttt.user_id = tttt.user_id
        left join cst_versioning ttttt on ttttt.version_id = t.aut_cs_id
        where t.`status` = '1'
        <if test="distributeHospitalName != null and distributeHospitalName != ''">AND tt.hospital_name like
            concat('%',#{distributeHospitalName}, '%')
        </if>
        <if test="hospitalApplyNo != null and hospitalApplyNo != ''">
            AND tt.apply_no = #{hospitalApplyNo}
        </if>
        <if test="distributeStatus != null and distributeStatus != ''">AND t.aut_status = #{distributeStatus}</if>
        GROUP BY hospitalName,applyNo,autCode,autStatus,versionId,versionName,createTime,updateTime
        order by t.update_time desc
    </select>
    <select id="selectAutSaRelationByHospitalApplyNo" resultType="java.lang.String">
    	SELECT aut_status FROM aut_sa_relation WHERE hospital_apply_no = #{hospitalApplyNo} AND `status` = 1
    </select>

    <select id="selectAutSaRelationByAutStatus" parameterType="list"
            resultType="com.thas.web.domain.vo.TraineesReviewRecVO">
        SELECT
        hbi.apply_no applyNo,
        hbi.hospital_name hospitalName,
        asr.aut_code autCode,
        hrc.cycle startTime
        FROM
        aut_sa_relation asr
        LEFT JOIN hospital_base_info hbi ON asr.hospital_apply_no = hbi.apply_no
        LEFT JOIN hospital_review_cycle hrc ON asr.hospital_apply_no = hrc.apply_no
        AND hrc.stage_value = 4
        AND hrc.status = 1
        AND asr.aut_status IN
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectAutSaRelationListByQueryRes" resultType="com.thas.web.domain.AutSaRelation"
            resultMap="AutSaRelationResult">
        <include refid="selectAutSaRelationVo"/>
        <where>
            1 = 1
            <if test="hospitalApplyNoList != null  and hospitalApplyNoList.size > 0 ">
                and hospital_apply_no in
                <foreach collection="hospitalApplyNoList" index="index" item="item" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="evaluateFlag != null">and evaluate_flag = #{evaluateFlag}</if>
            <if test="status != null">and status = #{status}</if>
            <if test="autCode != null and autCode != ''">and aut_code = #{autCode}</if>
            <if test="hospitalApplyNo != null and hospitalApplyNo != ''">and hospital_apply_no = #{hospitalApplyNo}</if>
        </where>
    </select>
    <select id="selectAutSaRelationByTraineesAssessorId" resultType="com.thas.web.domain.AutSaRelation">
        SELECT asr.id,
               asr.aut_code autCode,
               asr.aut_cs_id autCsId,
               asr.hospital_apply_no hospitalApplyNo,
               asr.aut_status autStatus,
               asr.status,
               asr.evaluate_flag evaluateFlag
               FROM aut_sa_relation asr
        LEFT JOIN hospital_reviewer hr ON hr.apply_no = asr.hospital_apply_no
        WHERE
        asr.`status` = 1
        AND hr.`status` = 1
        AND hr.reviewer_id = #{traineesAssessorId}
    </select>

    <select id="selectAutSaRelationToStatisticsReport" resultType="com.thas.web.domain.StatisticsReportVo">
        SELECT asr.aut_code autCode,asr.aut_status autStatus,hbi.hospital_name hospitalName,
        AES_DECRYPT(unhex(su.nick_name),<include refid="SQL_AES_ENCRYPT"/>) reviewerName,
        hr.interest_desc interestDesc
        FROM aut_sa_relation asr
        LEFT JOIN hospital_reviewer hr ON hr.aut_code = asr.aut_code
        LEFT JOIN hospital_base_info hbi ON hbi.apply_no = hr.apply_no
        LEFT JOIN sys_user su ON su.user_id = hr.reviewer_id
        WHERE
        asr.status = 1
        AND hbi.status = 1
        <if test="req.hrStatus != null and req.hrStatus != ''">AND  hr.status = #{req.hrStatus}</if>
        <if test="req.hrReviewerStatus != null and req.hrReviewerStatus != ''">AND  hr.reviewer_status = #{req.hrReviewerStatus}</if>
        <if test="req.hasInterest != null and req.hasInterest != ''">AND  hr.has_interest = #{req.hasInterest}</if>
        <if test="req.autCode != null and req.autCode != ''">AND  asr.aut_code = #{req.autCode}</if>
        ORDER BY asr.create_time DESC
    </select>

    <select id="selectReviewerWorkStatisticsReport" resultType="com.thas.web.domain.StatisticsReportVo">
        SELECT sur.role_id roleId,
        AES_DECRYPT(unhex(su.nick_name),<include refid="SQL_AES_ENCRYPT"/>) reviewerName,
        hr.aut_code autCode,hr.status hrStatus ,asr.aut_status autStatus,hr.field_id_list fieldIdList
        FROM sys_user_role sur
        LEFT JOIN sys_user su ON sur.user_id = su.user_id
        LEFT JOIN hospital_reviewer hr ON su.user_id = hr.reviewer_id
        LEFT JOIN aut_sa_relation asr ON asr.aut_code = hr.aut_code
        WHERE
        sur.role_id in('101','106') AND
        su.status = 0
        ORDER BY su.create_time DESC
    </select>

    <select id="selectAcceptStatisticsReport" resultType="com.thas.web.domain.StatisticsReportVo">
        SELECT asr.aut_code autCode,asr.aut_status autStatus,hbi.hospital_name hospitalName
        FROM aut_sa_relation asr
        LEFT JOIN hospital_base_info hbi ON hbi.apply_no = asr.hospital_apply_no
        WHERE asr.status = 1 AND hbi.status = 1
        <if test="req.acceptDetailType != null and req.acceptDetailType == 2 ">
            AND  asr.aut_status <![CDATA[ <> ]]> #{req.qryAutStatus}</if>
        <if test="req.acceptDetailType != null and req.acceptDetailType == 3 ">
            AND  asr.aut_status = #{req.qryAutStatus}</if>
        GROUP BY asr.aut_code,asr.aut_status,hbi.hospital_name
        ORDER BY asr.create_time DESC
    </select>
    <select id="selectAsaStatusConfigVo" resultType="com.thas.web.domain.AsaStatusConfigVo">
        SELECT id,current_status currentStatus FROM asa_status_config
    </select>

    <select id="selectAutSaRelationListByCstVersioningId" resultType="com.thas.web.domain.AutSaRelation">
        select t.aut_code,t.hospital_apply_no,t.aut_cs_id from aut_sa_relation t
        where
        t.aut_cs_id = ( select version_id from cst_versioning where id = #{cstVersioningId} and status != 1)
        and status = 1
    </select>

    <select id="qryReviewerExpNotice" resultType="com.thas.web.dto.ReviewerExpNoticeVo">
        SELECT asr.hospital_apply_no applyNo,hbi.hospital_name hospitalName,`asc`.cycle_stage cycleStage ,TO_DAYS(substring_index(hrc.cycle, ',', -1)) - TO_DAYS(NOW()) `day`
        FROM aut_sa_relation asr
        LEFT JOIN asa_status_config `asc` ON `asc`.current_status = asr.aut_status
        LEFT JOIN hospital_review_cycle hrc ON hrc.apply_no = asr.hospital_apply_no AND hrc.stage_value = `asc`.cycle_stage
        LEFT JOIN hospital_base_info hbi ON hbi.apply_no = asr.hospital_apply_no
        WHERE
        asr.`status` = 1 AND
        hrc.`status` = 1 AND
        TO_DAYS(substring_index(hrc.cycle, ',', -1)) - TO_DAYS(NOW()) BETWEEN '0' AND #{date}
    </select>

</mapper>
