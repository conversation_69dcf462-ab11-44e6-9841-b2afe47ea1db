<!--  -->
<template>
  <div class="audit-evaluation" v-loading="loading">
    <div v-if="distributed">
      <!-- 医院名称 -->
      <el-card class="box-card" shadow="always" style="margin-bottom: 10px; margin-left: -5px">
        <h1 style="margin: 0">{{ detail.hospitalName }}</h1>
      </el-card>

      <!-- 总结详情 -->

      <el-card v-if="showModifyConfirm" class="box-card" shadow="never">
        <div slot="header" class="clearfix">
          <!-- <h4>修改事实准确性审查结果</h4> -->
          <h4>{{ cardTitle }}</h4>
        </div>
        <el-form ref="summaryForm" :model="summaryForm" label-width="120px" :rules="rules">
          <el-form-item label="需修改项：" v-if="modifyClause.length || isSubmitConfirm">
            <modify-table :data="modifyClause" :small="true" :versionId='relation.autCsId' :showAll='false' split>
              <el-table-column label="评价结果" width="90">
                <template slot-scope="scope">
                  <span>{{ modifyAutResult(scope.row.clauseId) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="评审员评价">
                <template slot-scope="scope">
                  <span style="white-space: pre-line;">{{ modifyAutDesc(scope.row.clauseId) }}</span>
                </template>
              </el-table-column>
              <el-table-column>
                <div slot="header">
                  医院反馈：<br>
                  事实准确性审查报告中的错误/需要注意/澄清的地方：
                </div>
                <template slot-scope="scope">
                  <span>{{ modifyAutQuestion(scope.row.clauseId) }}</span>
                </template>
              </el-table-column>
              <el-table-column>
                <div slot="header">
                  评审员答复：<br>
                  请酌情考虑修改评审报告及其评级，如果没有更改，请说明原因。
                </div>
                <template slot-scope="scope">
                  <span style="white-space: pre-line;">{{ modifyAutQuestionResult(scope.row.clauseId) }}</span>
                  <span style="white-space: pre-line;">{{ modifyAutQuestionDesc(scope.row.clauseId) }}</span>
                </template>
              </el-table-column>
            </modify-table>
          </el-form-item>
          <div v-if="isFirst || isSecond || isThird">
            <el-form-item label="亮点：" prop="autAdvantage">
              <el-input type="textarea" v-model="summaryForm.autAdvantage" :maxlength="10000" :rows="6"
               ></el-input>
            </el-form-item>
            <el-form-item label="不足：" prop="autEvaluate">
              <el-input type="textarea" v-model="summaryForm.autEvaluate" :maxlength="10000" :rows="6"
               ></el-input>
            </el-form-item>
            <el-form-item label="整改建议：" prop="autProposal">
              <el-input type="textarea" v-model="summaryForm.autProposal" :maxlength="10000" :rows="6"
               ></el-input>
            </el-form-item>
            <el-form-item label="改进机会：" prop="autImprove">
              <el-input type="textarea" v-model="summaryForm.autImprove" :maxlength="10000" :rows="6"
               ></el-input>
            </el-form-item>
          </div>
          <el-form-item v-if="isSubmitConfirm" label="评审组长意见：" prop="leaderOpinion"
            :rules="[{ required: true, message: '请填写评审组长意见', trigger: ['blur'] }]">
            <el-input type="textarea" v-model="summaryForm.leaderOpinion" :maxlength="10000" :rows="6"
             ></el-input>
          </el-form-item>


          <el-form-item v-if="isSubmitConfirm" label="下载：" prop="downloaded"
            :rules="[{ required: isSubmitConfirm, message: '请下载事实准确性查询表', trigger: ['blur'] }]">
            <el-button v-if="isSubmitConfirm" :disabled="!summaryForm.leaderOpinion" v-loading='downloadLoading'
              @click="downloadFarReport">下载事实准确性查询表</el-button>
          </el-form-item>
          <el-form-item v-if="isSubmitConfirm" label="上传：" prop="fileIds"
            :rules="[{ required: true, message: '请上传已盖章的事实准确性查询表', trigger: ['blur'] }]">
            <upload :accept='`.PDF`' :disabled="!summaryForm.downloaded" single class="evaluation-upload" :files="files" :data="{type: '1', downLoadFileName: `${detail.hospitalName}-事实准确性确认表.pdf`}"  @uploadSuccess="onSuccess" @fileRemove="onError" :text="`上传签字盖章后的事实准确性查询表`"></upload>
          </el-form-item>

<!--
          <el-form-item v-if="isThird" label="下载：" prop="downloaded"
            :rules="[{ required: isThird, message: '请下载验证报告', trigger: ['blur'] }]">
            <el-button v-if="isThird" v-loading='downloadLoading'
              @click="downloadVerifyReport">下载验证报告</el-button>
          </el-form-item>
          <el-form-item v-if="isThird" label="上传：" prop="fileIds"
            :rules="[{ required: true, message: '请上传已盖章的验证报告', trigger: ['blur'] }]">
            <upload :disabled="!summaryForm.downloaded" single class="evaluation-upload" :files="files"
              :data="{ type: '1' }" @uploadSuccess="onSuccess" @fileRemove="onError" :text="`上传签字盖章后的验证报告`"></upload>
          </el-form-item> -->

          <el-form-item label=" ">
            <el-button v-if="isFirst" type="primary" @click="submitSummary"
              :disabled='termSubmitLoading'>提交修改结果</el-button>
            <el-button v-if="isSecond" type="primary" @click="submitSummary"
              :disabled='termSubmitLoading'>提交修改结果</el-button>
            <el-button v-if="isThird" type="primary" @click="submitSummary"
              :disabled='termSubmitLoading'>提交修改结果</el-button>
            <el-button v-if="isSubmitConfirm" type="primary" @click="submitSummary"
              :disabled='termSubmitLoading || files.length == 0'>提交修改结果</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 评价  -->
      <el-card class="box-card" shadow="never" v-if="!showModifyConfirm">
        <div slot="header" class="clearfix">
          <!-- <h4>修改验证报告审查结果</h4> -->
          <h4>{{ cardTitle }}</h4>
          <div class="header-right" v-if="standardLoadedKey" :key="headerSlotKeys">
            <!--  v-if="showExamineClauseList.length && isFirst" :class="{lightHigh: lightHigh == 10}"  @click="handleClauseFilter(10, showExamineClauseList)" -->
            <span v-if="showExamineClauseList.length">被驳回 {{ showExamineClauseList.length }} 款</span>
            <span v-if="showExamineClauseListLeft.length && isFirst" :class="{ lightHigh: lightHigh == 20 }"
              @click="handleClauseFilter(20, showExamineClauseListLeft)">待修改 {{ showExamineClauseListLeft.length }}
              款</span>

            <el-button type="primary" size="mini" v-if="isCheck" @click="submitAll">审核完成</el-button>
          </div>
        </div>
        <!-- 款项选择器 -->
        <standard-card ref="standardCard" :key="`base${loadingKeys}`" :clauseIds="clauseIds" :versionId="relation.autCsId"
          @selectChange="selectChange" @loaded="standardLoaded">
        </standard-card>

        <!-- 款项  审查评审报告-->
        <el-form v-if="isFirst" :key="loadingKeys" label-width="100px">
          <!-- <clause-item v-for="(clause, index) in clauseList" v-show="isThisPage(index + 1)" :clause="clause" :key="clause.clauseId"> -->
          <clause-item v-for="(clause) in clauseList" :clause="clause" :key="clause.clauseId">
            <evaluation-self :readonly="true" :type="AutSaAudCurrentStatusEnum.SA_CLAUSE" :clause="clause"
              :aut="saMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-self>
            <evaluation-review :key="clauseItemKeys" isLeader :readonly="true" :type="AutSaAudCurrentStatusEnum.SR_CLAUSE"
              :clause="clause" :aut="srMap[clause.clauseId]" :status="autSaAudStatus">
            </evaluation-review>
            <evaluation-audit :readonly='true' :type="AutSaAudCurrentStatusEnum.FR_REPORT_R_CLAUSE" :clause="clause"
              :aut="frrMap[clause.clauseId]" :status="autSaAudStatus">
            </evaluation-audit>
            <!-- <evaluation-check :readonly="true" v-if="showReviewCheck" :type="AutSaAudCurrentStatusEnum.FR_V_CLAUSE" :clause="clause" :aut="frvMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-check> -->
            <evaluation-modify :type="AutSaAudCurrentStatusEnum.SR_CLAUSE_M"
              :prevAutResult="srMap[clause.clauseId].autResult" :clause="clause" :aut="lwmMap[clause.clauseId]"
              :status="autSaAudStatus">
            </evaluation-modify>
          </clause-item>
        </el-form>

        <!-- 款项  事实准确性-->
        <el-form v-if="isSecond" :key="loadingKeys" label-width="100px">
          <!-- <clause-item v-for="(clause, index) in clauseList" v-show="isThisPage(index + 1)" :clause="clause" :key="clause.clauseId"> -->
          <clause-item v-for="(clause) in clauseList" :clause="clause" :key="clause.clauseId">
            <evaluation-self :readonly="true" :type="AutSaAudCurrentStatusEnum.SA_CLAUSE" :clause="clause"
              :aut="saMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-self>
            <evaluation-review isLeader :readonly="true" :type="AutSaAudCurrentStatusEnum.SR_CLAUSE" :clause="clause"
              :aut="srMap[clause.clauseId]" :status="autSaAudStatus">
            </evaluation-review>
            <evaluation-confirm :readonly='true' v-if="farMap[clause.clauseId]" :type="AutSaAudCurrentStatusEnum.FAR_CLAUSE" :clause="clause"
              :aut="farMap[clause.clauseId]" :status="autSaAudStatus">
            </evaluation-confirm>
            <evaluation-modify v-if="farMap[clause.clauseId]" :key="clauseItemKeys" :refuse="!!fvmSKMap[clause.clauseId]" isLeader
              :type="AutSaAudCurrentStatusEnum.FAR_CLAUSE_M" :rejectType="AutSaAudCurrentStatusEnum.FAR_CLAUSE_M_SKIP"
              :prevAutResult="srMap[clause.clauseId].autResult" :clause="clause" :aut="fwmOrSkip(clause.clauseId)"
              :status="autSaAudStatus">
            </evaluation-modify>
            <evaluation-modify v-if="!farMap[clause.clauseId]" :key="clauseItemKeys" isLeader startModify
              :type="AutSaAudCurrentStatusEnum.FAR_CLAUSE_M" :rejectType="AutSaAudCurrentStatusEnum.FAR_CLAUSE_M_SKIP"
              :prevAutResult="srMap[clause.clauseId].autResult" :clause="clause" :aut="fwmMap[clause.clauseId] || srMap[clause.clauseId]"
              :status="autSaAudStatus">
            </evaluation-modify>
            <evaluation-query v-if="!fwmMap[clause.clauseId] && fmRjMap[clause.clauseId]" :readonly='true'
              :type="AutSaAudCurrentStatusEnum.FAR_CLAUSE_RJ_E_M" :clause="clause" :aut="fmRjMap[clause.clauseId]"
              :status="autSaAudStatus">
            </evaluation-query>
            <!-- <evaluation-check v-if="showCheck(clause.clauseId)" :readonly="shellReadonly(clause.clauseId)"
              :type="AutSaAudCurrentStatusEnum.FAR_V_CLAUSE_M" :clause="clause" :aut="fvmMap[clause.clauseId]"
              :status="autSaAudStatus">
            </evaluation-check> -->
          </clause-item>
        </el-form>

        <!-- 款项 验证评审 -->
        <el-form v-if="isThird" :key="loadingKeys" label-width="100px">
          <!-- <clause-item v-for="(clause, index) in clauseList" v-show="isThisPage(index + 1)" :clause="clause" :key="clause.clauseId"> -->
          <clause-item v-for="(clause) in clauseList" :clause="clause" :key="clause.clauseId">
            <evaluation-self :readonly="true" :type="AutSaAudCurrentStatusEnum.SA_CLAUSE" :clause="clause"
              :aut="saMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-self>
            <evaluation-review isLeader :readonly="true" :type="AutSaAudCurrentStatusEnum.SR_CLAUSE" :clause="clause"
              :aut="pre(clause.clauseId)" :status="autSaAudStatus">
            </evaluation-review>
            <evaluation-verify :readonly='true' :type="AutSaAudCurrentStatusEnum.TR_CLAUSE" :clause="clause"
              :aut="trMap[clause.clauseId]" :status="autSaAudStatus">
            </evaluation-verify>
            <!-- <evaluation-check :readonly="true" v-if="showReviewCheck" :type="AutSaAudCurrentStatusEnum.FR_V_CLAUSE" :clause="clause" :aut="frvMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-check> -->
            <evaluation-modify :key="clauseItemKeys" :refuse="!!tvmSKMap[clause.clauseId]" isLeader
              :type="AutSaAudCurrentStatusEnum.TR_CLAUSE_M" :rejectType="AutSaAudCurrentStatusEnum.TR_CLAUSE_M_SKIP"
              :clause="clause" :aut="twmOrSkip(clause.clauseId)" :status="autSaAudStatus">
            </evaluation-modify>
            <!-- <evaluation-check v-if="showCheck(clause.clauseId)" :readonly="shellReadonly(clause.clauseId)"
              :type="AutSaAudCurrentStatusEnum.TR_V_CLAUSE_M" :clause="clause" :aut="tvmMap[clause.clauseId]"
              :status="autSaAudStatus">
            </evaluation-check> -->
          </clause-item>
        </el-form>
      </el-card>
      <!-- 总结提交 -->
    </div>
  </div>
</template>

<script>
import { downloadPDF, download } from "@/utils/request";
import evaluationMixin from "@/mixins/evaluation.vue";
import EvaluationConfirm from '../../components/EvaluationForm/evaluationConfirm.vue';

export default {
  name: "CAssessorIndex",
  mixins: [evaluationMixin],
  components: {
    EvaluationConfirm
  },
  props: {},
  watch: {
    questionKeys() {
      this.questionList = this.questions;
    },
    detail: {
      deep: true,
      immediate: true,
      handler() {
        if (
          this.detail &&
          this.detail.autSaAudStatus &&
          this.detail.autSaAudStatus ==
          this.AutSaAudCurrentStatusEnum.WAIT_REPORT
        ) {
          this.handleError();
        }
      },
    },
  },
  data() {
    return {
      relation: {},
      detail: {},
      clauseIds: [],
      specialInit: true,
      questionKeys: 0,
      questionList: [],
      summaryForm: {
        autDesc: "",
        autResult: "1",
        fileIds: "",
        downloaded: "",
        autAdvantage: "",
        autEvaluate: "",
        autProposal: "",
        autImprove: "",
        leaderOpinion: "",
      },
      downloadLoading: false,
      files: [],
      termSubmitLoading: false,
      reviewResultOptions: [],
      waitModifyClauseIdListLength: -1,
      cardTitle: '',
      rules: {
        autAdvantage: [{
          required: true,
          validator: (rule, value, callback) => {
            if (this.isAutEmpty('autAdvantage')) callback('请填写亮点')
            callback()
          },
          trigger: ['blur'],
        }],
        autEvaluate: [{
          required: true,
          validator: (rule, value, callback) => {
            if (this.isAutEmpty('autEvaluate')) callback('请填写不足')
            callback()
          },
          trigger: ['blur'],
        }],
        autProposal: [{
          required: true,
          validator: (rule, value, callback) => {
            if (this.isAutEmpty('autProposal')) callback('请填写整改建议')
            callback()
          },
          trigger: ['blur'],
        }],
        autImprove: [{
          required: true,
          validator: (rule, value, callback) => {
            if (this.isAutEmpty('autImprove')) callback('请填写改进机会')
            callback()
          },
          trigger: ['blur'],
        }],
      }
    };
  },

  watch: {
    $route: {
      handler(val) {
        this.cardTitle = val?.meta?.title
      },
      immediate: true
    }
  },

  computed: {
    pre() {
      return id => {
        let fwm = this.fwmMap[id]
        let sr = this.srMap[id]
        return fwm || sr
      }
    },
    modifyClause() {
      return Object.values(this.farMap)
        .filter(({ autResult }) => autResult == 2)
        .map(({ clauseId }) => Number(clauseId));
    },

    modifyAutResult() {
      return (clauseId) => {
        let result = this.srMap[clauseId].autResult;
        let option = this.reviewResultOptions.find(
          ({ dictValue }) => dictValue == result
        );
        return option ? option.dictLabel : "";
      };
    },

    modifyAutDesc() {
      return (clauseId) => {
        let aut = this.srMap[clauseId];
        return this.descSubdivision(aut)
      };
    },

    modifyAutQuestion() {
      return (clauseId) => {
        return this.farMap[clauseId].autDesc;
      };
    },

    modifyAutQuestionResult() {
      return (clauseId) => {
        try {
          let aut = this.fwmMap[clauseId];
          if (!aut) {
            aut = this.fvmSKMap[clauseId]
          }
          let result = aut.autResult;
          // if(result == 0) {
          //   return '评价结果：拒绝修改 \n'
          // }
          let option = this.reviewResultOptions.find(
            ({ dictValue }) => dictValue == result
          );
          return option ? '评价结果：' + option.dictLabel + '\n' : "";
        } catch (error) {
          return ''
        }
      }
    },

    modifyAutQuestionDesc() {
      return (clauseId) => {
        try {
          let aut = this.fvmSKMap[clauseId];
          if (!aut) {
            aut = this.fwmMap[clauseId]
          }
          console.log(aut);
          return this.descSubdivision(aut, true)
        } catch (error) {
          console.log(error);
          return '--'
        }
      };
    },

    descSubdivision() {
      return (aut, showTitle) => {
        let title = showTitle ? '评价描述：' : ''
        let autResult = aut.autResult;
        try {
          let { autAdvantage, autEvaluate, autProposal, autImprove, autDesc } = JSON.parse(aut.autDesc)
          if (autResult == 0) {
            return '拒绝原因：' + (autDesc || '--')
          }
          if (autResult == 1 || autResult == 2) {
            return title + (autAdvantage || '--')
          } else if (autResult == 3) {
            return title + (autImprove || '--')
          } else {
            if (autResult != 6) {
              let a = autEvaluate ? `不足：${autEvaluate}  \n` : '--'
              let b = autProposal ? `整改建议：${autProposal}  \n` : '--'
              let c = autImprove ? `改进机会：${autImprove}  \n` : '--'
              if (autEvaluate || autProposal || autImprove) {
                return [a, b, c].join(' ')
              }
            }
            return title + (autDesc || '--');
          }
        } catch (error) {
          if (autResult == 0) {
            return '拒绝原因：' + (aut.autDesc || '--')
          }
          return title + (aut.autDesc || '--');
        }
      }
    },

    showModifyConfirm() {
      return (
        this.relation.isLeader == 1 &&
        (this.autSaAudStatus ===
          this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_REVIEW_CONFIRM ||
          this.autSaAudStatus ===
          this.AutSaAudCurrentStatusEnum.MODIFICATION_TERMS_CONFIRM ||
          this.autSaAudStatus ===
          this.AutSaAudCurrentStatusEnum.MODIFICATION_TERMS_SUM ||
          this.autSaAudStatus ===
          this.AutSaAudCurrentStatusEnum.MODIFICATION_TERMS_CONFIRM ||
          this.autSaAudStatus ===
          this.AutSaAudCurrentStatusEnum
            .REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW_SUM)
      );
    },

    // 评审组长修改审查组长有意见的款项
    isFirst() {
      let statues = [
        this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_REVIEW,
        this.AutSaAudCurrentStatusEnum.REVIEWER_MODIFICATION_REVIEW_CONFIRM,
      ];
      return statues.includes(this.autSaAudStatus);
    },

    // 评审员修改医院有意见的款项
    isSecond() {
      let statues = [
        this.AutSaAudCurrentStatusEnum.MODIFIED_TERMS_FIRST_PROC,
        this.AutSaAudCurrentStatusEnum.MODIFIED_TERMS_TRIAL_FIRST_PROC,
        this.AutSaAudCurrentStatusEnum.MODIFICATION_TERMS_SUM,
      ];
      return statues.includes(this.autSaAudStatus);
    },
    // 评审员修改验证评审员有意见的款项
    isThird() {
      let statues = [
        this.AutSaAudCurrentStatusEnum
          .REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW,
        this.AutSaAudCurrentStatusEnum
          .REVIEWER_MODIFICATION_TRIAL_SENIOR_REVIEWER_REVIEW,
        this.AutSaAudCurrentStatusEnum
          .REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW_SUM,
      ];
      return statues.includes(this.autSaAudStatus);
    },

    isCheck() {
      let statues = [
        this.AutSaAudCurrentStatusEnum
          .REVIEWER_MODIFICATION_TRIAL_SENIOR_REVIEWER_REVIEW,
        this.AutSaAudCurrentStatusEnum.MODIFIED_TERMS_TRIAL_FIRST_PROC,
      ];
      return statues.includes(this.autSaAudStatus);
    },

    showCheck() {
      return (clauseId) => {
        // let flag = false;
        if (this.isSecond) {
          // flag = !this.fvmMap[clauseId] || !this.fvmMap[clauseId].rejected
          if (
            this.autSaAudStatus ==
            this.AutSaAudCurrentStatusEnum.MODIFIED_TERMS_FIRST_PROC
          ) {
            if (this.fvmMap[clauseId] && this.fvmMap[clauseId].rejected) {
              return true;
            }
          } else if (
            this.autSaAudStatus ==
            this.AutSaAudCurrentStatusEnum.MODIFIED_TERMS_TRIAL_FIRST_PROC
          ) {
            return true;
          }
        } else if (this.isThird) {
          // flag = this.tvmMap[clauseId] || !this.tvmMap[clauseId].rejected
          if (
            this.autSaAudStatus ==
            this.AutSaAudCurrentStatusEnum
              .REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW
          ) {
            if (this.tvmMap[clauseId] && this.tvmMap[clauseId].rejected) {
              return true;
            }
          } else if (
            this.autSaAudStatus ==
            this.AutSaAudCurrentStatusEnum
              .REVIEWER_MODIFICATION_TRIAL_SENIOR_REVIEWER_REVIEW
          ) {
            return true;
          }
        }
        return false;
      };
    },

    fwmOrSkip() {
      return clauseId => {
        return this.fwmMap[clauseId] || this.fvmSKMap[clauseId]
      }
    },

    twmOrSkip() {
      return clauseId => {
        return this.twmMap[clauseId] || this.tvmSKMap[clauseId]
      }
    },

    shellReadonly() {
      return (clauseId) => {
        if (this.isSecond) {
          return (
            (!!this.fvmMap[clauseId] && !this.fvmMap[clauseId].rejected) ||
            (this.autSaAudStatus !=
              this.AutSaAudCurrentStatusEnum.MODIFIED_TERMS_TRIAL_FIRST_PROC &&
              this.autSaAudStatus !=
              this.AutSaAudCurrentStatusEnum
                .REVIEWER_MODIFICATION_TRIAL_SENIOR_REVIEWER_REVIEW)
          );
        } else if (this.isThird) {
          return (
            (!!this.tvmMap[clauseId] && !this.tvmMap[clauseId].rejected) ||
            (this.autSaAudStatus !=
              this.AutSaAudCurrentStatusEnum.MODIFIED_TERMS_TRIAL_FIRST_PROC &&
              this.autSaAudStatus !=
              this.AutSaAudCurrentStatusEnum
                .REVIEWER_MODIFICATION_TRIAL_SENIOR_REVIEWER_REVIEW)
          );
        }
        return true;
      };
    },

    isSubmitConfirm() {
      return (
        this.autSaAudStatus ===
        this.AutSaAudCurrentStatusEnum.MODIFICATION_TERMS_CONFIRM
      );
    },

    showExamineClauseList() {
      return Object.values(this.frrMap).map(({ clauseId }) => clauseId);
    },

    showExamineClauseListLeft() {
      let list1 = Object.values(this.frrMap).map(({ clauseId }) => clauseId);
      let list2 = Object.values(this.lwmMap).map(({ clauseId }) => clauseId);
      return list1.filter((id) => !list2.includes(id));
    },

    showTimes() {
      try {
        let audSecondCycle = this.relation.audSecondCycle;
        if (audSecondCycle) {
          let cycles = audSecondCycle.split(",");
          let times = cycles.map((cycle) => cycle.split("-"));
          let formatTimes = times.map((time) => {
            return `${time[0]}年${time[1]}月${time[2]}日`;
          });
          return formatTimes.join(" ~ ");
        }
        return "";
      } catch (error) {
        return "";
      }
    },

    questions() {
      let trMapList = Object.values(this.farMap).filter(
        (tr) => tr.autResult == 2
      );
      let trIds = trMapList.map(({ clauseId }) => clauseId);
      let srMapList = Object.values(this.srMap).filter((sr) =>
        trIds.includes(sr.clauseId)
      );
      let fwmMapList = Object.values(this.fwmMap);
      const clauses = this.$store.getters
        .standardTypeItemByVersionId(this.relation.autCsId, "clause", "list")
        .filter((sr) => trIds.includes(`${sr.clauseId}`));
      let list = clauses.map(({ clauseId, clauseNo }) => {
        let tr = trMapList.find((data) => data.clauseId == clauseId);
        let sr = srMapList.find((data) => data.clauseId == clauseId);
        let farm = fwmMapList.find((data) => data.clauseId == clauseId);
        let result = this.reviewResultOptions.find(
          ({ dictValue }) => dictValue == sr.autResult
        );
        return {
          clauseNo,
          clauseId,
          yzDesc: tr.autDesc,
          psDesc: sr.autDesc,
          psResult: result ? result.dictLabel : "",
          answer: farm ? farm.autDesc : "",
        };
      });
      return list;
    },
  },

  created() { },

  mounted() {
    this.init2(
      true,
      this.$route.query.autCode,
      this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_W_M
    );

    this.getDicts("review_result").then((response) => {
      this.reviewResultOptions = response.data;
      this.questionKeys++;
    });
  },

  methods: {
    isAutEmpty(field) {
      let initStr = ''
      this.detail.domainGroupNodes.forEach(item => initStr += `${item.groupDetail}:\n`)
      let cloneAutStr = initStr.replace(/[\n\s]+/g, '');
      let autStr = this.summaryForm[field].replace(/[\n\s]+/g, '');
      return !autStr || autStr === cloneAutStr
    },
    initFinalBack() {
      if (this.waitModifyClauseIdListLength == -1) {
        this.waitModifyClauseIdListLength = (
          this.relation.waitModifyClauseIdList || []
        ).length;
      } else if (
        this.AutSaAudCurrentStatusEnum.MODIFICATION_TERMS_SUM !=
        this.autSaAudStatus &&
        this.AutSaAudCurrentStatusEnum
          .REVIEWER_MODIFICATION_SENIOR_REVIEWER_REVIEW_SUM !=
        this.autSaAudStatus &&
        this.AutSaAudCurrentStatusEnum.MODIFICATION_TERMS_CONFIRM !=
        this.autSaAudStatus
      ) {
        if (
          this.waitModifyClauseIdListLength != 0 &&
          this.relation.waitModifyClauseIdList.length == 0
        ) {
          this.handleError();
          return;
        } else if (
          this.waitModifyClauseIdListLength == 0 &&
          this.relation.waitModifyClauseIdList &&
          this.relation.waitModifyClauseIdList.length != 0
        ) {
          this.handleError();
          return;
        }
      }

      let wholeIds = [];
      if (this.isFirst) {
        let ffrList = Object.values(this.frrMap);
        wholeIds = ffrList.map(({ clauseId }) => clauseId);
      } else if (this.isSecond) {
        // wholeIds = this.relation.waitModifyClauseIdList || [];
        if (
          this.AutSaAudCurrentStatusEnum.MODIFIED_TERMS_TRIAL_FIRST_PROC ==
          this.autSaAudStatus
        ) {
          let farList = Object.values(this.farMap);
          console.log(this.farMap);
          wholeIds = farList.map(({ clauseId }) => clauseId);
        }
        // let farList = Object.values(this.farMap);
        // console.log(this.farMap);
        // wholeIds = farList.map(({ clauseId }) => clauseId);
        wholeIds = wholeIds.length ? [...new Set([...(this.relation.waitModifyClauseIdList || []), ...wholeIds])] : (this.relation.waitModifyClauseIdList || []);
        // wholeIds = wholeIds.filter(
        //   (id) => !this.fvmMap[id] || this.fvmMap[id].rejected
        // );
      } else if (this.isThird) {
        wholeIds = this.relation.waitModifyClauseIdList || [];
        // console.log(this.relation.waitModifyClauseIdList);
        if (
          this.AutSaAudCurrentStatusEnum
            .REVIEWER_MODIFICATION_TRIAL_SENIOR_REVIEWER_REVIEW ==
          this.autSaAudStatus
        ) {
          let ffrList = Object.values(this.trMap);
          wholeIds = ffrList.map(({ clauseId }) => clauseId);
        }
        // console.log(wholeIds);
        wholeIds = wholeIds.filter(
          (id) => !this.tvmMap[id] || this.tvmMap[id].rejected
        );
      }

      this.$set(this, "distributeClauseIds", wholeIds);
      this.$set(this, "clauseIds", wholeIds);
      this.headerSlotKeys++;

      this.getStandard(this.relation.autCsId, false, true, () => {
        this.questionKeys++;
      });

      if (
        (this.isFirst || this.isSecond || this.isThird) &&
        (this.detail.audSecondTrialSum || this.detail.latestAutSaAudSum)
      ) {
        try {
          let desc = {};
          let { autAdvantage, autEvaluate, autProposal, autImprove } = this.detail;
          desc = {
            autAdvantage,
            autEvaluate,
            autProposal,
            autImprove,
          };
          if (!autAdvantage && !autEvaluate && !autProposal && !autImprove) {
            desc = JSON.parse(this.detail.audSecondTrialSum.autDesc);
          }

          this.$set(
            this,
            "summaryForm",
            Object.assign(this.summaryForm, {
              ...desc,
              autResult: this.detail.autSaAudReport.autSaAudResult,
            })
          );
        } catch (error) {
          // console.log(error);
        }
      }
    },

    handleError() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push({ path: "/fAssessor/m/assessorEvaluationList" });
    },

    submitSummary(back = true) {
      this.$refs.summaryForm.validate(async (valid) => {
        if (!valid) return;
        this.termSubmitLoading = true;
        let submitType = "";
        let { autAdvantage, autEvaluate, autProposal, autImprove } =
          this.summaryForm;
        this.summaryForm.autDesc = JSON.stringify({
          autAdvantage,
          autEvaluate,
          autProposal,
          autImprove,
        });
        if (this.isFirst) {
          submitType = this.AutSaAudCurrentStatusEnum.SR_CLAUSE_M_SUMMARY;
        } else if (this.isSecond) {
          submitType = this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_M_SUMMARY;
        } else if (this.isThird) {
          submitType = this.AutSaAudCurrentStatusEnum.TR_CLAUSE_M_SUMMARY;
        } else if (this.isSubmitConfirm) {
          submitType = this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_M_SUBMIT;
        }

        let data = {
          autCode: this.relation.autCode,
          accountId: this.$store.getters.userId + "",
          submitType,
          autSaAudLists: [this.summaryForm],
        };

        try {
          let res = await this.submit(data);
          // 报错就不提示
          if (!res) return;
          let ids = this.files.map(file => file.fileId)
          if (ids.length) {
            // 评审员上传
            this.shareCreate(ids, this.$store.getters.userId, 'reviewer_evaluation_party', this.detail.hospitalName);
          }

          this.$message({
            type: "success",
            message: "确认成功"
          });
          if (this.isSecond && !back) {
            this.downloadFarReport();
          }
        } finally {
          this.termSubmitLoading = false;
          // if(this.detail)
          if (this.detail.autSaAudStatus == this.AutSaAudCurrentStatusEnum.MODIFICATION_TERMS_SUM) {
            this.init2(
              true,
              this.$route.query.autCode,
              this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_W_M
            );
          } else {
            back && this.handleError();
          }
        }
      });
    },

    downloadFarReport() {
      this.downloadLoading = false;
      downloadPDF(
        {
          filename: `${this.detail.hospitalName}-事实准确性确认表.pdf`,
          autCode: this.relation.autCode,
          ftlTemplateCode: "far_report_reviewer",
          checkStatus: "N",
          leaderOpinion: this.summaryForm.leaderOpinion
        },
        (flag) => {
          if (flag) {
            this.summaryForm.downloaded = 1;
            this.$refs.summaryForm.clearValidate("downloaded");
          }
          this.downloadLoading = false;
        }
      );
    },

    downloadVerifyReport() {
      let fileMap = this.relation.fileDetailMap;
      let fileMapList = Object.values(fileMap);
        let files = fileMapList.find(files => {
          let file = files.find(file => {
            return file.fileType === 'verify_review_report'
          })
          return file
        })
        let file = (files || [])[0];
        download("/common/downloadFile", { fileId: file.fileId },  file.downLoadFileName);
        console.log(file);
    },

    onSuccess(res, file) {
      file.fileId = res.data.fileId;
      this.$set(this.files, 0, file);
      // this.files[0] = file;
      this.setFileIDs();
    },

    onError() {
      if (this.files[0]) {
        this.files.splice(0, 1);
        this.setFileIDs();
      }
    },

    setFileIDs() {
      let fileIds = (this.files.map((file) => file.fileId) || []).join(",");

      this.$set(this.summaryForm, "fileIds", fileIds);
      this.$refs.summaryForm.validateField("fileIds");
    },

    async submitAll() {
      let submitIds = [];

      if (this.isSecond) {
        let fwmList = Object.values(this.fwmMap),
          fvmList = Object.values(this.fvmMap);
        let fwmIds = fwmList.map((data) => data.clauseId);
        let fvmIds = fvmList
          .filter((data) => !data.rejected)
          .map((data) => data.clauseId);
        submitIds = fwmIds.filter((id) => !fvmIds.includes(id));
      } else if (this.isThird) {
        let twmList = Object.values(this.twmMap),
          tvmList = Object.values(this.tvmMap);
        let twmIds = twmList.map((data) => data.clauseId);
        let tvmIds = tvmList
          .filter((data) => !data.rejected)
          .map((data) => data.clauseId);
        submitIds = twmIds.filter((id) => !tvmIds.includes(id));
      }

      let autSaAudLists = submitIds.map((clauseId) => {
        return {
          clauseId,
          autResult: 1,
          autDesc: "",
        };
      });

      let submitType =
        this.AutSaAudCurrentStatusEnum.MODIFIED_TERMS_TRIAL_FIRST_PROC ==
          this.autSaAudStatus
          ? this.AutSaAudCurrentStatusEnum.FAR_V_CLAUSE_M
          : this.AutSaAudCurrentStatusEnum.TR_V_CLAUSE_M;

      let data = {
        autCode: this.relation.autCode,
        accountId: this.$store.getters.userId + "",
        submitType,
        autSaAudLists,
      };

      try {
        let res = await this.submit(data);
        // 报错就不提示
        if (!res) return;
        this.$message({
          type: 'success',
          message: "审核完成"
        });
      } finally {
        this.termSubmitLoading = false;

        if (this.detail.autSaAudStatus == this.AutSaAudCurrentStatusEnum.MODIFIED_TERMS_TRIAL_FIRST_PROC) {

          this.init2(
            true,
            this.$route.query.autCode,
            this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_W_M
          );
        } else {
          this.handleError();
        }
      }
    },
  },
};
</script>

<style lang='scss' scoped>
::v-deep .el-textarea .el-input__count {
  right: 23px;
}
</style>

<style lang='scss' >
.header-right>span::after {
  content: " | ";
}

.header-right>span:last-child::after {
  content: "";
}

.firstModifyShowForm {
  .el-form-item {
    margin-bottom: 0;

    .el-form-item__label {
      border: 1px solid black;
      height: 38px;
    }

    .el-form-item__content {
      text-align: center;
      border: 1px solid black;
      border-left: 0;
      height: 38px;
    }

    &+.el-form-item {
      .el-form-item__label {
        border-top: 0;
      }

      .el-form-item__content {
        border-top: 0;
      }
    }
  }

  .getFileDateLabel {
    .el-form-item__label {
      line-height: 18px;
      // border-top: 1px solid black !important;
      border-left: 0;
    }
  }

  .emptyLabel {
    .el-form-item__label {
      height: 38px;
    }
  }
}
</style>
