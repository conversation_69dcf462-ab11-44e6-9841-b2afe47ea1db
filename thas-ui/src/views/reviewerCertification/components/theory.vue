<!--  -->
<template>
  <div class='theory'>
    <div class="test-scores">
      <el-card shadow="never">
        <div slot="header" class="clearfix">
          <div style="float:left;">
            <h4 style="margin:0">线上学习</h4>
          </div>
        </div>
        <el-table :data="dataSource" border>
          <el-table-column label="学习资源" align="center" prop="title">
            <template slot-scope="scope">
              <span @click="scope.row.status != 1 && toLearn(scope.row.learnResourceId)" :style="{
              color: scope.row.status != 1 ? '#409EFF' : '',
              cursor: scope.row.status != 1 ? 'pointer' : '',
            }">{{ scope.row.title }}</span>
            </template>
          </el-table-column>
          <el-table-column label="考卷名称" align="center" prop="paperName" />
          <el-table-column label="通过状态" align="center" prop="status">
            <template slot-scope="scope">
              <span v-if="scope.row.status == 1" style="color: green">通过</span>
              <span v-else-if="scope.row.status == 0" style="color: red">未通过</span>
              <span v-else>还未考试</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <div class="training-records">
      <el-card shadow="never">
        <div slot="header" class="clearfix">
          <div style="float:left;">
            <h4 style="margin:0">线下培训</h4>
          </div>
        </div>
        <el-table ref="multipleTable" :data="dataSource2" stripe border :row-key="getKey" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" :reserve-selection="true">
          </el-table-column>
          <el-table-column label="培训名称" prop="trainingName"></el-table-column>
          <el-table-column label="举办时间" prop="trainingTime"></el-table-column>
          <el-table-column label="举办地点" prop="trainingLocation"></el-table-column>
          <!-- <el-table-column label="培训类型" prop="trainingType"></el-table-column> -->
          <el-table-column label="举办方" prop="organizers"></el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query2" />
      </el-card>
    </div>

  </div>
</template>

<script>
import request from "@/utils/request";

export default {
  name: '',
  components: {},
  props: {},
  watch: {},
  data() {
    return {
      url: {
        list: "/system/exam/list",
        trainList: "/reviewer/query/cstOffline/training",
        submit: "/reviewer/offline/training/submit",
      },
      dataSource: [],
      dataSource2: [],
      selectedIds: [],
      selectedIdsInList: [],
      queryData: {
        pageSize: 10,
        pageNum: 1,
      },
      total: 0,
    };
  },

  computed: {
    lastOne() {
      return (index) => this.formData.list.length === index + 1;
    },
  },

  created() { },

  mounted() {
    this.query();
    this.query2();
  },

  methods: {
    checkboxT(row) {
      if (row.status == 0) {
        return 1
      } else {
        return 0
      }
    },

    query() {
      request({
        url: this.url.list,
        method: "get",
        params: this.queryData,
      }).then((res) => {
        this.dataSource = (res.rows || []).filter((li) => !!li);
      });
    },

    query2() {
      request({
        url:
          this.url.trainList +
          `?pageNum=${this.queryData.pageNum}&pageSize=${this.queryData.pageSize}`,
        method: "post",
        data: {
          accountId: this.$store.getters.userId + "",
          filterIs: "5",
          ...this.queryData,
        },
      })
        .then((res) => {
          if (res.code == 200) {
            let fields = ['trainingName',
              'trainingLocation',
              'trainingType',
              'organizers',]
            this.dataSource2 = res.rows.map(row => {
              for (const key in row) {
                if (Object.hasOwnProperty.call(row, key)) {
                  if (fields.includes(key)) {
                    row[key] = row[key] || '--'
                  }
                }
              }

              return row;
            });
            this.total = res.total;
            const selects = this.dataSource.filter((item) => {
              return item.participatesTraining == 0;
            });
            selects.forEach((row) => {
              if (!this.selectedIds.includes(row.id)) {
                if (!this.selectedIdsInList.includes(row.id)) {
                  this.selectedIdsInList.push(row.id);
                  this.$refs.multipleTable.toggleRowSelection(row);
                }
              }
            });
          }
        })
        .catch((error) => {
          // console.log(error);
        });
    },


    getKey(row) {
      return row.id;
    },


    async pass() {
      return request({
        url: "/system/exam/pass",
        method: "get",
      })
    },

    async getValue() {
      try {
        if (!this.dataSource.length) {
          this.$message({
            type: 'error',
            message: '请联系管理员发布考卷，并全部考试通过！'
          })
          return false;
        }
        await this.pass();
        let res2 = await this.submit();
        return res2 && true;
      } catch (error) {
        this.query()
        return false;
      }
    },

    handleSelectionChange(val) {
      this.selectedIds = val.map((item) => item.id);
    },

    submit() {
      if (!this.selectedIds.length) {
        this.$message({
          type: "warning",
          message: "至少需要选择一个线下培训项目！",
        });
        return false;
      }

      return request({
        url: '/reviewer/offline/training/apply',
        method: 'post',
        data: this.selectedIds.map(id => ({
          trainingId: `${id}`,
          applyStatus: '1'
        }))
      })
    },



    toLearn(learnResourceId) {
      this.$router.push({
        path: '/learningMaterials/courseDetail',
        query: {
          id: learnResourceId
        }
      })
    },
  }
}

</script>
<style lang='scss' scoped>
.test-scores,
.training-records {
  padding: 24px;
}
</style>