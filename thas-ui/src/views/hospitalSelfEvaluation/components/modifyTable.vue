<!--  -->
<template>
  <div class="">
    <el-table :key="keys" :data="dataSource" border :span-method="spanMethod" v-bind="$attrs">
      <el-table-column label="" prop="title" :width="small ? 80 : 140" align="center"></el-table-column>
      <el-table-column label="章" prop="chapterNo" :width="small ? 80 : 120" align="center"></el-table-column>
      <el-table-column label="节" prop="sectionNo" :width="small ? 90 : 130" align="center"></el-table-column>
      <el-table-column label="条" prop="articleNo" :width="small ? 90 : 140" align="center"></el-table-column>
      <el-table-column label="款" prop="clause" align="center" :width="small ? 90 : undefined"></el-table-column>
      <slot></slot>
    </el-table>
  </div>
</template>

<script>
import { getStandard } from "@/api/system/standard";

export default {
  name: "ModifyTable",
  components: {},
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    versionId: {
      type: [String, Number],
      default: () => "",
    },
    showAll: {
      type: Boolean,
      default: () => true,
    },
    split: {
      type: Boolean,
      default: () => false,
    },
    small: {
      type: Boolean,
      default: () => false,
    }
  },
  watch: {
    data: {
      immediate: true,
      deep: true,
      handler() {
        if (Array.isArray(this.data)) {
          this.$nextTick(async () => {
            this.simpleShow();
          });
        }
      },
    },
  },
  data() {
    return {
      dataSource: [],
      basicList: [],
      nonBasicList: [],
      basicColumnSpans: [],
      NonBasicColumnSpans: [],
      keys: 0,
      basicOtherSize: [],
      nonBasicOtherSize: [],
    };
  },

  computed: {},

  created() { },

  mounted() { },

  methods: {
    /**
     * 只要在 不需要展示全部以及不需要展示其他新增列的情况使用
     */
    async simpleShow() {
      const versionId = this.versionId || this.$store.getters.standardBaseVersionId;
      await getStandard(versionId);

      const clauseMap = this.$store.getters.standardTypeItemByVersionId(versionId, "clause", "map") || {};

      let clauseList = this.data.map(id => clauseMap[id]);

      let basicClauseList = clauseList.filter(data => {
        return data.isStar == 1
      })
      let nonBasicClauseList = clauseList.filter(data => {
        return data.isStar != 1
      })


      let basicSource = this.abc(basicClauseList, 1);
      let nonBasicSource = this.abc(nonBasicClauseList, 0);

      let basicOtherSize = [{ 0: basicSource.length }, {}, {}, {}], nonBasicOtherSize = [{ 0: nonBasicSource.length }, {}, {}, {}];

      basicSource.forEach(data => {
        let { chapterId, sectionId, articleId, } = data
        let cMap = basicOtherSize[1], sMap = basicOtherSize[2], aMap = basicOtherSize[3];
        !cMap[chapterId] ? cMap[chapterId] = 1 : cMap[chapterId] += 1
        !sMap[sectionId] ? sMap[sectionId] = 1 : sMap[sectionId] += 1
        !aMap[articleId] ? aMap[articleId] = 1 : aMap[articleId] += 1
      })

      nonBasicSource.forEach(data => {
        let { chapterId, sectionId, articleId } = data
        let cMap = nonBasicOtherSize[1], sMap = nonBasicOtherSize[2], aMap = nonBasicOtherSize[3];
        !cMap[chapterId] ? cMap[chapterId] = 1 : cMap[chapterId] += 1
        !sMap[sectionId] ? sMap[sectionId] = 1 : sMap[sectionId] += 1
        !aMap[articleId] ? aMap[articleId] = 1 : aMap[articleId] += 1
      })

      this.basicOtherSize = basicOtherSize;
      this.nonBasicOtherSize = nonBasicOtherSize;
      this.dataSource = [...basicSource, ...nonBasicSource]
    },


    abc(clauseList, basic) {
      const versionId = this.versionId || this.$store.getters.standardBaseVersionId;
      let title = clauseList.length;

      const chapterMap = this.$store.getters.standardTypeItemByVersionId(versionId, "chapter", "map") || {};
      const sectionMap = this.$store.getters.standardTypeItemByVersionId(versionId, "section", "map") || {};
      const articleMap = this.$store.getters.standardTypeItemByVersionId(versionId, "article", "map") || {};

      let dataSource = clauseList.map(clause => {
        let chapter = chapterMap[clause.chapterId]
        let section = sectionMap[clause.sectionId]
        let article = articleMap[clause.articleId]

        return {
          chapterId: clause.chapterId,
          sectionId: clause.sectionId,
          articleId: clause.articleId,
          clauseId: clause.clauseId,
          chapterNo: chapter.chapterNo,
          sectionNo: section.sectionNo,
          articleNo: article.articleNo,
          clause: clause.clauseNo,
          title: `${basic == 1 ? '' : '非'}基本款（${title}款）`
        }
      })


      let finalSource = []

      if (!this.split) {
        let articleMapList = {}
        dataSource.forEach(data => {
          let { articleId } = data;
          if (!articleMapList[articleId]) {
            articleMapList[articleId] = [data]
          } else {
            articleMapList[articleId].push(data);
          }
        })
        finalSource = Object.values(articleMapList).map(list => {
          if (list.length > 1) {
            let clauseNos = list.map(({ clause }) => clause).join(' , ')
            list[0].clause = clauseNos;
            return list[0]
          } else {
            return list[0]
          }
        })
      } else {
        finalSource = dataSource;
      }

      return (finalSource || []).sort((a, b) => {
        return Number(a.clauseId) - Number(b.clauseId)
      })
    },

    spanMethod({ rowIndex, columnIndex }) {
      if (this.showAll) {
        return this.isShowAll(rowIndex, columnIndex);
      } else {
        return this.notShowAll2(rowIndex, columnIndex);
      }
    },

    notShowAll2(rowIndex, columnIndex) {
      if (this.basicOtherSize[0][0]) {
        // 存在基本款
        if (columnIndex == 0) {
          if (rowIndex == 0) {
            return [this.basicOtherSize[0][0], 1]
          } else if (rowIndex == this.basicOtherSize[0][0]) {
            return [this.nonBasicOtherSize[0][0], 1]
          }
          return [0, 1]
        } else {
          if (columnIndex >= 1 && columnIndex <= 3) {
            let map1 = this.basicOtherSize[columnIndex];
            let map2 = this.nonBasicOtherSize[columnIndex];
            let total1 = Object.values(map1).reduce((sum, val) => sum + val, 0);
            let total2 = rowIndex < total1 ? 0 : total1;

            let map = rowIndex < total1 ? map1 : map2;
            if (map) {
              let list = Object.values(map);
              let list2 = [0];
              list.forEach(val => {
                list2.push(list2[list2.length - 1] + val)
              })
              for (let index = 0; index < list2.length; index++) {
                if (rowIndex === list2[index] + total2) {
                  return [list[index] || 0, 1]
                }
              }
            }
            return [0, 1]
          }
        }
      } else if (this.nonBasicOtherSize[0][0]) {
        // 不存在基本款
        if (columnIndex == 0) {
          if (rowIndex == 0) {
            return [this.nonBasicOtherSize[0][0], 1]
          }
          return [0, 1]
        } else {
          if (columnIndex >= 1 && columnIndex <= 3) {
            let map = this.nonBasicOtherSize[columnIndex];
            if (map) {
              let list = Object.values(map);
              let list2 = [0];
              list.forEach(val => {
                list2.push(list2[list2.length - 1] + val)
              })
              for (let index = 0; index < list2.length; index++) {
                if (rowIndex === list2[index]) {
                  return [list[index] || 0, 1]
                }
              }
            }
            return [0, 1]
          }
        }
      }
    },

    notShowAll(rowIndex, columnIndex) {
      if (columnIndex == 0) {
        if (this.basicList.length != 0) {
          if (rowIndex == 0) {
            return [this.basicList.length, 1];
          } else if (rowIndex === this.basicList.length) {
            return [this.nonBasicList.length, 1];
          }
        } else if (this.nonBasicList.length != 0) {
          if (rowIndex == 0) {
            return [this.nonBasicList.length, 1];
          }
        }
        return [1, 0];
      } else {
        if (columnIndex == 1) {
          let data = this.basicColumnSpans[0];
          let data2 = this.NonBasicColumnSpans[0];
          let keys2 = Object.keys(data2);
          let back = [];
          let keys = Object.keys(data);
          let flag = false;
          for (let i = 0; i < keys.length; i++) {
            const element = data[keys[i]];
            if (rowIndex == element[0]) {
              back = [element[1], 1];
              flag = true;
              break;
            } else {
              back = [1, 0];
              flag = false;
            }
          }
          if (!flag) {
            for (let i = 0; i < keys2.length; i++) {
              const element = data2[keys2[i]];
              if (rowIndex == element[0]) {
                back = [element[1], 1];
                break;
              } else {
                back = [1, 0];
              }
            }
          }
          return back;
        } else if (columnIndex == 2) {
          let data = this.basicColumnSpans[1];
          let data2 = this.NonBasicColumnSpans[1];
          let back = [];
          let keys = Object.keys(data);
          let keys2 = Object.keys(data2);
          let flag = false;
          for (let i = 0; i < keys.length; i++) {
            const element = data[keys[i]];
            if (rowIndex == element[0]) {
              back = [element[1], 1];
              flag = true;
              break;
            } else {
              back = [1, 0];
              flag = false;
            }
          }
          if (!flag) {
            for (let i = 0; i < keys2.length; i++) {
              const element = data2[keys2[i]];
              if (rowIndex == element[0]) {
                back = [element[1], 1];
                break;
              } else {
                back = [1, 0];
              }
            }
          }
          return back;
        }
      }
    },

    isShowAll(rowIndex, columnIndex) {
      if (columnIndex == 0) {
        if (rowIndex == 0) {
          return [this.basicList.length, 1];
        } else if (rowIndex == this.basicList.length) {
          return [this.nonBasicList.length, 1];
        }
        return [1, 0];
      }
      if (columnIndex == 1) {
        let data = this.basicColumnSpans[0];
        let data2 = this.NonBasicColumnSpans[0];
        let keys2 = Object.keys(data2);
        let back = [];
        let keys = Object.keys(data);
        let flag = false;
        for (let i = 0; i < keys.length; i++) {
          const element = data[keys[i]];
          if (rowIndex == element[0]) {
            back = [element[1], 1];
            flag = true;
            break;
          } else {
            back = [1, 0];
            flag = false;
          }
        }
        if (!flag) {
          for (let i = 0; i < keys2.length; i++) {
            const element = data2[keys2[i]];
            if (rowIndex == element[0]) {
              back = [element[1], 1];
              break;
            } else {
              back = [1, 0];
            }
          }
        }
        return back;
      } else if (columnIndex == 2) {
        let data = this.basicColumnSpans[1];
        let data2 = this.NonBasicColumnSpans[1];
        let back = [];
        let keys = Object.keys(data);
        let keys2 = Object.keys(data2);
        let flag = false;
        for (let i = 0; i < keys.length; i++) {
          const element = data[keys[i]];
          if (rowIndex == element[0]) {
            back = [element[1], 1];
            flag = true;
            break;
          } else {
            back = [1, 0];
            flag = false;
          }
        }
        if (!flag) {
          for (let i = 0; i < keys2.length; i++) {
            const element = data2[keys2[i]];
            if (rowIndex == element[0]) {
              back = [element[1], 1];
              break;
            } else {
              back = [1, 0];
            }
          }
        }
        return back;
      }
    },
  },
};
</script>
<style lang="scss" scoped></style>
