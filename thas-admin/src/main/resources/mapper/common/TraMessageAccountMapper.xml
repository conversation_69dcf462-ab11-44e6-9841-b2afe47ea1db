<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.TraMessageAccountMapper">
    <insert id="add">
        INSERT INTO `tra_message_account`(`message_id`, `account`)
        VALUES (#{messageId}, #{account});
    </insert>

    <delete id="delete">
        delete
        from tra_message_account
        where account = #{account}
          and message_id = #{messageId};
    </delete>

    <select id="getByAccount" resultType="com.thas.web.domain.TraMessageAccount">
        select *
        from `tra_message_account`
        where message_id = #{messageId}
          and account = #{account};
    </select>
</mapper>