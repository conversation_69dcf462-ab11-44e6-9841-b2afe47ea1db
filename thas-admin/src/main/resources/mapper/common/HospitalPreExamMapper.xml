<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.HospitalPreExamMapper">

    <resultMap type="com.thas.web.domain.HospitalPreExam" id="HospitalPreExamResult">
        <result property="id" column="id"/>
        <result property="applyNo" column="apply_no"/>
        <result property="preExamId" column="pre_exam_id"/>
        <result property="clauseList" column="clause_list"/>
        <result property="groupIdList" column="group_id_list"/>
        <result property="leaderIs" column="leader_is"/>
        <result property="status" column="status"/>
        <result property="creator" column="creator"/>
        <result property="createTime" column="create_time"/>
        <result property="updater" column="updater"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectHospitalPreExamVo">
        select id, apply_no, pre_exam_id, clause_list, group_id_list, leader_is, status, creator, create_time, updater, update_time from hospital_pre_exam
    </sql>

    <sql id="SQL_AES_ENCRYPT">'${@com.thas.web.config.SqlAesEncryptConfig@SQL_AES_ENCRYPT}'</sql>

    <select id="selectHospitalPreExamList" parameterType="HospitalPreExam" resultMap="HospitalPreExamResult">
        <include refid="selectHospitalPreExamVo"/>
        <where>
            <if test="applyNo != null  and applyNo != ''">and apply_no = #{applyNo}</if>
            <if test="preExamId != null  and preExamId != ''">and pre_exam_id = #{preExamId}</if>
            <if test="clauseList != null  and clauseList != ''">and clause_list = #{clauseList}</if>
            <if test="leaderIs != null ">and leader_is = #{leaderIs}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="creator != null  and creator != ''">and creator = #{creator}</if>
            <if test="updater != null  and updater != ''">and updater = #{updater}</if>
        </where>
    </select>

    <select id="selectHospitalPreExamByApplyNoAndAccountId" parameterType="HospitalPreExam"
            resultMap="HospitalPreExamResult">
        <include refid="selectHospitalPreExamVo"/>
        where apply_no = #{applyNo} and pre_exam_id = #{preExamId} and status = 1
    </select>

    <select id="selectHospitalPreExamById" parameterType="Long" resultMap="HospitalPreExamResult">
        <include refid="selectHospitalPreExamVo"/>
        where id = #{id}
    </select>

    <select id="selectHosPreExamByLeader" resultMap="HospitalPreExamResult">
        <include refid="selectHospitalPreExamVo"/>
        where apply_no = #{applyNo} and leader_is = #{leaderIs} and status = 1
    </select>

    <select id="selectHospitalPreExamByApplyNo" parameterType="String" resultMap="HospitalPreExamResult">
        <include refid="selectHospitalPreExamVo"/>
        where apply_no = #{ApplyNo} and status = 1;
    </select>

    <select id="selectHosPlanUserInfoByAccountId" parameterType="HospitalPreExam"
            resultType="com.thas.web.dto.HosPlanUserInfoVO">
        select
        user_id accountId,
        AES_DECRYPT(unhex(nick_name),<include refid="SQL_AES_ENCRYPT"/>) name
        from sys_user
        where user_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.preExamId}
        </foreach>
    </select>


    <insert id="insertHospitalPreExam" parameterType="HospitalPreExam" useGeneratedKeys="true" keyProperty="id">
        insert into hospital_pre_exam
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">apply_no,</if>
            <if test="preExamId != null and preExamId != ''">pre_exam_id,</if>
            <if test="clauseList != null and clauseList != ''">clause_list,</if>
            <if test="groupIdList != null and groupIdList != ''">group_id_list,</if>
            <if test="leaderIs != null ">leader_is,</if>
            <if test="status != null">status,</if>
            <if test="creator != null and creator != ''">creator,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updater != null and updater != ''">updater,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">#{applyNo},</if>
            <if test="preExamId != null and preExamId != ''">#{preExamId},</if>
            <if test="clauseList != null and clauseList != '' ">#{clauseList},</if>
            <if test="groupIdList != null and groupIdList != ''">#{groupIdList},</if>
            <if test="leaderIs != null ">#{leaderIs},</if>
            <if test="status != null">#{status},</if>
            <if test="creator != null and creator != ''">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updater != null and updater != ''">#{updater},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateHospitalPreExam" parameterType="HospitalPreExam">
        update hospital_pre_exam
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyNo != null and applyNo != ''">apply_no = #{applyNo},</if>
            <if test="preExamId != null and preExamId != ''">pre_exam_id = #{preExamId},</if>
            <if test="clauseList != null">clause_list = #{clauseList},</if>
            <if test="groupIdList != null and groupIdList != ''">group_id_list = #{groupIdList},</if>
            <if test="leaderIs != null ">leader_is = #{leaderIs},</if>
            <if test="status != null">status = #{status},</if>
            <if test="creator != null and creator != ''">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>


    <update id="updateHospitalPreExamByApplyNo" parameterType="HospitalPreExam">
        update hospital_pre_exam
        <trim prefix="SET" suffixOverrides=",">
            <if test="preExamId != null and preExamId != ''">pre_exam_id = #{preExamId},</if>
            <if test="clauseList != null">clause_list = #{clauseList},</if>
            <if test="leaderIs != null ">leader_is = #{leaderIs},</if>
            <if test="status != null">status = #{status},</if>
            <if test="creator != null and creator != ''">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where apply_no = #{applyNo} and status = 1
    </update>

    <update id="updateHospitalPreExamByApplyNoList">
        update hospital_pre_exam set status = 0 where apply_no in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and status = 1
    </update>

    <delete id="deleteHospitalPreExamById" parameterType="Long">
        delete from hospital_pre_exam where id = #{id}
    </delete>

    <delete id="deleteHospitalPreExamByIds" parameterType="String">
        delete from hospital_pre_exam where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteHospitalPreExamByPreExamIdsAndApplyNo">
        delete from hospital_pre_exam where pre_exam_id in
        <foreach item="item" index="index" collection="preExamIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        and apply_no = #{applyNo}
        and status = 1
    </delete>

    <select id="selectApplyNosByPreExamId" parameterType="String" resultType="com.thas.web.domain.HospitalBaseInfo">
        select distinct t.apply_no applyNo, ttt.hospital_name hospitalName
        from hospital_pre_exam t
                 left join hospital_planned_distribution tt on t.apply_no = tt.apply_no and tt.status = 1
                 left join hospital_base_info ttt on ttt.apply_no = t.apply_no and ttt.status = 1
        where t.pre_exam_id = #{preExamId}
          and t.status = 1
          and tt.pre_dis_complete = 1
    </select>

    <select id="queryReviewManage" parameterType="com.thas.web.dto.ReviewManageDTO"
            resultType="com.thas.web.dto.ReviewManageVO">
        SELECT
        hbi.apply_no applyNo,
        hbi.hospital_name hospitalName,
        hpe.pre_exam_id accountId,
        AES_DECRYPT(unhex(su.nick_name),<include refid="SQL_AES_ENCRYPT"/>) reviewerName,
        AES_DECRYPT(unhex(su.phonenumber),<include refid="SQL_AES_ENCRYPT"/>) mobile,
        AES_DECRYPT(unhex(sy.nick_name),<include refid="SQL_AES_ENCRYPT"/>) disPersonName,
        hpe.leader_is leaderIs,
        hpe.create_time disTime,
        su.is_leave leaveIs
        FROM
        hospital_pre_exam hpe
        LEFT JOIN hospital_base_info hbi ON hpe.apply_no = hbi.apply_no
        LEFT JOIN sys_user su ON hpe.pre_exam_id = su.user_id
        LEFT JOIN sys_user sy ON hpe.creator = sy.user_id
        <where>
            hpe.status = 1 and hbi.auth_status = 2 and hbi.status = 1
            <if test="hospitalName != null  and hospitalName != ''">and hbi.hospital_name like concat('%',
                #{hospitalName}, '%')
            </if>
        </where>
    </select>

    <select id="selectStatusByPreExamId" parameterType="Long" resultType="int">
        SELECT COUNT(1) FROM hospital_pre_exam hpe
        LEFT JOIN aut_sa_relation asr ON hpe.apply_no = asr.hospital_apply_no
        WHERE
        hpe.`status` = 1
        AND asr.`status` = 1
        AND hpe.pre_exam_id = #{preExamId}
        AND asr.aut_status != '050202'
    </select>
    <select id="selectHospitalPreExamIdsByApplyNo" resultType="java.lang.String">
        SELECT pre_exam_id FROM hospital_pre_exam WHERE apply_no = #{applyNo}
        AND `status` = 1
    </select>

    <select id="workPreExam" resultType="com.thas.web.dto.ReviewCycleJudgeVo">
        SELECT hpe.apply_no applyNo ,hpe.pre_exam_id reviewId,hpe.leader_is leaderIs,`asc`.cycle_stage cycleStage
        FROM hospital_pre_exam hpe
        LEFT JOIN aut_sa_relation asr ON asr.hospital_apply_no = hpe.apply_no
        LEFT JOIN asa_status_config `asc` ON `asc`.current_status = asr.aut_status
        WHERE
        hpe.`status` = 1 AND
        asr.`status` = 1 AND
        `asc`.current_status != 050202 AND
        hpe.pre_exam_id IN
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>

    </select>


</mapper>
