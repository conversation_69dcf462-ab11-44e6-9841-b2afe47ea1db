<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.MessageSendUserMapper">

    <resultMap type="MessageSendUser" id="MessageSendUserResult">
        <result property="sendMessageId"    column="send_message_id"    />
        <result property="userId"    column="user_id"    />
    </resultMap>

    <sql id="selectMessageSendUserVo">
        select send_message_id, user_id from message_send_user
    </sql>

    <select id="selectMessageSendUserList" parameterType="MessageSendUser" resultMap="MessageSendUserResult">
        <include refid="selectMessageSendUserVo"/>
        <where>
        </where>
    </select>

    <select id="selectMessageSendUserBySendMessageId" parameterType="Long" resultMap="MessageSendUserResult">
        <include refid="selectMessageSendUserVo"/>
        where send_message_id = #{sendMessageId}
    </select>

    <insert id="insertMessageSendUser" parameterType="MessageSendUser"  useGeneratedKeys="true" keyProperty="id">
        insert into message_send_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sendMessageId != null">send_message_id,</if>
            <if test="userId != null">user_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sendMessageId != null">#{sendMessageId},</if>
            <if test="userId != null">#{userId},</if>
         </trim>
    </insert>

    <update id="updateMessageSendUser" parameterType="MessageSendUser">
        update message_send_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
        </trim>
        where send_message_id = #{sendMessageId}
    </update>

    <delete id="deleteMessageSendUserBySendMessageId" parameterType="Long">
        delete from message_send_user where send_message_id = #{sendMessageId}
    </delete>

    <delete id="deleteMessageSendUserBySendMessageIds" parameterType="String">
        delete from message_send_user where send_message_id in
        <foreach item="sendMessageId" collection="array" open="(" separator="," close=")">
            #{sendMessageId}
        </foreach>
    </delete>

    <insert id="insertMessageSendUserList" parameterType="list">
        insert into message_send_user(send_message_id,user_id) values
        <foreach collection="list" item="item" separator=",">
            (#{item.sendMessageId},#{item.userId})
        </foreach>
    </insert>
</mapper>
