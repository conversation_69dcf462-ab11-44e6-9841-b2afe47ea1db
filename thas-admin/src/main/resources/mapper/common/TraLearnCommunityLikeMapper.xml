<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.TraLearnCommunityLikeMapper">

    <resultMap type="TraLearnCommunityLike" id="TraLearnCommunityLikeResult">
        <result property="id" column="id"/>
        <result property="communityId" column="community_id"/>
        <result property="creator" column="creator"/>
    </resultMap>

    <sql id="selectTraLearnCommunityLikeVo">
        select id, community_id, creator
        from tra_learn_community_like
    </sql>

    <select id="selectTraLearnCommunityLikeList" parameterType="TraLearnCommunityLike"
            resultMap="TraLearnCommunityLikeResult">
        <include refid="selectTraLearnCommunityLikeVo"/>
        <where>
            <if test="communityId != null ">and community_id = #{communityId}</if>
            <if test="creator != null  and creator != ''">and creator = #{creator}</if>
        </where>
    </select>

    <select id="selectTraLearnCommunityLikeById" parameterType="Long" resultMap="TraLearnCommunityLikeResult">
        <include refid="selectTraLearnCommunityLikeVo"/>
        where id = #{id}
    </select>
    <select id="getCountByCommunityId" resultType="java.lang.Integer">
        select count(1)
        from tra_learn_community_like
        where community_id = #{communityId}
    </select>

    <insert id="insertTraLearnCommunityLike" parameterType="TraLearnCommunityLike">
        insert into tra_learn_community_like
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="communityId != null">community_id,</if>
            <if test="creator != null and creator != ''">creator,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="communityId != null">#{communityId},</if>
            <if test="creator != null and creator != ''">#{creator},</if>
        </trim>
    </insert>

    <update id="updateTraLearnCommunityLike" parameterType="TraLearnCommunityLike">
        update tra_learn_community_like
        <trim prefix="SET" suffixOverrides=",">
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="creator != null and creator != ''">creator = #{creator},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTraLearnCommunityLikeById" parameterType="Long">
        delete
        from tra_learn_community_like
        where id = #{id}
    </delete>

    <delete id="deleteTraLearnCommunityLikeByIds" parameterType="String">
        delete from tra_learn_community_like where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteTraLearnCommunityLike" parameterType="TraLearnCommunityLike">
        delete
        from tra_learn_community_like
        where community_id = #{communityId}
          and creator = #{creator}
    </delete>
</mapper>