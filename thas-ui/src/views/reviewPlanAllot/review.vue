<template>
  <div class="review-plan-allot-trail" v-if="showAll">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>医院基本详情</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="toHospitalDetail">详细信息
          <i class="el-icon-arrow-right" />
        </el-button>
      </div>
      <detail-message :form="hospitalForm"></detail-message>
    </el-card>

    <el-card v-if="allot.reviewDisComplete != 2" class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>评审员利益冲突</span>
      </div>
      <div>

        <el-table class="interest-table" ref="interestTable" :data="hosReviewClauseNumInfos" border @selection-change="selectDeletePersons">
          <el-table-column v-if="allFileSubmit" type="selection"></el-table-column>
          <el-table-column label="评审员姓名" prop="name" width="150"></el-table-column>
          <el-table-column label="是否存在利益冲突" width="150">
            <template slot-scope="{row}">
              <span v-if="row.hasInterest == 1">是</span>
              <span v-else-if="row.hasInterest == 0 && row.interestFileId">否</span>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column label="利益冲突表">
            <template slot-scope="{row}">
              <div v-if="row.interestFileId || row.adminInterestFileId">
                <div>{{ fileName(row.interestFileId) }}</div>
                <div>{{ fileName(row.adminInterestFileId) }}</div>
              </div>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template slot-scope="{row}">
              <el-button v-if="row.interestFileId && downLoadFileName(row.interestFileId)" type="text" size="mini"
                @click="download(row.interestFileId)">下载利益冲突表（评审员上传）</el-button>
              <el-button v-if="row.adminInterestFileId && downLoadFileName(row.adminInterestFileId)" type="text" size="mini"
                @click="download(row.adminInterestFileId)">下载利益冲突表（管理员上传）</el-button>
              <el-button v-if="(row.interestFileId && downLoadFileName(row.interestFileId)) && !(row.adminInterestFileId && downLoadFileName(row.adminInterestFileId))" type="text" size="mini" @click="uploadInterestSheet(row)" :disabled="uploading" v-loading="row.accountId === reviewerId && uploading">上传盖章后的利益冲突表</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-button v-if="allFileSubmit" :disabled="!checkDeletePersons.length && allFileSubmit" size="mini" type="primary"
          @click="revocation" style="margin-top:12px;">更换评审员</el-button>

        <!-- <el-checkbox-group v-model="checkDeletePersons">
          <div class="conflict_of_interest" v-for="(person,index) in hosReviewClauseNumInfos" :key="index" style="margin-top:10px;">
            <el-checkbox v-if="allFileSubmit" :key="person.accountId" :label="person.accountId">{{person.name}}</el-checkbox>
            <span v-else style="font-size:14px;">{{person.name}}</span>
            <el-button v-if="person.interestFileId && fileName(person.interestFileId)" type="text" size="mini" style="margin-left: 10px;color:blue;cursor:pointer;">{{fileName(person.interestFileId)}}</el-button>
            <el-button v-if="person.interestFileId && fileName(person.interestFileId)" type="text" size="mini" style="margin-left: 10px;" @click="download(person.interestFileId, person.name)">下载利益冲突表</el-button>
          </div>
        </el-checkbox-group> -->

        <el-table :data="hosReviewers" :key="hosReviewers.length" border style="margin-top:15px;">
          <el-table-column label="分组" align="center" width="120">
            <b slot="header" style="color:black;">分组</b>
            <template>
              <b style="color:black;">评审员姓名</b>
            </template>
          </el-table-column>
          <el-table-column v-for="(domain, index) in allDomainListVOList" :key="index" :label="domain.domainName"
            :prop="domain.id" align="center">
            <span slot="header" style="color:black;font-weight: lighter;">{{ domain.domainName }}</span>
            <template slot-scope="{row}">
              {{ row[domain.id] || '(未分配)' }}
            </template>
          </el-table-column>
        </el-table>

      </div>
    </el-card>

    <el-card v-if="allot.reviewDisComplete == 2" class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>分配评审员</span>
      </div>
      <div style="display:flex;">
        <div style=" margin-right: 24px;">
          <span style="font-size: 14px;"> 分组：</span>
          <el-select v-model.trim="domainId" @change="domainChange" clearable filterable>
            <el-option v-for="(domain, index) in allDomainListVOList" :key="index" :label="domain.groupName"
              :value="domain.id"></el-option>
          </el-select>
        </div>
        <div style=" margin-right: 24px;">
          <span style="font-size: 14px;">评审员：</span>
          <el-select v-model.trim="userId" clearable filterable>
            <el-option v-for="(user, index) in userList" :key="index" :label="user.name"
              :value="user.accountId"></el-option>
          </el-select>
        </div>
        <div>
          <el-button type="primary" @click="beforeSubmit">保存</el-button>
          <el-button type="primary" @click="submit2All">保存为全部(测试)</el-button>
          <el-button type="primary" :disabled="!!unDomainListVOList.length" @click="beforeConfirm">确认</el-button>
        </div>
      </div>

      <el-table :data="hosReviewers" :key="hosReviewers.length" border style="margin-top:15px;width:700px">
        <el-table-column label="分组" align="center">
          <b slot="header" style="color:black;">分组</b>
          <template>
            <b style="color:black;">评审员姓名</b>
          </template>
        </el-table-column>
        <el-table-column v-for="(domain, index) in allDomainListVOList" :key="index" :label="domain.domainName"
          :prop="domain.id" align="center" width="100">
          <span slot="header" style="color:black;font-weight: lighter;">{{ domain.domainName }}</span>
          <template slot-scope="{row}">
            {{ row[domain.id] || '(未分配)' }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-card class="box-card" shadow="never" v-if="allot.reviewDisComplete == 2 && revFile">
      <div slot="header" class="clearfix">评审员名单确认表</div>
      {{ revFile.fileName }}
      <el-button type="text" @click="preview(revFile.url)" style="margin-left:15px;">预览</el-button>
      <el-button type="text" @click="download2(revFile.fileId, revFile.downLoadFileName)" style="margin-left:15px;">下载</el-button>
    </el-card>

    <!--  && allFileSubmit -->
    <el-card v-if="getData && unDomainListVOList.length == 0 && allFileSubmit" class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>选择评审组长</span>
      </div>
      <el-radio-group v-model.trim="leader">
        <el-radio v-for="(item, index) in hosReview" :key="index" :label="item.accountId">{{ item.name }}</el-radio>
      </el-radio-group>
      <div v-show="isFitLeader" class="leader-tip">该评审员的带教培训评估结果为“适合当评审员，不考虑培训为评审组长”，请确认是否仍选择该评审员为评审组长？</div>
      <div style="margin-top: 20px">
        <el-button @click="setLeader" type="primary">保存</el-button>
      </div>
    </el-card>

    <el-card class="box-card" shadow="never" v-if="unDomainListVOList.length">
      <div slot="header" class="clearfix">
        <span>待分配分组({{ unDomainListVOList.length }})</span>

        <template v-if="hosReview.length === 0">
          <el-button :disabled="toggleOrInapplicabilityList.length === 0" type="primary" size="mini" @click="showToggle"
            style="padding: 5px; margin-left: 10px;">更换分组</el-button>

          <el-button :disabled="toggleOrInapplicabilityList.length === 0" type="primary" size="mini"
            @click="showInapplicability" style="padding: 5px; margin-left: 5px;">改为‘不适用’</el-button>

          <el-divider direction="vertical"></el-divider>

          <el-button type="info" size="mini" @click="testSelectAllClauseInCurrGroup"
            style="padding: 5px; ">全选当前分页(测试)</el-button>

          <el-button type="info" size="mini" @click="testLeaveOneClauseInCurrGroup"
            style="padding: 5px; margin-left: 5px;">当前分页保留一款(测试)</el-button>

          <el-divider direction="vertical"></el-divider>

          <el-button type="danger" size="mini" @click="testClearAllClauseInCurrGroup"
            style="padding: 5px; ">取消当前页全选(测试)</el-button>

          <!-- <el-divider direction="vertical"></el-divider>

          <el-button type="danger" size="mini" @click="inapplicabilitySelectAllValidate"
            style="padding: 5px; ">选择项校验</el-button> -->

        </template>

      </div>
      <el-tabs v-model.trim="activeName" v-loading="collapseLoading" @tab-click="handleClick">
        <el-tab-pane v-for="item in unDomainListVOList" :key="`udl${item.id}`" :label="item.groupName" :name="item.id">
          <span v-if="!clauseList4MapPage.length">此分组暂无款项，可前往分组管理分配款项!</span>
          <el-checkbox-group v-model="toggleOrInapplicabilityList">
            <el-collapse v-model="collapseName" accordion class="group-collapse">
              <el-collapse-item :title="`${item.groupDetail}(${item.clauseTotal})`" :name="item.groupId" v-for="(item, index) in topicClauseList" :key="index">
                <div v-if="item.children && item.children.length">
                  <div v-for="(clause, cIndex) in item.children" :key="cIndex" :style="{ color: clause.fitStatus == 1 ? 'gray' : '' }" class="topic-clause">
                    <el-checkbox v-if="hosReview.length === 0" :label="clause.clauseId"
                    @change="(value) => clauseCheck(value, clause.clauseId)"></el-checkbox>
                    <span class="label">
                      {{ clause.isStar == 1 ? "★" : "" }}{{ clause.clauseNo }}
                    </span>
                    <span class="tag">
                      {{ clause.clause }}
                      <el-tag v-if="clause.fitStatus == 1" size="small" effect="plain" type="info"
                        style="text-align: center;">不适用</el-tag>
                    </span>
                  </div>
                </div>
                <div v-else class="no-data">暂无数据</div>
              </el-collapse-item>
            </el-collapse>
          </el-checkbox-group>
        </el-tab-pane>
      </el-tabs>
      <!-- <pagination v-show="total > 0" :total="total" :page.sync="pageNum" :limit.sync="pageSize"
        :pageSizes="[6, 10, 20, 30, 50]" /> -->
    </el-card>

    <el-card class="box-card" shadow="never" v-if="hosReview.length">
      <div slot="header" class="clearfix">
        <span>已分配分组</span>
      </div>
      <el-tabs v-model.trim="activeName2" @tab-click="tabClick">
        <el-tab-pane v-for="(item, i) in hosReview" :key="`hc${i}`" :label="`${item.name}(${item.clauseNum})`"
          :name="item.accountId">
          <span v-if="!clauseList1.length">此分组暂无款项，可前往分组管理分配款项!</span>
          <div v-else>
            <modify-table :key="i" :data="clauseList1.filter(clause => clause.fitStatus == 0).map(clause => clause.clauseId)" :versionId="versionId"
              :showAll="false"></modify-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <el-dialog title="更换分组" :visible="toggleVisible" width="80%" destroy-on-close :close-on-click-modal="false"
      @close="handlerClose">
      <div v-loading="toggleOrInapplicabilityLoading">
        <el-form ref="toggleForm" :model="toggleFormData" :rules="toggleFormRule" hide-required-asterisk inline>
          <el-form-item label="需要更换到的分组："></el-form-item>
          <el-form-item label="选择分组" prop="groupId">
            <el-select v-model="toggleFormData.groupId" @change="getTopicByGroupId" placeholder="请选择分组" clearable filterable>
              <el-option v-for="item in unDomainListVOList" :key="item.id" :label="item.groupName"
                :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="选择主题" prop="topicId" label-width="100px">
            <el-select v-model="toggleFormData.topicId" @change="getSubTopicByTopic" placeholder="请选择主题" clearable filterable>
              <el-option v-for="item in topicList" :key="item.groupId" :label="item.groupDetail"
                :value="item.groupId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="选择小主题" prop="subTopicId" label-width="100px">
            <el-select v-model="toggleFormData.subTopicId" placeholder="请选择小主题" clearable filterable>
              <el-option v-for="item in subTopicList" :key="item.groupId" :label="item.groupDetail"
                :value="item.groupId"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <p style="padding-left: 0; margin: 0;font-weight: 700;">需要更换分组的款项：</p>
        <el-table :data="inapplicabilityData" :key="toggleOrInapplicabilityList.length" height="441" border stripe>
          <el-table-column label="分组" prop="groupName"></el-table-column>
          <el-table-column label="款号" prop="clauseNo"></el-table-column>
        </el-table>
        <pagination v-show="inapplicabilityAllData.length > 0" :total="inapplicabilityAllData.length"
          :page.sync="toggleOrInapplicabilityPage.page" :limit.sync="toggleOrInapplicabilityPage.rows"
          @pagination="inapplicabilityPageChange" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :disabled="toggleOrInapplicabilityLoading" @click="handlerClose">取 消</el-button>
        <el-button :disabled="toggleOrInapplicabilityLoading" type="primary" @click="submitToggle">确定并更改</el-button>
      </span>
    </el-dialog>


    <el-dialog title="不适用款项确认" :visible="inapplicabilityVisible" width="80%" destroy-on-close
      :close-on-click-modal="false" @close="handlerClose">
      <div style="height:500px" v-loading="toggleOrInapplicabilityLoading">
        <el-table :data="inapplicabilityData" :key="toggleOrInapplicabilityList.length" height="441" border stripe>
          <el-table-column label="分组" prop="groupName"></el-table-column>
          <el-table-column label="款号" prop="clauseNo"></el-table-column>
        </el-table>
        <pagination v-show="inapplicabilityAllData.length > 0" :total="inapplicabilityAllData.length"
          :page.sync="toggleOrInapplicabilityPage.page" :limit.sync="toggleOrInapplicabilityPage.rows"
          @pagination="inapplicabilityPageChange" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :disabled="toggleOrInapplicabilityLoading" @click="handlerClose">取 消</el-button>
        <el-button :disabled="toggleOrInapplicabilityLoading" type="primary"
          @click="submitInapplicability(0)">还原</el-button>
        <el-button :disabled="toggleOrInapplicabilityLoading" type="primary"
          @click="submitInapplicability">确定并更改</el-button>
      </span>
    </el-dialog>

    <el-upload ref="upload" :action="action" :before-upload="beforeUpload" :on-success="onSuccess" :on-error="onError" :with-credentials="true" :headers="{
        Authorization: 'Bearer ' + getToken(),
      }" :data="{ applyNo: allot.applyNo, reviewerId, downLoadFileName: `${reviewerName}-评审员利益冲突表-${allot.hospitalName}（管理员上传）.pdf`}" accept=".JPG,.JPEG,.PNG,.PDF" style="display: none">
      <span id="interestSheetUpload" slot="trigger">
      </span>
    </el-upload>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { deepClone } from "@/utils/index";
import { Loading } from "element-ui";
import request, { download } from "@/utils/request";
import ModifyTable from '@/views/hospitalSelfEvaluation/components/modifyTable.vue'

export default {
  name: "ReviewPlanAllotReview",
  components: {
    ModifyTable
  },
  data() {
    return {
      showAll: false,
      url: {
        detail: "/hospital/hos/plan/detail",
        submit: "",
      },
      leader: 0,
      userId: null,
      domainId: null,
      user: {},
      userList: [],
      clauseList: [],
      clauseByUserMap: {
        1: [],
        2: [],
      },
      checkList: [],
      checkAll: false,
      isIndeterminate: true,
      pageNum: 1,
      pageSize: 6,
      total: 0,
      unDomainListVOList: [],
      allDomainListVOList: [],
      hospitalForm: {},
      hospital: {},
      activeName: 0,
      activeName2: 0,
      clauseMap: {},
      allClauseMap: {},
      clauseTotalMap: {},
      getData: false,
      hosReviewClauseNumInfos: [],
      clauseList1: [],
      loading: null,
      versionId: "",
      traineesList: [],
      selectedTraineesList: [],
      traineeId: '',
      traineeLoading: false,
      fileDetailMap: {},
      checkDeletePersons: [],

      toggleOrInapplicabilityList: [],
      toggleOrInapplicabilityMap: {},
      inapplicabilityVisible: false,
      toggleVisible: false,
      toggleOrInapplicabilityLoading: false,
      toggleFormData: {
        groupId: '',
        topicId: '',
        subTopicId: '',
      },
      toggleFormRule: {
        groupId: [{
          required: true,
          message: '请选择分组',
          trigger: 'change'
        }],
        topicId: [{
          required: true,
          message: '请选择主题',
          trigger: 'change'
        }],
        subTopicId: [{
          required: true,
          message: '请选择小主题',
          trigger: 'change'
        }],
      },
      groupData: {},
      toggleOrInapplicabilityPage: {
        page: 1,
        rows: 10,
      },
      collapseName: 0,
      collapseClauseMap: {},
      moveCollapseClauseMap: [],
      collapseLoading: false,
      // 上传
      interest: null,
      uploading: false,
      uploadDom: null,
      reviewerId: null,
      reviewerName: null,
      // 手风琴数据
      topicClauseList: [],
      // 拒绝后展示评审员名单确认表
      revFileId: null
    };
  },

  computed: {
    action() {
      return `${localStorage.baseUrl || process.env.VUE_APP_BASE_API}/system/reviewer/upload/adminInterestFile`;
    },
    clauseList4Map() {
      return this.clauseMap[this.activeName] || [];
    },
    clauseList4MapPage() {
      return this.clauseList4Map.filter((clause, index) => index >= (this.pageNum - 1) * this.pageSize && index < this.pageNum * this.pageSize)
    },
    clauseCount4Map() {
      return this.clauseTotalMap[this.activeName] || 0;
    },
    hosReview() {
      if (this.hosReviewClauseNumInfos.length == 0) return false;
      return this.hosReviewClauseNumInfos.filter(
        (hos) => hos.accountId != null && hos.clauseNum != 0
      );
    },
    hosTrainees() {
      if (this.selectedTraineesList.length == 0) return [];
      return this.selectedTraineesList.filter(
        (hos) => hos.accountId != null && (hos.clauseNum === null || hos.clauseNum === 0)
      );
    },

    allFileSubmit() {
      return this.hosReviewClauseNumInfos.every(({ interestFileId }) => !!interestFileId)
    },

    fileName() {
      return id => {
        let list = this.fileDetailMap[id];
        let file = Array.isArray(list) ? list[0] : {}
        return file ? file.fileName : ''
      }
    },

    downLoadFileName() {
      return id => {
        let list = this.fileDetailMap[id];
        let file = Array.isArray(list) ? list[0] : {}
        return file ? file.downLoadFileName : ''
      }
    },

    hosReviewers() {
      let hosReviews = this.hosReviewClauseNumInfos.filter(reviewer => reviewer.accountId)
      let list = [{}]
      hosReviews.forEach(({ fieldIds, name }) => {
        let data = list[0];
        let fieldIdList = fieldIds.split(',')
        fieldIdList.forEach(id => {
          data[id] = name
        })
      })
      // console.log(list);
      return list
    },

    inapplicabilityAllData() {
      return Object.values(this.toggleOrInapplicabilityMap)
    },

    inapplicabilityData() {
      let { page, rows } = this.toggleOrInapplicabilityPage
      return this.inapplicabilityAllData.slice((page - 1) * rows, page * rows)
    },

    topicList() {
      if (!this.toggleFormData.groupId) return [];
      if (!this.groupData[this.toggleFormData.groupId]) return [];
      let group = this.groupData[this.toggleFormData.groupId]
      if (!group) return []
      let children = group.children || []
      return children
    },

    subTopicList() {
      let parents = this.topicList;
      if (parents.length === 0) return []
      let parent = parents.find(topic => topic.groupId == this.toggleFormData.topicId)
      if (!parent) return []
      return parent.children || []
    },

    isFitLeader() {
      let leader = this.hosReview.filter(item => item.accountId === this.leader) || []
      return leader[0]?.conclusion == 21
    },
    revFile() {
      if (!this.revFileId) return false;
      let ids = this.revFileId.split(',');
      let maxId = Math.max(...ids);
      let fileList = this.fileDetailMap[maxId]
      if (!fileList || fileList.length == 0) return false;
      return fileList[0];
    },
  },
  watch: {
    groupData: {
      deep: true,
      handler(val) {
        if (!val[this.activeName]) {
          this.topicClauseList = []
        };
        // 拷贝数据，避免影响源数据
        let cloneGroup = deepClone(val[this.activeName])
        let themeGroup = cloneGroup.children.map(item => {
          return {
            ...item,
            children:  item.children.reduce((cur, pre) => {
              return cur.concat(pre.children)
            }, [])
          }
        });
        this.topicClauseList = themeGroup.map(theme => {
          let clauseTotal = 0;
          theme.children = theme.children.reduce((pre, cur) => {
            if (this.collapseClauseMap[cur.groupDetail]) {
              return pre.concat(this.collapseClauseMap[cur.groupDetail])
            }
            return pre
          }, []);
          this.moveCollapseClauseMap.forEach(move => {
            if (move.themeId === theme.groupId) {
              theme.children.push(move)
            }
          });
          clauseTotal+=theme.children.length;
          return {
            clauseTotal,
            ...theme
          }
        })
      },
    },
  },

  async mounted() {
    this.start()
  },

  methods: {
    getToken,
    async start() {
      const versionId = await this.getEnabledVersionId();
      this.versionId = versionId || this.$store.getters.standardBaseVersionId;
      this.allot = this.$route.query;
      this.$set(this, "checkList", this.clauseByUserMap[1]);
      // this.versionId = this.$store.getters.standardBaseVersionId;
      if (!this.allot) {
        return this.$message({
          type: "warning",
          message: "数据不足",
        });
      }
      this.getDetail(true);
      this.getTrainees();
    },


    query() {
      if (this.tableLoading) return;
      this.tableLoading = true;
      request({
        url: '/hospital/review/plan/list',
        method: "post",
        data: {
          hospitalName: this.allot.hospitalName
        },
      })
        .then((res) => {
          if (res.code == 200 && res.rows[0]) {
            this.allot = res.rows[0]
          }
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    preview(url) {
      window.open(url, '_blank');
    },
    download(fileId) {
      download("/common/downloadFile", { fileId }, this.downLoadFileName(fileId));
    },
    download2(fileId, downLoadFileName) {
      download("/common/downloadFile", { fileId }, downLoadFileName);
    },
    toHospitalDetail() {
      this.$router.push({
        path: "/auditManagement/hospitalDetailMessage",
        query: { applyNo: this.allot.applyNo },
      });
    },

    async getDetail(update = false) {
      await this.query()
      request({
        url: this.url.detail,
        method: "post",
        data: {
          applyNo: this.allot.applyNo,
          versionId: this.versionId,
          personForm: "2",
        },
      }).then((res) => {
        this.hospital = res.hospitalAuthContact;
        this.fileDetailMap = res.fileDetailMap || {}
        this.userList = res.preExamList || [];
        this.hosReviewClauseNumInfos = res.hosReviewClauseNumInfoVOList || [];
        this.allDomainListVOList = res.allDomainListVOList || [];
        this.unDomainListVOList = res.unDomainListVOList || [];
        this.revFileId = res.revFileId
        if (this.allDomainListVOList.length != 0) {
          this.activeName = this.allDomainListVOList[0].id;
          this.getClauseByDomainId();
        }
        if (this.hosReviewClauseNumInfos.length != 0) {
          if (this.hosReviewClauseNumInfos[0].accountId) {
            this.activeName2 = this.hosReviewClauseNumInfos[0].accountId;
            this.getClause2();
          }

          setTimeout(() => {
            if (this.allFileSubmit) {
              this.hosReviewClauseNumInfos.forEach(user => {
                if (user.hasInterest == 1) {
                  this.$refs.interestTable?.toggleRowSelection(user, true);
                }
              })
            }
          }, 250);
        }

        // if (this.allot.reviewDisComplete == 4 && this.allot.anuStatus == this.AutSaAudCurrentStatusEnum.ASSESSOR_WRITE_SHEET) {
        //   this.updateAutStatus(1)
        // }

        this.getData = true;
        this.hospitalForm = {
          shadow: "never",
          list: [
            {
              label: "医院名称",
              value: this.allot.hospitalName,
              type: "text",
            },
            {
              label: "联系人姓名",
              value: res.hospitalAuthContact.authContactName,
              type: "text",
            },
            {
              label: "联系人手机号",
              value: res.hospitalAuthContact.authContactMobile,
              type: "text",
            },
            {
              label: "联系人邮箱",
              value: res.hospitalAuthContact.authContactEmail,
              type: "text",
            },
          ],
        };

        this.showAll = true;
      });
    },

    checkNODE() {
      return new Promise((resolve, reject) => {
        request({
          url: '/hospital/review/plan/list',
          method: "post",
          data: {
            hospitalName: this.allot.hospitalName
          },
        }).then(res => {
          if (res.rows[0].reviewDisComplete == 4 && res.rows[0].anuStatus == this.AutSaAudCurrentStatusEnum.ASSESSOR_WRITE_SHEET) {
            this.updateAutStatus(1, flag => {
              if (flag) {
                resolve()
              } else {
                reject();
              }
            })
          } else {
            resolve()
          }
        }).catch(() => {
          reject();
        })
      })
    },

    getTrainees() {
      request({
        url: this.url.detail,
        method: "post",
        data: {
          applyNo: this.allot.applyNo,
          versionId: this.versionId,
          personForm: "3",
        },
      }).then((res) => {
        this.traineesList = res.traineesList || [];
        this.selectedTraineesList = res.hosReviewClauseNumInfoVOList || []
      });
    },

    getUserByDomain(fieldId) {
      request({
        url: this.url.detail,
        method: "post",
        data: {
          fieldId,
          versionId: this.versionId,
          personForm: "2",
        },
      }).then((res) => {
        // console.log(res);
      });
    },

    getClauseByDomainId() {
      this.collapseLoading = true;
      request({
        url:
          "/hospital/hos/plan/clause/detail",
        method: "post",
        data: {
          personForm: "2",
          versionId: this.versionId,
          fieldId: this.activeName,
          autCode: this.allot.autCode
        },
      }).then((res) => {
        this.collapseLoading = false;
        this.$set(this.clauseMap, this.activeName, res.rows);
        this.$set(this.clauseTotalMap, this.activeName, res.total);
        if (!this.allClauseMap[this.activeName]) {
          this.$set(this.allClauseMap, this.activeName, res.rows.map(({ clauseId }) => clauseId));
        } else {
          this.$set(this.allClauseMap, this.activeName, [...this.allClauseMap[this.activeName], ...res.rows.map(({ clauseId }) => clauseId)]);
        }
        // 重置映射
        this.collapseClauseMap = {};
        // 剔除掉不存在的款
        this.moveCollapseClauseMap = res.rows.filter(item => {
          if (!item.themeId) {
            this.collapseClauseMap[item.clauseId] = item;
            return false
          } else {
            return true
          }
        });
        this.total = res.total;
        if (!this.versionId && res.rows[0]) {
          this.versionId = res.rows[0].versionId;
        }
        this.getClauseByVersionId();
      }).catch(() => {
        this.collapseLoading = false;
      });
    },

    domainChange(id) {
      let filterHosReview = this.hosReviewClauseNumInfos.filter(item => item.fieldIds === id)[0];
      // 深拷贝一次，避免影响源数据
      let deepCloneList = deepClone(this.allDomainListVOList);
      this.userList = deepCloneList.find(
        (domain) => domain.id == id
      ).reviewerList;
      // 当前分组列表插入已分配的评审员
      if (filterHosReview) {
        this.userList.unshift(filterHosReview);
        this.userId = filterHosReview.accountId;
      } else {
        this.userId = "";
      }

      this.activeName = id;
      this.getClauseByDomainId();

      if (this.userList.length == 0) {
        this.$message({
          type: "warning",
          message: "当前分组未分配评审员，请在审核管理-评审员信息审核页面进行分组分配！",
        });
      }
    },

    handleClick() {
      this.topicClauseList = [];
      // 切换分组，重置勾选项
      this.toggleOrInapplicabilityList = [];
      this.getClauseByDomainId();
    },

    tabClick() {
      this.getClause2();
    },

    getClause2() {
      if (!this.activeName2) return;
      request({
        url:
          "/hospital/hos/plan/clause/detail?" +
          `time=${Date.now()}`,
        method: "post",
        data: {
          personForm: "2",
          applyNo: this.allot.applyNo,
          versionId: this.versionId,
          accountId: this.activeName2,
          autCode: this.allot.autCode
        },
      }).then((res) => {
        this.pageNum1 = 1;
        this.$set(this, "clauseList1", res.rows);
        this.total1 = res.total;
        if (!this.versionId && res.rows[0]) {
          this.versionId = res.rows[0].versionId;
        }
      });
    },

    setTrainee() {
      if (!this.traineeId) {
        return this.$message({
          type: 'warning',
          message: '请选择学员！'
        })
      }
      this.traineeLoading = true;
      request({
        url: "/hospital/plan/submit",
        method: "post",
        data: {
          accountId: this.traineeId,
          applyNo: this.allot.applyNo,
          personForm: "2",
          fieldIdList: "TRAINEES_REVIEW",
          autCode: this.allot.autCode,
        },
      }).then(() => {
        this.traineeId = ''
        this.getTrainees();
        this.getDetail();
      }).finally(() => this.traineeLoading = false)
    },

    async revocation() {
      try {
        await this.$confirm("确定要更换当前选中的评审员？", "更换评审员确认", {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })

        request({
          url: 'system/reviewer/del/hos/rev',
          method: 'post',
          data: {
            applyNo: this.allot.applyNo,
            accountIdList: this.checkDeletePersons,
            completeIs: 2
          }
        }).then(() => {
          let ids = this.hosReviewClauseNumInfos.filter(({ accountId }) => this.checkDeletePersons.includes(accountId)).map(({ interestFileId }) => interestFileId)
          if (ids.length) {
            this.shareDelete(ids);
          }

          this.getDetail();
          this.getTrainees();
          this.updateAutStatus(2)
        })
      } catch (error) {
        // // console.log(error);
      }

    },

    selectDeletePersons(selection) {
      this.checkDeletePersons = selection.map(({ accountId }) => accountId)
    },

    async setLeader() {
      try {
        if (!this.leader) {
          return this.$message({
            type: "warning",
            message: "请选择组长",
          });
        }

        await this.checkNODE();

        await new Promise((resolve, reject) => {
          const h = this.$createElement;
          this.$msgbox({
            title: '评审学员分配提示',
            message: h('div', null, [
              h('span', {
                style: {
                  color: 'red',
                  fontSize: '14px'
                }
              }, '请确认是否需要分配评审学员'),
              h('p', null, `当前已分配评审学员：${this.hosTrainees.map(trainees => trainees?.name).join(',') || '-'}`),
            ]),
            showCancelButton: true,
            cancelButtonText: '取消',
            confirmButtonText: '保存',
          }).then(() => {
            resolve()
          }).catch((action) => {
            reject(action)
          });
        })

        this.loading = Loading.service({
          lock: true,
          text: "数据提交中",
          spinner: "el-icon-loading",
        });
        request({
          url: "/hospital/plan/submit",
          method: "post",
          data: {
            accountId: this.leader,
            leaderIs: "1",
            applyNo: this.allot.applyNo,
            completeIs: 1,
            personForm: "2",
            versionId: this.versionId,
            autCode: this.allot.autCode,
          },
        })
          .then((res) => {
            if (res.code == 200) {
              this.updateAutStatus(1)
              this.$store.dispatch("tagsView/delView", this.$route)
              this.$router.push({ name: "ReviewPlanAllotList" })
            }
          })
          .finally(() => {
            this.loading.close();
            this.loading = null;
          });
      } catch (error) {
        // console.log(error);
      }
    },

    submit2All() {
      if (this.activeName == 0)
        return this.$message({
          type: "warning",
          message: "请选择分组",
        });
      if (!this.userId)
        return this.$message({
          type: "warning",
          message: "请选择评审员",
        });
      // if (this.unDomainListVOList.length < 6)
      //   return this.$message({
      //     type: "warning",
      //     message: "当前数据不符合一次性提交全部",
      //   });
      this.loading = Loading.service({
        lock: true,
        text: "数据提交中",
        spinner: "el-icon-loading",
      });

      request({
        url: "/hospital/plan/submit",
        method: "post",
        data: {
          accountId: this.userId,
          applyNo: this.allot.applyNo,
          completeIs: 2,
          fieldIdList: this.unDomainListVOList.map(({ id }) => id).join(','),
          leaderIs: 2,
          personForm: "2",
          versionId: this.versionId,
          autCode: this.allot.autCode,
        }
      }).then(() => {
        this.domainId = "";
        this.userId = "";
        this.getDetail();
        // if (this.allot.anuStatus == this.AutSaAudCurrentStatusEnum.ADMIN_ALLOT_ASSESSOR) {
        //   this.updateAutStatus(1)
        // }
        // this.$store.dispatch("tagsView/delView", this.$route)
        // this.$router.push({ name: "ReviewPlanAllotList" })
      }).finally(() => {
        this.loading.close();
        this.loading = null;
      });
    },

    // 保存分配评审员操作
    beforeSubmit() {
      if (this.activeName == 0) {
        return this.$message({
          type: "warning",
          message: "请选择分组",
        });
      }
      if (!this.userId) {
        return this.$message({
          type: "warning",
          message: "请选择评审员",
        });
      }
      // 是否存在已匹配
      // let allocatedStudent = this.studentId.filter(item => this.allot.reviewerList.includes(item)) || [];
      // if (allocatedStudent.length) {
      //   this.$confirm(`学员${allocatedStudent[0].name}已分配在第一组，请确认是否继续分配？`, '提示', {
      //     confirmButtonText: '确认',
      //     cancelButtonText: '取消',
      //     type: 'warning'
      //   }).then(() => {
      //     this.submit();
      //   }).catch(err => {
      //     console.log(err);
      //   })
      // } else {
      //   this.submit();
      // }
      let filterHosReview = this.hosReviewClauseNumInfos.filter(item => item.accountId === this.userId)[0];
      // 当前分组列表插入已分配的评审员
      if (filterHosReview) {
        return this.$message({
          type: "warning",
          message: "当前分组已分配给该评审员，请勿重复保存",
        });
      }
      this.submit(2);
    },
    // 确认分配评审员操作
    beforeConfirm() {
      this.submit(3);
    },

    submit(completeIs) {
      this.loading = Loading.service({
        lock: true,
        text: "数据提交中",
        spinner: "el-icon-loading",
      });

      // [确认操作]转为上传冲突表节点，这三个字段非必填 accuntld，fieldldList，leaderls
      let data = completeIs == 2 ? {
        accountId: this.userId,
        fieldIdList: this.domainId,
        leaderIs: this.leader == this.userId ? 1 : 2
      } : {}

      request({
        url: "/hospital/plan/submit",
        method: "post",
        data: {
          applyNo: this.allot.applyNo,
          completeIs,
          personForm: "2",
          versionId: this.versionId,
          autCode: this.allot.autCode,
          ...data
        },
      })
        .then((res) => {
          if (res.code == 200) {
            this.domainId = "";
            this.userId = "";
            this.getDetail();
            if (completeIs == 3) {
              if (this.allot.anuStatus == this.AutSaAudCurrentStatusEnum.ADMIN_ALLOT_ASSESSOR) {
                this.updateAutStatus(1)
              }
              this.$store.dispatch("tagsView/delView", this.$route)
              this.$router.push({ name: "ReviewPlanAllotList" })
            }

          }
        })
        .finally(() => {
          this.loading.close();
          this.loading = null;
        });
    },

    updateAutStatus(autResult, callback) {
      request({
        url: "/aut/sa/aud/submitAutSaAud",
        method: "post",
        data: {
          time: Date.now(),
          autCode: this.allot.autCode,
          accountId: `${this.$store.getters.userId}`,
          submitType: this.AutSaAudCurrentStatusEnum.NODE_FILP,
          statusProcess: autResult == 1 ? 'PS' : 'RJ',
          autSaAudLists: [{
            autResult,
            autDesc: '--'
          }]
        },
      }).then((res) => {
        // console.log(res);

        callback && callback(true);
      }).catch(() => {
        callback && callback(false)
      })
    },

    clauseCheck(value, clauseId) {
      if (value) {
        let clause = this.clauseList4Map.find(clause => clauseId == clause.clauseId)
        let group = this.unDomainListVOList.find(group => group.id == this.activeName)
        if (!clause) return;
        let data = {
          ...clause,
          groupName: group.groupName
        }
        this.$set(this.toggleOrInapplicabilityMap, clauseId, data)
      } else {
        this.$delete(this.toggleOrInapplicabilityMap, clauseId)
      }
      console.log(this.toggleOrInapplicabilityMap);
    },

    handlerClose() {
      this.toggleOrInapplicabilityLoading = false;
      this.inapplicabilityVisible = false;
      this.toggleVisible = false
      this.toggleOrInapplicabilityPage.page = 1
      this.toggleOrInapplicabilityPage.rows = 10
      this.toggleFormData = this.$options.data().toggleFormData
    },

    inapplicabilityPageChange() {
      // console.log(this.toggleOrInapplicabilityPage);
    },

    getTopicByGroupId() {
      this.toggleFormData.topicId = ''
      this.toggleFormData.subTopicId = ''

      if (!this.toggleFormData.groupId) return;
      if (this.groupData[this.toggleFormData.groupId]) return;
      request({
        url: '/system/domain/' + this.toggleFormData.groupId,
        method: 'get',
      }).then(res => {
        let data = res.data;
        let child = (data.children || [])[0]
        if (child) {
          this.$set(this.groupData, this.toggleFormData.groupId, child)
        }
      })
    },

    getClauseByVersionId() {
      if (!this.versionId || !this.activeName) return;
      request({
        url: `/system/domain/${this.activeName}?versionId=${new String(this.versionId)}`,
        method: 'get',
      }).then(res => {
        let data = res.data;
        let child = (data.children || [])[0]
        if (child) {
          this.$set(this.groupData, this.activeName, child)
        }
      })
    },

    getSubTopicByTopic() {
      this.toggleFormData.subTopicId = ''
    },

    testSelectAllClauseInCurrGroup() {
      let ids = this.clauseList4Map.map(({ clauseId }) => clauseId)
      let list = [...new Set([...this.toggleOrInapplicabilityList, ...ids])]
      this.$set(this, 'toggleOrInapplicabilityList', list)
      this.toggleOrInapplicabilityList.forEach(id => {
        this.clauseCheck(1, id)
      })
    },

    testLeaveOneClauseInCurrGroup() {
      let ids = this.clauseList4Map.map(({ clauseId }) => clauseId)
      ids.pop();
      let list = [...new Set([...this.toggleOrInapplicabilityList, ...ids])]
      this.$set(this, 'toggleOrInapplicabilityList', list)
      this.toggleOrInapplicabilityList.forEach(id => {
        this.clauseCheck(1, id)
      })
    },

    testClearAllClauseInCurrGroup() {
      let ids = this.clauseList4Map.map(({ clauseId }) => clauseId)
      let list = [...new Set([...this.toggleOrInapplicabilityList])].reverse()
      ids.forEach(id => {
        let d_id = list.splice(list.indexOf(id), 1);
        this.clauseCheck(null, d_id[0])
      })
      this.$set(this, 'toggleOrInapplicabilityList', list.reverse())

      list.forEach(id => {
        this.clauseCheck(1, id)
      })
    },

    inapplicabilitySelectAllValidate() {
      try {
        // 已选择的款项
        let selectIds = [...this.toggleOrInapplicabilityList];
        // 所有获取到款项的分组 以及对应的款项集合 表
        let allGroupClauseMap = this.allClauseMap;
        // // 所有获取到款项的分组 以及对应的款项集合 2维数组
        // let allGroupIdsList = Object.values(this.clauseMap);
        // 所有获取到的分组key/name
        let groupKeys = Object.keys(allGroupClauseMap);
        // 所有获取到款项的分组 以及对应的款项总数
        let allGroupIdsTotalMap = this.clauseTotalMap;

        // 将已选择款按照分组进行分类完毕后的款项 表
        let selectIdsMap = {}
        groupKeys.forEach(key => {
          if (!selectIdsMap[key]) {
            selectIdsMap[key] = []
          }
        })

        // 将选择的款项按原有分组进行分类
        let entries = Object.entries(allGroupClauseMap);
        entries.forEach(([groupId, clauseIds]) => {
          let new_ids = selectIds.filter(id => {
            let flag = clauseIds.includes(id)
            if (flag) {
              selectIdsMap[groupId].push(id)
            }
            return !flag;
          })
          selectIds = new_ids
        })
        // 确保每个
        let final_entries = Object.entries(selectIdsMap);
        let finalFlag = final_entries.some(([groupId, clauses]) => {
          return clauses.length >= allGroupIdsTotalMap[groupId]
        })

        if (finalFlag) {
          throw '每一组必须至少有1款'
        }
      } catch (error) {
        console.log(error);
        return error
      }
    },

    showToggle() {
      // let error = this.inapplicabilitySelectAllValidate();
      // if (error) {
      //   this.$message({
      //     type: 'error',
      //     message: error
      //   })
      //   return;
      // }
      this.toggleVisible = true
    },

    showInapplicability() {
      // let error = this.inapplicabilitySelectAllValidate();
      // if (error) {
      //   this.$message({
      //     type: 'error',
      //     message: error
      //   })
      //   return;
      // }
      this.inapplicabilityVisible = true

      // console.log('toggleOrInapplicabilityList', this.toggleOrInapplicabilityList);
    },

    submitToggle() {
      this.$refs.toggleForm.validate(flag => {
        if (!flag) return;
        let subTopicList = this.subTopicList;
        let subTopic = subTopicList.find(({ groupId }) => groupId == this.toggleFormData.subTopicId)
        if (!subTopic) {
          return
        }

        let { children } = subTopic
        let ids = children.map(({ groupDetail }) => groupDetail)
        let updateIds = this.toggleOrInapplicabilityList.filter(clauseId => !ids.includes(`${clauseId}`))
        console.log('需要更改的款项ID', updateIds);
        console.log('如果发现【需要挪动的款项数量】与【勾选的款项数量】不一致，很可能是因为：当前选择的小主题包含了部分款项 所以就不需要再挪动了');
        if (updateIds.length === 0) {
          this.$message({
            type: 'warning',
            message: '选中款项已在当前小主题下，无需更换'
          })
          return;
        }
        let clauseMoveInfoList = updateIds.map(clauseId => {
          return {
            clauseId,
            smallThemeId: this.toggleFormData.subTopicId,
            themeId: this.toggleFormData.topicId,
            groupId: this.toggleFormData.groupId,
          }
        })

        let data = {
          autCode: this.allot.autCode,
          clauseMoveInfoList
        }
        request({
          url: '/review/fit/move/clause/clause-move',
          method: 'post',
          data
        }).then(() => {
          setTimeout(() => {
            this.handleClick();
            this.handlerClose();
            this.$set(this, 'toggleOrInapplicabilityList', [])
            this.$set(this, 'toggleOrInapplicabilityMap', {})
          }, 1000);
        }).finally(() => {
          this.toggleOrInapplicabilityLoading = false
        })
      })
    },


    submitInapplicability(fitStatus) {
      let mapIds = Object.keys(this.toggleOrInapplicabilityMap).sort().join()
      let listIds = this.toggleOrInapplicabilityList.sort().join();
      if (mapIds != listIds) {
        this.$set(this, 'toggleOrInapplicabilityList', [])
        this.$set(this, 'toggleOrInapplicabilityMap', {})
        return this.$message({
          type: 'warning',
          message: '勾选数据异常，请重新勾选！'
        })
      }
      this.toggleOrInapplicabilityLoading = true;
      request({
        url: '/review/fit/move/clause/submit',
        method: 'post',
        data: {
          "autCode": this.allot.autCode,
          "clauseIdList": this.toggleOrInapplicabilityList.map(id => `${id}`),
          "fitStatus": fitStatus === 0 ? 0 : 1,
          "operationFlag": "0"
        }
      }).then(() => {
        setTimeout(() => {
          this.handleClick();
          this.handlerClose();
          this.$set(this, 'toggleOrInapplicabilityList', [])
          this.$set(this, 'toggleOrInapplicabilityMap', {})
        }, 1000);
      }).finally(() => {
        this.toggleOrInapplicabilityLoading = false
      })
    },

    activated() {
      if (!this.allot) return;
      this.getDetail();
      this.getTrainees();
    },
    uploadInterestSheet({accountId, name}) {
      if (!this.uploadDom) {
        this.uploadDom = document.getElementById("interestSheetUpload");
      };
      this.reviewerId = accountId;
      this.reviewerName = name;
      this.uploadDom.click();
    },

    beforeUpload(file) {
      const list = '.JPG,.JPEG,.PNG,.PDF'.split(',').map(accept => accept.toLocaleUpperCase());
      const suffix = '.' + file.name.split('.').pop().toLocaleUpperCase();
      if (!list.includes(suffix)) {
        this.$message({
          type: 'warning',
          message: '请上传指定格式的文件！'
        })
        return false;
      }
      // 当fileSize大于0时才检查文件大小是否符合，为0或者小于0则不校验文件大小
      if (file.size / 1024 / 1024 > 100) {
        this.$message({
          type: 'warning',
          message: `上传的文件大小不得超过100M`
        })
        return false;
      }
      this.uploading = true;
      return true;
    },

    onSuccess(response, file, fileList) {
      if (response.code == 200) {
        let ids = [response.data?.fileId] || []
        if (ids.length) {
          // 管理员上传
          this.shareCreate(ids, this.$store.getters.userId, 'reviewer_evaluation_party', this.allot.hospitalName, this.reviewerName)
          this.getDetail();
        }
      } else {
        this.$message({
          type: 'error',
          message: response.msg || response.message
        })
      }
      this.uploading = false;
    },

    onError(error) {
      this.uploading = false;
      this.$message({
        type: 'error',
        message: error.msg
      })
    },
  }
};
</script>

<style lang="scss" scoped>
.review-plan-allot-trail {
  background-color: transparent !important;
  padding: 10px;

  ::v-deep .box-card.el-card {
    border: 0;

    .el-tab-pane {
      padding-bottom: 20px;
    }
  }

  ::v-deep .el-radio {
    margin-left: 10px;

    .el-radio__label {
      padding-left: 4px;
    }
  }

  .box-card+.box-card {
    margin-top: 10px;
  }
  .interest-table {
    ::v-deep .el-button + .el-button {
      margin-left: 0px;
    }
  }
  .group-collapse {
    ::v-deep .el-collapse-item .el-collapse-item__header {
      font-size: 14px;
      font-weight: bold;
      height: 40px;
      line-height: 40px;
    }
    ::v-deep .el-collapse-item__content {
      .no-data {
        padding-left: 20px;
      }
      .topic-clause {
        display: flex;
        align-items: center;
        margin: 20px 0;
        padding-left: 20px;
        font-size: 13px;
        .label {
          font-weight: bold;
          padding-right: 20px;
          min-width: 115px;
        }
        .tag {
          min-width: 60px;
        }
        &:first-child {
          margin-top: 10px;
        }
      }
    }
  }

  .has-trainee {
    span {
      font-size: 14px;
    }

    span+span {
      margin-right: 10px;
    }
  }

  ::v-deep .el-checkbox {
    margin-right: 5px;
    margin-top: 1px;

    .el-checkbox__label {
      display: none;
    }
  }

  .leader-tip {
    font-size: 14px;
    color: #F56C6C;
    margin-top: 10px;
    margin-left: 10px;
  }
}
</style>
