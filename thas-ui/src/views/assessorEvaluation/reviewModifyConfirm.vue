<template>
  <div class="review-modify-confirm">
    <el-card class="box-card" shadow="never" style="margin-bottom: 10px;">
      <div slot="header" class="clearfix">
        <h4>提交评审修改结果</h4>
      </div>
      <el-form>
        <el-form-item label="需修改项：" v-if="modifyClause.length">
          <modify-table :data="modifyClause"  :small="true" :versionId='relation.autCsId' :showAll='false'>
            <el-table-column label="评价结果">
              <template slot-scope="scope">
                <!-- <span>{{ modifyAutResult(scope.row.clauseId)}}</span> -->
              </template>
            </el-table-column>
            <el-table-column label="评审员评价">
              <template slot-scope="scope">
                <!-- <span>{{ modifyAutDesc(scope.row.clauseId)}}</span> -->
              </template>
            </el-table-column>
            <el-table-column>
              <div slot="header">
                医院反馈：<br>
                事实准确性审查报告中的错误/需要注意/澄清的地方：
              </div>
              <template slot-scope="scope">
                <!-- <span>{{ modifyAutQuestion(scope.row.clauseId)}}</span> -->
              </template>
            </el-table-column>
          </modify-table>
        </el-form-item>
        <el-form-item label="确认描述：" :rules="[{required:true, message: '请填写确认描述', trigger: ['blur']}]" prop="autDesc">
          <el-input type="textarea" v-model="summaryForm.autDesc" :rows="3" :maxlength="1000"></el-input>
        </el-form-item>
        <el-form-item label="下载：" prop="downloaded" :rules="[{required:true, message: '请下载事实准确性查询表', trigger: ['blur']}]">
          <el-button v-loading='downloadLoading' @click="downloadFarReport">下载事实准确性查询表</el-button>
        </el-form-item>
        <el-form-item label="上传：" prop="fileIds" :rules="[{required:true, message: '请上传已盖章的事实准确性查询表', trigger: ['blur']}]">
          <upload :accept='`.PDF`' :disabled="!summaryForm.downloaded" single class="evaluation-upload" :files="files" :data="{type: '1', downLoadFileName: `${$store.getters.nickName}-事实准确性确认表.pdf`}" @uploadSuccess="onSuccess" @fileRemove="onError" :text="`上传签字盖章后的事实准确性查询表`"></upload>
        </el-form-item>

        <el-form-item label=" ">
          <el-button :disabled="files.length == 0" type="primary" @click="submitSummary" v-loading='termSubmitLoading'>提交评审修改结果</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'ReviewModifyConfirm',
  props: {
    relation: {
      type: Object,
      default: () => { }
    },
    detail: {
      type: Object,
      default: () => { }
    },
    modifyClause: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
        summaryForm: {
            autResult: '',
            autDesc: ''
        }
    };
  },
  components: {},

  computed: {},

  mounted() { },

  methods: {}
}

</script>
<style lang='scss' scoped>
</style>
