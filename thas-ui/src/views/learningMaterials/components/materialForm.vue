<template>
  <div>
    <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="100px">
      <el-form-item label="资源标题" prop="title">
        <el-input v-model.trim="formData.title" :disabled="disabled" placeholder="请输入资源标题" :maxlength="127" clearable :style="{ width: '100%' }"></el-input>
      </el-form-item>
      <el-form-item label="资源类型" prop="type">
        <el-select v-model.trim="formData.type" :disabled="disabled" placeholder="请选择资源类型" clearable style="width: 40%" @change="typeChange">
          <el-option v-for="(item, index) in typeOptions" :key="index" :label="item.dictLabel" :value="+item.dictValue" :disabled="item.disabled"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="资源类别" prop="category">
        <el-select v-model.trim="formData.category" :disabled="disabled" placeholder="请选择资源类别" clearable :style="{ width: '40%' }">
          <el-option v-for="(item, index) in categoryOptions" :key="index" :label="item.dictLabel" :value="+item.dictValue"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="资源状态" prop="status">
        <el-radio-group v-model.trim="formData.status" :disabled="disabled">
          <el-radio :label="1">上线</el-radio>
          <el-radio :label="0">下线</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="资源封面" prop="resourceCoverAddress">
        <upload :files="coverFiles" :disabled="disabled" :data="{ type: '1' }" single :tip="false" :accept="`.JPG,.JPEG,.PNG`" @uploadSuccess="coverSuccess" @fileRemove="coverRemove"></upload>
      </el-form-item>
      <el-form-item v-if="formData.type == '1'" label="上传视频" prop="resourceAddress">
        <shard-uploader :files='videoFiles' ref="shardUploader" @uploadSuccess="videoSuccess" @fileRemove="videoRemove"></shard-uploader>
        <span style="font-size: 12px;">仅支持.MP4格式文件</span>
      </el-form-item>
      <el-form-item v-if="formData.type == '1'" label="内容描述" prop="contentDescription">
        <el-input v-model.trim="formData.contentDescription" type="textarea" :disabled="disabled" placeholder="请输入内容描述" clearable :maxlength="50" :style="{ width: '100%' }"></el-input>
      </el-form-item>
      <el-form-item v-if="formData.type == '2'" label="内容描述" prop="contentDescription">
        <editor v-model.trim="formData.contentDescription" :disabled="disabled" :minHeight="200"></editor>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => ({
        title: "",
        type: 1,
        category: "",
        resourceCoverAddress: "",
        resourceAddress: "",
        contentDescription: "",
        content: "",
        status: 1,
      }),
    },
  },
  data() {
    var validateContent = (rule, value, callback) => {
      if (value === '<p><br></p>') {
        callback(new Error('请输入文章内容'))
      } else {
        callback()
      }
    }
    return {
      rules: {
        title: [
          {
            required: true,
            message: "请输入资源标题",
            trigger: "blur",
          },
        ],
        type: [
          {
            required: true,
            message: "请选择资源类型",
            trigger: "change",
          },
        ],
        category: [
          {
            required: true,
            message: "请选择资源类别",
            trigger: "change",
          },
        ],
        contentDescription: [
          {
            required: true,
            message: "请输入内容描述",
            trigger: "blur",
          },
          { validator: validateContent, trigger: 'blur' }
        ],
        resourceCoverAddress: [
          {
            required: true,
            message: "请上传资源封面",
            trigger: "blur",
          },
        ],
        resourceAddress: [
          {
            required: true,
            message: "请上传资源视频",
            trigger: "blur",
          },
        ],
      },
      typeOptions: [],
      categoryOptions: [],
      coverFiles: [],
      videoFiles: [],
      videoIds: [],
    };
  },
  computed: {
    // coverFiles() {
    //   // console.log(this.formData.resourceCoverAddress)
    //   // return JSON.parse(this.formData.resourceCoverAddress)
    //   return []
    // }
  },
  watch: {},
  created() { },
  mounted() {
    this.initDicts();
  },
  methods: {
    initDicts() {
      this.getDicts("study_type").then((response) => {
        this.typeOptions = response.data;
      });
      this.getDicts("study_category").then((response) => {
        this.categoryOptions = response.data;
      });
    },
    submitForm() {
      this.$refs["elForm"].validate((valid) => {
        if (!valid) return;
        // TODO 提交表单
      });
    },
    resetForm() {
      this.$refs["elForm"].resetFields();
    },

    coverSuccess(res, file) {
      if (res.code != 200) return;
      this.$set(this.formData, "resourceCoverAddress", res.data.fileId);
      this.coverFiles = [file];
      this.$refs.elForm.validateField("resourceCoverAddress");
    },

    coverRemove(file) {
      this.coverFiles.length = 0;
      this.formData.resourceCoverAddress = ''
    },

    // videoSuccess(res, file) {
    //   if (res.code != 200) return;
    //   this.videoIds.push(res.data.fileId);
    //   this.videoFiles.push(file);
    // },
    videoSuccess(fileId) {
      this.$set(this.videoIds, 0, [fileId])
      this.formData.resourceAddress = fileId
      this.$refs.elForm.validateField("resourceAddress");
    },

    videoRemove() {
      // console.log(123);
      this.$set(this, 'videoIds', []);
      this.formData.resourceAddress = ''
      this.$refs.elForm.validateField("resourceAddress");
    },

    async getValue() {
      try {
        const result = await this.$refs["elForm"].validate();
        if (!result) return false;
        return {
          ...this.formData,
          resourceAddress: this.videoIds.join(","),
          contentDescription: encodeURI(this.formData.contentDescription)
        }
      } catch (error) {
        return false
      }
    },
    clearValidate() {
      this.$refs.elForm.clearValidate();
    },
    typeChange(value) {
      // console.log(value)
      this.formData.contentDescription = ''
    }
  }
}
</script>

<style>
</style>
