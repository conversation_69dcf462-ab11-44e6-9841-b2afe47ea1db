package com.thas.web.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.itextpdf.text.PageSize;
import com.thas.common.constant.Constants;
import com.thas.common.core.domain.entity.SysDictData;
import com.thas.common.core.domain.entity.SysRole;
import com.thas.common.core.domain.entity.SysUser;
import com.thas.common.core.text.Convert;
import com.thas.common.enums.*;
import com.thas.common.exception.ServiceException;
import com.thas.common.properties.AbstractSftpProperties;
import com.thas.common.utils.DateUtils;
import com.thas.common.utils.SecurityUtils;
import com.thas.common.utils.SftpUtil;
import com.thas.system.domain.SysUserHospital;
import com.thas.system.mapper.SysDictDataMapper;
import com.thas.system.mapper.SysUserHospitalMapper;
import com.thas.system.service.ISysUserHospitalService;
import com.thas.system.service.ISysUserService;
import com.thas.web.domain.AutSaAud;
import com.thas.web.domain.AutSaAudBusinessData;
import com.thas.web.domain.AutSaAudQueryDTO;
import com.thas.web.domain.AutSaAudReport;
import com.thas.web.domain.AutSaRelation;
import com.thas.web.domain.CertificateAbility;
import com.thas.web.domain.CstCertificationStandards;
import com.thas.web.domain.FileInfoDTO;
import com.thas.web.domain.FtlToPdfDTO;
import com.thas.web.domain.HospitalBaseInfo;
import com.thas.web.domain.HospitalDepartment;
import com.thas.web.domain.HospitalReviewCycle;
import com.thas.web.domain.HospitalReviewer;
import com.thas.web.domain.vo.ArticleVo;
import com.thas.web.domain.vo.AutSaAudDetailVO;
import com.thas.web.domain.vo.ChapterVo;
import com.thas.web.domain.vo.ClauseVo;
import com.thas.web.domain.vo.DomainGroupNode;
import com.thas.web.domain.vo.FileInfoVO;
import com.thas.web.domain.vo.SectionVo;
import com.thas.web.dto.*;
import com.thas.web.mapper.AutSaAudMapper;
import com.thas.web.mapper.CommonProcessMapper;
import com.thas.web.mapper.HospitalReviewerMapper;
import com.thas.web.pdf.PdfGenerateBuilder;
import com.thas.web.service.CommonService;
import com.thas.web.service.IAutSaAudBusinessDataService;
import com.thas.web.service.IAutSaAudService;
import com.thas.web.service.IAutSaRelationService;
import com.thas.web.service.ICstCertificationStandardsService;
import com.thas.web.service.IFileShareService;
import com.thas.web.service.IHospitalBaseInfoService;
import com.thas.web.service.IHospitalDomainGroupService;
import com.thas.web.service.IHospitalReviewCycleService;
import com.thas.web.service.IReviewFitMoveClauseService;
import com.thas.web.service.IUploadFileInfoService;
import com.thas.web.service.PdfGenParamService;
import com.thas.web.service.PdfGenerateService;
import com.thas.web.service.process.CommonProcessService;
import com.thas.web.utils.PdfUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.OutputStream;
import java.lang.reflect.Method;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * pdf生成 Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-19
 */
@Validated
@Transactional(rollbackFor = Exception.class)
@Slf4j
@Component
public class PdfGenerateServiceImpl implements PdfGenerateService {

    @Resource
    @Lazy
    private CommonProcessService commonProcessService;

    @Resource
    private IHospitalReviewCycleService hospitalReviewCycleService;

    @Resource
    private IAutSaRelationService autSaRelationService;

    @Autowired
    private IAutSaAudService autSaAudService;

    @Resource
    private SysUserHospitalMapper sysUserHospitalMapper;

    @Resource
    private ICstCertificationStandardsService cstCertificationStandardsService;

    @Resource
    private IAutSaAudBusinessDataService autSaAudBusinessDataService;

    @Resource
    private IHospitalBaseInfoService hospitalBaseInfoService;

    @Resource
    private CommonProcessMapper commonProcessMapper;

    @Resource
    private HospitalReviewerMapper hospitalReviewerMapper;

    @Resource
    private ISysUserService sysUserService;

    @Autowired
    private AbstractSftpProperties sftpImageProperties;

    @Resource
    private AutSaAudMapper autSaAudMapper;

    @Resource
    private ISysUserHospitalService sysUserHospitalService;

    @Resource
    private IHospitalDomainGroupService hospitalDomainGroupService;

    @Autowired
    private PdfGenParamService pdfGenParamService;

    @Resource
    private IUploadFileInfoService uploadFileInfoService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private IReviewFitMoveClauseService reviewFitMoveClauseService;

    @Autowired
    private IFileShareService fileShareService;

    /**
     * pdf模板配置信息路径
     */
    @Value("${thas.pdf.config}")
    private String thasPdfConfigUrl;

    private final static Map<String, String> riskScoreZhMap = new HashMap();
    private final static Map<String, String> autResultToScoreMap = new HashMap();
    private final static Map<String, String> initScoreClauseMap = new HashMap();
    /**
     * 当前节点，生成预评审报告枚举
     */
    public final static List<AutSaAudStatusEnum> temProcessEnums = new ArrayList();

    {
        riskScoreZhMap.put("0", "零");
        riskScoreZhMap.put("1", "一");
        riskScoreZhMap.put("2", "二");
        riskScoreZhMap.put("3", "三");
        riskScoreZhMap.put("4", "四");
        riskScoreZhMap.put("5", "五");
        riskScoreZhMap.put("6", "六");
        riskScoreZhMap.put("7", "七");
        riskScoreZhMap.put("8", "八");
        riskScoreZhMap.put("9", "九");

        autResultToScoreMap.put("1", "5");
        autResultToScoreMap.put("2", "4");
        autResultToScoreMap.put("3", "3");
        autResultToScoreMap.put("4", "2");
        autResultToScoreMap.put("5", "1");
        autResultToScoreMap.put("6", "0");
        //管理员不适用不用算占比
        autResultToScoreMap.put("9", "");

        initScoreClauseMap.put("clauseNo", "");
        initScoreClauseMap.put("isStar", "");
        initScoreClauseMap.put("saScore", "");
        initScoreClauseMap.put("srScore", "");
        initScoreClauseMap.put("srScoreZh", "");

        temProcessEnums.add(AutSaAudStatusEnum.SR_CLAUSE_PROCESS);
        temProcessEnums.add(AutSaAudStatusEnum.SR_V_CLAUSE_REJECT);
        temProcessEnums.add(AutSaAudStatusEnum.FR_REPORT_F_M);
        temProcessEnums.add(AutSaAudStatusEnum.FAR_CLAUSE_M);
        temProcessEnums.add(AutSaAudStatusEnum.TR_CLAUSE_M);
    }

    /**
     * 根据ftl模板生成对应的pdf文件
     *
     * @param ftlToPdfDTO 流程参数
     * @param response    响应
     */
    @Override
    public void ftlToPdf(FtlToPdfDTO ftlToPdfDTO, HttpServletResponse response) {
        // 根据ftl模板编码获取ftl模板枚举
        FtlToPdfEnum ftlToPdfEnum = FtlToPdfEnum.getFtlToPdfEnum(ftlToPdfDTO.getFtlTemplateCode());
        // 获取处理方法
        String methodName = ftlToPdfEnum.getMethod();
        log.error("pdfGenerateService -- ftl模板：{} ,模板编码：{} 对应处理方法：{}", ftlToPdfEnum.getDesc(), ftlToPdfEnum.getCode(), methodName);
        if (StringUtils.isBlank(methodName)) {
            log.error("pdfGenerateService -- ftl模板对应处理方法为空");
            throw new ServiceException(ServiceExceptionEnum.PDF_GENERATE_ERROR_1000000);
        }
        ftlToPdfDTO.setFtlToPdfEnum(ftlToPdfEnum);
        // 调用处理方法
        Method method = commonProcessService.getMethodByClassAndName(this.getClass(), methodName);
        if (method == null) {
            log.error("pdfGenerateService --未找到ftl模板对应处理方法：{}", methodName);
            throw new ServiceException(ServiceExceptionEnum.PDF_GENERATE_ERROR_1000001);
        }
        try {
            method.invoke(this, ftlToPdfDTO, response);
        } catch (Exception e) {
            log.error("ftl模板对应处理方法：{} 调用异常：{}", methodName, e.getCause().getMessage());
            throw new ServiceException(ServiceExceptionEnum.PDF_GENERATE_ERROR_1000002);
        }
    }

    @Override
    public void bingFtlToPdf(FtlToPdfDTO ftlToPdfDTO, HttpServletResponse response) {
        String ftlTemplateCode = ftlToPdfDTO.getFtlTemplateCode();
        // 匹配模板code 只通过免登的
        if ("hos_info_gen".equals(ftlTemplateCode)) {
            ftlToPdf(ftlToPdfDTO, response);
        }
    }

    /**
     * 生成医疗机构基本数据
     *
     * @param ftlToPdfDTO 流程参数
     * @param response    响应
     */
    private void hosInfoGen(FtlToPdfDTO ftlToPdfDTO, HttpServletResponse response) {
        // 生成医疗机构详情的时候，还无医院账号，生成记录是无状态的，需要前端传入applyNo，
        // 根据applyNo查询到对应的数据，然后生成对应的PDF。
        Map<String, Object> otherParam = ftlToPdfDTO.getOtherParam();
        String applyNo;
        if (MapUtil.isEmpty(otherParam) || StrUtil.isEmpty(applyNo = MapUtil.getStr(otherParam, "applyNo"))) {
            log.error(ServiceExceptionEnum.PDF_GENERATE_ERROR_1000004.getMessage());
            throw new ServiceException(ServiceExceptionEnum.PDF_GENERATE_ERROR_1000004);
        }
        long startTime1 = System.currentTimeMillis();
        Map<String, Object> paramMap = genHosInfoParams(applyNo);
        long startTime2 = System.currentTimeMillis();
        try (OutputStream out = response.getOutputStream()) {
            log.info("生成医疗机构基本数据->获取pdf文档参数耗时：{}", startTime2 - startTime1);
            PdfGenerateBuilder pdfGenerateBuilder = new PdfGenerateBuilder(null, null, 6, PageSize.A4, ftlToPdfDTO.getFtlTemplateCode());
            byte[] pdfBytes = PdfUtils.generatePdf(thasPdfConfigUrl, ftlToPdfDTO.getFtlToPdfEnum().getImagePath(), ftlToPdfDTO.getFtlToPdfEnum().getTemplateName(), paramMap, pdfGenerateBuilder);
            log.info("生成医疗机构基本数据->生成pdf文档耗时：{}", System.currentTimeMillis() - startTime2);
            out.write(pdfBytes);
            out.flush();
        } catch (Exception e) {
            log.error("生成pdf医疗机构基本数据 e:{}", e.getMessage());
            throw new ServiceException(ServiceExceptionEnum.FILE_ERROR_1000004);
        }
    }

    private Map<String, Object> genHosInfoParams(String applyNo) {
        QueryBaseConditionDTO queryBaseConditionDTO = new QueryBaseConditionDTO();
        queryBaseConditionDTO.setCommonId(applyNo);
        HospitalBaseInfoDTO hospitalBaseInfoDTO = hospitalBaseInfoService.queryHospitalDetail(queryBaseConditionDTO);
        // 拆分科室类型
        List<HospitalDepartment> medicalServiceDepartmentList = new ArrayList<>();
        List<HospitalDepartment> clinicalServiceDepartmentList = new ArrayList<>();
        List<HospitalDepartment> hospitalDepartmentList = hospitalBaseInfoDTO.getHospitalDepartmentList();
        for (HospitalDepartment hospitalDepartment : hospitalDepartmentList) {
            if ("medical_service".equals(hospitalDepartment.getDepartmentDictType())) {
                medicalServiceDepartmentList.add(hospitalDepartment);
            }
            if ("clinical_service".equals(hospitalDepartment.getDepartmentDictType())) {
                clinicalServiceDepartmentList.add(hospitalDepartment);
            }
        }
        hospitalBaseInfoDTO.setHospitalDepartmentList(null);

        String businessTime = hospitalBaseInfoDTO.getHospitalBaseInfo().getBusinessTime();
        String newBusinessTime = DateUtil.format(DateUtil.parse(businessTime), DatePattern.CHINESE_DATE_PATTERN);
        String beforeReviewerDate = hospitalBaseInfoDTO.getHospitalBaseInfo().getBeforeReviewerDate();
        if (StringUtils.isNotEmpty(beforeReviewerDate)) {
            String newBeforeReviewerDate = DateUtil.format(DateUtil.parse(beforeReviewerDate, "yyyy-MM"), "yyyy年MM月");
            hospitalBaseInfoDTO.getHospitalBaseInfo().setBeforeReviewerDate(newBeforeReviewerDate);
        }
        String expectReviewDate = hospitalBaseInfoDTO.getHospitalBaseInfo().getExpectReviewDate();
        String newExpectReviewDate = DateUtil.format(DateUtil.parse(expectReviewDate), DatePattern.CHINESE_DATE_PATTERN);
        hospitalBaseInfoDTO.getHospitalBaseInfo().setExpectReviewDate(newExpectReviewDate);

        hospitalBaseInfoDTO.getHospitalBaseInfo().setBusinessTime(newBusinessTime);

        String jsonStr = JSONUtil.toJsonStr(hospitalBaseInfoDTO);
        Map<String, Object> params = JSONUtil.parseObj(jsonStr).toBean(Map.class);
        params.put("medicalServiceDepartmentList", medicalServiceDepartmentList);
        params.put("clinicalServiceDepartmentList", clinicalServiceDepartmentList);

        List<CertificateAbility> certificateAbilityList = hospitalBaseInfoDTO.getCertificateAbilityList();
        if (certificateAbilityList == null) {
            certificateAbilityList = new ArrayList<>();
        }
        // 查询"hold_certification"类型字典。
        String dictDataType = "hold_certification";
        List<SysDictData> sysDictDataList = sysDictDataMapper.selectDictDataByType(dictDataType);
        if (CollectionUtils.isEmpty(sysDictDataList)) {
            return params;
        }

        List<CertificateAbility> allCertificateAbilityList = new ArrayList<>();
        for (SysDictData sysDictData : sysDictDataList) {
            CertificateAbility certificateAbility = new CertificateAbility();
            certificateAbility.setAbilityDictType(dictDataType);
            certificateAbility.setAbilityDictValue(sysDictData.getDictValue());
            certificateAbility.setAbilityName(sysDictData.getDictLabel());
            allCertificateAbilityList.add(certificateAbility);

            for (CertificateAbility cer : certificateAbilityList) {
                if (sysDictData.getDictValue().equals(cer.getAbilityDictValue())) {
                    certificateAbility.setExpireDate(cer.getExpireDate());
                    certificateAbility.setAbilityName(cer.getAbilityName());
                    String expireDate = cer.getExpireDate();
                    if (StringUtils.isNotEmpty(expireDate)) {
                        String newExpireDate = DateUtil.format(DateUtil.parse(expireDate, "yyyy-MM"), "yyyy年MM月");
                        certificateAbility.setExpireDate(newExpireDate);
                    }
                }
            }
            params.put("allCertificateAbilityList", allCertificateAbilityList);
        }
        return params;

    }

    @Autowired
    private SysDictDataMapper sysDictDataMapper;

    /**
     * 医疗结构对评审计划审核结果生成PDF 医院端盖章上传。
     */
    private void hosRewPlan(FtlToPdfDTO ftlToPdfDTO, HttpServletResponse response) {
        Map<String, Object> otherParam = ftlToPdfDTO.getOtherParam();
        // 获取到对应的审核里结果
        String description = MapUtil.getStr(otherParam, "description");
        if (CharSequenceUtil.isEmpty(description)) {
            throw new ServiceException(ServiceExceptionEnum.PDF_GENERATE_ERROR_1000005);
        }
        String refuseStudentReason = MapUtil.getStr(otherParam, "refuseStudentReason", "");

        SysRole sysRole = commonService.selectSysRole();
        log.info("当前登录角色key:{}", sysRole.getRoleKey());
        if (!Constants.HospitalConstants.ROLE_HOSPITAL.equals(sysRole.getRoleKey())) {
            throw new ServiceException(ServiceExceptionEnum.SYS_USER_ERROR_1000004);
        }
        Long userId = SecurityUtils.getUserId();
        SysUserHospital sysUserHospital = sysUserHospitalMapper.selectSysUserHospitalByUserId(userId);
        if (ObjectUtils.isEmpty(sysUserHospital)) {
            throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000000);
        }
        String applyNo = sysUserHospital.getHospitalApplyNo();
        // String applyNo = MapUtil.getStr(otherParam, "applyNo");;
        // 获取到对应生成PDF需要的参数
        long startTime1 = System.currentTimeMillis();
        Map<String, Object> params = pdfGenParamService.hosRewPlanParam(applyNo);
        params.put("rejectReason", description);
        params.put("refuseStudentReason", refuseStudentReason);
        log.info("{}", JSONUtil.toJsonStr(params));
        long startTime2 = System.currentTimeMillis();
        try (OutputStream out = response.getOutputStream()) {
            log.info("医疗结构对评审计划审核结果->获取pdf文档参数耗时：{}", startTime2 - startTime1);
            byte[] pdfBytes = PdfUtils.generatePdf(thasPdfConfigUrl, ftlToPdfDTO.getFtlToPdfEnum().getImagePath(), ftlToPdfDTO.getFtlToPdfEnum().getTemplateName(), params, null);
            log.info("医疗结构对评审计划审核结果->生成pdf文档耗时：{}", System.currentTimeMillis() - startTime2);
            out.write(pdfBytes);
            out.flush();
        } catch (Exception e) {
            log.error("医疗结构对评审计划审核结果 e:{}", e.getMessage());
            throw new ServiceException(ServiceExceptionEnum.FILE_ERROR_1000009);
        }
    }

    /**
     * 评审员利益冲突申报表
     */
    private void reviewInterest(FtlToPdfDTO ftlToPdfDTO, HttpServletResponse response) {
        Map<String, Object> otherParam = ftlToPdfDTO.getOtherParam();
        String accountId = MapUtil.getStr(otherParam, "accountId");
        String applyNo = MapUtil.getStr(otherParam, "applyNo");
        String hospitalName = MapUtil.getStr(otherParam, "hospitalName");
        if (StringUtils.isEmpty(accountId)) {
            throw new ServiceException("评审员利益冲突列表accountId不能为空");
        }
        if (StringUtils.isEmpty(hospitalName)) {
            throw new ServiceException("评审员利益冲突列表hospitalName不能为空");
        }
        if (StringUtils.isEmpty(applyNo)) {
            throw new ServiceException("评审员利益冲突列表applyNo不能为空");
        }
        long startTime1 = System.currentTimeMillis();
        Map<String, Object> params = pdfGenParamService.reviewInterestParam(applyNo, accountId);
        params.put("hospitalName", hospitalName);
        long startTime2 = System.currentTimeMillis();
        try (OutputStream out = response.getOutputStream()) {
            log.info("评审员利益冲突申报表->获取pdf文档参数耗时：{}", startTime2 - startTime1);
            byte[] pdfBytes = PdfUtils.generatePdf(thasPdfConfigUrl, ftlToPdfDTO.getFtlToPdfEnum().getImagePath(), ftlToPdfDTO.getFtlToPdfEnum().getTemplateName(), params, null);
            log.info("评审员利益冲突申报表->生成pdf文档耗时：{}", System.currentTimeMillis() - startTime2);
            out.write(pdfBytes);
            out.flush();
        } catch (Exception e) {
            log.error("评审员利益冲突申报表 e:{}", e.getMessage());
            throw new ServiceException(ServiceExceptionEnum.FILE_ERROR_1000012);
        }
    }

    /**
     * 生成医院自评报告
     *
     * @param ftlToPdfDTO 流程参数
     * @param response    响应
     */
    private void audSaReport(FtlToPdfDTO ftlToPdfDTO, HttpServletResponse response) {
        // 校验并获取关联信息
        AutSaRelation autSaRelation = this.audSaReportCheckAndGetAutSaRelation(ftlToPdfDTO);
        // pdf生成校验节点
        if (StringUtils.equals(MapUtils.getString(ftlToPdfDTO.getOtherParam(), Constants.CHECK_STATUS, Constants.FLAG_VALUE_Y), Constants.FLAG_VALUE_Y)) {
            // 校验对应的节点信息
            if (!AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.SA_SUMMARY, AutSaAudStatusEnum.FR_REPORT_SUBMIT)) {
                log.error("账户：{} 操作 医疗机构：{} 自评编码：{} 生成报告时 节点：{} 不正确", autSaRelation.getAutStatus(), autSaRelation.getHospitalApplyNo(), autSaRelation.getAutCode(), autSaRelation.getAutStatus());
                throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000003);
            }
        }
        // 要生成自评报告需要先提交自评报告生成  sa_report_desc
        List<AutSaAud> termSaSumList = autSaAudService.selectAutSaAudListByAutCodeAndTypes(autSaRelation.getAutCode(), AutSaAudSubmitTypeEnum.SA_REPORT_DESC.getSubmitType());
        if (CollectionUtils.isEmpty(termSaSumList)) {
            log.error("自评编码：{} 对应医疗机构：{} 未进行自评报告生成操作，不能生成自评报告", autSaRelation.getAutCode(), autSaRelation.getHospitalApplyNo());
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000003);
        }
        // 获取登录用户生成自评报告的相关参数
        long startTime1 = System.currentTimeMillis();
        Map<String, Object> paramMap = this.tissueAudSaReport(autSaRelation, termSaSumList);
        // 医疗机构基本信息
        paramMap.putAll(this.getHospitalBaseInfo(autSaRelation.getHospitalApplyNo()));
        //模板主题后缀;文件名：[医院名]-自评报告初稿、[医院名]-自评报告终稿
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.FR_REPORT_SUBMIT)) {
            paramMap.put("topicSuffix", " ");
            paramMap.put("pdfFileName", paramMap.get("hospitalName").toString() + "-自评报告.pdf");
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.SA_SUMMARY)) {
            paramMap.put("topicSuffix", "初稿");
            paramMap.put("pdfFileName", paramMap.get("hospitalName").toString() + "-自评报告初稿.pdf");
        }
        log.info("获取pdf文档参数耗时：{}", System.currentTimeMillis() - startTime1);
        PdfGenerateBuilder pdfGenerateBuilder = new PdfGenerateBuilder(" " + MapUtils.getString(paramMap, "hospitalName"), MapUtils.getString(paramMap, "hospitalCode"), 9, PageSize.A4, ftlToPdfDTO.getFtlTemplateCode());
        ftlToPdfDTO.getOtherParam().put("autCode", autSaRelation.getAutCode());
        this.generatePdf(ftlToPdfDTO, response, paramMap, pdfGenerateBuilder);
    }

    /**
     * 校验并获取关联信息
     *
     * @param ftlToPdfDTO 流程参数
     * @return 关联信息
     */
    private AutSaRelation checkAndGetAutSaRelation(FtlToPdfDTO ftlToPdfDTO, AutSaAudRoleEnum autSaAudRoleEnum) {
        // 获取登录用户信息
        SysUser sysUser = sysUserService.getSysUserInfo(null);
        // 校验角色
        if (!AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(sysUser.getRoleKey(), autSaAudRoleEnum)) {
            log.error("账户:{} 角色:{} 和指定角色：{} 不匹配， 无权限操作", sysUser.getUserId(), sysUser.getRoleKey(), autSaAudRoleEnum.getRoleKey());
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000003);
        }
        AutSaRelation autSaRelation;
        if (AutSaAudRoleEnum.HOSPITAL == autSaAudRoleEnum) {
            // 医院端处理时，需要使用登录用户信息获取关联信息
            SysUserHospital sysUserHospital = sysUserHospitalService.selectSysUserHospitalByUserId(sysUser.getUserId());
            if (sysUserHospital == null) {
                log.error("账户:{}未关联医疗机构", sysUser.getUserId());
                throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000000);
            }
            // 根据医疗机构编码获取对应自评编码信息
            autSaRelation = autSaRelationService.selectAutSaRelationByHospitalApplyNo(sysUserHospital.getHospitalApplyNo(), false);
            if (autSaRelation == null) {
                // 数据有问题，没有查到自评关联数据
                log.error("账户：{}对应的applyNo：{}，没有查到自评关联数据", sysUser.getUserId(), sysUserHospital.getHospitalApplyNo());
                throw new ServiceException(ServiceExceptionEnum.AUT_SA_RELATION_ERROR_1000015);
            }
        } else {
            // 非医院端处理时，需要使用autCode获取关联信息
            String autCode = MapUtils.getString(ftlToPdfDTO.getOtherParam(), Constants.AUT_CODE);
            if (StringUtils.isBlank(autCode)) {
                log.error("生成报告时 必传参数autCode为空");
                throw new ServiceException(ServiceExceptionEnum.AUT_SA_RELATION_ERROR_1000004);
            }
            // 根据医疗机构编码获取对应自评编码信息
            autSaRelation = autSaRelationService.selectAutSaRelationByAutCode(autCode, Constants.HospitalConstants.NUM_1);
            // 校验权限
            if (commonProcessMapper.checkPdfGeneratePermission(sysUser.getUserId().toString(), sysUser.getRoleKey(), autSaRelation.getHospitalApplyNo()) == Constants.HospitalConstants.NUM_0) {
                log.error("账户:{} 未关联医疗机构：{}， 无权限操作", sysUser.getUserId(), autSaRelation.getHospitalApplyNo());
                throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000003);
            }
        }
        // 校验对应的时间信息
        commonProcessService.checkQueryCycle(autSaRelation.getHospitalApplyNo(), autSaRelation.getAutCode(), autSaRelation.getAutStatus());
        return autSaRelation;
    }

    /**
     * 自评报告校验并获取关联信息
     *
     * @param ftlToPdfDTO 流程参数
     * @return 关联信息
     */
    private AutSaRelation audSaReportCheckAndGetAutSaRelation(FtlToPdfDTO ftlToPdfDTO) {
        // 获取登录用户信息
        SysUser sysUser = sysUserService.getSysUserInfo(null);

        AutSaRelation autSaRelation = null;
        if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(sysUser.getRoleKey(), AutSaAudRoleEnum.HOSPITAL)) {
            // 医院端处理时，需要使用登录用户信息获取关联信息
            SysUserHospital sysUserHospital = sysUserHospitalService.selectSysUserHospitalByUserId(sysUser.getUserId());
            if (sysUserHospital == null) {
                log.error("账户:{}未关联医疗机构", sysUser.getUserId());
                throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000000);
            }
            // 根据医疗机构编码获取对应自评编码信息
            autSaRelation = autSaRelationService.selectAutSaRelationByHospitalApplyNo(sysUserHospital.getHospitalApplyNo(), false);
            if (autSaRelation == null) {
                // 数据有问题，没有查到自评关联数据
                log.error("账户：{}对应的applyNo：{}，没有查到自评关联数据", sysUser.getUserId(), sysUserHospital.getHospitalApplyNo());
                throw new ServiceException(ServiceExceptionEnum.AUT_SA_RELATION_ERROR_1000015);
            }
        } else if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(sysUser.getRoleKey(), AutSaAudRoleEnum.INSPECTOR)) {
            String autCode = ftlToPdfDTO.getOtherParam().get("autCode").toString();
            if (StringUtils.isBlank(autCode)) {
                throw new ServiceException("自评终稿提交-入参autCode不能为空！");
            }
            autSaRelation = autSaRelationService.selectAutSaRelationByAutCode(autCode, Constants.INT_ONE);
            if (!AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.FR_REPORT_SUBMIT)) {
                log.error("审查员:{} 医疗机构：{}，当前节点：{}， 无权限操作", sysUser.getUserId(), autSaRelation.getHospitalApplyNo(), autSaRelation.getAutStatus());
                throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000003);
            }
        } else {
            log.error("账户:{} 角色:{} 和指定角色：{} 不匹配， 无权限操作", sysUser.getUserId(), sysUser.getRoleKey(), "医院和审查员");
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000003);
        }
        // 校验对应的时间信息
        commonProcessService.checkQueryCycle(autSaRelation.getHospitalApplyNo(), autSaRelation.getAutCode(), autSaRelation.getAutStatus());
        return autSaRelation;
    }

    /**
     * 组织自评报告模板参数
     *
     * @param autSaRelation 关联关系
     * @param termSaSumList 自评报告生成信息
     * @return 模板参数
     */
    private Map<String, Object> tissueAudSaReport(AutSaRelation autSaRelation, List<AutSaAud> termSaSumList) {
        Map<String, Object> paramMap = new HashMap();
        // 提交日期 xxxx年xx月xx日
        paramMap.put("submitDate", DateUtils.parseDateToStr("yyyy年MM月dd日", termSaSumList.get(0).getSubmitDate()));
        String autDesc = termSaSumList.get(0).getAutDesc();
        Map<String, Object> termSaSum = null;
        if (StringUtils.isNotBlank(autDesc)) {
            termSaSum = JSON.parseObject(autDesc, Map.class);
        }
        // 亮点      autAdvantage
        paramMap.put("autAdvantage", MapUtils.getString(termSaSum, "autAdvantage", "").replaceAll("<", "&lt;").replaceAll(">", "&gt;"));
        // 不足      autEvaluate
        paramMap.put("autEvaluate", MapUtils.getString(termSaSum, "autEvaluate", "").replaceAll("<", "&lt;").replaceAll(">", "&gt;"));
        // 改进计划   autProposal
        paramMap.put("autProposal", MapUtils.getString(termSaSum, "autProposal", "").replaceAll("<", "&lt;").replaceAll(">", "&gt;"));
        // 根据自评编码查询对应的所有自评信息
        List<AutSaAud> autSaAuds = autSaAudService.selectAutSaAudListByAutCodeAndTypes(autSaRelation.getAutCode(), String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SA_CLAUSE.getSubmitType())));
        if (CollectionUtils.isEmpty(autSaAuds)) {
            log.error("操作 医疗机构：{} 自评编码：{}  生成报告时 自评数据为空", autSaRelation.getHospitalApplyNo(), autSaRelation.getAutCode());
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000003);
        }

        //封装’（二）条款评分情况‘
        this.packChapterVosList(autSaRelation, autSaAuds, paramMap, "1", null);

        List<AutSaAudBusinessData> saReportBusinessDatas = autSaAudBusinessDataService.selectAutSaAudBusinessData(autSaRelation.getAutCode(), AutSaAudBusinessCodeEnum.AUT_SA_AUD_SA_REPORT.getCode());
        AutSaAudReport autSaAudReport;
        if (CollectionUtils.isNotEmpty(saReportBusinessDatas) && StringUtils.isNotBlank(saReportBusinessDatas.get(0).getData())) {
            autSaAudReport = JSON.parseObject(saReportBusinessDatas.get(0).getData(), AutSaAudReport.class);
        } else {
            autSaAudReport = commonProcessService.generateReport(autSaRelation.getAutCode(), autSaRelation.getAutCsId(), String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SA_CLAUSE.getSubmitType())), AutSaAudBusinessCodeEnum.AUT_SA_AUD_SA_REPORT.getCode(), autSaAuds);
        }
        paramMap.put("autSaAudReportListVo", autSaAudReport.getAutSaAudReportListVos());

        return paramMap;
    }

    /**
     * 封装评审结果明细（条款评分明细）
     *  @param autSaRelation
     * @param srClauseAutSaAuds 根据自评编码查询对应的所有评审信息(取最新更新的款)
     * @param paramMap
     * @param flag              1-自评报告 2-评审报告
     * @param riskInfoList
     */
    private void packChapterVosList(AutSaRelation autSaRelation, List<AutSaAud> srClauseAutSaAuds, Map<String, Object> paramMap, String flag, List<RiskInfoVO> riskInfoList) {
        List<AutSaAud> autSaAuds;
        // 根据版本号获取对应的版本信息
        List<ChapterVo> chapterVosList = cstCertificationStandardsService.selectCstCertificationStandardsByVersionId(autSaRelation.getAutCsId());
        if (CollectionUtils.isEmpty(chapterVosList)) {
            log.error("操作 医疗机构：{}  自评编码：{}  生成报告时 根据版本号:{} 获取版本信息为空", autSaRelation.getHospitalApplyNo(), autSaRelation.getAutCode(), autSaRelation.getAutCsId());
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
        }

        // 根据自评编码查询对应的所有自评信息
        List<AutSaAud> saClauseAutSaAudList = autSaAudService.selectAutSaAudListByAutCodeAndTypes(autSaRelation.getAutCode(), String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SA_CLAUSE.getSubmitType())));
        if (CollectionUtils.isEmpty(saClauseAutSaAudList)) {
            log.error("操作 医疗机构：{} 自评编码：{}  生成报告时 自评数据为空", autSaRelation.getHospitalApplyNo(), autSaRelation.getAutCode());
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000003);
        }
        Map<String, String> saClauseMap = saClauseAutSaAudList.stream().collect(Collectors.toMap(AutSaAud::getClauseId, AutSaAud::getAutResult));

        //自评报告生成时，封装文件信息
        Map<String, List<FileInfoVO>> fileDetailMap = new HashMap<>();
        if (Constants.STR_NUM_1.equals(flag)) {
            List<String> fileIds = saClauseAutSaAudList.stream().filter(a -> StringUtils.isNotBlank(a.getFileIds())).map(a -> a.getFileIds()).collect(Collectors.toList());
            // 获取所有的文件列表信息
            if (CollectionUtils.isNotEmpty(fileIds)) {
                fileIds = Arrays.asList(String.join(",", fileIds).split(",")).stream().distinct().collect(Collectors.toList());
            }
            List<FileInfoVO> fileInfoVOList = commonService.fileInfoDtoToVo(uploadFileInfoService.getUploadFileInfoByIds(String.join(",", fileIds)));
            fileDetailMap = fileInfoVOList.stream().collect(Collectors.groupingBy(FileInfoVO::getFileId));
            autSaAuds = saClauseAutSaAudList;
        } else if (Constants.STR_NUM_2.equals(flag)) {
            //评审报告
            autSaAuds = srClauseAutSaAuds;
        } else {
            throw new ServiceException("除了自评报告/评审报告，暂时不支持其他报告调用此方法！");
        }

        Map<String, List<AutSaAud>> termSaMap = autSaAuds.stream().collect(Collectors.groupingBy(AutSaAud::getClauseId));
        // 条款具体自评情况      填充数据
        Map<String, List<FileInfoVO>> finalFileDetailMap = fileDetailMap;
        chapterVosList.forEach(a -> {
            List<SectionVo> sectionVoList = a.getSectionVoList();
            if (CollectionUtils.isNotEmpty(sectionVoList)) {
                sectionVoList.forEach(b -> {
                    List<ArticleVo> articleVoList = b.getArticleVoList();
                    if (CollectionUtils.isNotEmpty(articleVoList)) {
                        articleVoList.forEach(c -> {
                            List<ClauseVo> clauseVoList = c.getClauseVoList();
                            if (CollectionUtils.isNotEmpty(clauseVoList)) {
                                clauseVoList.forEach(d -> {

                                    if (termSaMap.containsKey(d.getClauseId().toString())) {
                                        List<AutSaAud> termSas = termSaMap.get(d.getClauseId().toString());
                                        if (CollectionUtils.isNotEmpty(termSas)) {

                                            //自评报告
                                            if (Constants.STR_NUM_1.equals(flag)) {
                                                // 款描述
                                                d.setClauseDesc(StringUtils.isNotBlank(termSas.get(0).getAutDesc()) ? descStrReplaceAll(termSas.get(0).getAutDesc()) : "");

                                                String clauseFileNames = "";
                                                List<FileInfoVO> clauseFileInfoList = new ArrayList<>();

                                                if (StringUtils.isNotBlank(termSas.get(0).getFileIds())) {
                                                    clauseFileInfoList = Arrays.asList(termSas.get(0).getFileIds().split(",")).stream()
                                                            .filter(fileId -> CollectionUtils.isNotEmpty(finalFileDetailMap.get(fileId)))
                                                            .map(fileId -> finalFileDetailMap.get(fileId).get(0)).collect(Collectors.toList());
                                                    clauseFileNames = clauseFileInfoList.stream().map(FileInfoVO::getFileName).collect(Collectors.joining(";"));
                                                }
                                                d.setFileNames(clauseFileNames);

                                                //fileDetailMap文件信息，termSas为sa_clause提交类型医院自评信息；
                                                d.setClauseFileInfoList(clauseFileInfoList);

                                                // 款结果
                                                d.setClauseResult(termSas.get(0).getAutResult());
                                            } else {
                                                //评审报告
                                                //当前款信息
                                                AutSaAud autSaAud = termSas.get(0);

                                                //封装风险分析数据
                                                //1，此款为基本款(是否带*(0否 1是))且不达标或部分达标时，封装风险评估信息
                                                if (Constants.STR_NUM_1.equals(d.getIsStar()) && AutSaAudResultEnum.checkIsAutSaAudResultEnum(
                                                        autSaAud.getAutResult(), AutSaAudResultEnum.PARTIAL_COMPLIANCE, AutSaAudResultEnum.NOT_STANDARD)) {
                                                    //2，封装款号（名称），评价结果，不足（aut_desc字段封装的不足字段autEvaluate），风险等级（分配两个字段状态1，2，3，需要相乘得到分数）
                                                    RiskInfoVO riskInfoVO = new RiskInfoVO();
                                                    riskInfoVO.setClauseId(autSaAud.getClauseId());
                                                    riskInfoVO.setClauseNo(d.getClauseNo());
                                                    riskInfoVO.setAutResult(autSaAud.getAutResult());
                                                    Map<String, Object> autDescMap = JSON.parseObject(autSaAud.getAutDesc(), Map.class);
                                                    riskInfoVO.setAutEvaluate(this.descStrReplaceAll( MapUtils.getString(autDescMap, "autEvaluate", "")));
                                                    riskInfoVO.setRiskLevel(String.valueOf(autSaAud.getRiskImpact() * autSaAud.getRiskPossibility()));
                                                    riskInfoList.add(riskInfoVO);
                                                }
                                                //自评结果
                                                d.setSaClauseResult(saClauseMap.get(autSaAud.getClauseId()));
                                                //评审结果
                                                d.setSrClauseResult(autSaAud.getAutResult());
                                                //评审描述
                                                Map<String, Object> descMap = JSON.parseObject(autSaAud.getAutDesc(), Map.class);
                                                d.setAutAdvantage(descStrReplaceAll(MapUtils.getString(descMap, "autAdvantage", "")));
                                                d.setAutImprove(descStrReplaceAll(MapUtils.getString(descMap, "autImprove", "")));
                                                d.setAutEvaluate(descStrReplaceAll(MapUtils.getString(descMap, "autEvaluate", "")));
                                                d.setAutProposal(descStrReplaceAll(MapUtils.getString(descMap, "autProposal", "")));
                                            }
                                        }
                                    }
                                });
                            }
                        });
                    }
                });
            }
        });
        paramMap.put("chapterVosList", chapterVosList);

    }

    /**
     * 获取医院编码
     *
     * @param applyNo 医疗机构编码
     * @return 医院编码
     */
    private Map<String, Object> getHospitalBaseInfo(String applyNo) {
        Map<String, Object> hospitalBaseInfoMap = new HashMap();
        QueryBaseConditionDTO queryBaseConditionDTO = new QueryBaseConditionDTO();
        queryBaseConditionDTO.setCommonId(applyNo);
        HospitalBaseInfo hospitalBaseInfo = hospitalBaseInfoService.selectHospitalByApplyNo(queryBaseConditionDTO);
        String hospitalCode = "";
        String hospitalName = "";
        if (hospitalBaseInfo != null) {
            if (StringUtils.isNotBlank(hospitalBaseInfo.getPostalOde()) && hospitalBaseInfo.getPostalOde().length() > 4) {
                String postalOde = hospitalBaseInfo.getPostalOde();
                Map<String, Object> countMap = hospitalBaseInfoService.selectCurrentYearApplyCountByApplyNO(applyNo);
                hospitalCode = " " + postalOde.substring(0, 4) + MapUtils.getString(countMap, "year").substring(2) + String.format("%03d", MapUtils.getIntValue(countMap, "count"));
            }
            if (StringUtils.isNotBlank(hospitalBaseInfo.getHospitalName())) {
                hospitalName = hospitalBaseInfo.getHospitalName().replaceAll("<", "&lt;").replaceAll(">", "&gt;");
            }
        }
        hospitalBaseInfoMap.put("hospitalCode", hospitalCode);
        hospitalBaseInfoMap.put("applyNo", applyNo);
        hospitalBaseInfoMap.put("hospitalName", hospitalName);
        //医院级别:1一级医院 2二级医院 3三级医院
        hospitalBaseInfoMap.put("hospitalLevelName", AudSaReportEnum.HospitalLevelEnum.getHospitalLevelDesc(hospitalBaseInfo.getHospitalLevel()));
        //等级
        hospitalBaseInfoMap.put("BeforeReviewerConclusionLevelName",
                AudSaReportEnum.BeforeReviewerConclusionLevelEnum.getBeforeReviewerConclusionLevelDesc(hospitalBaseInfo.getBeforeReviewerConclusionLevel()));
        //属性
        hospitalBaseInfoMap.put("NatureOperationName", AudSaReportEnum.NatureOperationEnum.getNatureOperationDesc(hospitalBaseInfo.getNatureOperation()));
        return hospitalBaseInfoMap;
    }

    /**
     * 生成PDF
     *
     * @param paramMap           模板相关参数
     * @param ftlToPdfDTO        流程参数
     * @param response           响应
     * @param pdfGenerateBuilder 页面页眉页脚配置信息
     */
    public void generatePdf(FtlToPdfDTO ftlToPdfDTO, HttpServletResponse response, Map<String, Object> paramMap, PdfGenerateBuilder pdfGenerateBuilder) {
        // 获取生成的pdf文件流
        byte[] pdfBytes;
        try {
            long startTime1 = System.currentTimeMillis();
            pdfBytes = PdfUtils.generatePdf1(thasPdfConfigUrl, ftlToPdfDTO.getFtlToPdfEnum().getImagePath(), ftlToPdfDTO.getFtlToPdfEnum().getTemplateName(), paramMap, pdfGenerateBuilder);
            log.info("生成pdf文档耗时：{}", System.currentTimeMillis() - startTime1);
        } catch (Exception e) {
            log.error("生成pdf自评报告异常 e:{}", e.getMessage());
            throw new ServiceException(ServiceExceptionEnum.FILE_ERROR_1000004);
        }
        if (ObjectUtil.isNull(response)) {
            // 将生成的pdf文件保存到sftp远程服务器上
            this.savePdfFile(ftlToPdfDTO, pdfBytes, paramMap);
        } else {
            try (OutputStream out = response.getOutputStream();) {
                out.write(pdfBytes);
                out.flush();
            } catch (Exception e) {
                log.error("自评报告导出异常 e:{}", e.getMessage());
                throw new ServiceException(ServiceExceptionEnum.FILE_ERROR_1000010);
            }
        }
    }

    /**
     * 将生成的pdf文件保存到sftp远程服务器上
     *
     * @param ftlToPdfDTO 流程参数
     * @param pdfBytes    pdf文件
     */
    private void savePdfFile(FtlToPdfDTO ftlToPdfDTO, byte[] pdfBytes, Map<String, Object> paramMap) {
        // 需要保存生成的pdf文件
        String autCode = MapUtils.getString(ftlToPdfDTO.getOtherParam(), "autCode");
        if (StringUtils.isBlank(autCode)) {
            log.error("将生成的pdf文件上传文件到sftp服务器时，获取到自评编码为空");
            throw new ServiceException(ServiceExceptionEnum.FILE_ERROR_1000011);
        }
        String fileName = MapUtils.getString(ftlToPdfDTO.getOtherParam(), "pdfFileName");
        if (StringUtils.isBlank(fileName)) {
            fileName = MapUtils.getString(paramMap, "pdfFileName", ftlToPdfDTO.getFtlToPdfEnum().getCode() + "_" + autCode + ".pdf");
        }

        // sftp服务器上传路径
        String path = sftpImageProperties.getBasePath() + "/pdf/" + autCode + "/";
        log.info("savePdfFile 开始 fileName：{}，path：{}", fileName, path);
        try (SftpUtil sftpUtil = new SftpUtil(sftpImageProperties)) {
            long start = Instant.now().toEpochMilli();
            sftpUtil.upload(path, fileName, new ByteArrayInputStream(pdfBytes));
            long end = Instant.now().toEpochMilli();
            log.info("将生成的pdf文件上传SFTP服务器 耗时:{}", end - start);
        } catch (Exception e) {
            log.error("将生成的pdf文件上传文件到sftp服务器失败:{}", e.getMessage());
            throw new ServiceException(ServiceExceptionEnum.FILE_ERROR_1000006);
        }
        String url = path + fileName;
        FileInfoDTO fileInfoDTO = new FileInfoDTO();
        // 文件名
        fileInfoDTO.setOrigin(fileName);
        // 文件存储路径
        fileInfoDTO.setPath(url);
        // 文件来源
        fileInfoDTO.setPlatform(sftpImageProperties.getPlatform());
        // 文件类型
        fileInfoDTO.setType("pdf");
        fileInfoDTO = uploadFileInfoService.saveUploadFileInfo(fileInfoDTO);
        Map<String, String> dataMap = new HashMap();
        dataMap.put("url", url);
        dataMap.put("fileId", fileInfoDTO.getId().toString());
        AutSaAudBusinessData autSaAudBusinessData = new AutSaAudBusinessData();
        autSaAudBusinessData.setAutCode(autCode);
        autSaAudBusinessData.setBusinessCode(ftlToPdfDTO.getFtlTemplateCode());
        autSaAudBusinessData.setData(JSON.toJSONString(dataMap));
        autSaAudBusinessData.setUpdateTime(DateUtils.getNowDate());
        autSaAudBusinessDataService.saveAutSaAudBusinessData(autSaAudBusinessData);

        Long userId = SecurityUtils.getUserId();
        // 保存到资源库
        FileShareCreateRequest fileShareCreateRequest = new FileShareCreateRequest();
        fileShareCreateRequest.setRoleKey(new HashSet<>());
        fileShareCreateRequest.setFileIdList(Collections.singletonList(fileInfoDTO.getId()));
        fileShareCreateRequest.setSource("hospital_review");
        fileShareCreateRequest.setOwnerId(userId.toString());
        fileShareCreateRequest.setReviewerId(paramMap.getOrDefault("reviewerId", "").toString());
        fileShareCreateRequest.setReviewerName(paramMap.getOrDefault("reviewerName", "").toString());
        fileShareCreateRequest.setApplyNo(paramMap.getOrDefault("applyNo", "").toString());
        fileShareCreateRequest.setHospitalName(paramMap.getOrDefault("hospitalName", "").toString());
        fileShareService.fileShareCreate(fileShareCreateRequest);

    }

    /**
     * 生成评审报告
     *
     * @param ftlToPdfDTO 流程参数
     * @param response    响应
     */
    private void reviewReport(FtlToPdfDTO ftlToPdfDTO, HttpServletResponse response) {
        String autCode = MapUtils.getString(ftlToPdfDTO.getOtherParam(), Constants.AUT_CODE);
        if (StringUtils.isBlank(autCode)) {
            log.error("生成报告时 必传参数autCode为空");
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_RELATION_ERROR_1000004);
        }
        // 根据医疗机构编码获取对应自评编码信息
        AutSaRelation autSaRelation = autSaRelationService.selectAutSaRelationByAutCode(autCode, Constants.HospitalConstants.NUM_1);
        // 校验对应的节点信息
        String submitTypes = "";
        String title = "";
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.FR_REPORT_GENERATE, AutSaAudStatusEnum.SR_REJECT,
                AutSaAudStatusEnum.SR_CONDITIONAL_CERTIFICATION)) {
            // 待提交评审报告
            submitTypes = AutSaAudSubmitTypeEnum.SR_SUMMARY.getSubmitType();
            title = "(初稿)";
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.WAIT_FR_REPORT_R_CLAUSE)) {
            // 评审报告待审查
            submitTypes = AutSaAudSubmitTypeEnum.SR_CLAUSE_M_SUMMARY.getSubmitType();
            title = "(初稿)";
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.FAR_CLAUSE_M_SUBMIT)) {
            // 提交评审修改
            submitTypes = AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SUMMARY.getSubmitType();
            title = "(事实准确性确认)";
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.SR_REPORT_SUBMIT)) {
            // 提交评审报告
            submitTypes = AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SUMMARY.getSubmitType();
            title = "(终稿)";
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.SR_REPORT_M_MEET)) {
            // 评审流程结束  -- 修改评审报告总结 sr_report_m_summary
            submitTypes = AutSaAudSubmitTypeEnum.SR_REPORT_M_SUMMARY.getSubmitType();
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum1(autSaRelation.getAutStatus(), temProcessEnums)) {
            // {030202-评审中} {030204-评审复查中}  -- 生成预评审报告
            // {0302041-评审复查驳回 -->030204-评审复查中 -->转下个节点}，评审复查中转下个节点-更新报告
            //{030301-修改评审报告 -->030302-修改评审报告总结},{0304011-评审修改中 -->030402-评审修改审核中}, {0305011-修改验证评审数据 -->030502-验证评审数据审核}
            title = "(预审核)";
        } else {
            log.error("账户：{} 操作 医疗机构：{} 自评编码：{} 生成报告时 节点：{} 不正确", autSaRelation.getAutStatus(), autSaRelation.getHospitalApplyNo(), autSaRelation.getAutCode(), autSaRelation.getAutStatus());
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000003);
        }

        List<AutSaAud> srSummaryList = new ArrayList<>();
        //预评审报告节点无需检验
        if (!AutSaAudStatusEnum.checkIsAutSaAudStatusEnum1(autSaRelation.getAutStatus(), temProcessEnums)) {
            // 要生成评审报告需要先提交总结
            srSummaryList = autSaAudService.selectAutSaAudListByAutCodeAndTypes(autSaRelation.getAutCode(), submitTypes);
            if (CollectionUtils.isEmpty(srSummaryList)) {
                log.error("自评编码：{} 对应医疗机构：{} 未进行评审总结操作，不能生成评审报告", autSaRelation.getAutCode(), autSaRelation.getHospitalApplyNo());
                throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000003);
            }
        }

        // 获取登录用户生成评审报告的相关参数
        long startTime1 = System.currentTimeMillis();
        Map<String, Object> paramMap = this.tissueReviewReport(autSaRelation, srSummaryList);
        // 医疗机构基本信息
        paramMap.putAll(this.getHospitalBaseInfo(autSaRelation.getHospitalApplyNo()));
        //资源库保存评审员信息
        paramMap.put("reviewerId", SecurityUtils.getUserId());
        paramMap.put("reviewerName", SecurityUtils.getNickName());
        log.info("获取pdf文档参数耗时：{}", System.currentTimeMillis() - startTime1);
        PdfGenerateBuilder pdfGenerateBuilder;
        paramMap.put("title", title);
//        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum1(autSaRelation.getAutStatus(), temProcessEnums)) {
//            paramMap.put("title", "(预审核)");
//            pdfGenerateBuilder = new PdfGenerateBuilder(" " + MapUtils.getString(paramMap, "hospitalName") + paramMap.get("title"),
//                    MapUtils.getString(paramMap, "hospitalCode") + paramMap.get("title"), 12, PageSize.A4, ftlToPdfDTO.getFtlTemplateCode());
//            paramMap.put("pdfFileName", MapUtils.getString(paramMap, "hospitalName") + "-评审报告(预审核).pdf");
//        } else {
//            pdfGenerateBuilder = new PdfGenerateBuilder(" " + MapUtils.getString(paramMap, "hospitalName"), MapUtils.getString(paramMap, "hospitalCode"), 12, PageSize.A4, ftlToPdfDTO.getFtlTemplateCode());
//            paramMap.put("pdfFileName", MapUtils.getString(paramMap, "hospitalName") + "-评审报告.pdf");
//        }
        //文件名：医院名-评审报告(标题)，内容标题名：评审报告(标题)
        pdfGenerateBuilder = new PdfGenerateBuilder(" " + MapUtils.getString(paramMap, "hospitalName") + paramMap.get("title"),
                MapUtils.getString(paramMap, "hospitalCode") + paramMap.get("title"), 9, PageSize.A4, ftlToPdfDTO.getFtlTemplateCode());
        paramMap.put("pdfFileName", MapUtils.getString(paramMap, "hospitalName") + "-评审报告" + paramMap.get("title") + ".pdf");
        ftlToPdfDTO.getOtherParam().put("autCode", autSaRelation.getAutCode());
        this.generatePdf(ftlToPdfDTO, response, paramMap, pdfGenerateBuilder);
    }

    /**
     * 生成验证评审报告
     *
     * @param ftlToPdfDTO 流程参数
     * @param response    响应
     */
    private void verifyReviewReport(FtlToPdfDTO ftlToPdfDTO, HttpServletResponse response) {
        String autCode = MapUtils.getString(ftlToPdfDTO.getOtherParam(), Constants.AUT_CODE);
        log.info("自评编码为：{}，生成验证评审报告--》开始", autCode);
        if (StringUtils.isBlank(autCode)) {
            log.error("生成报告时 必传参数autCode为空");
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_RELATION_ERROR_1000004);
        }
        // 要生成评审报告需要先提交总结
        List<AutSaAudBusinessData> businessDataList = autSaAudBusinessDataService.selectAutSaAudBusinessData(autCode, AutSaAudBusinessCodeEnum.VERIFY_REVIEW_REPORT_DATA.getCode());
        if (CollectionUtils.isEmpty(businessDataList) || businessDataList.size() > 1 || StringUtils.isBlank(businessDataList.get(0).getData())) {
            log.error("根据自评编码：{} 业务编码：{} 查询评审业务数据错误，查询结果为：{}", autCode,
                    AutSaAudBusinessCodeEnum.VERIFY_REVIEW_REPORT_DATA.getCode(), JSON.toJSONString(businessDataList));
            throw new ServiceException(ServiceExceptionEnum.VERIFY_REVIEW_REPORT_1000000);
        }
        Map<String, Object> paramMap = JSON.parseObject(businessDataList.get(0).getData(), Map.class);

        // 医疗机构基本信息
        AutSaRelation autSaRelation = autSaRelationService.selectAutSaRelationByAutCode(autCode, 1);
        paramMap.putAll(this.getHospitalBaseInfo(autSaRelation.getHospitalApplyNo()));
        //资源库保存评审员信息
        paramMap.put("reviewerId", SecurityUtils.getUserId());
        paramMap.put("reviewerName", SecurityUtils.getNickName());

        log.info("自评编码为：{}，生成验证评审报告数据为{}", autCode, paramMap);
        this.generatePdf(ftlToPdfDTO, response, paramMap, null);
        log.info("自评编码为：{}，生成验证评审报告--》结束", autCode);
    }

    /**
     * 组织评审报告模板参数
     *
     * @param autSaRelation 关联信息
     * @param srSummaryList 评审总结信息
     * @return 模板参数
     */
    private Map<String, Object> tissueReviewReport(AutSaRelation autSaRelation, List<AutSaAud> srSummaryList) {
        Map<String, Object> paramMap = new HashMap();
        //根据评审报告相关节点类型，查询认证评审信息
        List<AutSaAud> autSaAuds = packAutSaAudsBySubmitTypes(autSaRelation);

        //查询评审或临时评审报告的业务数据
        List<AutSaAudBusinessData> reportBusinessDatas = qryReviewReportBusinessDatas(autSaRelation);

        //封装封面数据以及‘认证授予建议’等
        this.packCoverData(autSaRelation, paramMap, reportBusinessDatas);

        // 封装评审分配信息,封装‘（二）评审方法’数据
        this.packReviewerDataByHospitalReviewer(autSaRelation, paramMap);

        //封装‘（三）总体评价’，评审中发现亮点，评审中发现不足，对受评医院的整改建议，受评医院需考虑的改进机会;返回分组信息
        List<DomainGroupNode> domainGroups = packReviewReporteTotalValuation(srSummaryList, autSaRelation, paramMap);

        // 基本款风险分析   -- 部分达标 + 不达标  --  整改建议/改进机会
        List<AutSaAud> autSaAudClausesLists = autSaAuds.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(),
                AutSaAudSubmitTypeEnum.SR_CLAUSE, AutSaAudSubmitTypeEnum.SR_CLAUSE_M, AutSaAudSubmitTypeEnum.FAR_CLAUSE_M, AutSaAudSubmitTypeEnum.TR_CLAUSE_M,
                AutSaAudSubmitTypeEnum.SR_REPORT_M)).collect(Collectors.toList());
        List<AutSaAud> autSaAudsLists = autSaAudClausesLists.stream().collect(Collectors.groupingBy(AutSaAud::getClauseId)).entrySet().stream().
                map(a -> a.getValue().stream().sorted(Comparator.comparing(AutSaAud::getUpdateTime).reversed()).
                        collect(Collectors.toList()).get(0)).collect(Collectors.toList());
        List<CstCertificationStandards> allClauseIds = cstCertificationStandardsService.selectAllClauseIdByVersionId(autSaRelation.getAutCsId());
        // List<ChapterVo> chapterVosList = cstCertificationStandardsService.selectCstCertificationStandardsByVersionId(autSaRelation.getAutCsId());
        Map<String, CstCertificationStandards> allClauseStars = allClauseIds.stream().collect(Collectors.toMap(a -> a.getClauseId().toString(), a -> a, (aa, bb) -> bb));

        //基本款风险数据，旧逻辑
//        List<Map<String, String>> basicNotStandards = autSaAudsLists.stream().filter(a ->
//                Constants.HospitalConstants.STR_NUM_1.equals(allClauseStars.get(a.getClauseId()).getIsStar()) &&
//                        AutSaAudResultEnum.checkIsAutSaAudResultEnum(a.getAutResult(), AutSaAudResultEnum.NOT_STANDARD)).map(aa -> {
//            Map<String, String> tem = new HashMap();
//            CstCertificationStandards cstCertificationStandard = allClauseStars.get(aa.getClauseId());
//            tem.put("clauseNo", cstCertificationStandard.getClauseNo());
//            tem.put("autResult", aa.getAutResult());
//
//            String riskScore = "";
//            if (ObjectUtil.isNotNull(aa.getRiskPossibility()) && ObjectUtil.isNotNull(aa.getRiskImpact())) {
//                riskScore = String.valueOf(NumberUtil.mul(aa.getRiskPossibility(), aa.getRiskImpact()));
//            }
//            tem.put("riskScore", riskScore);
//
//            String riskAutProposal = "--";
//            if (StringUtils.isNotBlank(aa.getAutDesc())) {
//                Map autDescMap = JSON.parseObject(aa.getAutDesc(), Map.class);
//                riskAutProposal = MapUtils.getString(autDescMap, "autProposal", "").replaceAll("<", "&lt;").replaceAll(">", "&gt;");
//            }
//            tem.put("autProposal", riskAutProposal);
//            return tem;
//        }).collect(Collectors.toList());
//        paramMap.put("reportList", basicNotStandards);

        //封装‘三、评价结果’数据

        //获取’三、评价结果‘
        AutSaAudReport autSaAudReport = getValuationResult(reportBusinessDatas, autSaRelation, autSaAudClausesLists);
        paramMap.put("autSaAudReportListVo", autSaAudReport.getAutSaAudReportListVos());

        //封装‘（一）整改建议‘
        this.packReviewReportRectifiedSuggestions(autSaAudsLists, allClauseStars, paramMap);

        //风险分析数据
        List<RiskInfoVO> riskInfoList = new ArrayList<>();

        //封装’结果明细‘
        this.packChapterVosList(autSaRelation, autSaAudsLists, paramMap, "2",riskInfoList);

//        //封装各组评审情况及总结数据
//        paramMap.put("groupList", packGroupClauses(domainGroups, autSaRelation, allClauseStars, autSaAuds, autSaAudsLists));


        //风险分析数据
        //评审报告去掉分组展示，以章节条款方式展示
        paramMap.put("riskInfoList", riskInfoList);

        //评分矩阵表数据
        this.packScoringMatrixTableData(autSaAudsLists, autSaAuds, allClauseIds, paramMap);

        return paramMap;
    }

    /**
     * 封装评审报告整改建议
     *
     * @param autSaAudsLists 相关节点以最新的更新时间去重获取的评审数据
     * @param allClauseStars 配置的版本对应的所有款
     * @param paramMap
     */
    private void packReviewReportRectifiedSuggestions(List<AutSaAud> autSaAudsLists, Map<String, CstCertificationStandards> allClauseStars, Map<String, Object> paramMap) {
        // 整改建议/改进机会 ---  填写“部分达标”、“不达标”条款所需的整改建议填写“部分达标”、“不达标”条款所需的整改建议
        List<Map<String, String>> partCompAndNotStandards = autSaAudsLists.stream().filter(a -> AutSaAudResultEnum.checkIsAutSaAudResultEnum(a.getAutResult(),
                AutSaAudResultEnum.NOT_STANDARD, AutSaAudResultEnum.PARTIAL_COMPLIANCE)).map(aa -> {
            Map<String, String> tem = new HashMap();
            CstCertificationStandards cstCertificationStandard = allClauseStars.get(aa.getClauseId());
            tem.put("clauseNo", cstCertificationStandard.getClauseNo());
            tem.put("isStar", cstCertificationStandard.getIsStar());
            String problem = "";
            String suggestion = "";
            String autDesc = aa.getAutDesc();
            if (StringUtils.isNotBlank(autDesc)) {
                Map autDescMap = JSON.parseObject(autDesc, Map.class);
                problem = MapUtils.getString(autDescMap, "autEvaluate", "").replaceAll("<", "&lt;").replaceAll(">", "&gt;");
                suggestion = MapUtils.getString(autDescMap, "autProposal", "").replaceAll("<", "&lt;").replaceAll(">", "&gt;");
            }
            tem.put("problem", problem);
            tem.put("suggestion", suggestion);
            return tem;
        }).collect(Collectors.toList());
        paramMap.put("rectifiedList", partCompAndNotStandards);
    }

    /**
     * 评分矩阵表数据
     *
     * @param autSaAudsLists 相关节点以最新的更新时间去重获取的评审数据
     * @param autSaAuds      查询相关节点的认证评审信息
     * @param allClauseIds   配置的版本对应的所有款
     * @param paramMap
     */
    private void packScoringMatrixTableData(List<AutSaAud> autSaAudsLists, List<AutSaAud> autSaAuds, List<CstCertificationStandards> allClauseIds, Map<String, Object> paramMap) {

        Map<String, List<AutSaAud>> srClauseListMap = autSaAudsLists.stream().collect(Collectors.groupingBy(AutSaAud::getClauseId));
        Map<String, List<AutSaAud>> saClauseListMap = autSaAuds.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.SA_CLAUSE)).collect(Collectors.groupingBy(AutSaAud::getClauseId));
        Map<String, List<CstCertificationStandards>> chpters = allClauseIds.stream().collect(Collectors.groupingBy(a -> a.getChapterId().toString()));
        List<Map<String, Object>> scoreMatrixs = chpters.entrySet().stream().map(aa -> {
            Map<String, Object> tem = new HashMap();
            List<CstCertificationStandards> chapters = aa.getValue();
            // 章数
            tem.put("chapterNo", chapters.get(0).getChapterNo());
            // 最高分  各条款按优秀5分，良好4分，达标3分，部分达标2分，不达标1分，不适用 0分计算，满分共计930分。
            tem.put("highestScore", chapters.size() * 5);
            AtomicReference<Integer> totalSaScore = new AtomicReference<>(0);
            AtomicReference<Integer> totalSrScore = new AtomicReference<>(0);
            //统计不适用款数量
            AtomicReference<Integer> fitSum = new AtomicReference<>(0);
            List<Map<String, String>> scoreClauseList = chapters.stream().map(bb -> {
                Map<String, String> clauseTem = new HashMap();
                // 款数
                clauseTem.put("clauseNo", bb.getClauseNo());
                // 是否带*(0否 1是)
                clauseTem.put("isStar", bb.getIsStar());
                // 自评分数
                String saScore = MapUtils.getString(autResultToScoreMap, saClauseListMap.get(bb.getClauseId().toString()).get(0).getAutResult(), "");
                clauseTem.put("saScore", saScore);
                totalSaScore.updateAndGet(v -> v + Integer.valueOf(saScore));
                // 评审分数
                AutSaAud srClauseAutSaAud = srClauseListMap.get(bb.getClauseId().toString()).get(0);
                String srScore = MapUtils.getString(autResultToScoreMap, srClauseAutSaAud.getAutResult(), "");
                //统计不适用款数量
                if (AutSaAudResultEnum.checkIsAutSaAudResultEnum(srClauseAutSaAud.getAutResult(), AutSaAudResultEnum.ADMIN_NOT_APPLICABLE)) {
                    fitSum.updateAndGet(v -> ++v);
                }
                //评审：管理员不适用款Map映射分数为空
                clauseTem.put("srScore", StringUtils.isBlank(srScore) ? "-" : srScore);
                totalSrScore.updateAndGet(v -> v + (StringUtils.isBlank(srScore) ? 0 : Integer.valueOf(srScore)));
                String srScoreZh = srScore;
                boolean isNotStandard = AutSaAudResultEnum.checkIsAutSaAudResultEnum(srClauseAutSaAud.getAutResult(), AutSaAudResultEnum.NOT_STANDARD);
                if (StringUtils.equals(bb.getIsStar(), Constants.HospitalConstants.STR_NUM_1) && isNotStandard && ObjectUtil.isNotNull(srClauseAutSaAud.getRiskPossibility()) && ObjectUtil.isNotNull(srClauseAutSaAud.getRiskImpact())) {
                    // 计算风险分数
                    String riskScore = String.valueOf(NumberUtil.mul(srClauseAutSaAud.getRiskPossibility(), srClauseAutSaAud.getRiskImpact()));
                    // 风险等级简写
                    String levelShortHand = AutSaAudRiskLevelEnum.getAutSaAudRiskLevelEnum(Integer.valueOf(riskScore)).getLevelShortHand();
                    // 分线分数中文
                    String riskScoreZh = MapUtils.getString(riskScoreZhMap, riskScore, "");
                    srScoreZh = srScore + "/" + riskScoreZh + "(" + levelShortHand + ")";
                }
                clauseTem.put("srScoreZh", StringUtils.isBlank(srScoreZh) ? "-" : srScoreZh);
                return clauseTem;
            }).collect(Collectors.toList());
            tem.put("scoreClauseList", scoreClauseList);
            // 自评总计分数
            tem.put("totalSaScore", totalSaScore.get());
            // 自评总计百分数
            tem.put("totalSaScoreRate", (totalSaScore.get() * 100 / (chapters.size() * 5)));
            // 评审总计分数
            tem.put("totalSrScore", totalSrScore.get());
            // 评审总计百分数
            //评审分数：当前章节总评价分数（排除管理员不适用款）/章节总条款数（排除管理员不适用款）*最高5分 * 转换百分比100
            if (chapters.size() - fitSum.get() == 0) {
                tem.put("totalSrScoreRate", 0);
            } else {
                tem.put("totalSrScoreRate", totalSrScore.get() * 100 / ((chapters.size() - fitSum.get()) * 5));
            }
            return tem;
        }).collect(Collectors.toList());
        paramMap.put("totalHighestScore", allClauseIds.size() * 5);
        paramMap.put("scoreMatrixs", this.spiltScoreMatrixs(scoreMatrixs));


    }

    private AutSaAudReport getValuationResult(List<AutSaAudBusinessData> reportBusinessDatas, AutSaRelation autSaRelation, List<AutSaAud> autSaAudClausesLists) {
        AutSaAudReport autSaAudReport;
        if (CollectionUtils.isNotEmpty(reportBusinessDatas) && StringUtils.isNotBlank(reportBusinessDatas.get(0).getData())) {
            autSaAudReport = JSON.parseObject(reportBusinessDatas.get(0).getData(), AutSaAudReport.class);
        } else {
            autSaAudReport = commonProcessService.generateReport(autSaRelation.getAutCode(), autSaRelation.getAutCsId(),
                    String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE_M.getSubmitType(),
                            AutSaAudSubmitTypeEnum.FAR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE_M.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SR_REPORT_M.getSubmitType())),
                    AutSaAudBusinessCodeEnum.AUT_SA_AUD_REPORT.getCode(), autSaAudClausesLists);
        }
        return autSaAudReport;
    }

    private List<DomainGroupNode> packReviewReporteTotalValuation(List<AutSaAud> srSummaryList, AutSaRelation autSaRelation, Map<String, Object> paramMap) {
        // 评审中发现亮点      autAdvantage
        String autAdvantage = "";
        // 评审中发现不足      autEvaluate    --- 评审中发现不足及对受评医院的整改建议
        String autEvaluate = "";
        // 对受评医院的整改建议
        String autProposal = "";
        // 受评医院需考虑的改进机会
        String autImprove = "";

        List<DomainGroupNode> domainGroups = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(srSummaryList) && StringUtils.isNotBlank(srSummaryList.get(0).getAutDesc())) {
            String srSummaryDescStr = srSummaryList.get(0).getAutDesc();
            Map<String, Object> srSummaryDesc = JSON.parseObject(srSummaryDescStr, Map.class);
            autAdvantage = MapUtils.getString(srSummaryDesc, "autAdvantage", "").replaceAll("<", "&lt;").replaceAll(">", "&gt;");
            autEvaluate = MapUtils.getString(srSummaryDesc, "autEvaluate", "").replaceAll("<", "&lt;").replaceAll(">", "&gt;");
            autProposal = MapUtils.getString(srSummaryDesc, "autProposal", "").replaceAll("<", "&lt;").replaceAll(">", "&gt;");
            autImprove = MapUtils.getString(srSummaryDesc, "autImprove", "").replaceAll("<", "&lt;").replaceAll(">", "&gt;");
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum1(autSaRelation.getAutStatus(), temProcessEnums)) {
            QueryBaseConditionDTO queryBaseConditionDTO = new QueryBaseConditionDTO();
            queryBaseConditionDTO.setCommonId(autSaRelation.getHospitalApplyNo());
            HospitalBaseInfo hospitalBaseInfo = hospitalBaseInfoService.selectHospitalByApplyNo(queryBaseConditionDTO);
            if (ObjectUtil.isNull(hospitalBaseInfo)) {
                throw new ServiceException("查询医院信息为空，入参为：" + queryBaseConditionDTO.toString());
            }
            //评审中节点，查询详情封装
            AutSaAudQueryDTO autSaAudQueryDTO = new AutSaAudQueryDTO();
            autSaAudQueryDTO.setAutSaRelation(autSaRelation);
            autSaAudQueryDTO.setRoleKey(SecurityUtils.getSysRole().getRoleKey());
            autSaAudQueryDTO.setHospitalName(hospitalBaseInfo.getHospitalName());
            AutSaAudDetailVO autSaAudDetailVO = commonProcessService.tissueDetailResult(autSaAudQueryDTO);
            //因查询分组对应款信息会调用两次相同查询sql,其中第一次查询之后会清除款id信息，第二次查询获取的缓存数据会为空，固保存第一次数据用于第二次不用重复查询
            //      domainGroups = autSaAudDetailVO.getDomainGroupNodes();
            autAdvantage = descStrReplaceAll(autSaAudDetailVO.getAutAdvantage());
            autEvaluate = descStrReplaceAll(autSaAudDetailVO.getAutEvaluate());
            autProposal = descStrReplaceAll(autSaAudDetailVO.getAutProposal());
            autImprove = descStrReplaceAll(autSaAudDetailVO.getAutImprove());
        }
        paramMap.put("autAdvantage", autAdvantage);
        paramMap.put("autEvaluate", autEvaluate);
        paramMap.put("autProposal", autProposal);
        paramMap.put("autImprove", autImprove);

        return domainGroups;
    }

    private void packReviewerDataByHospitalReviewer(AutSaRelation autSaRelation, Map<String, Object> paramMap) {
        List<HospitalReviewer> hospitalReviewerList = hospitalReviewerMapper.selectHospitalReviewerByApplyNo(autSaRelation.getHospitalApplyNo());
        List<String> memberList = Lists.newArrayList();
        hospitalReviewerList.stream().filter(a -> !StringUtils.equalsAny(a.getFieldIdList(), "SENIOR_REVIEW", "TRAINEES_REVIEW")).forEach(a -> {
            String nickName = sysUserService.selectUserById(Long.valueOf(a.getReviewerId())).getNickName();
            if (a.getLeaderIs() != null && a.getLeaderIs().intValue() == Constants.HospitalConstants.NUM_1) {
                paramMap.put("reviewLeader", nickName);
            } else {
                memberList.add(nickName);
            }
        });
        String reviewer = "";
        if (CollectionUtils.isNotEmpty(memberList)) {
            StringBuffer memberName = new StringBuffer("" + String.join(" , ", memberList) + " ");
            if (memberName.lastIndexOf(",") != -1) {
                memberName.replace(memberName.lastIndexOf(","), memberName.lastIndexOf(",") + 1, "和");
            }
            reviewer = memberName.toString();
        }
        paramMap.put("reviewer", reviewer);
    }

    /**
     * 查询评审或临时评审报告的业务数据
     *
     * @param autSaRelation
     * @return
     */
    private List<AutSaAudBusinessData> qryReviewReportBusinessDatas(AutSaRelation autSaRelation) {
        String autSaAudBusinessCode;
        List<AutSaAudBusinessData> reportBusinessDatas;
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum1(autSaRelation.getAutStatus(), temProcessEnums)) {
            //预评审报告
            autSaAudBusinessCode = AutSaAudBusinessCodeEnum.TEM_REPORT.getCode();
        } else {
            autSaAudBusinessCode = AutSaAudBusinessCodeEnum.AUT_SA_AUD_REPORT.getCode();
        }
        reportBusinessDatas = autSaAudBusinessDataService.selectAutSaAudBusinessData(
                autSaRelation.getAutCode(), autSaAudBusinessCode);
        if (CollectionUtils.isEmpty(reportBusinessDatas) || StringUtils.isEmpty(reportBusinessDatas.get(0).getData())) {
            throw new ServiceException(String.format("根据自评编码:[%s]和环节：[%s]，查询自评审核业务数据对象表错误", autSaRelation.getAutCode(), autSaAudBusinessCode));
        }
        return reportBusinessDatas;
    }

    private void packCoverData(AutSaRelation autSaRelation, Map<String, Object> paramMap, List<AutSaAudBusinessData> reportBusinessDatas) {
        // 评审开始日期 xxxx年xx月xx日  医院端确认评审周期开始时间
        HospitalReviewCycle hospitalReviewCycle = hospitalReviewCycleService.selectByApplyNoAndStageValue(autSaRelation.getHospitalApplyNo(), AutSaAudCycleEnum.REVIEW_CYCLE.getCycleStageValue());
        String beginDate = "";
        String reviewCycleBegin = "";
        String reviewCycleEnd = "";
        if (ObjectUtil.isNotNull(hospitalReviewCycle) && StringUtils.isNotBlank(hospitalReviewCycle.getCycle())) {
            String[] cycleArr = Convert.toStrArray(hospitalReviewCycle.getCycle());
            beginDate = DateUtils.parseDateToStr("yyyy年MM月dd日", DateUtils.parseDate(cycleArr[Constants.HospitalConstants.NUM_0].trim()));
            // 现场评审周期
            reviewCycleBegin = DateUtils.parseDateToStr("yyyy年MM月dd日", DateUtils.parseDate(cycleArr[Constants.HospitalConstants.NUM_0].trim()));
            reviewCycleEnd = DateUtils.parseDateToStr("yyyy年MM月dd日", DateUtils.parseDate(cycleArr[Constants.HospitalConstants.NUM_1].trim()));
        }
        paramMap.put("beginDate", beginDate);
        paramMap.put("reviewCycleBegin", reviewCycleBegin);
        paramMap.put("reviewCycleEnd", reviewCycleEnd);
        //
        AutSaAudBusinessData autSaAudBusinessData = reportBusinessDatas.get(0);
        AutSaAudReport autSaAudReport = JSON.parseObject(autSaAudBusinessData.getData(), AutSaAudReport.class);
        String autResult = autSaAudReport.getAutSaAudResult().toString();
        String submitDate = DateUtils.parseDateToStr("yyyy年MM月dd日", autSaAudBusinessData.getUpdateTime());

        String autResultZh = (AutSaAudResultEnum.checkIsAutSaAudResultEnum(autResult, AutSaAudResultEnum.CERTIFIED)) ?
                AutSaAudResultEnum.CERTIFIED.getDesc() : ((AutSaAudResultEnum.checkIsAutSaAudResultEnum(autResult, AutSaAudResultEnum.CONDITIONAL_CERTIFICATION)) ?
                AutSaAudResultEnum.CONDITIONAL_CERTIFICATION.getDesc() : AutSaAudResultEnum.NOT_CERTIFIED.getDesc());
        paramMap.put("reviewResultsZh", autResultZh);
        paramMap.put("reviewResults", autResult);
        paramMap.put("submitDate", submitDate);

    }

    private List<AutSaAud> packAutSaAudsBySubmitTypes(AutSaRelation autSaRelation) {
        List<String> querySubmitTypes = Lists.newArrayList();
        querySubmitTypes.addAll(Arrays.asList(AutSaAudSubmitTypeEnum.SA_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SA_SUMMARY.getSubmitType(),
                AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_BT_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_GROUP_SUMMARY.getSubmitType()));
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.WAIT_FR_REPORT_R_CLAUSE)) {
            // 评审报告待审查 评审报告修改
            querySubmitTypes.addAll(Arrays.asList(AutSaAudSubmitTypeEnum.SR_CLAUSE_M.getSubmitType()));
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.FAR_CLAUSE_M_SUBMIT)) {
            // 提交评审修改
            querySubmitTypes.addAll(Arrays.asList(AutSaAudSubmitTypeEnum.SR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M.getSubmitType()));
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.SR_REPORT_SUBMIT) ||
                AutSaAudStatusEnum.checkIsAutSaAudStatusEnum1(autSaRelation.getAutStatus(), temProcessEnums)
        ) {
            // 提交评审报告
            querySubmitTypes.addAll(Arrays.asList(AutSaAudSubmitTypeEnum.SR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M.getSubmitType(),
                    AutSaAudSubmitTypeEnum.TR_CLAUSE_M.getSubmitType()));
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.SR_REPORT_M_MEET,
                AutSaAudStatusEnum.SR_REPORT_CONFIRM_MEET, AutSaAudStatusEnum.REVIEW_PROCESS_END)) {
            // 评审流程结束  -- 修改评审报告总结 sr_report_m_summary
            querySubmitTypes.addAll(Arrays.asList(AutSaAudSubmitTypeEnum.SR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M.getSubmitType(),
                    AutSaAudSubmitTypeEnum.TR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.SR_REPORT_M.getSubmitType()));
        }
        return autSaAudService.selectAutSaAudListByAutCodeAndTypes(autSaRelation.getAutCode(), String.join(",", querySubmitTypes));
    }

    /**
     * 封装各组评审情况及总结数据
     *
     * @param domainGroups              分组信息
     * @param autSaRelation             认证自评数据
     * @param allClauseStars<款id,款认证信息> 配置的版本对应的所有款
     * @param autSaAuds                 查询相关节点的认证评审信息
     * @param autSaAudsLists            相关节点以最新的更新时间去重获取的评审数据
     * @return
     */
    private List<Map<String, Object>> packGroupClauses(List<DomainGroupNode> domainGroups, AutSaRelation autSaRelation,
                                                       Map<String, CstCertificationStandards> allClauseStars, List<AutSaAud> autSaAuds,
                                                       List<AutSaAud> autSaAudsLists) {
        // 各组评分情况及总结 查询分组 + 主题 + 条款信息
        if (CollectionUtils.isEmpty(domainGroups)) {
            domainGroups = hospitalDomainGroupService.selectDomainGroup(null, autSaRelation.getAutCsId());
        }
        // 增加挪动逻辑
        reviewFitMoveClauseService.moveClause(autSaRelation.getAutCode(), domainGroups);

        Map<String, List<AutSaAud>> srBtSummaryMap = autSaAuds.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(),
                AutSaAudSubmitTypeEnum.SR_BT_SUMMARY)).collect(Collectors.groupingBy(AutSaAud::getClauseId));
        Map<String, List<AutSaAud>> srGroupSummaryMap = autSaAuds.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(),
                AutSaAudSubmitTypeEnum.SR_GROUP_SUMMARY)).collect(Collectors.groupingBy(AutSaAud::getClauseId));
        Map<String, AutSaAud> allAutSaAuds = autSaAudsLists.stream().collect(Collectors.toMap(a -> a.getClauseId(), a -> a, (aa, bb) -> bb));

        List<Map<String, Object>> groupClauses = domainGroups.stream().map(a -> {
            // 组
            Map<String, Object> groupMap = new HashMap();
            // 分组名
            groupMap.put("groupName", StringUtils.isNotBlank(a.getGroupDetail()) ? a.getGroupDetail() : "");
            List<Map<String, Object>> groupClauseLists = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(a.getChildren())) {
                groupClauseLists = a.getChildren().stream().map(aa -> {
                    // 大主题
                    Map<String, Object> bigThemeMap = new HashMap();
                    // 大主题名称
                    bigThemeMap.put("themeName", aa.getGroupDetail());
                    List<Map<String, String>> bigThemeClauseLists = Lists.newArrayList();
                    List<DomainGroupNode> sChildren = aa.getChildren();
                    sChildren.stream().forEach(aaa -> {
                        // 小主题
                        List<Map<String, String>> sThemeClauseLists = aaa.getChildren().stream().map(b -> {
                            Map<String, String> clauseMap = new HashMap();
                            CstCertificationStandards cstCertificationStandards = allClauseStars.get(b.getGroupDetail());
                            clauseMap.put("clauseId", b.getGroupDetail());
                            clauseMap.put("clauseNo", cstCertificationStandards.getClauseNo());
                            clauseMap.put("isStar", cstCertificationStandards.getIsStar());
                            AutSaAud autSaAud = allAutSaAuds.get(b.getGroupDetail());
                            clauseMap.put("clauseResult", autSaAud.getAutResult());

//                            //封装风险分析数据
//                            //1，此款为基本款(是否带*(0否 1是))且不达标或部分达标时，封装风险评估信息
//                            if (Constants.STR_NUM_1.equals(cstCertificationStandards.getIsStar()) &&
//                                    AutSaAudResultEnum.checkIsAutSaAudResultEnum(autSaAud.getAutResult(),
//                                            AutSaAudResultEnum.PARTIAL_COMPLIANCE, AutSaAudResultEnum.NOT_STANDARD)) {
//                                //2，封装款号（名称），评价结果，不足（aut_desc字段封装的不足字段autEvaluate），风险等级（分配两个字段状态1，2，3，需要相乘得到分数）
//                                RiskInfoVO riskInfoVO = new RiskInfoVO();
//                                riskInfoVO.setClauseId(autSaAud.getClauseId());
//                                riskInfoVO.setClauseNo(cstCertificationStandards.getClauseNo());
//                                riskInfoVO.setAutResult(autSaAud.getAutResult());
//                                Map<String, Object> autDescMap = JSON.parseObject(autSaAud.getAutDesc(), Map.class);
//                                riskInfoVO.setAutEvaluate(this.descStrReplaceAll(autDescMap.getOrDefault("autEvaluate", "").toString()));
//                                riskInfoVO.setRiskLevel(String.valueOf(autSaAud.getRiskImpact() * autSaAud.getRiskPossibility()));
//                                riskInfoList.add(riskInfoVO);
//                            }
                            String desc = autSaAud.getAutDesc();
                            Map<String, String> descMap = JSON.parseObject(desc, Map.class);
                            String clauseAutAdvantage = MapUtils.getString(descMap, "autAdvantage", "")
                                    .replaceAll("<", "&lt;").replaceAll(">", "&gt;");
                            String clauseAutImprovement = MapUtils.getString(descMap, "autImprove", "")
                                    .replaceAll("<", "&lt;").replaceAll(">", "&gt;");
//                            String clauseAutDisadvantageAndSuggestions = "";
                            // 评审中发现不足      autEvaluate    --- 评审中发现不足及对受评医院的整改建议
                            String autEvaluateStr = MapUtils.getString(descMap, "autEvaluate", "")
                                    .replaceAll("<", "&lt;").replaceAll(">", "&gt;");
                            // 对受评医院的整改建议
                            String autProposalStr = MapUtils.getString(descMap, "autProposal", "")
                                    .replaceAll("<", "&lt;").replaceAll(">", "&gt;");

                            clauseMap.put("autAdvantage", clauseAutAdvantage);
                            clauseMap.put("autImprovement", clauseAutImprovement);
                            clauseMap.put("autEvaluate", autEvaluateStr);
                            clauseMap.put("autProposal", autProposalStr);
                            return clauseMap;
                        }).collect(Collectors.toList());
                        bigThemeClauseLists.addAll(sThemeClauseLists);
                    });
                    // 大主题所属款项信息
                    bigThemeMap.put("clauseVoList", bigThemeClauseLists.stream().sorted(Comparator.comparing(map -> Integer.valueOf(map.get("clauseId")))).collect(Collectors.toList()));
                    // 大主题小结
                    bigThemeMap.put("summary", srBtSummaryMap.get(aa.getGroupId()).get(0).getAutDesc());
                    return bigThemeMap;
                }).collect(Collectors.toList());
            }
            // 分组款项信息
            groupMap.put("themeList", groupClauseLists);
            // 分组小结
            String summary = "";
            if (StringUtils.isNotBlank(a.getGroupId()) && srGroupSummaryMap.get(a.getGroupId()) != null
                    && srGroupSummaryMap.get(a.getGroupId()).get(0) != null
                    && StringUtils.isNotBlank(srGroupSummaryMap.get(a.getGroupId()).get(0).getAutDesc())) {
                summary = srGroupSummaryMap.get(a.getGroupId()).get(0).getAutDesc().replaceAll("<", "&lt;").replaceAll(">", "&gt;");
            }
            groupMap.put("summary", summary);
            return groupMap;
        }).collect(Collectors.toList());
        return groupClauses;
    }

    private String descStrReplaceAll(String descStr) {
        if (StringUtils.isNotBlank(descStr)) {
            descStr = descStr.replaceAll("<", "&lt;").replaceAll(">", "&gt;");
        }
        return descStr;
    }

    /**
     * 拆分分数矩阵列表
     *
     * @param scoreMatrixs 分数矩阵列表
     * @return 拆分结果
     */
    private List<List<Map<String, Object>>> spiltScoreMatrixs(List<Map<String, Object>> scoreMatrixs) {
        // 拆分分数矩阵列表 分为2步：
        // 1. 将章节拆分成多个list
        List<List<Map<String, Object>>> spiltScoreMatrixs = ListUtil.partition(scoreMatrixs, 6);
        spiltScoreMatrixs = spiltScoreMatrixs.stream().map(scoreMatrixsRows -> {
            // 计算最大行数
            int maxRows = this.calculateMaxRows(scoreMatrixsRows);
            // 以最大行数补全数据
            scoreMatrixsRows = scoreMatrixsRows.stream().map(a -> {
                List<Map<String, String>> scoreClauseList = (List<Map<String, String>>) a.get("scoreClauseList");
                List<List<Map<String, String>>> spiltScoreClauseLists = ListUtil.partition(scoreClauseList, maxRows);
                List<List<Map<String, String>>> fillScoreClauseLists = Lists.newArrayList();
                spiltScoreClauseLists.stream().forEach(aa -> {
                    List<Map<String, String>> fillMap = Lists.newArrayList();
                    fillMap.addAll(aa);
                    if (maxRows - aa.size() > 0) {
                        for (int k = 0; k < maxRows - aa.size(); k++) {
                            fillMap.add(initScoreClauseMap);
                        }
                    }
                    fillScoreClauseLists.add(fillMap);
                });
                a.put("scoreClauseList", fillScoreClauseLists);
                return a;
            }).collect(Collectors.toList());
            return scoreMatrixsRows;
        }).collect(Collectors.toList());
        return spiltScoreMatrixs;
    }

    /**
     * 计算最大行数
     *
     * @param scoreMatrixs 章节数据
     * @return 计算最大行数
     */
    private int calculateMaxRows(List<Map<String, Object>> scoreMatrixs) {
        int maxRow = 0;
        if (scoreMatrixs.size() == 0) {
            // 每章分一列，从每列数据中取出组大值，作为填充数据依据
            List<Integer> rowList = scoreMatrixs.stream().map(a -> ((List<Map<String, String>>) a.get("scoreClauseList")).size()).collect(Collectors.toList());
            maxRow = Collections.max(rowList);
        } else if (scoreMatrixs.size() == 1) {
            int chapter = ((List<Map<String, String>>) scoreMatrixs.get(0).get("scoreClauseList")).size();
            maxRow = (chapter / 6) + (chapter % 6 == 0 ? 0 : 1);
        } else if (scoreMatrixs.size() == 2) {
            // 1 5 -- 2 4  -- 3  3
            int chapter1 = ((List<Map<String, String>>) scoreMatrixs.get(0).get("scoreClauseList")).size();
            int chapter2 = ((List<Map<String, String>>) scoreMatrixs.get(1).get("scoreClauseList")).size();
            int maxChapter = Math.max(chapter1, chapter2);
            int minChapter = Math.min(chapter1, chapter2);
            int row15Max = Math.max((maxChapter / 5) + (maxChapter % 5 == 0 ? 0 : 1), minChapter);
            int row24Max = Math.max((maxChapter / 4) + (maxChapter % 4 == 0 ? 0 : 1), (minChapter / 2) + (minChapter % 2 == 0 ? 0 : 1));
            int row33Max = Math.max((maxChapter / 3) + (maxChapter % 3 == 0 ? 0 : 1), (minChapter / 3) + (minChapter % 3 == 0 ? 0 : 1));
            maxRow = Math.min(Math.min(row15Max, row24Max), row33Max);
        } else if (scoreMatrixs.size() == 3) {
            // 1 1 4  -- 1 2 3 -- 2 2 2
            List<Integer> chapters = scoreMatrixs.stream().map(a -> ((List<Map<String, String>>) a.get("scoreClauseList")).size()).collect(Collectors.toList());
            Collections.sort(chapters);
            Collections.reverse(chapters);
            int row114Max = Math.max(Math.max((chapters.get(0) / 4) + (chapters.get(0) % 4 == 0 ? 0 : 1), chapters.get(1)), chapters.get(2));
            int row123Max = Math.max(Math.max((chapters.get(0) / 3) + (chapters.get(0) % 3 == 0 ? 0 : 1), (chapters.get(1) / 2) + (chapters.get(1) % 2 == 0 ? 0 : 1)), chapters.get(2));
            int row222Max = Math.max(Math.max((chapters.get(0) / 2) + (chapters.get(0) % 2 == 0 ? 0 : 1), (chapters.get(1) / 2) + (chapters.get(1) % 2 == 0 ? 0 : 1)), (chapters.get(2) / 2) + (chapters.get(2) % 2 == 0 ? 0 : 1));
            maxRow = Math.min(Math.min(row114Max, row123Max), row222Max);
        } else if (scoreMatrixs.size() == 4) {
            // 1 1 1 3 -- 1 1 2 2
            List<Integer> chapters = scoreMatrixs.stream().map(a -> ((List<Map<String, String>>) a.get("scoreClauseList")).size()).collect(Collectors.toList());
            Collections.sort(chapters);
            Collections.reverse(chapters);
            int max11 = Math.max(chapters.get(2), chapters.get(3));
            int row1113Max = Math.max(Math.max((chapters.get(0) / 3) + (chapters.get(0) % 3 == 0 ? 0 : 1), chapters.get(1)), max11);
            int row1122Max = Math.max(Math.max((chapters.get(0) / 2) + (chapters.get(0) % 2 == 0 ? 0 : 1), (chapters.get(1) / 2) + (chapters.get(1) % 2 == 0 ? 0 : 1)), max11);
            maxRow = Math.min(row1113Max, row1122Max);
        } else {
            // 1 1 1 1 2
            List<Integer> chapters = scoreMatrixs.stream().map(a -> ((List<Map<String, String>>) a.get("scoreClauseList")).size()).collect(Collectors.toList());
            Collections.sort(chapters);
            Collections.reverse(chapters);
            maxRow = Math.max(Math.max(Math.max((chapters.get(0) / 2) + (chapters.get(0) % 2 == 0 ? 0 : 1), chapters.get(1)), Math.max(chapters.get(2), chapters.get(3))), chapters.get(4));
        }
        return maxRow;
    }

    /**
     * 事实准确性报告
     *
     * @param ftlToPdfDTO 流程参数
     * @param response    响应
     */
    private void farReport(FtlToPdfDTO ftlToPdfDTO, HttpServletResponse response) {
        Map<String, Object> otherParam = ftlToPdfDTO.getOtherParam();
        String autDesc = MapUtil.getStr(otherParam, "autDesc");
        // 判断角色
        AutSaAudRoleEnum checkRoleEnum = FtlToPdfEnum.FAR_REPORT_HOS == ftlToPdfDTO.getFtlToPdfEnum() ? AutSaAudRoleEnum.HOSPITAL : AutSaAudRoleEnum.ASSESSOR;
        // 校验并获取关联信息
        AutSaRelation autSaRelation = this.checkAndGetAutSaRelation(ftlToPdfDTO, checkRoleEnum);
        // pdf生成校验节点
        if (StringUtils.equals(MapUtils.getString(ftlToPdfDTO.getOtherParam(), Constants.CHECK_STATUS, Constants.FLAG_VALUE_Y), Constants.FLAG_VALUE_Y)) {
            // 校验对应的节点信息
            if (!AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.FAR_SUMMARY, AutSaAudStatusEnum.FAR_CLAUSE_M_SUBMIT)) {
                log.error("账户：{} 操作 医疗机构：{} 自评编码：{} 生成报告时 节点：{} 不正确", autSaRelation.getAutStatus(), autSaRelation.getHospitalApplyNo(), autSaRelation.getAutCode(), autSaRelation.getAutStatus());
                throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000003);
            }
        }
        // 获取登录用户生成事实准确性报告的相关参数
        long startTime1 = System.currentTimeMillis();
        Map<String, Object> paramMap = this.tissueFarReport(autSaRelation, ftlToPdfDTO);
        // 医疗机构基本信息
        paramMap.putAll(this.getHospitalBaseInfo(autSaRelation.getHospitalApplyNo()));
        paramMap.put("autDesc", autDesc);
        //资源库保存数据
        paramMap.put("reviewerId", SecurityUtils.getUserId());
        paramMap.put("reviewerName", SecurityUtils.getNickName());
        log.info("获取pdf文档参数耗时：{}", System.currentTimeMillis() - startTime1);
        this.generatePdf(ftlToPdfDTO, response, paramMap, null);
    }

    /**
     * 组织事实准确性报告模板参数
     *
     * @param autSaRelation 关联信息
     * @return 模板参数
     */
    private Map<String, Object> tissueFarReport(AutSaRelation autSaRelation, FtlToPdfDTO ftlToPdfDTO) {
        Map<String, Object> paramMap = new HashMap();
        // 评审周期
        HospitalReviewCycle hospitalReviewCycle = hospitalReviewCycleService.selectByApplyNoAndStageValue(autSaRelation.getHospitalApplyNo(), AutSaAudCycleEnum.REVIEW_CYCLE.getCycleStageValue());
        String[] cycleArr = Convert.toStrArray(hospitalReviewCycle.getCycle());
        paramMap.put("reviewCycleBegin", DateUtils.parseDateToStr("yyyy年MM月dd日", DateUtils.parseDate(cycleArr[0])));
        paramMap.put("reviewCycleEnd", DateUtils.parseDateToStr("yyyy年MM月dd日", DateUtils.parseDate(cycleArr[1])));

        // 评审团队成员
        String groupName = "";
        List<HosReviewPlanVO> hosPlanClauseList = hospitalBaseInfoService.selectHosPlanClauseDetail(autSaRelation.getHospitalApplyNo());
        if (CollectionUtils.isNotEmpty(hosPlanClauseList)) {
            HosReviewPlanVO hosReviewPlanVO = hosPlanClauseList.get(0);
            // 获取医疗机构计划分配成员信息
            List<HosPlanUserInfoVO> memberList = commonProcessService.getHosPlanMemberList(hosReviewPlanVO, AutSaAudRoleEnum.ASSESSOR.getRoleKey());
            List<String> groupNames = memberList.stream().map(a -> a.getName()).collect(Collectors.toList());
            groupName = String.join(",", groupNames);
        }
        paramMap.put("groupName", groupName);

        // 评审组长收到本文件日期
        List<AutSaAud> autSaAudLists = autSaAudService.selectAutSaAudListByAutCodeAndTypes(autSaRelation.getAutCode(),
                String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.FAR_SUMMARY_CONFIRM.getSubmitType()
                        , AutSaAudSubmitTypeEnum.FAR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SUMMARY.getSubmitType()
                        , AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SKIP.getSubmitType())));
        String farSummaryConfirmDate = "";
        List<AutSaAud> farSummaryConfirm = autSaAudLists.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(),
                AutSaAudSubmitTypeEnum.FAR_SUMMARY_CONFIRM)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(farSummaryConfirm)) {
            farSummaryConfirmDate = DateUtils.parseDateToStr("yyyy年MM月dd日", farSummaryConfirm.get(0).getSubmitDate());
        }
        paramMap.put("farSummaryConfirmDate", farSummaryConfirmDate);

        // 评价信息
        List<Map<String, String>> farClauseMapList = Lists.newArrayList();
        List<AutSaAud> farClauseLists = autSaAudLists.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(),
                AutSaAudSubmitTypeEnum.FAR_CLAUSE) && AutSaAudResultEnum.checkIsAutSaAudResultEnum(a.getAutResult(), AutSaAudResultEnum.DISAGREE)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(farClauseLists)) {
            List<CstCertificationStandards> allClauseIds = cstCertificationStandardsService.selectAllClauseIdByVersionId(autSaRelation.getAutCsId());
            Map<String, CstCertificationStandards> allClauseStars = allClauseIds.stream().collect(Collectors.toMap(a -> a.getClauseId().toString(), a -> a, (aa, bb) -> bb));
            List<String> clauseIds = farClauseLists.stream().map(a -> a.getClauseId()).collect(Collectors.toList());
            AutSaAud autSaAud = new AutSaAud();
            autSaAud.setSubmitType(String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE_M.getSubmitType(),
                    AutSaAudSubmitTypeEnum.FAR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SKIP.getSubmitType())));
            autSaAud.setClauseId(String.join(",", clauseIds));
            autSaAud.setAutCode(autSaRelation.getAutCode());
            autSaAud.setStatus(Constants.HospitalConstants.NUM_1);
            List<AutSaAud> queryLists = autSaAudMapper.selectAutSaAudList(autSaAud);
            Map<String, List<AutSaAud>> temMap = queryLists.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(),
                    AutSaAudSubmitTypeEnum.SR_CLAUSE, AutSaAudSubmitTypeEnum.SR_CLAUSE_M)).collect(Collectors.groupingBy(AutSaAud::getClauseId));
            List<AutSaAud> temList = temMap.entrySet().stream().
                    map(a -> a.getValue().stream().sorted(Comparator.comparing(AutSaAud::getSubmitDate).reversed()).
                            collect(Collectors.toList()).get(0)).collect(Collectors.toList());
            Map<String, List<AutSaAud>> saClauseQueryListsMap = temList.stream().collect(Collectors.groupingBy(AutSaAud::getClauseId));

//            Map<String, List<AutSaAud>> saClauseQueryListsMap = queryLists.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(),
//                    AutSaAudSubmitTypeEnum.SR_CLAUSE,AutSaAudSubmitTypeEnum.SR_CLAUSE_M)).collect(Collectors.groupingBy(AutSaAud::getClauseId)).entrySet().stream().
//                    map(a -> a.getValue().stream().sorted(Comparator.comparing(AutSaAud::getUpdateTime)).
//                            collect(Collectors.toList()).get(0)).collect(Collectors.toList()).stream().collect(Collectors.groupingBy(AutSaAud::getClauseId));
            //提交类型为：FAR_CLAUSE_M和FAR_CLAUSE_M_SKIP，取最新提交的数据封装事实准确性查询表
            Map<String, List<AutSaAud>> farClauseMAndSkipMap = packNewFarClauseMAndSkipMap(queryLists);
            farClauseLists.stream().forEach(a -> {
                Map<String, String> temp = new HashMap();
                // 评价结果
                String autResult = "";
                // 评审员评价
                String srClause = "";
                List<AutSaAud> saClauseQueryLists = saClauseQueryListsMap.get(a.getClauseId());
                if (CollectionUtils.isNotEmpty(saClauseQueryLists)) {
                    AutSaAud latestAutSaAud = saClauseQueryLists.get(0);
                    autResult = latestAutSaAud.getAutResult();
                    if (StringUtils.isNotBlank(latestAutSaAud.getAutDesc())) {
                        Map autDescMap = JSON.parseObject(latestAutSaAud.getAutDesc(), Map.class);
                        String autAdvantage = MapUtils.getString(autDescMap, "autAdvantage", "");
                        String autEvaluate = MapUtils.getString(autDescMap, "autEvaluate", "");
                        String autProposal = MapUtils.getString(autDescMap, "autProposal", "");
                        String autImprove = MapUtils.getString(autDescMap, "autImprove", "");
                        String autDesc1 = MapUtils.getString(autDescMap, "autDesc1", "");
                        String autDesc = MapUtils.getString(autDescMap, "autDesc", "");
                        srClause = ListUtil.toList(autAdvantage, autEvaluate, autProposal, autImprove, autDesc1, autDesc).stream()
                                .filter(bb -> StringUtils.isNotBlank(bb)).collect(Collectors.joining(","));
                    }
//                    if(StringUtils.isNotBlank(latestAutSaAud.getReviewerRespond())){
//                        srClause += latestAutSaAud.getReviewerRespond();
//                    }
                    srClause = srClause.replaceAll("<", "&lt;").replaceAll(">", "&gt;");
                }
                temp.put("autResult", autResult);
                temp.put("srClause", srClause);

                // 医院反馈
                temp.put("farClause", StringUtils.isNotBlank(a.getAutDesc()) ? a.getAutDesc().replaceAll("<", "&lt;").replaceAll(">", "&gt;") : "");

                // 评审员答复
                String farClauseM = "";
                List<AutSaAud> farClauseMQueryLists = farClauseMAndSkipMap.get(a.getClauseId());
                if (CollectionUtils.isNotEmpty(farClauseMQueryLists) && StringUtils.isNotBlank(farClauseMQueryLists.get(0).getAutDesc())) {
                    AutSaAud latestAutSaAudM = farClauseMQueryLists.get(0);
                    if (StringUtils.isNotBlank(latestAutSaAudM.getAutDesc())) {
                        String desc = latestAutSaAudM.getAutDesc();
                        Map<String, String> descMap = JSON.parseObject(desc, Map.class);
                        //前缀拼接：如果有评价结果为：评价结果：*，如果没有为：拒绝原因：
                        String prefixDesc = AutSaAudResultEnum.getAutSaAudResultDesc(latestAutSaAudM.getAutResult());
                        if (StringUtils.isNotBlank(prefixDesc)) {
                            prefixDesc = "评价结果：" + prefixDesc + "\n" + "评价描述：";
                        } else {
                            prefixDesc = "拒绝原因：";
                        }

                        //20230414改为拼接输出
                        String autAdvantage = MapUtils.getString(descMap, "autAdvantage", "");
                        String autEvaluate = MapUtils.getString(descMap, "autEvaluate", "");
                        String autProposal = MapUtils.getString(descMap, "autProposal", "");
                        String autImprove = MapUtils.getString(descMap, "autImprove", "");
                        String autDesc1 = MapUtils.getString(descMap, "autDesc1", "");
                        String autDesc = MapUtils.getString(descMap, "autDesc", "");
                        farClauseM = ListUtil.toList(autAdvantage, autEvaluate, autProposal, autImprove, autDesc1, autDesc).stream()
                                .filter(bb -> StringUtils.isNotBlank(bb)).collect(Collectors.joining(","));
                        farClauseM = farClauseM.replaceAll("<", "&lt;").replaceAll(">", "&gt;");
                        farClauseM = prefixDesc + farClauseM;
                    }
//                    if(StringUtils.isNotBlank(latestAutSaAudM.getReviewerRespond())){
//                        farClauseM = latestAutSaAudM.getReviewerRespond().replaceAll("<", "&lt;").replaceAll(">", "&gt;");
//                    }
                } else if (CollectionUtils.isNotEmpty(farClauseMQueryLists) &&
                        AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(farClauseMQueryLists.get(0).getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SKIP) &&
                        ObjectUtil.equal(farClauseMQueryLists.get(0).getAutResult(), Constants.STR_NUM_0)) {
                    farClauseM = "拒绝原因:--";

                }
                temp.put("farClauseM", farClauseM);

                CstCertificationStandards cstCertificationStandards = allClauseStars.get(a.getClauseId());
                temp.put("clauseNo", cstCertificationStandards.getClauseNo());
                temp.put("isStar", cstCertificationStandards.getIsStar());
                farClauseMapList.add(temp);
            });
        }
        paramMap.put("farClauseMapList", farClauseMapList);

        // 评审组长意见
//        List<AutSaAud> leaderOpinions = autSaAudLists.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(),
//                AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SUMMARY,AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SKIP)).collect(Collectors.toList());
        String leaderOpinion = MapUtils.getString(ftlToPdfDTO.getOtherParam(), "leaderOpinion", "");
//        if (CollectionUtils.isNotEmpty(leaderOpinions) && StringUtils.isNotBlank(leaderOpinions.get(0).getAutDesc())) {
//            Map leaderOpinionMap = JSON.parseObject(leaderOpinions.get(0).getAutDesc(), Map.class);
//            leaderOpinion = MapUtils.getString(leaderOpinionMap, "leaderOpinion", "")
//                    .replaceAll("<", "&lt;").replaceAll(">", "&gt;");
//        }
        paramMap.put("leaderOpinion", leaderOpinion);
        return paramMap;
    }

    /**
     * 封装此提交类型，按最新提交时间返回
     *
     * @param queryLists
     * @return
     */
    private Map<String, List<AutSaAud>> packNewFarClauseMAndSkipMap(List<AutSaAud> queryLists) {
        //获取FAR_CLAUSE_M数据
        //获取’拒绝修改‘数据,如果提交操作是全部拒绝修改，数据没有款id,MAP<款id，数据>
        Map<String, List<AutSaAud>> farClauseMQueryListsMap = queryLists.stream().
                filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(),
                        AutSaAudSubmitTypeEnum.FAR_CLAUSE_M, AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SKIP) &&
                        StrUtil.isNotBlank(a.getClauseId())).collect(Collectors.groupingBy(AutSaAud::getClauseId));

        List<AutSaAud> temList = farClauseMQueryListsMap.entrySet().stream().
                map(a -> a.getValue().stream().sorted(Comparator.comparing(AutSaAud::getSubmitDate).reversed()).
                        collect(Collectors.toList()).get(0)).collect(Collectors.toList());
        return temList.stream().collect(Collectors.groupingBy(AutSaAud::getClauseId));
    }

}
