<!--  -->
<template>
  <div class="audit-evaluation" v-loading="loading">
    <div v-if="distributed">
      <!-- 医院名称 -->
      <el-card class="box-card" shadow="always" style="margin-bottom: 10px; margin-left: -5px">
        <h1 style="margin: 0">{{ detail.hospitalName }}</h1>
      </el-card>

      <!-- 总结详情 -->
      <el-card class="box-card" shadow="never" v-if="showSummary">
        <div slot="header" class="clearfix">
          <h4>评审报告验证问询</h4>
        </div>
        <el-table :data="questionList" :key="questionKeys" stripe border>
          <el-table-column label="款号" prop="clauseNo" width="100" align="center"></el-table-column>
          <el-table-column label="评价结果" prop="psResult" width="100" align="center"></el-table-column>
          <el-table-column label="评价内容" prop="psDesc" align="center"></el-table-column>
          <el-table-column label="验证评审员疑问" prop="yzDesc" align="center"></el-table-column>
          <!-- <el-table-column label="评审员回应" align="center"></el-table-column> -->
        </el-table>
      </el-card>
      <el-card class="box-card" shadow="never" v-if="showSummary" style="margin-top:15px;">
        <div slot="header" class="clearfix">
          <h4>评审报告验证总结</h4>
        </div>
        <el-form ref="autSumForm" :model="form" label-width="160px" :rules="rules">
          <el-form-item label="一般性评价：" prop="autEvaluate">
            <el-input type="textarea" :rows="3" :maxlength="1000" v-model="form.autEvaluate"></el-input>
          </el-form-item>
          <el-form-item label="评审报告优点：" prop="autAdvantage">
            <el-input type="textarea" :rows="3" :maxlength="1000" v-model="form.autAdvantage"></el-input>
          </el-form-item>
          <el-form-item label="修订评审报告建议：" prop="autProposal">
            <el-input type="textarea" :rows="3" :maxlength="1000" v-model="form.autProposal"></el-input>
          </el-form-item>
          <el-form-item label="报告整体评分：" prop="autResult">
            <el-select v-model="form.autResult" clearable filterable>
              <el-option v-for="(suggestion, index) in suggestionOptions" :key="index" :value="suggestion.dictValue" :label="suggestion.dictLabel"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="认证授予建议：" prop="autSuggestion">
            <el-select v-model="form.autSuggestion" clearable filterable>
              <el-option v-for="(summary, index) in summaryOptions" :key="index" :value="summary.dictValue" :label="summary.dictLabel"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="认证授予建议理由：" prop="autDesc">
            <el-input type="textarea" :rows="3" :maxlength="1000" v-model="form.autDesc"></el-input>
          </el-form-item>
          <el-form-item label=" ">
            <el-button type="primary" size="mini" @click="submitSummary">提交验证总结</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 评价  -->
      <el-card class="box-card" shadow="never" v-if="!showSummary">
        <div slot="header" class="clearfix">
          <h4>评审报告验证</h4>
          <div class="header-right" v-if="standardLoadedKey" :key="headerSlotKeys">
            <el-button type="primary" size="mini" @click="trConfirm">验证完成</el-button>
          </div>
        </div>
        <!-- 款项选择器 -->
        <standard-card ref="standardCard" :key="`base${loadingKeys}`" :clauseIds="clauseIds" :fitClauseIds="allFitClauseIds" :versionId="relation.autCsId" @selectChange="selectChange" @loaded="standardLoaded">
        </standard-card>

        <!-- 款项 -->
        <el-form :key="loadingKeys" label-width="100px">
          <!-- <clause-item v-for="(clause, index) in clauseList" v-show="isThisPage(index + 1)" :clause="clause" :key="clause.clauseId"> -->
          <clause-item v-for="(clause) in clauseList" :clause="clause" :key="clause.clauseId">
            <evaluation-self :readonly="true" :type="AutSaAudCurrentStatusEnum.SA_CLAUSE" :clause="clause" :aut="saMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-self>
            <evaluation-review :readonly="true" :type="AutSaAudCurrentStatusEnum.SR_CLAUSE" :clause="clause" :aut="lastMap[clause.clauseId]" :status="autSaAudStatus">
            </evaluation-review>
            <evaluation-verify :readonly="!!trMap[clause.clauseId]" :type="AutSaAudCurrentStatusEnum.TR_CLAUSE" :clause="clause" :aut="trMap[clause.clauseId]" :status="autSaAudStatus">
            </evaluation-verify>
            <evaluation-query v-if="trjMap[clause.clauseId]" :type="AutSaAudCurrentStatusEnum.TR_CLAUSE_RJ_E"
              :clause="clause" :aut="trjMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-query>
          </clause-item>
          <pagination v-show="total > limit" :total="total" :page.sync="pageNum" :limit.sync="limit" :pageSizes="[limit]" />
        </el-form>
      </el-card>
      <!-- 总结提交 -->
    </div>
  </div>
</template>

<script>
import evaluationMixin from "@/mixins/evaluation.vue";

export default {
  name: "SeniorAssessorIndex",
  mixins: [evaluationMixin],
  components: {
  },
  props: {},
  watch: {
    questionKeys() {
      this.questionList = this.questions;
    },
    detail(val) {
      if (!val?.seniorReviewerReviewSum?.autDesc) return;
      let { autEvaluate, autAdvantage, autProposal, autResult, autSuggestion, autDesc } = JSON.parse(val.seniorReviewerReviewSum.autDesc);
      this.form = {
        autEvaluate, // 一般评价
        autAdvantage, // 优点
        autProposal, // 报告建议
        autResult, // 评分
        autSuggestion, // 认证建议
        autDesc, // 理由
      }
    }
  },
  data() {
    return {
      form: {
        autEvaluate: '', // 一般评价
        autAdvantage: '', // 优点
        autProposal: '', // 报告建议
        autResult: '', // 评分
        autSuggestion: '', // 认证建议
        autDesc: '', // 理由
      },
      summaryForm: {
        autResult: '1',
        autDesc: '',
      },
      suggestionOptions: [
        {
          dictValue: "1",
          dictLabel: "优秀",
        },
        {
          dictValue: "2",
          dictLabel: "良好",
        },
        {
          dictValue: "3",
          dictLabel: "中等",
        },
        {
          dictValue: "4",
          dictLabel: "尚可",
        },
        {
          dictValue: "5",
          dictLabel: "较差",
        },
      ],
      summaryOptions: [],
      reviewResultOptions: [],
      rules: {
        autEvaluate: [{
          required: true,
          message: '请填写一般性评价',
          trigger: ['change', 'blur']
        }],
        autAdvantage: [{
          required: true,
          message: '请填写评审报告优点',
          trigger: ['change', 'blur']
        }],
        autProposal: [{
          required: true,
          message: '请填写修订评审报告建议',
          trigger: ['change', 'blur']
        }],
        autResult: [{
          required: true,
          message: '请对报告进行整体评分',
          trigger: ['change', 'blur']
        }],
        autSuggestion: [{
          required: true,
          message: '请选择认证授予建议',
          trigger: ['change', 'blur']
        }],
        autDesc: [{
          required: true,
          message: '请填写认证授予建议的理由',
          trigger: ['change', 'blur']
        }],
      },
      questionKeys: 0,
      questionList: [],
      specialInit: true
    };
  },

  computed: {
    showSummary() {
      return this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.SENIOR_REVIEWER_REVIEW_SUM
    },

    questions() {
      let trMapList = Object.values(this.trMap).filter(tr => tr.autResult == 2);
      let trIds = trMapList.map(({ clauseId }) => clauseId);
      let srMapList = Object.values(this.srMap).filter(sr => trIds.includes(sr.clauseId));
      let fwmMapList = Object.values(this.fwmMap).filter(sr => trIds.includes(sr.clauseId));
      const clauses = this.$store.getters.standardTypeItemByVersionId(
        this.relation.autCsId,
        "clause",
        "list"
      ).filter(sr => trIds.includes(`${sr.clauseId}`));
      let list = clauses.map(({ clauseId, clauseNo }) => {
        let tr = trMapList.find(data => data.clauseId == clauseId);
        let sr = srMapList.find(data => data.clauseId == clauseId);
        let fwm = fwmMapList.find(data => data.clauseId == clauseId);
        let result = this.reviewResultOptions.find(({ dictValue }) => dictValue == (fwm || sr).autResult)
        return {
          clauseNo,
          clauseId,
          yzDesc: tr.autDesc,
          psDesc: this.srDesc(fwm || sr),
          psResult: result ? result.dictLabel : '',
        }
      })
      return list;
    },

    srDesc() {
      return ({autResult, autDesc: autDescStr}) => {
        try {
          let { autAdvantage, autEvaluate, autProposal, autImprove, autDesc } = JSON.parse(autDescStr)
          if (autResult == 1 || autResult == 2) {
            return autAdvantage
          } else if (autResult == 3) {
            return autImprove
          } else if (autResult == 6) {
            return autDesc
          } else {
            return `不足：${autEvaluate}、整改建议：${autProposal}、改进机会：${autImprove}`
          }
        } catch (error) {
          return '--'
        }
      }
    }
  },

  created() { },

  mounted() {
    this.init2(true, this.$route.query.autCode, this.$route.query.type);

    this.getDicts('review_summary').then((response) => {
      this.summaryOptions = response.data;
    });

    this.getDicts("review_result").then((response) => {
      this.reviewResultOptions = response.data;
      this.questionKeys++;
    });
  },

  methods: {
    initFinalBack() {
      this.getStandard(this.relation.autCsId, false, true, () => {
        // this.questionList;
        this.questionKeys++;
      })

      if (Object.keys(this.trjMap).length) {
        this.clauseIds = Object.values(this.trjMap).map(({ clauseId }) => clauseId)
      }

    },
    async trConfirm() {
      let data = {
        autCode: this.relation.autCode,
        accountId: this.$store.getters.userId + "",
        submitType: this.AutSaAudCurrentStatusEnum.TR_CLAUSE_CONFIRM,
        autSaAudLists: []
      }
      try {
        let res = await this.submit(data);
        // 报错就不提示
        if (!res) return;
        this.$message("确认成功");
      } finally {
        this.init2(false, this.$route.query.autCode, this.$route.query.type);
      }
    },
    async submitSummary() {
      try {
        await this.$refs.autSumForm.validate()
        let autDesc = JSON.stringify(this.form);
        this.summaryForm.autDesc = autDesc
        let data = {
          autCode: this.relation.autCode,
          accountId: this.$store.getters.userId + "",
          submitType: this.AutSaAudCurrentStatusEnum.TR_SUMMARY,
          autSaAudLists: [{
            ...this.summaryForm
          }]
        };

        let res = await this.submit(data);
        // 报错就不提示
        if (!res) return;
        this.summarySubmit();
        // this.init2(true, this.$route.query.autCode, this.$route.query.type);
      } catch (error) {

      }
    },
    summarySubmit() {
      this.$store.dispatch("tagsView/delView", this.$route) &&
        this.$router.push({
          path: "/seniorEvaluationList",
          // query: {
          //   preliminary: 0,
          // },
        });
    },
  },
};
</script>
<style lang='scss' scoped>
</style>
