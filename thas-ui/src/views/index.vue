<template>
  <div class="app-container home">
    <div v-loading="loading" v-if="roles.includes('admin') || roles.includes('common-admin')">
      <el-row class="card-panel">
        <div class="card-panel-title">受评医院明细清单</div>
        <panel-group :data="acceptResMap" @handleSetTableData="handleSetTableData" />

        <el-table :data="hospitalList" border height="260" :key="key" v-loading="tableLoading">
          <el-table-column align="left" :label="`${acceptDetailType === '1' ? '受评' : acceptDetailType === '2' ? '受评中' : acceptDetailType === '3' ? '已受评' : ''}医院名称`" prop="hospitalName"> </el-table-column>
          <el-table-column align="left" label="状态" prop="autStatus">
            <template slot-scope="scope">
              <span>{{ AutSaAudCurrentStatusEnum[scope.row.autStatus] }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-row>

      <el-row class="card-panel">
        <div class="card-panel-title">有利益冲突的评审员数量及明细清单</div>
        <bar-chart :chartData="interestResData" />
      </el-row>

      <el-row class="card-panel">
        <div class="card-panel-title">医院拒绝分配的评审员数量及明细清单</div>
        <bar-chart :chartData="rjAllotReviewerResData" />
      </el-row>

      <el-row class="card-panel-group">
        <el-col class="card-panel-col">
          <div class="card-panel-title">各评审员的工作量</div>
          <div class="chart-wrapper">
            <bar-chart :chartData="reviewersWorkResData" />
          </div>
        </el-col>
        <el-col class="card-panel-col">
          <div class="card-panel-title">培训教员的培训工作量</div>
          <div class="chart-wrapper">
            <bar-chart :chartData="trainTeacherResData" />
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import request from "@/utils/request";
import { mapState } from 'vuex'
import { deepClone } from "@/utils/index";
import PanelGroup from './dashboard/PanelGroup'
import BarChart from './dashboard/BarChart'

export default {
  name: "Index",
  components: {
    PanelGroup,
    BarChart
  },
  data() {
    return {
      loading: false,
      tableLoading: false,
      // 版本号
      version: "3.8.1",
      hospitalList: [],
      key: "",
      url: {
        detail: "/statistics/report/detail",
        queryList: "/statistics/report/queryList"
      },
      // 有利益冲突的评审员数量及明细清单
      interestResData: {
        name: 'interest',
        xAxisData: [],
        yAxisData: [],
        yAxisName: "（人数）",
        dataZoomValue: 15,
        colorList: ['#83bff6', '#188df0', '#2378f7'],
        interestDescMap: {}
      },
      // 医院拒绝分配的评审员数量及明细清单
      rjAllotReviewerResData: {
        name: 'rjAllotReviewer',
        xAxisData: [],
        yAxisData: [],
        yAxisName: "（人数）",
        dataZoomValue: 15,
        // colorList: ['#ff9569', '#F56C6C', '#e92758']
        colorList: ['#83bff6', '#188df0', '#2378f7'],
        rjAllotReviewerResMap: {}
      },
      // 各评审员的工作量
      reviewersWorkResData: {
        name: 'reviewersWork',
        xAxisData: [],
        yAxisData: [],
        yAxisName: "（次数）",
        dataZoomValue: 10,
        // colorList: ['#C0C4CC', '#606266', '#303133']
        colorList: ['#83bff6', '#188df0', '#2378f7']
      },
      // 培训教员的培训工作量
      trainTeacherResData: {
        name: 'trainTeacher',
        xAxisData: [],
        yAxisData: [],
        yAxisName: "（次数）",
        dataZoomValue: 10,
        // colorList: ['#C0C4CC', '#606266', '#303133']
        colorList: ['#83bff6', '#188df0', '#2378f7'],
      },
      acceptResMap: {},
      acceptDetailType: '1'
    };
  },
  computed: {
    ...mapState({
      roles: state => state.user.roles,
      dictionaryMap: state => state.dictionary.dictionaryMap,
    }),
  },
  mounted() {
    if (localStorage.getItem('isFirstLogin') == 1) {
      this.$router.replace({ path: '/reset' }).catch(() => { })
    }
    if (this.roles.includes('admin') || this.roles.includes('common-admin')) {
      // 默认展示总受评医院
      this.queryDetail('1', '3');
      this.queryList();
    }
  },
  methods: {
    handleSetTableData(acceptDetailType) {
      this.acceptDetailType = acceptDetailType;
      // 	统计报表类型
      let statisticsReportType = '3';
      this.queryDetail(acceptDetailType, statisticsReportType)
    },
    queryDetail(acceptDetailType, statisticsReportType) {
      this.tableLoading = true;
      request({
        url: this.url.detail,
        method: "post",
        data: {
          acceptDetailType,
          statisticsReportType
        }
      })
        .then((res) => {
          this.tableLoading = false;
          if (res.code == 200) {
            this.hospitalList = res.data.acceptResList || [];
            this.key = new Date().getTime();
          }
        })
        .catch((error) => {
          // console.log(error);
          this.tableLoading = false;
        });
    },
    queryList() {
      this.loading = true;
      request({
        url: this.url.queryList,
        method: "post",
        data: {
          acceptDetailType: "",
          autCode: "",
          statisticsReportType: ""
        }
      })
        .then((res) => {
          this.loading = false;
          let deepData = deepClone(res.data);
          let { acceptResList, interestResList, rjAllotReviewerResList, reviewersWorkResList, trainTeacherResList } = deepData;
          let sortReviewersWorkResList = reviewersWorkResList.sort((a, b) => {
            return b.reviewerNum - a.reviewerNum
          })
          let sortTrainTeacherResList = trainTeacherResList.sort((a, b) => {
            return b.reviewerNum - a.reviewerNum
          })
          if (res.code == 200) {
            this.acceptResMap = acceptResList?.[0] || {}
            this.interestResData.xAxisData = interestResList?.map(item => item.hospitalName) || [];
            this.interestResData.yAxisData = interestResList?.map(item => item.interestReviewerNum) || [];
            interestResList.forEach(item => {
              this.interestResData.interestDescMap[item.hospitalName] = item.interestDetailResList
            })
            this.rjAllotReviewerResData.xAxisData = rjAllotReviewerResList?.map(item => item.hospitalName) || [];
            this.rjAllotReviewerResData.yAxisData = rjAllotReviewerResList?.map(item => item. rjAllotReviewerNum) || [];
            rjAllotReviewerResList.forEach(item => {
              this.rjAllotReviewerResData.rjAllotReviewerResMap[item.hospitalName] = item.rjAllotReviewerDetailResList
            })
            this.reviewersWorkResData.xAxisData = sortReviewersWorkResList?.map(item => item.reviewerName) || [];
            this.reviewersWorkResData.yAxisData = sortReviewersWorkResList?.map(item => item.reviewerNum) || [];

            this.trainTeacherResData.xAxisData = sortTrainTeacherResList?.map(item => item.reviewerName) || [];
            this.trainTeacherResData.yAxisData = sortTrainTeacherResList?.map(item => item.reviewerNum) || [];
          }
        })
        .catch((error) => {
          // console.log(error);
          this.loading = false;
        });
    },
  }
};
</script>

<style scoped lang="scss">
.home {
  background: #F6F9F8 !important;
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }

  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }

  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans",
  "Helvetica Neue",
  Helvetica,
  Arial,
  sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }

  // 图表样式
  .card-panel-title {
    font-size: 15px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 10px;
  }
  .card-panel {
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 10px;
    background: #fff;
  }
  .card-panel-group {
    display: flex;
    .card-panel-col {
      padding: 10px 20px;
      border-radius: 10px;
      background: #fff;
      &:first-child {
        margin-right: 20px;
      }
    }
  }
}
</style>

