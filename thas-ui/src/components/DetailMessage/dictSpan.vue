<template>
  <div class="dict-span">
    <span v-if="(option.listClass == 'default' || option.listClass == '') && type == 'default'" :class="option.cssClass" class="span" :style="{width: labelWidth || 'inherit'}"> {{ otherValue || label }} </span>
    <el-tag v-else :disable-transitions="true" :type="option.listClass == 'primary' ? '' : option.listClass" :class="[option.cssClass, {noTag: noTag}]" :style="{width: labelWidth || 'inherit'}"> {{ otherValue || label }} </el-tag>
    <slot v-show="label"></slot>
  </div>
</template>

<script>
import store from "@/store";

export default {
  name: "",
  props: {
    dictType: {
      type: String,
      require: true,
    },
    labelWidth: {
      type: String,
      default: () => ''
    },
    otherValue: {
      type: [String, Number],
      default: () => ''
    },
    value: {
      type: [String, Number],
      require: true,
    },
    options: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      default: () => 'default'
    },
    noTag: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      label: "",
      option: {}
    };
  },

  components: {},

  computed: {},

  mounted() {
    let flag = false;
    if (this.options && Array.isArray(this.options)) {
      this.options.forEach(data => {
        if (data.dictValue == this.value) {
          this.flag = true;
          this.setData(data);
        }
      })
    }
    if (flag) return;
    if (!this.dictType) return;
    const dictionary = store.getters.dictionaryMap;
    if (dictionary[this.dictType]) {
      dictionary[this.dictType].forEach((data) => {
        if (data.dictValue == this.value) {
          this.setData(data);
        }
      });
      return;
    }
    this.getDicts(this.dictType).then((res) => {
      if (res.code == 200) {
        res.data.forEach((data) => {
          if (data.dictValue == this.value) {
            this.setData(data);
          }
        });
        store.dispatch("SetDict", {
          type: this.dictType,
          dict: res.data,
        });
      }
    });
  },

  methods: {
    setData(data) {
      this.$set(this, 'option', data)
      this.$set(this, 'label', data.dictLabel)
    }
  },
};
</script>
<style lang='scss' scoped>
.dict-span {
  .span {
    display: inline-block;
    vertical-align: baseline;
  }
  & > * {
    vertical-align: middle;
  }
}

.noTag {
  background-color: transparent;
  border-color: transparent;
  vertical-align: baseline;
  padding-left: 0;
}
</style>