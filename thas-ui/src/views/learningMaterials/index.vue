<template>
  <el-card v-loading="loading" class="learning-materials-list">
    <el-row :gutter="24">
      <el-col :span="24" :xs="24">
        <el-form ref="queryForm" :model="queryData" size="small" label-width="80px" inline>
          <el-form-item label="标题" prop="title">
            <el-input v-model.trim="queryData.title" placeholder="请输入标题" maxlength="128" clearable :style="{ width: '100%' }">
            </el-input>
          </el-form-item>

          <el-form-item label="类型" prop="type">
            <el-select v-model.trim="queryData.type" placeholder="请选择类型" clearable filterable>
              <el-option v-for="cate in typeOptions" :key="cate.dictValue" :value="cate.dictValue" :label="cate.dictLabel">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="类别" prop="category">
            <el-select v-model.trim="queryData.category" placeholder="请选择类别" clearable filterable>
              <el-option v-for="cate in categoryOptions" :key="cate.dictValue" :value="cate.dictValue" :label="cate.dictLabel">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" size="small" @click="search">
              查询
            </el-button>
            <el-button type="primary" size="small" @click="resetForm">
              重 置
            </el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="24">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="showMaterialsForm(1)">添加</el-button>
      </el-col>
    </el-row>

    <el-row :gutter="24" style="margin-top: 24px">
      <el-col :span="24">
        <el-table :data="dataSource" border>
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="showMaterialsForm(2, scope.row)">编辑</el-button>
              <el-button type="text" @click="showMaterialDetail(scope.row)">详情</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" label="标题" prop="title"></el-table-column>
          <el-table-column align="center" label="类型" prop="type">
            <template slot-scope="scope">
              {{ studyType(`${scope.row.type}`) }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="类别" prop="category">
            <template slot-scope="scope">
              {{ studyCategory(`${scope.row.category}`) }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="学习次数" prop="learnNum"></el-table-column>
          <el-table-column align="center" label="留言次数" prop="messageNum"></el-table-column>
          <el-table-column align="center" label="创建人" prop="createId"></el-table-column>
          <el-table-column align="center" label="创建时间" prop="createTime"></el-table-column>
          <el-table-column align="center" label="状态" prop="status">
            <template slot-scope="scope">
              {{ studyStatus(`${scope.row.status}`) }}
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query" />
      </el-col>
    </el-row>

    <el-dialog :title="materialFormTitle" :visible.sync="materialDialogVisible" destroy-on-close width="60%" @close="close" :close-on-click-modal="false">
      <material-form ref="materialForm" :formData="current"></material-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="materialDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="addMaterial">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="学习资源详情" :visible.sync="dialogVisible" width="60%" :close-on-click-modal="false">
      <material-detail :formData="current"></material-detail>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </el-card>
</template>

<script>
import MaterialForm from "./components/materialForm.vue";
import MaterialDetail from "./components/materialDetail.vue";
import request from "@/utils/request";
export default {
  name: "LearningMaterials",
  components: {
    MaterialForm,
    MaterialDetail,
  },
  data() {
    return {
      dataSource: [],
      queryData: {
        title: "",
        type: null,
        category: null,
        pageSize: 10,
        pageNum: 0,
      },
      total: 0,
      materialDialogVisible: false,
      dialogVisible: false,
      materialFormTitle: "",
      studyResourceStatusOptions: [],
      typeOptions: [],
      categoryOptions: [],
      current: undefined,
      url: {
        list: "/system/resource/list",
      },
      loading: false
    };
  },

  computed: {
    studyStatus() {
      return (value) => {
        const status = this.studyResourceStatusOptions.find(
          (status) => status.dictValue == value
        );
        return status ? status.dictLabel : value;
      };
    },
    studyType() {
      return (value) => {
        const type = this.typeOptions.find((type) => type.dictValue == value);
        return type ? type.dictLabel : value;
      };
    },
    studyCategory() {
      return (value) => {
        const category = this.categoryOptions.find(
          (category) => category.dictValue == value
        );
        return category ? category.dictLabel : value;
      };
    },
  },

  mounted() {
    this.initDicts();
    this.query();
  },

  methods: {
    initDicts() {
      this.getDicts("study_type").then((response) => {
        this.typeOptions = response.data;
      });
      this.getDicts("study_category").then((response) => {
        this.categoryOptions = response.data;
      });
      this.getDicts("study_resource_status").then((response) => {
        this.studyResourceStatusOptions = response.data;
      });
    },
    search() {
      this.queryData.pageNum = 1;
      this.query();
    },
    query() {
      this.loading = true
      request({
        url: this.url.list,
        method: "get",
        params: this.queryData
      }).then((res) => {
        this.loading = false
        if (res.code != 200) return;
        this.dataSource = res.rows;
        this.total = res.total;
      }).catch((error) => {
        // console.log(error)
        this.loading = false
      })
    },
    resetForm() {
      this.$refs["queryForm"].resetFields();
      this.query();
    },
    close() {
      this.$refs.materialForm.videoFiles = []
      this.$refs.materialForm.videoIds = []
      if (this.current) {
        this.current.resourceAddress = ""
        this.current.resourceCoverAddress = ""
      }
      this.$refs.materialForm.coverFiles = []
      this.$refs.materialForm.resetForm()
    },
    showMaterialsForm(arg, material) {
      this.materialFormTitle = arg == 1 ? "添加学习资源" : "编辑学习资源";
      if (material) {
        request({
          url: "/system/resource/" + material.id,
          method: "get",
        })
          .then((res) => {
            if (res.code === 200) {
              this.current = res.data;
              // console.log(res.data.resourceCoverAddress)
              let fileInfoList = JSON.parse(res.data.resourceCoverAddress);
              let videoInfoList = ''
              if (res.data.resourceAddress) {
                videoInfoList = JSON.parse(res.data.resourceAddress)
              }
              if (fileInfoList && fileInfoList.length) {
                this.$refs.materialForm.coverFiles = [
                  {
                    name: fileInfoList[0].fileName,
                    file: fileInfoList[0].url,
                    fileId: fileInfoList[0].fileId
                  },
                ];
                this.current.resourceCoverAddress = fileInfoList[0].fileId
              } else {
                this.$refs.materialForm.coverFiles = [];
                this.current.resourceCoverAddress = "";
              }
              if (videoInfoList && videoInfoList.length) {
                let _videoIds = [];
                for (let index = 0; index < videoInfoList.length; index++) {
                  this.$refs.materialForm.videoFiles.push({
                    name: videoInfoList[index].fileName,
                    file: videoInfoList[index].url,
                    fileId: videoInfoList[index].fileId
                  });
                  _videoIds.push(videoInfoList[index].fileId);
                }
                this.current.resourceAddress = res.data.resourceAddress;
                this.$refs.materialForm.videoIds = _videoIds;
              } else {
                this.$refs.materialForm.videoFiles = [];
                this.$refs.materialForm.videoIds = [];
                this.current.resourceAddress = "";
              }
              if (res.data.contentDescription) {
                res.data.contentDescription = decodeURI(res.data.contentDescription)
              }
            }
          })
          .catch((error) => {
            // console.log(error);
          });
      } else {
        this.current = undefined;
      }
      this.materialDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.materialForm.clearValidate();
      });
    },
    showMaterialDetail(material) {
      request({
        url: "/system/resource/" + material.id,
        method: "get",
      })
        .then((res) => {
          if (res.code === 200) {
            this.current = res.data;
            let fileInfoList =
              res.data.resourceCoverAddress &&
              JSON.parse(res.data.resourceCoverAddress);
            let videoInfoList =
              res.data.resourceAddress && JSON.parse(res.data.resourceAddress);
            this.current.resourceCoverAddress = "";
            if (fileInfoList && fileInfoList.length) {
              this.current.resourceCoverAddress = fileInfoList[0].url;
            }
            this.current.resourceAddress = "";
            if (videoInfoList && videoInfoList.length) {
              let _videoIds = [];
              for (let index = 0; index < videoInfoList.length; index++) {
                _videoIds.push(videoInfoList[index].url);
              }
              this.current.resourceAddress = _videoIds.join("、");
            }
            if (res.data.contentDescription) {
              res.data.contentDescription = decodeURI(res.data.contentDescription)
            }
          }
        })
        .catch((error) => {
          // console.log(error);
        });
      this.dialogVisible = true;
    },
    async addMaterial() {
      if (
        this.$refs.materialForm.getValue &&
        this.$refs.materialForm.getValue instanceof Function
      ) {
        const data = await this.$refs.materialForm.getValue();
        if (!data) {
          return;
        }
        let method = "post";
        let message = "添加成功！"
        if (this.materialFormTitle === "编辑学习资源") {
          method = "put";
          message = "编辑成功！"
        }
        request({
          url: "/system/resource",
          method,
          data,
        })
          .then((res) => {
            if (res.code === 200) {
              this.$message({
                type: "success",
                message,
              });
              this.materialDialogVisible = false;
              this.query();
            }
          })
          .catch((error) => {
            // console.log(error);
          });
      }
    },
  },
};
</script>

<style lang='scss' scoped>
.learning-materials-list {
  padding: 24px;

  ::v-deep .el-dialog {
    .el-dialog__header {
      background-color: #3c81f5;
      padding-top: 10px;
      .el-dialog__title {
        color: white;
      }
      .el-dialog__headerbtn {
        top: 15px;

        .el-dialog__close {
          color: white;
        }
      }
    }
    .el-dialog__body {
      padding-bottom: 0;
      & > span {
        color: black;
      }
    }
  }
}
</style>
