<!--  -->
<template>
  <div class="audit-evaluation" ref="auditEvaluation" v-loading="loading">
    <div v-if="distributed">
      <!-- 医院名称 -->
      <el-card class="box-card" shadow="always" style="margin-bottom: 10px; margin-left: -5px">
        <h1 style="margin: 0">{{ detail.hospitalName }}</h1>
      </el-card>

      <!-- 总结详情 -->
      <el-card v-if="isReportS" class="box-card autScrollCard" shadow="never">
        <div slot="header" class="clearfix">
          <h4>最终修改总结</h4>
        </div>
        <div>
          <el-form ref="summaryForm" :model="summaryForm" label-width="110px" :rules="rules">
            <el-form-item label="评价结果：">
              <el-select disabled v-model="summaryForm.autResult" clearable>
                <el-option label="通过认证" value="1"></el-option>
                <el-option label="有条件通过认证" value="2"></el-option>
                <el-option label="不通过认证" value="3"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="亮点：" prop="autAdvantage">
              <el-input disabled type="textarea" v-model="summaryForm.autAdvantage" :maxlength="10000" :rows="3"
               ></el-input>
            </el-form-item>
            <el-form-item label="不足：" prop="autEvaluate">
              <el-input disabled type="textarea" v-model="summaryForm.autEvaluate" :maxlength="10000" :rows="3"
               ></el-input>
            </el-form-item>
            <el-form-item label="整改建议：" prop="autProposal">
              <el-input disabled type="textarea" v-model="summaryForm.autProposal" :maxlength="10000" :rows="3"
               ></el-input>
            </el-form-item>
            <el-form-item label="改进机会：" prop="autImprove">
              <el-input disabled type="textarea" v-model="summaryForm.autImprove" :maxlength="10000" :rows="3"
               ></el-input>
            </el-form-item>
            <el-form-item label="备注：" prop="reviewerRespond">
              <el-input type="textarea" v-model="summaryForm.reviewerRespond" :maxlength="10000" :rows="3"
               ></el-input>
            </el-form-item>

            <el-form-item label=" " v-if="autSaAudStatus == AutSaAudCurrentStatusEnum.REPORT_FINISH">
              <el-button type="primary" @click="summarySubmit" v-loading="summaryLoading"
                :disabled="summaryLoading">提交总结</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>

      <!-- 评价  -->
      <el-card v-if="isReportM" class="box-card autScrollCard" shadow="never" :style="{ height: autCardHeight }">
        <div slot="header" class="clearfix">
          <h4>最终修改</h4>
          <div class="header-right" v-if="standardLoadedKey" :key="headerSlotKeys">
            <el-button size="mini" type="primary" @click="submitFinal">最终修改完成</el-button>
          </div>
        </div>
        <!-- 款项选择器 -->
        <!-- <evaluation-base :versionId="relation.autCsId" :clauseIds="clauseIds" @selectChange="selectChange" @loaded="standardLoaded"></evaluation-base> -->
        <standard-card ref="standardCard" :key="`base${loadingKeys}`" :clauseIds="clauseIds" :versionId="relation.autCsId"
          @selectChange="selectChange" @loaded="standardLoaded">
        </standard-card>

        <!-- 条小结 -->

        <!-- 款项 -->
        <div v-show="clauseList.length" class="evaluation-content" :key="`ach${selectedKeys}`"
          :style="{ height: clauseItemHeight }">
          <el-form :key="loadingKeys" label-width="100px">
            <clause-item v-for="(clause, index) in clauseList" v-show="isThisPage(index + 1)" :clause="clause"
              :key="clause.clauseId">
              <!-- 最终修改之前的评审修改数据 （评审评价 / 评审组长修改）v-if="srmRjMap[clause.clauseId]" -->
              <evaluation-review v-if="showExModify(clause.clauseId)" isLeader :type="AutSaAudCurrentStatusEnum.SR_CLAUSE" :clause="clause"
                :aut="beforeMap(clause.clauseId)" :status="autSaAudStatus" readonly></evaluation-review>
              <!-- 最终修改 审查组长修改 -->
              <evaluation-review  :key="clauseItemKeys" isLeader :type="AutSaAudCurrentStatusEnum.SR_REPORT_M" :clause="clause"
                :aut="srMapC(clause.clauseId)" :status="autSaAudStatus" :readonly="!!srmMap[clause.clauseId]"
                :modifier="showModifier(clause.clauseId)"></evaluation-review>
              <!-- <evaluation-review v-if="srmMap[clause.clauseId]" isLeader :readonly="false"
                :type="AutSaAudCurrentStatusEnum.SR_REPORT_M" :clause="clause" :aut="srmMap[clause.clauseId]"
                :status="autSaAudStatus"></evaluation-review> -->
            </clause-item>
            <pagination v-show="total > limit" :total="total" :page.sync="pageNum" :limit.sync="limit"
              :pageSizes="[limit]" />
          </el-form>
        </div>
      </el-card>
      <!-- 总结提交 -->

    </div>
  </div>
</template>

<script>
import evaluationMixin from "@/mixins/evaluation.vue";

export default {
  name: "LInspectorIndex",
  mixins: [evaluationMixin],
  components: {
  },
  props: {},
  watch: {
    detail: {
      deep: true,
      immediate: true,
      handler() {
        if (this.detail && this.detail.autSaAudStatus && this.AutSaAudCurrentStatusEnum.WAIT_AUD_FIRST_TRIAL_SUM == this.detail.autSaAudStatus) {
          this.handleError();
        }
      },
    },
  },
  data() {
    return {
      show: false,
      loading: true,
      relation: {},
      detail: {},
      clauseIds: [],
      specialInit: true,
      summaryForm: {
        autResult: 0,
        autDesc: '',
        autAdvantage: '',
        autEvaluate: '',
        autProposal: '',
        autImprove: '',
        reviewerRespond: ''
      },
      rules: {
        autAdvantage: [{
          required: true,
          message: '请填写亮点',
          trigger: ['blur'],
        }],
        autEvaluate: [{
          required: true,
          message: '请填写不足',
          trigger: ['blur'],
        }],
        autProposal: [{
          required: true,
          message: '请填写整改建议',
          trigger: ['blur'],
        }],
        autImprove: [{
          required: true,
          message: '请填写改进机会',
          trigger: ['blur'],
        }],
      },
      summaryLoading: false
    };
  },

  computed: {
    waitFirstOrProc() {
      return (this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.WAIT_AUD_FIRST_TRIAL ||
        this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_INQUIRE_PROC) && this.showConvenientBtn
    },
    showExamine() {
      return true;
    },
    showExamineCheck() {
      // 审核驳回
      return this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_REVIEW_REJECT
    },

    showAutFilter() {
      const list = Object.values(this.frMap);
      let frClauseIds = list.map(({ clauseId }) => Number(clauseId));
      let list2 = this.distributeClauseIds.reduce((list, next) => {
        if (!frClauseIds.includes(Number(next))) list.push(Number(next));
        return list;
      }, [])
      return list2;
    },

    // 被驳回
    showAutRjFilter() {
      const list = Object.values(this.frMap);
      const rjList = list.filter(({ rejected }) => rejected).map(({ clauseId }) => clauseId);
      return rjList;
    },
    isReportM() {
      return this.autSaAudStatus == this.AutSaAudCurrentStatusEnum.WAIT_REPORT;
    },

    isReportS() {
      return this.autSaAudStatus == this.AutSaAudCurrentStatusEnum.REPORT_FINISH;
    },

    showExModify() {
      return clauseId => {
        return this.srmRjMap[clauseId] || this.srmMap[clauseId]
      }
    },

    beforeMap() {
      return id => {
        let sr = this.srMap[id];
        let frm = this.fwmMap[id];
        let trm = this.twmMap[id];

        if (trm) {
          return trm
        }
        if (frm) {
          return frm
        }
        if (sr.beforeAutDesc) {
          try {
            let { beforeAutDesc, beforeAutResult, autResult, autDesc, createBy, updateBy, ...data } = sr
            return {
              updateBy: sr.createBy,
              autResult: beforeAutResult,
              autDesc: JSON.parse(beforeAutResult),
              ...data
            }
          } catch (error) {
            return sr
          }
        } else {
          return sr
        }
      }
    },

    srMapC() {
      return id => {
        let srm = this.srmMap[id];
        if (srm) {
          return srm
        }
        let srmRj = this.srmRjMap[id]
        if (srmRj) {
          let { submitType, ...data } = srmRj
          return data
        }
        return this.beforeMap(id)
      }
    },

    showModifier() {
      return id => {
        let status = [
          this.AutSaAudCurrentStatusEnum.WAIT_REPORT,
          this.AutSaAudCurrentStatusEnum.REPORT_FINISH
        ]
        return status.includes(this.relation.status) && (this.srmMap[id] || this.srmRjMap[id]) ? '审查组长' : ''
      }
    }
  },

  mounted() {
    this.init2(true, this.$route.query.autCode, this.$route.query.type);
  },

  methods: {
    initFinalBack() {
      if (this.isReportM && this.relation.rejectIds && this.relation.rejectIds.length) {
        // this.$set(this, 'clauseIds', this.relation.rejectIds)
        // this.$set(this, 'distributeClauseIds', this.relation.rejectIds)
        // this.loadingKeys++;
      } else {
        if (this.showAutRjFilter.length) {
          this.$set(this, 'clauseIds', this.showAutRjFilter)
          this.$set(this, 'distributeClauseIds', this.showAutRjFilter)
          this.loadingKeys++;
        }
      }

      let summary = this.detail.latestAutSaAudSum || this.detail.audSecondTrialSum;
      if (summary) {
        this.summaryForm.autResult = summary.autResult
        this.summaryForm.autDesc = summary.autDesc
      } else {
        this.summaryForm.autResult = ` ${this.detail.autSaAudReport ? this.detail.autSaAudReport.autSaAudResult : 0}`
      }

      if (this.detail.autSaAudStatus == this.AutSaAudCurrentStatusEnum.REPORT_FINISH) {
        this.summaryForm.autResult = `${this.detail.autResult || this.detail.autSaAudReport?.autSaAudResult || 0}`
      }

      try {
        let desc = JSON.parse(this.summaryForm.autDesc);
        if (this.autSaAudStatus == this.AutSaAudCurrentStatusEnum.REPORT_FINISH) {
          this.summaryForm.autAdvantage = this.detail.autAdvantage || desc.autAdvantage || ''
          this.summaryForm.autEvaluate = this.detail.autEvaluate || desc.autEvaluate || ''
          this.summaryForm.autProposal = this.detail.autProposal || desc.autProposal || ''
          this.summaryForm.autImprove = this.detail.autImprove || desc.autImprove || ''
        }
      } catch (error) {
        // console.log(error);
      }

    },
    handleError() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push({ path: "/fInspector/c/inspectorEvaluationFinalList" });
    },

    async summarySubmit() {
      this.$refs.summaryForm.validate(async valid => {
        if (!valid) return;
        let {autAdvantage, autEvaluate,autImprove,autProposal, autDesc, ...form} = this.summaryForm;
        form.autDesc = JSON.stringify({
          autAdvantage, autEvaluate,autImprove,autProposal
        })
        let data = {
          autCode: this.relation.autCode,
          accountId: this.$store.getters.userId + "",
          submitType: this.AutSaAudCurrentStatusEnum.SR_REPORT_M_SUMMARY,
          autSaAudLists: [form]
        }
        try {
          let res = await this.submit(data);
          // 报错就不提示
          if (!res) return;
          this.$message({
            type: 'success',
            message: "提交成功"
          });
          this.handleError()
          // this.init2(true, this.$route.query.autCode, this.$route.query.type);
        } catch (error) {
          //  this.init2(true, this.$route.query.autCode, this.$route.query.type);
        }
      })
    },

    // 初审初查
    async submitFinal() {
      let data = {
        autCode: this.relation.autCode,
        accountId: this.$store.getters.userId + "",
        submitType: this.AutSaAudCurrentStatusEnum.SR_REPORT_M_CONFIRM,
        autSaAudLists: []
      }
      try {
        let res = await this.submit(data);
        // 报错就不提示
        if (!res) return;
        this.$message({
          type: 'success',
          message: "最终修改提交成功"
        });
        this.init2(true, this.$route.query.autCode, this.$route.query.type);
      } catch (error) {
        if (error.code == 1000003) {
          this.init2(true, this.$route.query.autCode, this.$route.query.type);
        }
      } finally {
        // this.termSubmitLoading = false;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.box-card+.box-card {
  margin-top: 15px;
}

h4 {
  margin: 10px 0;
}
</style>
