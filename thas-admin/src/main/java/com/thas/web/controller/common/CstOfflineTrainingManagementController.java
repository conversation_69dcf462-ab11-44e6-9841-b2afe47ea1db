package com.thas.web.controller.common;

import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.page.TableDataInfo;
import com.thas.common.enums.BusinessType;
import com.thas.common.utils.poi.ExcelUtil;
import com.thas.web.domain.CstOfflineTrainingManagement;
import com.thas.web.service.ICstOfflineTrainingManagementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 线下培训管理Controller
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
@RestController
@RequestMapping("/system/management")
@Api(value = "CstCertificationStandardsController", tags = "管理端线下培训管理")
public class CstOfflineTrainingManagementController extends BaseController {

    @Autowired
    private ICstOfflineTrainingManagementService cstOfflineTrainingManagementService;

    /**
     * 查询线下培训管理列表
     */
//    @PreAuthorize("@ss.hasPermi('system:management:list')")
    @GetMapping("/list")
    @ApiOperation(httpMethod = "GET", value = "查询线下培训管理列表")
    public TableDataInfo list(CstOfflineTrainingManagement cstOfflineTrainingManagement) {
        List<CstOfflineTrainingManagement> list = cstOfflineTrainingManagementService.selectCstOfflineTrainingManagementList(cstOfflineTrainingManagement);
        return getDataTable(list);
    }

    /**
     * 导出线下培训管理列表
     */
//    @PreAuthorize("@ss.hasPermi('system:management:export')")
    @Log(title = "线下培训管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CstOfflineTrainingManagement cstOfflineTrainingManagement) {
        List<CstOfflineTrainingManagement> list = cstOfflineTrainingManagementService.selectCstOfflineTrainingManagementList(cstOfflineTrainingManagement);
        ExcelUtil<CstOfflineTrainingManagement> util = new ExcelUtil<CstOfflineTrainingManagement>(CstOfflineTrainingManagement.class);
        util.exportExcel(response, list, "线下培训管理数据");
    }

    /**
     * 获取线下培训管理详细信息
     */
//    @PreAuthorize("@ss.hasPermi('system:management:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(cstOfflineTrainingManagementService.selectCstOfflineTrainingManagementById(id));
    }

    /**
     * 新增线下培训管理
     */
//    @PreAuthorize("@ss.hasPermi('system:management:add')")
    @Log(title = "线下培训管理", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(httpMethod = "POST", value = "新增线下培训管理")

    public AjaxResult add(@RequestBody CstOfflineTrainingManagement cstOfflineTrainingManagement) {
        return toAjax(cstOfflineTrainingManagementService.insertCstOfflineTrainingManagement(cstOfflineTrainingManagement));
    }

    /**
     * 修改线下培训管理
     */
//    @PreAuthorize("@ss.hasPermi('system:management:edit')")
    @Log(title = "线下培训管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(httpMethod = "PUT", value = "修改线下培训管理")
    public AjaxResult edit(@RequestBody CstOfflineTrainingManagement cstOfflineTrainingManagement) {
        return toAjax(cstOfflineTrainingManagementService.updateCstOfflineTrainingManagement(cstOfflineTrainingManagement));
    }

    /**
     * 删除线下培训管理
     */
//    @PreAuthorize("@ss.hasPermi('system:management:remove')")
    @Log(title = "线下培训管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(httpMethod = "DELETE", value = "删除线下培训管理")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(cstOfflineTrainingManagementService.deleteCstOfflineTrainingManagementByIds(ids));
    }

    /**
     * 修改培训状态
     *
     * @param id id
     */
    @Log(title = "线下培训管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}")
    @ApiOperation(httpMethod = "PUT", value = "修改培训状态")
    public void editTrainingStatus(@PathVariable Long id, @RequestParam("status") String status) {
        cstOfflineTrainingManagementService.updateTrainingStatus(id, status);
    }
}
