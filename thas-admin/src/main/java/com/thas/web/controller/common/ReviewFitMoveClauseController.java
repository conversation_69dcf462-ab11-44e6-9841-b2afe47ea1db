package com.thas.web.controller.common;

import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.page.TableDataInfo;
import com.thas.common.enums.BusinessType;
import com.thas.common.utils.poi.ExcelUtil;
import com.thas.web.domain.ReviewFitMoveClause;
import com.thas.web.domain.dto.MoveClauseRequest;
import com.thas.web.domain.dto.MoveClauseResponse;
import com.thas.web.domain.dto.ReviewClauseMoveRequest;
import com.thas.web.domain.dto.ReviewFitMoveClauseReq;
import com.thas.web.service.IReviewFitMoveClauseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 评审不适用与挪动款Controller
 * 
 * <AUTHOR>
 * @date 2023-03-28
 */
@RestController
@RequestMapping("/review/fit/move/clause")
@Api(value = "ReviewFitMoveClauseController", tags = "评审不适用与挪动款Controller")
public class ReviewFitMoveClauseController extends BaseController
{
    @Autowired
    private IReviewFitMoveClauseService reviewFitMoveClauseService;

    /**
     * 查询评审不适用与挪动款列表
     */
    @GetMapping("/list")
    public TableDataInfo list(ReviewFitMoveClause reviewFitMoveClause)
    {
        startPage();
        List<ReviewFitMoveClause> list = reviewFitMoveClauseService.selectReviewFitMoveClauseList(reviewFitMoveClause);
        return getDataTable(list);
    }

    /**
     * 导出评审不适用与挪动款列表
     */
    @Log(title = "测试评审不适用与挪动款", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ReviewFitMoveClause reviewFitMoveClause)
    {
        List<ReviewFitMoveClause> list = reviewFitMoveClauseService.selectReviewFitMoveClauseList(reviewFitMoveClause);
        ExcelUtil<ReviewFitMoveClause> util = new ExcelUtil<>(ReviewFitMoveClause.class);
        util.exportExcel(response, list, "测试评审不适用与挪动款数据");
    }

    /**
     * 获取评审不适用与挪动款详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(reviewFitMoveClauseService.selectReviewFitMoveClauseById(id));
    }

    /**
     * 新增评审不适用与挪动款
     */
    @Log(title = "新增评审不适用与挪动款信息", businessType = BusinessType.INSERT)
    @ApiOperation(httpMethod = "POST", value = "新增评审不适用与挪动款信息")
    @PostMapping
    public AjaxResult add(@RequestBody ReviewFitMoveClause reviewFitMoveClause)
    {
        return toAjax(reviewFitMoveClauseService.insertReviewFitMoveClause(reviewFitMoveClause));
    }

    /**
     * 修改评审不适用与挪动款信息
     */
    @Log(title = "修改评审不适用与挪动款信息", businessType = BusinessType.UPDATE)
    @ApiOperation(httpMethod = "PUT", value = "修改评审不适用与挪动款信息")
    @PutMapping
    public AjaxResult edit(@RequestBody ReviewFitMoveClause reviewFitMoveClause)
    {
        return toAjax(reviewFitMoveClauseService.updateReviewFitMoveClause(reviewFitMoveClause));
    }

    /**
     * 删除评审不适用与挪动款
     */
    @Log(title = "测试评审不适用与挪动款", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(reviewFitMoveClauseService.deleteReviewFitMoveClauseByIds(ids));
    }

    /**
     * 新增或更新评审不适用与挪动款
     */
    @Log(title = "提交-新增或更新评审不适用与挪动款", businessType = BusinessType.INSERT)
    @ApiOperation(httpMethod = "POST", value = "提交-新增或更新评审不适用与挪动款")
    @PostMapping("/submit")
    public AjaxResult addOrUpdateReviewFitMoveClause(@RequestBody @Validated ReviewFitMoveClauseReq reviewFitMoveClauseReq)
    {
        reviewFitMoveClauseService.submitReviewFitMoveClause(reviewFitMoveClauseReq);
        return AjaxResult.success();
    }

    @ApiOperation(httpMethod = "POST", value = "提交-挪动款")
    @PostMapping("/clause-move")
    public AjaxResult clauseMove(@RequestBody @Validated ReviewClauseMoveRequest reviewClauseMoveRequest) {
        reviewFitMoveClauseService.clauseMove(reviewClauseMoveRequest);
        return AjaxResult.success();
    }

    @ApiOperation(httpMethod = "POST", value = "详情-挪动款")
    @PostMapping("/move-info")
    public AjaxResult moveClauseInfo(@RequestBody @Validated MoveClauseRequest moveClauseRequest) {
        MoveClauseResponse moveClauseResponse = reviewFitMoveClauseService.moveClauseInfo(moveClauseRequest);
        return AjaxResult.success(moveClauseResponse);
    }
}
