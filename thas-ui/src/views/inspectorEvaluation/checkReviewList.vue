<!--  -->
<template>
  <div class="check-review-list">
    <el-form ref="form" :model="queryData" inline label-width="130px">
      <el-form-item label="医院名称：" prop="distributeHospitalName">
        <el-input v-model.trim="queryData.distributeHospitalName" :maxlength="20" placeholder="请输入医院名称"></el-input>
      </el-form-item>
      <el-form-item label="">
        <el-button type="primary" size="small" @click="query" v-loading='loading'>
          查 询
        </el-button>
        <el-button type="primary" size="small" @click="reset">
          重 置
        </el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataSource" border stripe>
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="scope">
          <div class="table-btn">
            <el-button type="text" v-if="showCReportBtn(scope.row)" @click="checkReport(scope.row)">评审报告审查</el-button>
            <el-button type="text" v-if="showToReviewLeader(scope.row)" @click="turnToLeader(scope.row)">事实准确性查询表确认
            </el-button>
            <el-button type="text" v-if="showSeniorReport(scope.row)" @click="seniorReport(scope.row)">验证报告审查</el-button>
            <el-button type="text" v-if="showReviewLeaderReport(scope.row)" @click="turnToLeader(scope.row)">评审结果修改审查
            </el-button>

            <el-button type="text" v-if="showEReportBtn(scope.row)" @click="examineReport(scope.row)">查看报告结果</el-button>
            <el-button type="text" @click="downloadReport(scope.row)">评审报告下载</el-button>
            <el-button v-if="showFarReport(scope.row)" type="text"
              @click="downloadRarReport(scope.row)">事实准确性查询表下载</el-button>
            <el-button v-if="showVerifyReport(scope.row)" type="text"
              @click="downloadVerifyReport(scope.row)">验证报告下载</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="医院名称" align="center" prop="hospitalName">
      </el-table-column>
      <el-table-column label="评审完成时间" align="center" prop="audSecondSubmitTime">
      </el-table-column>
      <el-table-column label="评审报告生成时间" align="center" prop="reportSubmitTime">
      </el-table-column>
      <el-table-column label="审查评审报告完成时间" align="center" prop="reviewReviewReportSubmitTime">
      </el-table-column>
      <el-table-column label="准确性审查完成时间" align="center" prop="accuracyReviewSubmitTime">
      </el-table-column>
      <el-table-column label="验证评审员验证完成时间" align="center" prop="seniorReviewerReviewSubmitTime">
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          {{ AutSaAudCurrentStatusEnum[scope.row.status] }}
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize"
      @pagination="query" />


    <el-dialog title="验证结果" :visible.sync="visible" width="80%" :close-on-click-modal="false" custom-class="elDialog"
      @close="trClose">
      <ModifyTable v-if="trClauseList.length" :data="modifyClause" :small="true" :versionId='selectRow.autCsId'
        :showAll='false' split :height="trClauseList.length >= 6 ? 380 : undefined">
        <el-table-column label="评价结果" width="90">
          <template slot-scope="scope">
            <dict-span noTag dictType="review_result" :value="modifyAutResult(scope.row.clauseId)"></dict-span>
          </template>
        </el-table-column>
        <el-table-column label="评价内容" align="center">
          <template slot-scope="scope">
            <div style="text-align: left;white-space: pre-line;"> {{ modifyAutDesc(scope.row.clauseId) }}</div>
          </template>
        </el-table-column>
        <el-table-column label="验证评审员疑问" prop="yzDesc" align="center">
          <template slot-scope="scope">
            {{ trDesc(scope.row.clauseId) || '--' }}
          </template>
        </el-table-column>
        <el-table-column v-if="showTrm" label="评审员回应" align="center">
          <template slot-scope="scope">
            <div style="text-align: left;white-space: pre-line;">{{ trmResult(scope.row.clauseId) }}</div>
            <div style="text-align: left;white-space: pre-line;"> {{ trmDesc(scope.row.clauseId) }}</div>
          </template>
        </el-table-column>
      </ModifyTable>
      <br />
      <el-descriptions :key="form.autCode" :column="1" border :labelStyle="{ width: '180px' }">
        <el-descriptions-item label="一般性评价：" prop="autEvaluate">
          {{ form.autEvaluate }}
        </el-descriptions-item>
        <el-descriptions-item label="评审报告优点：" prop="autAdvantage">
          {{ form.autAdvantage }}
        </el-descriptions-item>
        <el-descriptions-item label="修订评审报告建议：" prop="autProposal">
          {{ form.autProposal }}
        </el-descriptions-item>
        <el-descriptions-item label="报告整体评分：" prop="autResult">
          <dict-span noTag :options="suggestionOptions" :value="form.autResult"></dict-span>
        </el-descriptions-item>
        <el-descriptions-item label="认证授予建议：" prop="autSuggestion">
          <dict-span noTag dictType="review_summary" :value="form.autSuggestion"></dict-span>
        </el-descriptions-item>
        <el-descriptions-item label="认证授予建议理由：" prop="autDesc">
          {{ form.autDesc }}
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false">关 闭</el-button>
        <el-button type="primary" @click="turnToLeader2">进入审查</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request, { download } from "@/utils/request";
import DictSpan from "@/components/DetailMessage/dictSpan.vue";
import ModifyTable from "@/views/hospitalSelfEvaluation/components/modifyTable.vue";

export default {
  name: "CheckReviewList",
  components: {
    DictSpan,
    ModifyTable
  },
  props: {},
  watch: {},
  data() {
    return {
      queryData: {
        distributeHospitalName: "",
        distributeStatus: "",
        pageSize: 10,
        pageNum: 1,
      },
      url: {
        list: "/aut/sa/aud/queryList",
      },
      total: 0,
      dataSource: [],
      loading: false,
      visible: false,
      selectRow: null,
      trReport: null,

      form: {},
      trClauseList: [],
      srClauseList: [],
      trmClauseList: [],
      trmSkipClauseList: [],
      suggestionOptions: [
        {
          dictValue: "1",
          dictLabel: "优秀",
          listClass: '',
        },
        {
          dictValue: "2",
          dictLabel: "良好",
          listClass: '',
        },
        {
          dictValue: "3",
          dictLabel: "中等",
          listClass: '',
        },
        {
          dictValue: "4",
          dictLabel: "尚可",
          listClass: '',
        },
        {
          dictValue: "5",
          dictLabel: "较差",
          listClass: '',
        },
      ],
    };
  },

  watch: {
    $route(to, from) {
      if (
        to.path == "/fInspector/CheckReviewList" &&
        from.path == "/fInspector/CInspectorIndex"
      ) {
        this && this.query && this.query();
      }
    },
  },

  computed: {
    showCReportBtn() {
      return (row) => {
        let statuses = [
          this.AutSaAudCurrentStatusEnum.WAIT_REVIEW_REVIEW_REPORT,
          this.AutSaAudCurrentStatusEnum.REVIEW_REVIEW_REPORT_PROC,
          this.AutSaAudCurrentStatusEnum.REVIEW_REVIEW_REPORT_SUM
        ]
        return statuses.includes(row.status)
      }
    },
    showEReportBtn() {
      return (row) => row.autSaAudReport
    },

    showSeniorReport() {
      return row => {
        return row.isLeader == 1 && row.autSaAud && this.showTrReportModal(row)
      }
    },
    showReviewLeaderReport() {
      return row => row.status === this.AutSaAudCurrentStatusEnum.CONFIRM_REVIEW_FACTUAL_MODIFY
    },
    showToReviewLeader() {
      return row => {
        return row.status == this.AutSaAudCurrentStatusEnum.CONFIRM_REVIEW_FACTUAL
      }
    },

    showTrReportModal() {
      return row => {
        return row.status == this.AutSaAudCurrentStatusEnum.CONFIRM_REVIEW_VERIFY
      }
    },

    modifyClause() {
      return this.trClauseList.map(({ clauseId }) => clauseId)
    },

    modifyAutResult() {
      return id => {
        let aut = this.srClauseList.find(({ clauseId }) => clauseId == id)
        return aut?.autResult
      }
    },

    modifyAutDesc() {
      return (id) => {
        let aut = this.srClauseList.find(({ clauseId }) => clauseId == id)
        return this.descSubdivision(aut);
      };
    },

    descSubdivision() {
      return aut => {
        try {
          let autResult = aut.autResult;
          let { autAdvantage, autEvaluate, autProposal, autImprove, autDesc } = JSON.parse(aut.autDesc)
          if (autResult == 0) {
            return '拒绝原因：' + (autDesc || '--')
          }
          if (autResult == 1 || autResult == 2) {
            return '评价描述：' + (autAdvantage || '--')
          } else if (autResult == 3) {
            return '评价描述：' + (autImprove || '--')
          } else {
            if (autResult != 6) {
              let a = autEvaluate ? `不足：${autEvaluate}  \n` : '--'
              let b = autProposal ? `整改建议：${autProposal}  \n` : '--'
              let c = autImprove ? `改进机会：${autImprove}  \n` : '--'
              if (autEvaluate || autProposal || autImprove) {
                return [a, b, c].join(' ')
              }
            }
            return '评价描述：' + (autDesc || '--');
          }
        } catch (error) {
          return '评价描述：' + (aut.autDesc || '--');
        }
      }
    },

    trDesc() {
      return id => {
        return this.trClauseList.find(({ clauseId }) => clauseId == id)?.autDesc || '--'
      }
    },


    showTrm() {
      return this.trmClauseList.length || this.trmSkipClauseList.length

    },


    trmResult() {
      return (id) => {
        try {
          let aut = this.trmSkipClauseList.find(({ clauseId }) => clauseId == id)
          if (!aut) {
            aut = this.trmClauseList.find(({ clauseId }) => clauseId == id)
          }
          let result = aut.autResult;
          // if(result == 0) {
          //   return '评价结果：拒绝修改 \n'
          // }
          let option = this.reviewResultOptions.find(
            ({ dictValue }) => dictValue == result
          );
          return option ? '评价结果：' + option.dictLabel + '\n' : "";
        } catch (error) {
          return ''
        }
      }
    },

    trmDesc() {
      return id => {
        let aut = this.trmSkipClauseList.find(({ clauseId }) => clauseId == id)
        if (!aut) {
          aut = this.trmClauseList.find(({ clauseId }) => clauseId == id)
        }
        return this.descSubdivision(aut)
      }
    },

    showFarReport() {
      return row => {
        if (!row.fileDetailMap) return false;

        if (row.status == this.AutSaAudCurrentStatusEnum.CONFIRM_REVIEW_FACTUAL) {
          let farSummary = row.autSaAud.find(aut => aut.submitType == this.AutSaAudCurrentStatusEnum.FAR_SUMMARY)
          if (farSummary) {
            return (row.fileDetailMap[farSummary.fileIds] || []).length;
          }
          return false;
        }

        if (row.status == this.AutSaAudCurrentStatusEnum.CONFIRM_REVIEW_FACTUAL_MODIFY) {
          let farSummary = row.autSaAud.find(aut => aut.submitType == this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_M_SUBMIT)
          console.log(farSummary);
          if (farSummary) {
            return (row.fileDetailMap[farSummary.fileIds] || []).length;
          }
          return false;
        }

        return false;
      }
    },

    showVerifyReport() {
      return row => {
        let fileMap = row.fileDetailMap || {};
        let fileMapList = Object.values(fileMap);
        let files = fileMapList.find(files => {
          let file = files.find(file => {
            return file.fileType === 'verify_review_report'
          })
          return file
        })
        let file = (files || [])[0];
        return file;
      }
    }

  },

  created() { },

  mounted() {
    this.query();
  },

  methods: {
    query() {
      this.loading = true;
      request({
        url: this.url.list,
        method: "post",
        data: {
          accountId: this.$store.getters.userId + "",
          // type: this.AutSaAudCurrentStatusEnum.REVIEW_REVIEW_REPORT,
          pageType: 'fr_report_r_clause'
        },
      }).then((res) => {
        this.dataSource = (res.rows || [])
        this.total = res.total;
      }).catch(() => {
        this.dataSource = []
        this.total = 0
      }).finally(() => this.loading = false);
    },

    downloadReport({ reviewReportPdfFileId, reviewReportPdfFileName }) {
      download("/common/downloadFile", { fileId: reviewReportPdfFileId }, reviewReportPdfFileName);
    },

    downloadRarReport(row) {
      if (row.status == this.AutSaAudCurrentStatusEnum.CONFIRM_REVIEW_FACTUAL) {
        let farSummary = row.autSaAud.find(aut => aut.submitType == this.AutSaAudCurrentStatusEnum.FAR_SUMMARY)
        if (farSummary) {
          let fileDetailMap = row.fileDetailMap
          let files = fileDetailMap[farSummary.fileIds]
          download("/common/downloadFile", { fileId: files[0].fileId }, files[0].downLoadFileName);
        }
        return
      }

      if (row.status == this.AutSaAudCurrentStatusEnum.CONFIRM_REVIEW_FACTUAL_MODIFY) {
        let farSummary = row.autSaAud.find(aut => aut.submitType == this.AutSaAudCurrentStatusEnum.FAR_CLAUSE_M_SUBMIT)
        if (farSummary) {
          let fileDetailMap = row.fileDetailMap
          let files = fileDetailMap[farSummary.fileIds]
          download("/common/downloadFile", { fileId: files[0].fileId }, files[0].downLoadFileName);
        }
        return
      }
    },

    downloadVerifyReport(row) {
      let file =  this.showVerifyReport(row)
      download("/common/downloadFile", { fileId: file.fileId }, file.downLoadFileName);
    },

    checkReport({ autCode, autCsId, status }) {
      let path = status === this.AutSaAudCurrentStatusEnum.REVIEW_REVIEW_REPORT_SUM ? '/fInspector/reportReviewResults' : '/fInspector/CInspectorIndex'
      this.$router.push({
        path,
        query: {
          autCode,
          type: this.AutSaAudCurrentStatusEnum.FR_REPORT_R_CLAUSE,
          accountId: this.$store.getters.userId
        },
      });
    },

    seniorReport(row) {
      this.selectRow = row;
      this.turnToLeader2();
      // this.visible = true;
      // try {
      //   let list = row.autSaAud
      //   this.trClauseList = list.filter(({ submitType }) => submitType == 'tr_clause')
      //   this.srClauseList = list.filter(({ submitType }) => submitType == 'sr_clause')
      //   this.trmClauseList = list.filter(({ submitType }) => submitType == 'tr_clause_m')
      //   this.trmSkipClauseList = list.filter(({ submitType }) => submitType == 'tr_clause_m_skip')

      //   let autSaAud = list.find(({ submitType }) => submitType == 'tr_summary')
      //   this.form = JSON.parse(autSaAud.autDesc)
      //   this.form.autCode = row.autCode
      // } catch (error) {
      //   console.log(error);
      // }
    },

    examineReport({ autCode, autSaAudReport }) {
      let name =
        autSaAudReport.autSaAudResult == 1
          ? "BaseClausePass"
          : "BaseClauseReject";
      this.$router.push({
        name,
        query: {
          accountId: this.$store.getters.userId,
          pageType: 'fr_report_r_clause',
          autCode
        }
      });
    },

    reset() {
      this.$refs["form"].resetFields();
      this.query();
    },

    trClose() {
      this.form = {}
      this.trClauseList = []
      this.selectRow = null
      this.visible = null
    },

    turnToLeader({ autCode, status }, type) {
      let path = ''
      // 事实准确性查询表确认
      if (status === this.AutSaAudCurrentStatusEnum.CONFIRM_REVIEW_FACTUAL) {
        path = "/fInspector/ModificationConfirm"
      }
      // 评审结果修改审查
      if (status === this.AutSaAudCurrentStatusEnum.CONFIRM_REVIEW_FACTUAL_MODIFY) {
        path = "/fInspector/reviewResultsModified"
      }
      // 验证报告审查
      if (type === 'other') {
        path = "/fInspector/verifyReportReview"
      }
      this.$router.push({
        path,
        query: {
          autCode,
          type: this.AutSaAudCurrentStatusEnum.FR_REPORT_R_CLAUSE
          // type: status == this.AutSaAudCurrentStatusEnum.CONFIRM_REVIEW_FACTUAL ? this.AutSaAudCurrentStatusEnum.FR_REPORT_R_CLAUSE : this.AutSaAudCurrentStatusEnum.TR_CLAUSE
        }
      })
    },

    turnToLeader2() {
      if (!this.selectRow) {
        this.trClose();
        return
      }

      this.turnToLeader(this.selectRow, 'other')
    }
  },
};
</script>
<style lang='scss' scoped>
.check-review-list {
  min-height: inherit;
  background-color: white;
  padding: 12px;
}


::v-deep .table-btn {
  .el-button+.el-button {
    display: block;
    margin: 0 auto;
  }
}
</style>
