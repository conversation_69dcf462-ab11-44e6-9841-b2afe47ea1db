<!--  -->
<template>
  <div class='evaluation-self evaluation' v-loading="submitLoading"  >
    <el-form ref="evaluationForm" :model="formData" :label-width="labelWidth" :rules="(modifying || backToSa) ? rules: {}">
      <el-form-item class="form-item" :class="{readonly: !modifying && !backToSa}" label="自评结果：" prop="autResult">
        <div style="display: flex; justify-content: space-between;">
          <div>
            <el-select v-if="!hideSubmit && (modifying || backToSa)" v-model="formData.autResult" clearable filterable placeholder="请选择自评结果" @change="resultChange">
              <el-option v-for="(option, index) in reviewResultOptions" :key="index" :value="option.dictValue" :label="option.dictLabel"></el-option>
            </el-select>
            <dict-span :noTag='true' v-else :key="checkSummaryOptions.length + Number(formData.autResult)" :value="formData.autResult" :options="reviewResultOptions" type="default"></dict-span>
          </div>
          <el-button v-show="!modifying && !backToSa && !readonly" type="text" @click="toggleModify">编辑</el-button>
        </div>
      </el-form-item>
      <el-form-item class="form-item" :class="{readonly: !modifying && !backToSa}" label="自评依据：" prop="autDesc" :rules="selfDescRules">
        <el-input v-if="!hideSubmit && (modifying || backToSa)" type="textarea" v-model="formData.autDesc" placeholder="请填写自评依据" :rows="4" :maxlength="selfMaxLength"></el-input>
        <span v-else>{{formData.autDesc || '--'}}</span>
      </el-form-item>
      <el-form-item class="form-item" :class="{readonly: !modifying && !backToSa, hideStart: formData.autResult != '6' && formData.autResult != '1' }" label="佐证材料：" prop="fileIds">
        <upload v-if="!hideSubmit && (modifying || backToSa)" class="evaluation-upload" :files="files" :data="{ type: '1' }" @fileAdd="fileAdd" @uploadSuccess="onSuccess" @fileRemove="onError"></upload>
        <div v-else-if="Array.isArray(files) && files.length" v-for="(file, index) in files" :key="index" style="margin-right: 15px">
          <span class="file-readonly" @click="showPreview(file, index)">
            {{ file.fileName || file.name || '--' }}
          </span>
          <el-image :ref="`file${index}`" style="display:none;" :src="file.url" :preview-src-list="[file.url]">
          </el-image>
        </div>
        <span v-else> -- </span>
      </el-form-item>
      <el-form-item v-if="!hideSubmit && (modifying || backToSa)" class="form-item" label=" ">
        <el-button type="primary" :disabled="submitLoading || uploading" @click="submit">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import Risk from '@/components/EvaluationForm/components/risk.vue'
import mixin from './mixin'

export default {
  name: 'EvaluationSelf',
  mixins: [mixin],
  components: {
    Risk
  },
  props: {
    backToSa: {
      type: Boolean,
      default: () => false
    },
    hideSubmit: {
      type: Boolean,
      default: false
    }
  },
  watch: {},
  data() {
    return {
      modifying: false,
      hasRisk: true,
      autDescValidate: true,
      uploading: false,
      myName: '自评',
      deleteFileIds: []
      // type: this.AutSaAudCurrentStatusEnum.SA_CLAUSE
    };
  },

  computed: {
    selfDescRules() {
      return [
        {
          required: this.isBasicStar,
          message: this.autDescMessage,
          trigger: ["change", 'blur'],
        }
      ]
    }
  },

  created() {
    this.autResultMessage = '请选择自评结果'
    this.autDescMessage = '请填写自评依据'
  },

  mounted() {
    if (this.AutSaAudCurrentStatusEnum.WAIT_CONFIRM_SA_SUM == this.status) {
      this.modifying = true;
    }

    if (this.status === this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_SUM_REJECT) {
      this.modifying = this.aut.rejected;
    }
    // console.log('我刷新啦');
  },

  methods: {
    handleModify() { },

    onSuccess(res, file) {
      this.uploading = false;
      file.fileId = res.data.fileId
      this.files.push(file);
      this.setFileIDs();
    },

    onError(file, length, index) {
      if (length == -1 && index == -1) {
        this.uploading = false;
        return;
      }
      this.deleteFileIds.push(file.fileId)
      if (this.files[index]) {
        this.files.splice(index, 1);
        this.setFileIDs();
      }
    },

    setFileIDs() {
      let ids = this.files.map((file) => file.fileId) || []
      let fileIds = ids.join(",")
      this.$set(this.formData, 'fileIds', fileIds)
      this.$refs.evaluationForm.validateField("fileIds");
    },

    submit() {
      const evaluationForm = this.$refs.evaluationForm;
      if (evaluationForm && evaluationForm.validate) {
        evaluationForm.validate(valid => {
          if (!valid) return;
          this.submitEmit()
        })
      }
    },

    fileAdd() {
      this.uploading = true;
    },

    clearDesc() {
      this.formData.autDesc = ''
    },

    componentCallBack() {
      let delIds = [...new Set(this.deleteFileIds)]
      if (delIds.length) {
        this.shareDelete(delIds).then(() => {
          this.deleteFileIds = []
        })
      }

      if (this.formData.fileIds) {
        let ids = this.formData.fileIds.split(',') || []
        if (ids.length) {
          this.shareCreate(ids, this.$store.getters.userId, 'hospital_review');
        }
      }
    },
    // 切换可编辑状态
    toggleModify() {
      this.modifying = true
    }
  }
}

</script>

<style lang='scss'>
.modifyItem {
  margin-bottom: 0;
}
.modifyBtn {
  float: right;
}

.file-readonly {
  text-decoration: underline;
  color: #649af6;
}
</style>
