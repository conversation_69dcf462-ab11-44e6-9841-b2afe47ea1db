<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.TraAccountExamMapper">

    <resultMap type="TraAccountExam" id="TraAccountExamResult">
        <result property="id" column="id"/>
        <result property="learnResourceId" column="learn_resource_id"/>
        <result property="examPaperId" column="exam_paper_id"/>
        <result property="account" column="account"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="selectTraAccountExamVo">
        select *
        from tra_account_exam
    </sql>

    <select id="selectTraAccountExamList" parameterType="TraAccountExam"
            resultType="com.thas.web.domain.TraAccountExam">
        SELECT tlr.id learnResourceId,
        tlr.title title,
        tep.id examPaperId,
        tep.paper_name paperName,
        tae.account account,
        tae.`status` status,
        tae.id id,
        (SELECT count(*) FROM tra_answer_sheet tas WHERE tas.exam_paper_id = tep.id and tas.create_id = #{account}) `count`
        FROM
        tra_learn_resource tlr
        LEFT JOIN tra_exam_paper tep ON tep.learn_resource_id = tlr.id AND tep.status = 1
        LEFT JOIN tra_account_exam tae ON tae.exam_paper_id = tep.id AND tae.account = #{account}
        <where>
            tlr.status = 1
            and tep.status = 1
            and tep.valid_flag = 1
            and tep.`status` = 1
            <if test="learnResourceId != null ">and tlr.learn_resource_id = #{learnResourceId}</if>
            <if test="examPaperId != null ">and tep.id = #{examPaperId}</if>
        </where>
    </select>

    <select id="selectTraAccountExamById" parameterType="Long" resultMap="TraAccountExamResult">
        <include refid="selectTraAccountExamVo"/>
        where id = #{id}
    </select>
    <select id="pass" resultType="java.lang.Integer">
        SELECT count(1)
        FROM (SELECT tlr.id         learnResourceId,
                     tlr.title      title,
                     tep.id         examPaperId,
                     tep.paper_name paperName,
                     tae.account    account,
                     tae.`status`   status,
                     (SELECT count(*)
                      FROM tra_answer_sheet tas
                      WHERE tas.exam_paper_id = tep.id
                        and tas.create_id = #{account}) count
              FROM
                  tra_learn_resource tlr
                  LEFT JOIN tra_exam_paper tep
              ON tep.learn_resource_id = tlr.id AND tep.status = 1
                  LEFT JOIN tra_account_exam tae ON tae.exam_paper_id = tep.id AND tae.account = #{account}
              WHERE tep.status = 1) tables
        where tables.status = 0
           or tables.status IS NULL
    </select>
    <select id="selectByAccount" resultType="com.thas.web.domain.TraAccountExam">
        select * from tra_account_exam where exam_paper_id = #{examPaperId} and account = #{account}
    </select>

    <insert id="insertTraAccountExam" parameterType="TraAccountExam">
        insert into tra_account_exam
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="learnResourceId != null">learn_resource_id,</if>
            <if test="examPaperId != null">exam_paper_id,</if>
            <if test="account != null and account != ''">account,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="learnResourceId != null">#{learnResourceId},</if>
            <if test="examPaperId != null">#{examPaperId},</if>
            <if test="account != null and account != ''">#{account},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <update id="updateTraAccountExam" parameterType="TraAccountExam">
        update tra_account_exam
        <trim prefix="SET" suffixOverrides=",">
            <if test="learnResourceId != null">learn_resource_id = #{learnResourceId},</if>
            <if test="examPaperId != null">exam_paper_id = #{examPaperId},</if>
            <if test="account != null and account != ''">account = #{account},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTraAccountExamById" parameterType="Long">
        delete
        from tra_account_exam
        where id = #{id}
    </delete>

    <delete id="deleteTraAccountExamByIds" parameterType="String">
        delete from tra_account_exam where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>