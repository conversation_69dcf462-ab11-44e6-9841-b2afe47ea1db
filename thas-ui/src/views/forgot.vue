<template>
  <div class="forgot">
    <h1 class="headTitle">深圳卫健医院评审评价研究中心</h1>
    <div class="register">
      <el-form ref="registerForm" :model="registerForm" :rules="registerRules" class="register-form">
        <h1 class="title">
          <p>找回密码</p>
          <span>发送验证码至您邮箱</span>
        </h1>

        <el-form-item prop="toEmailNo">
          <el-input v-model.trim="registerForm.toEmailNo" type="text" auto-complete="off" placeholder="请输入您的邮箱" :maxlength="50">
            <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
          </el-input>
        </el-form-item>
        <el-form-item prop="emailCode">
          <el-input v-model.trim="registerForm.emailCode" :maxlength="6" type="verification" style="width: 63%" auto-complete="off" placeholder="请输入6位邮箱验证码" @keyup.enter.native="handleNext">
            <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
          </el-input>
          <el-button style="display: inline-block; float: right; width: 33%" type="primary" :disabled="verification" @click="getEmailCode">{{ emailCodeName }}</el-button>
        </el-form-item>
        <el-form-item style="width: 100%">
          <el-button v-loading="loading" :disabled="loading" size="medium" type="primary" style="width: 100%" @click.native.prevent="verifyEmail">
            <span>下一步</span>
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { getCodeImg, register } from "@/api/login";
import request from "@/utils/request";
export default {
  name: "Register",
  data() {
    const emailVerification = (rule, value, callback) => {
      if (!value) callback('请输入6位邮箱验证码')
      if (this.regVerCode) {
        !new RegExp(this.regVerCode).test(value) && callback('请输入6位邮箱验证码')
      }
      callback();
    };
    const emailValidate = (rule, value, callback) => {
      if (!value) callback('请输入联系邮箱')
      if (this.regEmail) {
        !new RegExp(this.regEmail).test(value) && callback('请输入正确的联系邮箱')
      }
      callback();
    };
    return {
      codeUrl: "",
      registerForm: {
        toEmailNo: "",
        imgCode: "",
        emailCode: "",
      },
      registerRules: {
        toEmailNo: [
          { required: true, trigger: "blur", validator: emailValidate },
          // { pattern: /^\w+@[a-zA-Z0-9]+((\.[a-z0-9A-Z]{1,})+)$/, message: '请输入正确的联系邮箱' }
        ],
        imgCode: [
          { required: true, trigger: "change", message: "请输入验证码" },
        ],
        emailCode: [
          { required: true, trigger: "change", validator: emailVerification },
          // { pattern: /^\d{6}$/, message: '请输入正确的验证码' }
        ],
      },
      url: {
        emailCode: "system/user/bing/sendEmailCode",
        verifyEmailCode: "/system/user/bing/verifyEmailCode",
        updatePwd: "system/user/updatePwdByEmail",
      },
      loading: false,
      captchaOnOff: true,
      verification: false,
      loadingTime: 60,
      loadingTimeout: null,
      regEmail: '',
      regVerCode: '',
    };
  },
  computed: {
    emailCodeName() {
      return this.verification ? `${this.loadingTime}s` : "获取验证码";
    },
  },
  created() {
    this.getBingConfigKey("reg.email").then(response => {
      this.regEmail = response.msg.trim();
    });

    this.getBingConfigKey("reg.verCode").then(response => {
      this.regVerCode = response.msg.trim();
    });

    this.getCode();
  },
  methods: {
    // 获取图片验证码
    getCode() {
      getCodeImg().then((res) => {
        this.captchaOnOff =
          res.captchaOnOff === undefined ? true : res.captchaOnOff;
        if (this.captchaOnOff) {
          this.codeUrl = "data:image/gif;base64," + res.img;
          this.registerForm.uuid = res.uuid;
        }
      });
    },
    // 获取邮箱验证码
    async getEmailCode() {
      const valid = await this.validate(["toEmailNo"]);
      let next = false;
      if (valid && valid.length) {
        next = valid.reduce((a, b) => a && b, true);
      }
      const data = Object.assign({}, this.registerForm);
      next &&
        request({
          url: this.url.emailCode,
          method: "post",
          data: data,
        }).then((res) => {
          if (res.code === 200) {
            this.verification = true;
            this.loadingTimeout = setInterval(() => {
              if (this.loadingTime > 0) {
                this.loadingTime--;
              } else {
                this.verification = false;
                clearInterval(this.loadingTimeout);
              }
            }, 1000);
          }
        });
    },
    // 验证邮箱
    verifyEmail() {
      this.$refs.registerForm.validate((valid) => {
        if (valid) {
          const { emailCode, toEmailNo } = this.registerForm;
          request({
            url: this.url.verifyEmailCode,
            method: "post",
            data: {
              emailCode,
              toEmailNo,
            },
          }).then((res) => {
            res.code === 200 &&
              this.$router.push({
                name: "reset",
                params: this.registerForm,
              });
          });
        }
      });
    },

    handleNext() {
      this.$refs.registerForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          // this.$router.push({
          //   name: "reset",
          //   params: this.registerForm,
          // });
        }
      });
    },

    validate(fields) {
      const promises = [];
      for (let index = 0; index < fields.length; index++) {
        const element = fields[index];
        const promise = new Promise((resolve, reject) => {
          this.$refs.registerForm.validateField(element, (valid) => {
            resolve(!valid);
          });
        });
        promises.push(promise);
      }
      if (promises.length === 0) {
        return false;
      }
      return Promise.all(promises);
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.forgot {
  height: 100%;
}
.headTitle {
  background-color: #3d81f4;
  color: white;
  margin: 0;
  padding: 20px 0;
  font-size: 18px;
  font-weight: bold;
  text-indent: 100px;
  letter-spacing: 1px;
}
.register {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100% - 60px);
  // background-image: url("../assets/images/login-background.jpg");
  background-size: cover;
}
.title {
  margin: 0px auto 30px auto;
  color: #707070;
  p {
    font-weight: bold;
    margin-bottom: 0;
  }
  span {
    font-size: 16px;
  }
}

.register-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 0 25px 25px 25px;
  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.register-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.register-imgCode {
  width: 33%;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-register-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.register-imgCode-img {
  height: 38px;
}
</style>
