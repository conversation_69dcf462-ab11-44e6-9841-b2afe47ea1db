package com.thas.web.controller.common;

import com.alibaba.fastjson.JSON;
import com.thas.common.annotation.RepeatSubmit;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.page.TableDataInfo;
import com.thas.common.enums.FtlToPdfEnum;
import com.thas.common.validate.ValidGroup;
import com.thas.web.domain.*;
import com.thas.web.domain.vo.VerifyReviewReportVo;
import com.thas.web.service.IAutSaAudService;
import com.thas.web.service.impl.PdfGenerateServiceImpl;
import com.thas.web.service.process.BaseProcessService;
import com.thas.web.service.process.impl.CommonProcessServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 认证自评审核Controller
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
@RestController
@RequestMapping("/aut/sa/aud")
@Api(value = "AutSaAudController", tags = "认证自评审核Controller")
public class AutSaAudController extends BaseController {

    @Autowired
    private IAutSaAudService autSaAudService;

    @Resource
    private BaseProcessService baseProcessService;

    @Resource
    private CommonProcessServiceImpl commonProcessService;

    /**
     * 查询认证自评列表信息
     * 审核员端查询认证自评审核信息列表/评审员端查询认证自评审核信息列表/验证评审员端查询认证自评审核信息列表
     */
    @PostMapping(value = "/queryList")
    @ApiOperation("查询认证自评列表信息")
    public TableDataInfo queryAutSaAudList(@Validated(ValidGroup.Group1.class) @RequestBody AutSaAudQueryDTO autSaAudQueryDTO) {
        startPage();
        return getDataTable(baseProcessService.queryList(autSaAudQueryDTO));
    }

    /**
     * 查询认证自评详情
     * 医院端查询认证自评详情/审核员端查询认证自评审核信息详情/评审员端查询认证自评审核信息详情
     */
    @PostMapping(value = "/queryDetail")
    @ApiOperation("查询认证自评详情")
    public AjaxResult queryAutSaAudDetail(@Validated(ValidGroup.Group2.class) @RequestBody AutSaAudQueryDTO autSaAudQueryDTO) {
        return AjaxResult.success(baseProcessService.queryDetail(autSaAudQueryDTO));
    }

    /**
     * 提交认证自评审核信息
     * 医院端提交认证自评信息/审核员端提交认证自评审核信息/评审员端提交认证自评审核信息
     */
    @PostMapping(value = "/submitAutSaAud")
    @ApiOperation("提交认证自评审核信息")
    @RepeatSubmit
    public AjaxResult submitAutSaAud(@Valid @RequestBody AutSaAudSaveDTO autSaAudSaveDTO) {
        // 提交认证自评审核信息
        baseProcessService.process(autSaAudSaveDTO);
        return AjaxResult.success();
    }

    /**
     * 获取当前自评审核信息  -- 医院端审核进度
     */
    @GetMapping(value = "/selectCurrentStageSaAud")
    @ApiOperation("获取当前自评审核信息")
    public AjaxResult selectCurrentStageSaAud() {
        return AjaxResult.success(autSaAudService.selectCurrentStageSaAud());
    }

    /**
     * 获取医院的全部自评审核信息     -- 医院端历史评审记录
     */
    @GetMapping(value = "/selectAutSaAudDetailList")
    @ApiOperation("获取医院的全部自评审核信息")
    public AjaxResult selectAutSaAudDetailList(String autCode) {
        return AjaxResult.success(autSaAudService.selectAutSaAudDetailList(autCode));
    }

    /**
     * 查询组员修订信息
     */
    @GetMapping(value = "/memberRevisionInfo/{autCode}")
    @ApiOperation("查询组员修订信息")
    public AjaxResult selectMemberRevisionInfos(@PathVariable("autCode") String autCode) {
        return AjaxResult.success(autSaAudService.selectMemberRevisionInfos(autCode));
    }

    /**
     * 更新组员修订信息备注
     */
    @ApiOperation("更新组员修订信息备注")
    @PostMapping(value = "/memberRevisionInfo")
    public AjaxResult updateMemberRevisionInfo(@Valid @RequestBody MemberRevisionInfo memberRevisionInfo) {
        autSaAudService.updateMemberRevisionInfo(memberRevisionInfo);
        return AjaxResult.success();
    }

    /**
     * 获取预评审报告文件信息
     * 预评审报告预览，返回报告文件信息
     *
     */
    @ApiOperation("获取预评审报告文件信息")
    @GetMapping(value = "/getTemReviewReportFileInfo/{autCode}")
    public AjaxResult getTemReviewReportFileInfo(@ApiParam(value = "自评编码")@NotNull(message = "自评编码不能为空") @PathVariable(value = "autCode") String autCode
    ) {
        return AjaxResult.success(commonProcessService.getTemReviewReportFileInfo(autCode));
    }

//    @Autowired
//    private PdfGenerateServiceImpl pdfGenerateService;
//
//    @ApiOperation("验证审核报告测试接口")
//    @PostMapping(value = "/test")
//    public AjaxResult test(HttpServletResponse response) {
//
//        String data = "{\"autAdvantage\":\"瀹℃煡缁勯暱椹冲洖鍚庡啀娆℃彁浜?==璇勫\uE178鎶ュ憡浼樼偣锛\",\"autDesc\":\"瀹℃煡缁勯暱椹冲洖鍚庡啀娆℃彁浜?==璁よ瘉鎺堜簣寤鸿\uE185鐞嗙敱锛歕n鎶ュ憡鏁翠綋璇勫垎锛歫iaocha \\n璁よ瘉鎺堜簣寤鸿\uE185锛歜utongguorenzheng\",\"autEvaluate\":\"瀹℃煡缁勯暱椹冲洖鍚庡啀娆℃彁浜?==涓€鑸\uE101€ц瘎浠凤細\",\"autProposal\":\"瀹℃煡缁勯暱椹冲洖鍚庡啀娆℃彁浜?==淇\uE1BF\uE179璇勫\uE178鎶ュ憡寤鸿\uE185锛\",\"autResult\":\"5\",\"autSuggestion\":\"3\",\"firstAttachmentVoList\":[{\"clauseNo\":\"鍔熻兘1.1.1\",\"reviewAnswerVo\":{\"autAdvantage\":\"鍔熻兘1.1.1鏈夐獙璇佺枒闂\uE1BC\uE178鏌ョ粍闀块┏鍥烇紝鍐嶆\uE0BC鎻愬嚭楠岃瘉鐤戦棶,璇勫\uE178缁勯暱灏嗚揪鏍囨敼涓轰紭绉€\",\"autDesc\":\"\",\"autEvaluate\":\"\",\"autImprove\":\"\",\"autProposal\":\"\",\"reviewAnswerResult\":\"1\"},\"reviewScoreDesc\":{\"autAdvantage\":\"1212\",\"autDesc\":\"\",\"autEvaluate\":\"\",\"autImprove\":\"\",\"autProposal\":\"\"},\"reviewScoreResult\":\"1\",\"verifyReviewDoubtDesc\":\"鍔熻兘1.1.1鏈夐獙璇佺枒闂\uE1BC\uE178鏌ョ粍闀块┏鍥烇紝鍐嶆\uE0BC鎻愬嚭楠岃瘉鐤戦棶\"},{\"clauseNo\":\"鍔熻兘1.1.2\",\"reviewAnswerVo\":{\"autAdvantage\":\"\",\"autDesc\":\"鍔熻兘1.1.2鏈夐獙璇佺枒闂\uE1C6紝璇勫\uE178缁勯暱鎷掔粷淇\uE1BD敼\",\"autEvaluate\":\"\",\"autImprove\":\"\",\"autProposal\":\"\",\"reviewAnswerResult\":\"0\"},\"reviewScoreDesc\":{\"autAdvantage\":\"\",\"autDesc\":\"\",\"autEvaluate\":\"12\",\"autImprove\":\"12\",\"autProposal\":\"12\"},\"reviewScoreResult\":\"5\",\"verifyReviewDoubtDesc\":\"鍔熻兘1.1.2鏈夐獙璇佺枒闂\"},{\"clauseNo\":\"绠＄悊2.1.4\",\"reviewAnswerVo\":{\"autAdvantage\":\"\",\"autDesc\":\"鈽呯\uE178鐞?.1.4鏈夐獙璇佺枒闂\uE1C6紝璇勫\uE178缁勯暱鎷掔粷淇\uE1BD敼\",\"autEvaluate\":\"\",\"autImprove\":\"\",\"autProposal\":\"\",\"reviewAnswerResult\":\"0\"},\"reviewScoreDesc\":{\"autAdvantage\":\"-\"},\"reviewScoreResult\":\"1\",\"verifyReviewDoubtDesc\":\"鈽呯\uE178鐞?.1.4鏈夐獙璇佺枒闂\"}]}";
//        Map<String, Object> paramMap = JSON.parseObject(data, Map.class);
//        FtlToPdfDTO ftlToPdfDTO = new FtlToPdfDTO();
//        ftlToPdfDTO.setFtlTemplateCode(FtlToPdfEnum.VERIFY_REVIEW_REPORT.getCode());
//        Map<String, Object> otherParam = new HashMap<>();
//        otherParam.put("autCode", "test11111");
//        otherParam.put("pdfFileName", "测试验证报告名称.pdf");
//        ftlToPdfDTO.setOtherParam(otherParam);
//        ftlToPdfDTO.setFtlToPdfEnum(FtlToPdfEnum.VERIFY_REVIEW_REPORT);
//        //需response为空文件信息才会落表，测试报告名称时要置为空
//        pdfGenerateService.generatePdf(ftlToPdfDTO, response, paramMap, null);
//
//        return AjaxResult.success();
//    }

}
