<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.TrainingEvaluateResultMapper">

    <resultMap type="TrainingEvaluateResult" id="TrainingEvaluateResultResult">
        <result property="resultId" column="result_id"/>
        <result property="traineesAssessorId" column="trainees_assessor_id"/>
        <result property="courseStartTime" column="course_start_time"/>
        <result property="courseEndTime" column="course_end_time"/>
        <result property="courseFormat" column="course_format"/>
        <result property="trainerId" column="trainer_id"/>
        <result property="trainerName" column="trainer_name"/>
        <result property="conclusion" column="conclusion"/>
        <result property="validFlag" column="valid_flag"/>
        <result property="reviewResultType" column="review_result_type"/>
        <result property="fileId" column="file_id"/>
        <result property="creatorId" column="creator_id"/>
        <result property="createTime" column="create_time"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="otherRemark" column="other_remark"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectTrainingEvaluateResultVo">
        select result_id, trainees_assessor_id, course_start_time, course_end_time, course_format, trainer_id, trainer_name, conclusion, valid_flag, review_result_type,file_id, creator_id, create_time, modifier_id, modify_time, other_remark, remark from training_evaluate_result
    </sql>

    <select id="selectTrainingEvaluateResultList" parameterType="TrainingEvaluateResult"
            resultMap="TrainingEvaluateResultResult">
        <include refid="selectTrainingEvaluateResultVo"/>
        <where>
            <if test="traineesAssessorId != null ">and trainees_assessor_id = #{traineesAssessorId}</if>
            <if test="courseStartTime != null ">and course_start_time = #{courseStartTime}</if>
            <if test="courseEndTime != null ">and course_end_time = #{courseEndTime}</if>
            <if test="courseFormat != null ">and course_format = #{courseFormat}</if>
            <if test="trainerId != null ">and trainer_id = #{trainerId}</if>
            <if test="trainerName != null  and trainerName != ''">and trainer_name like concat('%', #{trainerName},
                '%')
            </if>
            <if test="conclusion != null ">and conclusion = #{conclusion}</if>
            <if test="validFlag != null ">and valid_flag = #{validFlag}</if>
            <if test="reviewResultType != null ">and review_result_type = #{reviewResultType}</if>
            <if test="creatorId != null ">and creator_id = #{creatorId}</if>
            <if test="modifierId != null ">and modifier_id = #{modifierId}</if>
            <if test="modifyTime != null ">and modify_time = #{modifyTime}</if>
        </where>
    </select>

    <select id="selectTrainingEvaluateResultByResultId" parameterType="Long" resultMap="TrainingEvaluateResultResult">
        <include refid="selectTrainingEvaluateResultVo"/>
        where result_id = #{resultId}
    </select>

    <select id="selectTrainingEvaluateResultListByConclusion" parameterType="String"
            resultMap="TrainingEvaluateResultResult">
        <include refid="selectTrainingEvaluateResultVo"/>
        where
        valid_flag = 1
        and
        conclusion in
        <foreach collection="conclusion.split(',')" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectTrainingEvaluateResultByTraineesAssessorIdList" parameterType="list"
            resultMap="TrainingEvaluateResultResult">
        <include refid="selectTrainingEvaluateResultVo"/>
        where
        valid_flag = 1
        and
        trainees_assessor_id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="selectByIdList" parameterType="list"
            resultMap="TrainingEvaluateResultResult">
        <include refid="selectTrainingEvaluateResultVo"/>
        where
        valid_flag = 1
        and
        trainees_assessor_id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertTrainingEvaluateResult" parameterType="TrainingEvaluateResult" useGeneratedKeys="true"
            keyProperty="resultId">
        insert into training_evaluate_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="traineesAssessorId != null">trainees_assessor_id,</if>
            <if test="courseStartTime != null">course_start_time,</if>
            <if test="courseEndTime != null">course_end_time,</if>
            <if test="courseFormat != null">course_format,</if>
            <if test="trainerId != null">trainer_id,</if>
            <if test="trainerName != null and trainerName != ''">trainer_name,</if>
            <if test="conclusion != null">conclusion,</if>
            <if test="validFlag != null">valid_flag,</if>
            <if test="reviewResultType != null">review_result_type,</if>
            <if test="fileId != null">file_id,</if>
            <if test="creatorId != null">creator_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="modifierId != null">modifier_id,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="otherRemark != null">other_remark,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="traineesAssessorId != null">#{traineesAssessorId},</if>
            <if test="courseStartTime != null">#{courseStartTime},</if>
            <if test="courseEndTime != null">#{courseEndTime},</if>
            <if test="courseFormat != null">#{courseFormat},</if>
            <if test="trainerId != null">#{trainerId},</if>
            <if test="trainerName != null and trainerName != ''">#{trainerName},</if>
            <if test="conclusion != null">#{conclusion},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="reviewResultType != null">#{reviewResultType},</if>
            <if test="fileId != null">#{fileId},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="modifierId != null">#{modifierId},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="otherRemark != null">#{otherRemark},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateTrainingEvaluateResult" parameterType="TrainingEvaluateResult">
        update training_evaluate_result
        <trim prefix="SET" suffixOverrides=",">
            <if test="traineesAssessorId != null">trainees_assessor_id = #{traineesAssessorId},</if>
            <if test="courseStartTime != null">course_start_time = #{courseStartTime},</if>
            <if test="courseEndTime != null">course_end_time = #{courseEndTime},</if>
            <if test="courseFormat != null">course_format = #{courseFormat},</if>
            <if test="trainerId != null">trainer_id = #{trainerId},</if>
            <if test="trainerName != null and trainerName != ''">trainer_name = #{trainerName},</if>
            <if test="conclusion != null">conclusion = #{conclusion},</if>
            <if test="validFlag != null">valid_flag = #{validFlag},</if>
            <if test="reviewResultType != null">review_result_type = #{reviewResultType},</if>
            <if test="fileId != null">file_id = #{fileId},</if>
            <if test="creatorId != null">creator_id = #{creatorId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="modifierId != null">modifier_id = #{modifierId},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="otherRemark != null">other_remark = #{otherRemark},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where result_id = #{resultId}
    </update>

    <update id="batchUpdateTrainingEvaluateResult" parameterType="java.util.List" >
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update training_evaluate_result
            <set>
                <if test="item.traineesAssessorId != null">trainees_assessor_id = #{item.traineesAssessorId},</if>
                <if test="item.courseStartTime != null">course_start_time = #{item.courseStartTime},</if>
                <if test="item.courseEndTime != null">course_end_time = #{item.courseEndTime},</if>
                <if test="item.courseFormat != null">course_format = #{item.courseFormat},</if>
                <if test="item.trainerId != null">trainer_id = #{item.trainerId},</if>
                <if test="item.trainerName != null and item.trainerName != ''">trainer_name = #{item.trainerName},</if>
                <if test="item.conclusion != null">conclusion = #{item.conclusion},</if>
                <if test="item.validFlag != null">valid_flag = #{item.validFlag},</if>
                <if test="item.reviewResultType != null">review_result_type = #{item.reviewResultType},</if>
                <if test="item.fileId != null">file_id = #{item.fileId},</if>
                <if test="item.creatorId != null">creator_id = #{item.creatorId},</if>
                <if test="item.createTime != null">create_time = #{item.createTime},</if>
                <if test="item.modifierId != null">modifier_id = #{item.modifierId},</if>
                <if test="item.modifyTime != null">modify_time = #{item.modifyTime},</if>
                <if test="item.otherRemark != null">other_remark = #{item.otherRemark},</if>
                <if test="item.remark != null">remark = #{item.remark},</if>
            </set>
            where result_id = #{item.resultId}
        </foreach>

    </update>

    <delete id="deleteTrainingEvaluateResultByResultId" parameterType="Long">
        delete from training_evaluate_result where result_id = #{resultId}
    </delete>

    <delete id="deleteTrainingEvaluateResultByResultIds" parameterType="String">
        delete from training_evaluate_result where result_id in
        <foreach item="resultId" collection="array" open="(" separator="," close=")">
            #{resultId}
        </foreach>
    </delete>
</mapper>