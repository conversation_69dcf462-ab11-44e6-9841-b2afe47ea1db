<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.ReviewerBaseInfoMapper">
    
    <resultMap type="com.thas.web.domain.ReviewerBaseInfo" id="ReviewerBaseInfoResult">
        <result property="id"    column="id"    />
        <result property="accountId"    column="account_id"    />
        <result property="reviewerName"    column="reviewer_name"    />
        <result property="reviewerGender"    column="reviewer_gender"    />
        <result property="certificateType"    column="certificate_type"    />
        <result property="certificateNumber"    column="certificate_number"    />
        <result property="reviewerBirthday"    column="reviewer_birthday"    />
        <result property="reviewerMobile"    column="reviewer_mobile"    />
        <result property="reviewerEmail"    column="reviewer_email"    />
        <result property="liveAddress"    column="live_address"    />
        <result property="company"    column="company"    />
        <result property="companyPost"    column="company_post"    />
        <result property="englishWrit"    column="english_writ"    />
        <result property="englishOral"    column="english_oral"    />
        <result property="workExperience"    column="work_experience"    />
        <result property="certificateList"    column="certificate_list"    />
        <result property="otherPost"    column="other_post"    />
        <result property="learnExperience"    column="learn_experience"    />
        <result property="reviewerIs"    column="reviewer_is"    />
        <result property="organization"    column="organization"    />
        <result property="reviewHospitalIs"    column="review_hospital_is"    />
        <result property="reviewHospitalYear"    column="review_hospital_year"    />
        <result property="reviewHospitalNum"    column="review_hospital_num"    />
        <result property="reviewMajor"    column="review_major"    />
        <result property="reviewHospitalCoach"    column="review_hospital_coach"    />
        <result property="coachHospitalNum"    column="coach_hospital_num"    />
        <result property="trainReviewerIs"    column="train_reviewer_is"    />
        <result property="trainNum"    column="train_num"    />
        <result property="trainOrganization"    column="train_organization"    />
        <result property="otherExperience"    column="other_experience"    />
        <result property="appSkip"    column="app_skip"    />
        <result property="appSkipDesc"    column="app_skip_desc"    />
        <result property="skipFlag"    column="skip_flag"    />
        <result property="submitStatus"    column="submit_status"    />
        <result property="infoStatus" column="info_status"/>
        <result property="authStatus" column="auth_status"/>
        <result property="authDesc" column="auth_desc"/>
        <result property="authPersonId" column="auth_person_id"/>
        <result property="authPersonName" column="auth_person_name"/>
        <result property="authDate" column="auth_date"/>
        <result property="status"    column="status"    />
        <result property="majorField"    column="major_field"    />
        <result property="majorDirection"    column="major_direction"    />
        <result property="trainWarning"    column="train_warning"    />
        <result property="hosRevIs"    column="hos_rev_is"    />
        <result property="hosRevDetail"    column="hos_rev_detail"    />
        <result property="hosRevOther"    column="hos_rev_other"    />
        <result property="firstBatch"    column="first_batch"    />
        <result property="headPortrait"    column="head_portrait"    />
        <result property="profile"    column="profile"    />
        <result property="creator"    column="creator"    />
        <result property="createTime"    column="create_time"    />
        <result property="updater"    column="updater"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="SQL_AES_ENCRYPT">'${@com.thas.web.config.SqlAesEncryptConfig@SQL_AES_ENCRYPT}'</sql>

    <sql id="SELECT_ENCRYPT_FIELD">
        AES_DECRYPT(unhex(reviewer_name),<include refid="SQL_AES_ENCRYPT"/>) reviewer_name,
        AES_DECRYPT(unhex(certificate_number),<include refid="SQL_AES_ENCRYPT"/>) certificate_number,
        AES_DECRYPT(unhex(reviewer_mobile),<include refid="SQL_AES_ENCRYPT"/>) reviewer_mobile,
        AES_DECRYPT(unhex(reviewer_email),<include refid="SQL_AES_ENCRYPT"/>) reviewer_email,
        AES_DECRYPT(unhex(live_address),<include refid="SQL_AES_ENCRYPT"/>) live_address,
        AES_DECRYPT(unhex(auth_person_name),<include refid="SQL_AES_ENCRYPT"/>) auth_person_name
    </sql>

    <sql id="selectReviewerBaseInfoVo">
        select id,
               account_id,
               reviewer_gender,
               certificate_type,
               reviewer_birthday,
               company,
               company_post,
               english_writ,
               english_oral,
               work_experience,
               certificate_list,
               other_post,
               learn_experience,
               reviewer_is,
               organization,
               review_hospital_is,
               review_hospital_year,
               review_hospital_num,
               review_major,
               review_hospital_coach,
               coach_hospital_num,
               train_reviewer_is,
               train_num,
               train_organization,
               other_experience,
               app_skip,
               app_skip_desc,
               skip_flag,
               submit_status,
               info_status,
               auth_status,
               auth_desc,
               auth_person_id,
               auth_date,
               status,
               major_field,
               major_direction,
               train_warning,
               hos_rev_is,
               hos_rev_detail,
               hos_rev_other,
               first_batch,
               head_portrait,
               profile,
               creator,
               create_time,
               updater,
               update_time,
               <include refid="SELECT_ENCRYPT_FIELD"/>
        from reviewer_base_info
    </sql>

    <select id="selectReviewerBaseInfoList" parameterType="ReviewerBaseInfo" resultMap="ReviewerBaseInfoResult">
        select * from (
        <include refid="selectReviewerBaseInfoVo"/>
        <where>
            <if test="accountId != null  and accountId != ''"> and account_id = #{accountId}</if>
            <if test="reviewerGender != null "> and reviewer_gender = #{reviewerGender}</if>
            <if test="certificateType != null "> and certificate_type = #{certificateType}</if>
            <if test="reviewerBirthday != null  and reviewerBirthday != ''"> and reviewer_birthday = #{reviewerBirthday}</if>
            <if test="company != null  and company != ''"> and company = #{company}</if>
            <if test="companyPost != null  and companyPost != ''"> and company_post = #{companyPost}</if>
            <if test="englishWrit != null "> and english_writ = #{englishWrit}</if>
            <if test="englishOral != null "> and english_oral = #{englishOral}</if>
            <if test="workExperience != null  and workExperience != ''"> and work_experience = #{workExperience}</if>
            <if test="certificateList != null  and certificateList != ''"> and certificate_list = #{certificateList}</if>
            <if test="otherPost != null  and otherPost != ''"> and other_post = #{otherPost}</if>
            <if test="learnExperience != null "> and learn_experience = #{learnExperience}</if>
            <if test="reviewerIs != null "> and reviewer_is = #{reviewerIs}</if>
            <if test="organization != null  and organization != ''"> and organization = #{organization}</if>
            <if test="reviewHospitalIs != null "> and review_hospital_is = #{reviewHospitalIs}</if>
            <if test="reviewHospitalYear != null  and reviewHospitalYear != ''"> and review_hospital_year = #{reviewHospitalYear}</if>
            <if test="reviewHospitalNum != null  and reviewHospitalNum != ''"> and review_hospital_num = #{reviewHospitalNum}</if>
            <if test="reviewMajor != null  and reviewMajor != ''"> and review_major = #{reviewMajor}</if>
            <if test="reviewHospitalCoach != null "> and review_hospital_coach = #{reviewHospitalCoach}</if>
            <if test="coachHospitalNum != null  and coachHospitalNum != ''"> and coach_hospital_num = #{coachHospitalNum}</if>
            <if test="trainReviewerIs != null "> and train_reviewer_is = #{trainReviewerIs}</if>
            <if test="trainNum != null  and trainNum != ''"> and train_num = #{trainNum}</if>
            <if test="trainOrganization != null  and trainOrganization != ''"> and train_organization = #{trainOrganization}</if>
            <if test="otherExperience != null  and otherExperience != ''"> and other_experience = #{otherExperience}</if>
            <if test="appSkip != null"> and app_skip = #{appSkip}</if>
            <if test="appSkipDesc != null and appSkipDesc != ''"> and app_skip_desc = #{appSkipDesc}</if>
            <if test="skipFlag != null"> and skip_flag = #{skipFlag}</if>
            <if test="submitStatus != null "> and submit_status = #{submitStatus}</if>
            <if test="authPersonId != null and authPersonId != ''" > and auth_person_id = #{authPersonId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="creator != null  and creator != ''"> and creator = #{creator}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
        </where>
        ) aa
        where
        1=1
        <if test="reviewerName != null  and reviewerName != ''"> and reviewer_name like concat('%', #{reviewerName}, '%')</if>
        <if test="certificateNumber != null  and certificateNumber != ''"> and certificate_number = #{certificateNumber}</if>
        <if test="reviewerMobile != null  and reviewerMobile != ''"> and reviewer_mobile = #{reviewerMobile}</if>
        <if test="reviewerEmail != null  and reviewerEmail != ''"> and reviewer_email = #{reviewerEmail}</if>
        <if test="liveAddress != null  and liveAddress != ''"> and live_address = #{liveAddress}</if>
        <if test="authPersonName != null and authPersonName != ''"> and auth_person_name = #{authPersonName}</if>
    </select>

    <select id="selectReviewerBaseInfoByAccountIdList" parameterType="list" resultMap="ReviewerBaseInfoResult">
        <include refid="selectReviewerBaseInfoVo"/>
        where
        <if test="authStatus != null "> auth_status = #{authStatus} and </if>
        account_id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectReviewerBaseInfos" parameterType="com.thas.web.dto.QueryReviewerListDTO" resultType="com.thas.web.dto.QueryReviewerListDTO">
        select * from (
        SELECT
            rbi.account_id accountId,
            AES_DECRYPT(unhex(reviewer_name),<include refid="SQL_AES_ENCRYPT"/>) reviewerName,
            AES_DECRYPT(unhex(reviewer_mobile),<include refid="SQL_AES_ENCRYPT"/>) reviewerMobile,
            AES_DECRYPT(unhex(live_address),<include refid="SQL_AES_ENCRYPT"/>) liveAddress,
            AES_DECRYPT(unhex(auth_person_name),<include refid="SQL_AES_ENCRYPT"/>) authPersonName,
            rbi.company company,
            rbi.company_post companyPost,
            rbi.app_skip appSkip,
            rbi.app_skip_desc appSkipDesc,
            rbi.skip_flag skipFlag,
            rbi.info_status infoStatus,
            rbi.major_field majorField,
            rbi.auth_status authStatus,
            rbi.submit_status submitStatus,
            rbi.first_batch firstBatch,
            sur.role_id roleId,
            sr.role_name roleName,
            su.user_name userName
        FROM
            reviewer_base_info rbi
                LEFT JOIN sys_user_role sur ON rbi.account_id = sur.user_id
                LEFT JOIN sys_role sr ON sur.role_id = sr.role_id
                LEFT JOIN sys_user su ON rbi.account_id = su.user_id
        <where>
            (rbi.submit_status = 1 or rbi.submit_status = 3) and rbi.status = 1
            <if test="company != null  and company != ''"> and rbi.company = #{company}</if>
            <if test="companyPost != null  and companyPost != ''"> and rbi.company_post = #{companyPost}</if>
            <if test="authStatus != null"> and rbi.auth_status = #{authStatus}</if>
            <if test="roleName != null"> and sr.role_name = #{roleName}</if>
        </where>
        ORDER BY rbi.create_time DESC
        ) aa
        where
        1=1
        <if test="reviewerName != null  and reviewerName != ''"> and reviewerName like concat('%', #{reviewerName}, '%')</if>
        <if test="reviewerMobile != null  and reviewerMobile != ''"> and reviewerMobile = #{reviewerMobile}</if>

    </select>

    <select id="selectReviewerBaseInfoByCertificateNumber" parameterType="String" resultMap="ReviewerBaseInfoResult">
        <include refid="selectReviewerBaseInfoVo"/>
        where certificate_number =  HEX(AES_ENCRYPT(#{certificateNumber},<include refid="SQL_AES_ENCRYPT"/>))
            and (submit_status = 1 or submit_status = 3)
            and status = 1
    </select>

    <select id="selectReviewerBaseInfoByAccountId" parameterType="String" resultMap="ReviewerBaseInfoResult">
        <include refid="selectReviewerBaseInfoVo"/>
        where account_id = #{accountId} and status = 1;
    </select>

    <select id="queryReviewerTmpList" parameterType="ReviewerBaseInfo"
            resultType="com.thas.web.domain.ReviewerBaseInfo">
        select * from (
        SELECT
        rta.account_id accountId,
        AES_DECRYPT(unhex(rbi.reviewer_name),<include refid="SQL_AES_ENCRYPT"/>) reviewerName,
        AES_DECRYPT(unhex(rbi.reviewer_mobile),<include refid="SQL_AES_ENCRYPT"/>) reviewerMobile,
        AES_DECRYPT(unhex(rbi.reviewer_Email),<include refid="SQL_AES_ENCRYPT"/>) reviewerEmail,
        AES_DECRYPT(unhex(rbi.certificate_number),<include refid="SQL_AES_ENCRYPT"/>) certificateNumber,
        rbi.reviewer_gender reviewerGender
        FROM
        reviewer_tmp_acc rta
        LEFT JOIN reviewer_base_info rbi ON rbi.account_id = rta.account_id
        <where>
            rbi.STATUS = 1 and rbi.info_status = 1
        </where>
        ) aa
        where
        1=1
        <if test="reviewerName != null  and reviewerName != ''">and reviewerName like concat('%',
            #{reviewerName}, '%')
        </if>

    </select>

    <select id="selectReviewerBaseInfoById" parameterType="Long" resultMap="ReviewerBaseInfoResult">
        <include refid="selectReviewerBaseInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectCountByAccountId" parameterType="String" resultType="Integer">
        select count(*) from reviewer_base_info where account_id = #{account_id} and status = 1
    </select>

    <select id="selectCountByCertificateNumber" parameterType="String" resultType="Integer">
        select count(*) from reviewer_base_info where certificate_number = HEX(AES_ENCRYPT(#{certificateNumber},<include refid="SQL_AES_ENCRYPT"/>))
    </select>

    <select id="selectByCertificateNumber" parameterType="String" resultMap="ReviewerBaseInfoResult">
        <include refid="selectReviewerBaseInfoVo"/>
        where certificate_number = HEX(AES_ENCRYPT(#{certificateNumber},<include refid="SQL_AES_ENCRYPT"/>))
    </select>

    <update id="updateAccountId" parameterType="String">
        update reviewer_base_info set account_id = #{userId}, submit_status = 1 where account_id = #{accountId} and status = 1
    </update>


    <select id="selectTraBaseInfoListByApplyNo" parameterType="String" resultType="com.thas.web.dto.SysUserBaseInfo">
        SELECT
            su.user_id `accountId`,
            su.nick_name `name`
        FROM
            hospital_reviewer hr
                LEFT JOIN sys_user su ON hr.reviewer_id = su.user_id
                LEFT JOIN sys_user_role sur ON su.user_id = sur.user_id
                LEFT JOIN sys_role sr ON sur.role_id = sr.role_id
        WHERE
            hr.applyNo = #{applyNo}
          AND sr.role_key = 'trainees_assessor'
          AND hr.status = 1
    </select>

    <select id="qryReviewerBaseInfoByEmail" resultMap="ReviewerBaseInfoResult">
        <include refid="selectReviewerBaseInfoVo"/>
        where reviewer_email = HEX(AES_ENCRYPT(#{email},<include refid="SQL_AES_ENCRYPT"/>)) and status = 1
    </select>

    <insert id="insertReviewerBaseInfo" parameterType="ReviewerBaseInfo" useGeneratedKeys="true" keyProperty="id">
        insert into reviewer_base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountId != null and accountId != ''">account_id,</if>
            <if test="reviewerName != null and reviewerName != ''">reviewer_name,</if>
            <if test="reviewerGender != null">reviewer_gender,</if>
            <if test="certificateType != null">certificate_type,</if>
            <if test="certificateNumber != null and certificateNumber != ''">certificate_number,</if>
            <if test="reviewerBirthday != null and reviewerBirthday != ''">reviewer_birthday,</if>
            <if test="reviewerMobile != null and reviewerMobile != ''">reviewer_mobile,</if>
            <if test="reviewerEmail != null and reviewerEmail != ''">reviewer_email,</if>
            <if test="liveAddress != null and liveAddress != ''">live_address,</if>
            <if test="company != null and company != ''">company,</if>
            <if test="companyPost != null and companyPost != ''">company_post,</if>
            <if test="englishWrit != null">english_writ,</if>
            <if test="englishOral != null">english_oral,</if>
            <if test="workExperience != null">work_experience,</if>
            <if test="certificateList != null">certificate_list,</if>
            <if test="otherPost != null and otherPost != ''">other_post,</if>
            <if test="learnExperience != null">learn_experience,</if>
            <if test="reviewerIs != null">reviewer_is,</if>
            <if test="organization != null and organization != ''">organization,</if>
            <if test="reviewHospitalIs != null">review_hospital_is,</if>
            <if test="reviewHospitalYear != null and reviewHospitalYear != ''">review_hospital_year,</if>
            <if test="reviewHospitalNum != null and reviewHospitalNum != ''">review_hospital_num,</if>
            <if test="reviewMajor != null and reviewMajor != ''">review_major,</if>
            <if test="reviewHospitalCoach != null">review_hospital_coach,</if>
            <if test="coachHospitalNum != null and coachHospitalNum != ''">coach_hospital_num,</if>
            <if test="trainReviewerIs != null">train_reviewer_is,</if>
            <if test="trainNum != null and trainNum != ''">train_num,</if>
            <if test="trainOrganization != null and trainOrganization != ''">train_organization,</if>
            <if test="otherExperience != null and otherExperience != ''">other_experience,</if>
            <if test="appSkip != null">app_skip,</if>
            <if test="appSkipDesc != null and appSkipDesc != ''">app_skip_desc,</if>
            <if test="skipFlag != null">skip_flag,</if>
            <if test="status != null">status,</if>
            <if test="submitStatus != null">submit_status,</if>
            <if test="infoStatus != null">info_status,</if>
            <if test="authStatus != null">auth_status,</if>
            <if test="authDesc != null and authDesc != ''">auth_Desc,</if>
            <if test="authPersonId != null and authPersonId != ''">auth_person_id,</if>
            <if test="authPersonName != null and authPersonName != ''">auth_person_name,</if>
            <if test="authDate != null and authDate != ''">auth_date,</if>
            <if test="majorField != null and majorField != ''">major_field,</if>
            <if test="majorDirection != null and majorDirection != ''">major_direction,</if>
            <if test="trainWarning != null and trainWarning != ''">train_warning,</if>
            <if test="hosRevIs != null != ''">hos_rev_is,</if>
            <if test="hosRevDetail != null and hosRevDetail != ''">hos_rev_detail,</if>
            <if test="hosRevOther != null and hosRevOther != ''">hos_rev_other,</if>
            <if test="firstBatch != null">first_batch,</if>
            <if test="headPortrait != null">head_portrait,</if>
            <if test="profile != null and profile != ''">profile,</if>
            <if test="creator != null and creator != ''">creator,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updater != null and updater != ''">updater,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountId != null and accountId != ''">#{accountId},</if>
            <if test="reviewerName != null and reviewerName != ''">HEX(AES_ENCRYPT(#{reviewerName},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="reviewerGender != null">#{reviewerGender},</if>
            <if test="certificateType != null">#{certificateType},</if>
            <if test="certificateNumber != null and certificateNumber != ''">HEX(AES_ENCRYPT(#{certificateNumber},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="reviewerBirthday != null and reviewerBirthday != ''">#{reviewerBirthday},</if>
            <if test="reviewerMobile != null and reviewerMobile != ''">HEX(AES_ENCRYPT(#{reviewerMobile},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="reviewerEmail != null and reviewerEmail != ''">HEX(AES_ENCRYPT(#{reviewerEmail},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="liveAddress != null and liveAddress != ''">HEX(AES_ENCRYPT(#{liveAddress},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="company != null and company != ''">#{company},</if>
            <if test="companyPost != null and companyPost != ''">#{companyPost},</if>
            <if test="englishWrit != null">#{englishWrit},</if>
            <if test="englishOral != null">#{englishOral},</if>
            <if test="workExperience != null">#{workExperience},</if>
            <if test="certificateList != null">#{certificateList},</if>
            <if test="otherPost != null and otherPost != ''">#{otherPost},</if>
            <if test="learnExperience != null">#{learnExperience},</if>
            <if test="reviewerIs != null">#{reviewerIs},</if>
            <if test="organization != null and organization != ''">#{organization},</if>
            <if test="reviewHospitalIs != null">#{reviewHospitalIs},</if>
            <if test="reviewHospitalYear != null and reviewHospitalYear != ''">#{reviewHospitalYear},</if>
            <if test="reviewHospitalNum != null and reviewHospitalNum != ''">#{reviewHospitalNum},</if>
            <if test="reviewMajor != null and reviewMajor != ''">#{reviewMajor},</if>
            <if test="reviewHospitalCoach != null">#{reviewHospitalCoach},</if>
            <if test="coachHospitalNum != null and coachHospitalNum != ''">#{coachHospitalNum},</if>
            <if test="trainReviewerIs != null">#{trainReviewerIs},</if>
            <if test="trainNum != null and trainNum != ''">#{trainNum},</if>
            <if test="trainOrganization != null and trainOrganization != ''">#{trainOrganization},</if>
            <if test="otherExperience != null and otherExperience != ''">#{otherExperience},</if>
            <if test="appSkip != null">#{appSkip},</if>
            <if test="appSkipDesc != null and appSkipDesc != ''">#{appSkipDesc},</if>
            <if test="skipFlag != null">#{skipFlag},</if>
            <if test="status != null">#{status},</if>
            <if test="submitStatus != null">#{submitStatus},</if>
            <if test="infoStatus != null">#{infoStatus},</if>
            <if test="authStatus != null">#{authStatus},</if>
            <if test="authDesc != null and authDesc != ''">#{authDesc},</if>
            <if test="authPersonId != null and authPersonId != ''">#{authPersonId},</if>
            <if test="authPersonName != null and authPersonName != ''">HEX(AES_ENCRYPT(#{authPersonName},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="authDate != null and authDate != ''">#{authDate},</if>
            <if test="majorField != null and majorField != ''">#{majorField},</if>
            <if test="majorDirection != null and majorDirection != ''">#{majorDirection},</if>
            <if test="trainWarning != null and trainWarning != ''">#{trainWarning},</if>
            <if test="hosRevIs != null != ''">#{hosRevIs},</if>
            <if test="hosRevDetail != null and hosRevDetail != ''">#{hosRevDetail},</if>
            <if test="hosRevOther != null and hosRevOther != ''">#{hosRevOther},</if>
            <if test="firstBatch != null">#{firstBatch},</if>
            <if test="headPortrait != null">#{headPortrait},</if>
            <if test="profile != null and profile != ''">#{profile},</if>
            <if test="creator != null and creator != ''">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updater != null and updater != ''">#{updater},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateReviewerBaseInfoByAccountId" parameterType="ReviewerBaseInfo">
        update reviewer_base_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountId != null and accountId != ''">account_id = #{accountId},</if>

            <if test="reviewerName != null and reviewerName != ''">reviewer_name = HEX(AES_ENCRYPT(#{reviewerName},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="certificateNumber != null and certificateNumber != ''">certificate_number = HEX(AES_ENCRYPT(#{certificateNumber},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="reviewerMobile != null and reviewerMobile != ''">reviewer_mobile = HEX(AES_ENCRYPT(#{reviewerMobile},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="reviewerEmail != null and reviewerEmail != ''">reviewer_email = HEX(AES_ENCRYPT(#{reviewerEmail},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="liveAddress != null and liveAddress != ''">live_address = HEX(AES_ENCRYPT(#{liveAddress},<include refid="SQL_AES_ENCRYPT"/>)),</if>
            <if test="authPersonName != null and authPersonName != ''">auth_person_name = HEX(AES_ENCRYPT(#{authPersonName},<include refid="SQL_AES_ENCRYPT"/>)),</if>

            <if test="reviewerGender != null">reviewer_gender = #{reviewerGender},</if>
            <if test="certificateType != null">certificate_type = #{certificateType},</if>
            <if test="reviewerBirthday != null and reviewerBirthday != ''">reviewer_birthday = #{reviewerBirthday},</if>
            <if test="company != null and company != ''">company = #{company},</if>
            <if test="companyPost != null and companyPost != ''">company_post = #{companyPost},</if>
            <if test="englishWrit != null">english_writ = #{englishWrit},</if>
            <if test="englishOral != null">english_oral = #{englishOral},</if>
            <if test="workExperience != null">work_experience = #{workExperience},</if>
            <if test="certificateList != null">certificate_list = #{certificateList},</if>
            <if test="otherPost != null and otherPost != ''">other_post = #{otherPost},</if>
            <if test="learnExperience != null">learn_experience = #{learnExperience},</if>
            <if test="reviewerIs != null">reviewer_is = #{reviewerIs},</if>
            organization = #{organization},
            <if test="reviewHospitalIs != null">review_hospital_is = #{reviewHospitalIs},</if>
            review_hospital_year = #{reviewHospitalYear},
            review_hospital_num = #{reviewHospitalNum},
            review_major = #{reviewMajor},
            <if test="reviewHospitalCoach != null">review_hospital_coach = #{reviewHospitalCoach},</if>
            coach_hospital_num = #{coachHospitalNum},
            <if test="trainReviewerIs != null">train_reviewer_is = #{trainReviewerIs},</if>
            train_num = #{trainNum},
            train_organization = #{trainOrganization},
            other_experience = #{otherExperience},
            <if test="appSkip != null">app_skip = #{appSkip},</if>
            <if test="appSkipDesc != null and appSkipDesc != ''">app_skip_desc = #{appSkipDesc},</if>
            <if test="skipFlag != null">skip_flag = #{skipFlag},</if>
            <if test="submitStatus != null">submit_status = #{submitStatus},</if>
            <if test="infoStatus != null">info_status = #{infoStatus},</if>
            <if test="authStatus != null">auth_status = #{authStatus},</if>
            <if test="authDesc != null and authDesc != ''">auth_desc = #{authDesc},</if>
            <if test="authPersonId != null and authPersonId != ''">auth_person_id = #{authPersonId},</if>
            <if test="authDate != null and authDate != ''">auth_date = #{authDate},</if>
            major_field = #{majorField},
            major_direction = #{majorDirection},
            train_warning = #{trainWarning},
            <if test="hosRevIs != null != ''">hos_rev_is = #{hosRevIs},</if>
            hos_rev_detail = #{hosRevDetail},
            <if test="hosRevOther != null and hosRevOther != ''">hos_rev_other = #{hosRevOther},</if>
            <if test="firstBatch != null">first_batch = #{firstBatch},</if>
            <if test="headPortrait != null">head_portrait = #{headPortrait},</if>
            <if test="profile != null and profile != ''">profile = #{profile},</if>
            <if test="status != null">status = #{status},</if>
            <if test="creator != null and creator != ''">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where account_id = #{accountId}
    </update>

    <!--<update id="updateSubmitStatusByAccountId" parameterType="String">
        update reviewer_base_info set submit_status = 1 where account_id = #{accountId}
    </update>-->

    <delete id="deleteReviewerBaseInfoById" parameterType="Long">
        delete from reviewer_base_info where id = #{id}
    </delete>

    <delete id="deleteReviewerBaseInfoByIds" parameterType="String">
        delete from reviewer_base_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>