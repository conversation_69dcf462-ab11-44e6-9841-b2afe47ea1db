package com.thas.web.dto;

import com.thas.web.domain.HospitalAuthContact;
import com.thas.web.domain.vo.DomainGroupNode;
import com.thas.web.domain.vo.FileInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 医疗分配分配详情
 */
@Data
@ApiModel(value = "HosPlanDetailVO")
public class HosPlanDetailVO {

    /** 被授权人信息 */
    @ApiModelProperty(value = "被授权人信息")
    private HospitalAuthContact hospitalAuthContact;

    /** 已分配列表 */
    @ApiModelProperty(value = "已分配列表和对应条款数量")
    private List<HosReviewClauseNumInfoVO> hosReviewClauseNumInfoVOList;

    // 如果是初审员则查询对应的初审员列表给前端
    @ApiModelProperty(value = "角色为初审员的列表 {'accountId':'', 'name':''}")
    List<SysUserBaseInfo> preExamList;

    // 如果初审员未分配完成需要展示未分配的组
    @ApiModelProperty(value = "未被分配的领域分组列表")
    List<DomainGroupNode> unDomainGroupNodeList;

    /**
     * 分组详情
     */
    List<UnDomainListVO> allDomainListVOList;

    // 如果是审查员查询对应未被分配的领域以及领域对应的评审员
    @ApiModelProperty(value = "未被分配的领域列表")
    List<UnDomainListVO> unDomainListVOList;

    @ApiModelProperty(value = "角色为资深评审员的列表")
    List<SysUserBaseInfo> seniorAssessorList;

    @ApiModelProperty(value = "角色为学员的列表")
    List<SysUserBaseInfo> traineesList;

    /**
     * 对应评审计划分配详情，如果评审员已经将所有的利益冲突申报表提交
     * 则返回对应的详情。
     */
    private Map<String, List<FileInfoVO>> fileDetailMap;

    /**
     * 当为4的时候则说明利益冲突列表已经全部提交 fileDetailMap 则有对应的数据
     */
    private Integer reviewDisComplete;

    /**
     * 医疗结构审核评审计划PDF文件id(多次评审可以使用'',''隔开) hospital_planned_distribution
     */
    private String revFileId;
}

