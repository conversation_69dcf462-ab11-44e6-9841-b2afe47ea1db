package com.thas.web.service.impl;


import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.thas.common.constant.Constants;
import com.thas.common.core.domain.entity.SysUser;
import com.thas.common.core.domain.model.LoginUser;
import com.thas.common.enums.AutSaAudCycleEnum;
import com.thas.common.enums.AutSaAudRoleEnum;
import com.thas.common.enums.ServiceExceptionEnum;
import com.thas.common.enums.TraQuestionnaireFeedBackTypeEnum;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.SecurityUtils;
import com.thas.common.utils.StringUtils;
import com.thas.system.domain.SysUserHospital;
import com.thas.system.domain.vo.ReviewerInfoVo;
import com.thas.system.domain.vo.TraineesAssessorListVo;
import com.thas.system.mapper.SysUserHospitalMapper;
import com.thas.system.mapper.SysUserMapper;
import com.thas.system.service.ISysRoleService;
import com.thas.system.service.ISysUserService;
import com.thas.web.domain.*;
import com.thas.web.domain.dto.TraQuestionnaireFeedBackDTO;
import com.thas.web.dto.HosPlanUserInfoVO;
import com.thas.web.dto.QueryBaseConditionDTO;
import com.thas.web.mapper.*;
import com.thas.web.service.IAutSaRelationService;
import com.thas.web.service.IHospitalReviewerService;
import com.thas.web.service.ITraAnswerSheetService;
import com.thas.web.service.ITraQuestionnaireFeedBackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 反馈问卷Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-10-26
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TraQuestionnaireFeedBackServiceImpl implements ITraQuestionnaireFeedBackService {
    @Autowired
    private TraQuestionnaireFeedBackMapper traQuestionnaireFeedBackMapper;
    @Autowired
    private ITraAnswerSheetService traAnswerSheetService;
    @Autowired
    private CstOfflineTrainingManagementMapper cstOfflineTrainingManagementMapper;
    @Autowired
    private HospitalReviewerMapper hospitalReviewerMapper;
    @Autowired
    private AutSaRelationMapper autSaRelationMapper;
    @Autowired
    private HospitalBaseInfoMapper hospitalBaseInfoMapper;
    @Autowired
    private SysUserHospitalMapper sysUserHospitalMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private HospitalReviewCycleMapper hospitalReviewCycleMapper;
    @Autowired
    private IAutSaRelationService autSaRelationService;
    @Autowired
    private IHospitalReviewerService iHospitalReviewerService;
    @Autowired
    private TraAccountExamMapper traAccountExamMapper;
    @Autowired
    private TraQuestionnaireFeedBackRecordMapper traQuestionnaireFeedBackRecordMapper;
    @Autowired
    private TrainingEvaluateResultMapper trainingEvaluateResultMapper;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private ISysRoleService sysRoleService;

    @Override
    public TraineesAssessorListVo selectTraQuestionnaireFeedBackList(Long questionnaireId, Long feedBackType) {
        // 获取当前登录用户名称
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();

        //附件3：理论培训节点后发放，需要卡流程（需要返回培训教员信息）
        //现场带教培训：附件3：对实践培训的反馈，当次一起和评审员观摩学习的带教培训结束后发放（需要返回培训教员信息）
        if (TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_5.getCode().equals(String.valueOf(feedBackType)) ||
                TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_8.getCode().equals(String.valueOf(feedBackType))) {
            return packFeedBackType5And8(user, questionnaireId, feedBackType);

        } else if (TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_7.getCode().equals(String.valueOf(feedBackType))) {
            //附件9：培训教员反馈表，理论培训结束后发放
            return packFeedBackType7(user, questionnaireId, feedBackType);

        } else if (TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_6.getCode().equals(String.valueOf(feedBackType))) {
            //附件4：医院反馈表，现场评审结束后发放问卷；
            return packFeedBackType6(user, questionnaireId, feedBackType);

        } else if (TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_9.getCode().equals(String.valueOf(feedBackType))) {
            //附件8：评审员反馈表，现场评审结束后发放问卷；
            return packFeedBackType9(user, questionnaireId, feedBackType);

        } else {
            throw new ServiceException("入参反馈类型错误：" + feedBackType);
        }
    }

    private TraineesAssessorListVo packFeedBackType9(SysUser user, Long questionnaireId, Long feedBackType) {
        TraineesAssessorListVo traineesAssessorListVo = new TraineesAssessorListVo();
        //查询反馈统计记录表
        List<TraQuestionnaireFeedBackRecord> traQuestionnaireFeedBackRecordList = queryTraQuestionnaireFeedBackRecordList(String.valueOf(user.getUserId()), questionnaireId, feedBackType);
        TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord = traQuestionnaireFeedBackRecordList.get(0);
        //封装医院信息
        //查询医院周期表，初始化数据时，避免脏数据，这里只会有一条autCode唯一数据
        List<HospitalReviewCycle> hospitalReviewCycleList = hospitalReviewCycleMapper.selectByApplyNoAndStageValue2(null, AutSaAudCycleEnum.REVIEW_CYCLE.getCycleStageValue(), traQuestionnaireFeedBackRecord.getAutCode());
        if (CollectionUtils.isEmpty(hospitalReviewCycleList)) {
            log.error("根据自评编码：{}，查询医院周期表为空！", traQuestionnaireFeedBackRecord.getAutCode());
            throw new ServiceException(ServiceExceptionEnum.BACK_ERROR_1000001);
        }
        //查询医院基本信息
        HospitalBaseInfo hospitalBaseInfo = queryHospitalByApplyNo(hospitalReviewCycleList.get(0).getApplyNo());

        traineesAssessorListVo.setApplyNo(hospitalReviewCycleList.get(0).getApplyNo());
        traineesAssessorListVo.setApplyName(hospitalBaseInfo.getHospitalName());
        traineesAssessorListVo.setAutCode(traQuestionnaireFeedBackRecord.getAutCode());
        traineesAssessorListVo.setSceneReviewTime(hospitalReviewCycleList.get(0).getCycle());
        traineesAssessorListVo.setNeedId(String.valueOf(user.getUserId()));
        traineesAssessorListVo.setNeedName(user.getNickName());
        traineesAssessorListVo.setReviewerInfoVo(packReviewerInfoVo(null, traQuestionnaireFeedBackRecord.getAutCode()));
        traineesAssessorListVo.setNotFilledQuestionnaireTotal(traQuestionnaireFeedBackRecordList.size());
        traineesAssessorListVo.setRecordId(traQuestionnaireFeedBackRecord.getRecordId());
        return traineesAssessorListVo;

    }

    private TraineesAssessorListVo packFeedBackType6(SysUser user, Long questionnaireId, Long feedBackType) {
        TraineesAssessorListVo traineesAssessorListVo = new TraineesAssessorListVo();

        SysUserHospital sysUserHospital = sysUserHospitalMapper.selectSysUserHospitalByUserId(user.getUserId());
        if (sysUserHospital == null) {
            log.error("根据当前登录医院用户，查询用户医院关联表为空！用户id：" + user.getUserId());
            throw new ServiceException(ServiceExceptionEnum.BACK_ERROR_1000001);
        }
        //查询医院基本信息
        HospitalBaseInfo hospitalBaseInfo = queryHospitalByApplyNo(sysUserHospital.getHospitalApplyNo());
        //查询反馈统计记录表
        List<TraQuestionnaireFeedBackRecord> traQuestionnaireFeedBackRecordList = queryTraQuestionnaireFeedBackRecordList(sysUserHospital.getHospitalApplyNo(), questionnaireId, feedBackType);
        TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord = traQuestionnaireFeedBackRecordList.get(0);
        //封装医院信息
        //查询医院周期表，初始化数据时，避免脏数据，这里只会有一条autCode唯一数据
        List<HospitalReviewCycle> hospitalReviewCycleList = hospitalReviewCycleMapper.selectByApplyNoAndStageValue2(sysUserHospital.getHospitalApplyNo(), AutSaAudCycleEnum.REVIEW_CYCLE.getCycleStageValue(), traQuestionnaireFeedBackRecord.getAutCode());
        if (CollectionUtils.isEmpty(hospitalReviewCycleList)) {
            log.error("根据医院id：{},自评编码：{}，查询医院周期表为空！", traQuestionnaireFeedBackRecord.getNeedId(), traQuestionnaireFeedBackRecord.getAutCode());
            throw new ServiceException(ServiceExceptionEnum.BACK_ERROR_1000001);
        }
        //AutCode数据按最新创建时间排序，取第一个做为填写反馈表
        traineesAssessorListVo.setApplyNo(sysUserHospital.getHospitalApplyNo());
        traineesAssessorListVo.setApplyName(hospitalBaseInfo.getHospitalName());
        traineesAssessorListVo.setAutCode(traQuestionnaireFeedBackRecord.getAutCode());
        traineesAssessorListVo.setSceneReviewTime(hospitalReviewCycleList.get(0).getCycle());
        traineesAssessorListVo.setNeedId(sysUserHospital.getHospitalApplyNo());
        traineesAssessorListVo.setNeedName(hospitalBaseInfo.getHospitalName());
        traineesAssessorListVo.setReviewerInfoVo(packReviewerInfoVo(sysUserHospital.getHospitalApplyNo(), traQuestionnaireFeedBackRecord.getAutCode()));
        traineesAssessorListVo.setNotFilledQuestionnaireTotal(traQuestionnaireFeedBackRecordList.size());
        traineesAssessorListVo.setRecordId(traQuestionnaireFeedBackRecord.getRecordId());
        return traineesAssessorListVo;
    }

    private TraineesAssessorListVo packFeedBackType7(SysUser user, Long questionnaireId, Long feedBackType) {
        TraineesAssessorListVo traineesAssessorListVo = new TraineesAssessorListVo();

        List<TraQuestionnaireFeedBackRecord> traQuestionnaireFeedBackRecordList = queryTraQuestionnaireFeedBackRecordList(String.valueOf(user.getUserId()), questionnaireId, feedBackType);
        TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord = traQuestionnaireFeedBackRecordList.get(0);
        TrainingEvaluateResult trainingEvaluateResult = queryTrainingEvaluateResultByResultId(traQuestionnaireFeedBackRecord.getResultId());
        traineesAssessorListVo.setNeedId(String.valueOf(user.getUserId()));
        traineesAssessorListVo.setNeedName(user.getNickName());
        traineesAssessorListVo.setSex(user.getSex());
        traineesAssessorListVo.setPhoneNumber(user.getPhonenumber());
        traineesAssessorListVo.setEmail(user.getEmail());
        traineesAssessorListVo.setCourseStartTime(trainingEvaluateResult.getCourseStartTime());
        traineesAssessorListVo.setCourseEndTime(trainingEvaluateResult.getCourseEndTime());
        traineesAssessorListVo.setNotFilledQuestionnaireTotal(traQuestionnaireFeedBackRecordList.size());
        traineesAssessorListVo.setRecordId(traQuestionnaireFeedBackRecord.getRecordId());
        //封装学员用户信息
        traineesAssessorListVo.setReviewerInfoVo(packTraineesList(trainingEvaluateResult.getTraineesAssessorId()));
        return traineesAssessorListVo;
    }

    private ReviewerInfoVo packTraineesList(Long userId) {
        SysUser sysUser = userService.selectUserById(userId);
        if (sysUser == null) {
            log.error("根据学员id{}，查询用户表为空：", userId);
            throw new ServiceException("查询用户信息数据为空，请联系管理员！");
        }
        ReviewerInfoVo reviewerInfoVo = new ReviewerInfoVo();
        List<ReviewerInfoVo.HosPlanUserInfoVO> userInfoVOList = new ArrayList<>();
        ReviewerInfoVo.HosPlanUserInfoVO hosPlanUserInfoVO = new ReviewerInfoVo.HosPlanUserInfoVO();
        hosPlanUserInfoVO.setAccountId(String.valueOf(sysUser.getUserId()));
        hosPlanUserInfoVO.setName(sysUser.getNickName());
        userInfoVOList.add(hosPlanUserInfoVO);
        reviewerInfoVo.setTraineesList(userInfoVOList);
        return reviewerInfoVo;
    }

    private TraineesAssessorListVo packFeedBackType5And8(SysUser user, Long questionnaireId, Long feedBackType) {
        TraineesAssessorListVo traineesAssessorListVo = new TraineesAssessorListVo();

        //1-根据当前登录用户id做为评审学员id条件且反馈表类型为理论培训和未填写反馈问卷类型，查询反馈问卷填写统计记录表，按创建时间升序排序
        List<TraQuestionnaireFeedBackRecord> traQuestionnaireFeedBackRecordList = queryTraQuestionnaireFeedBackRecordList(String.valueOf(user.getUserId()), questionnaireId, feedBackType);
        TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord = traQuestionnaireFeedBackRecordList.get(0);

        //4-根据记录表获取评估结果id，根据结果id查询结果表，封装对应数据返回
        //学员实践培训记录没有关联结果Id时报错提示封装
        if (traQuestionnaireFeedBackRecord.getResultId() == null) {
            log.error("根据记录表获取评估结果id为空,记录表数据为：" + traQuestionnaireFeedBackRecord.toString());
            boolean b = TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_8.getCode().equals(String.valueOf(feedBackType));
            throw new ServiceException( b ? "管理员尚未对带教培训进行评估，暂不能填写反馈表。" : "根据记录表获取评估结果id为空，请联系管理员！");
        }
        TrainingEvaluateResult trainingEvaluateResult = queryTrainingEvaluateResultByResultId(traQuestionnaireFeedBackRecord.getResultId());

        //5-根据培训教员id，查询用户表，封装对应数据返回
        List<SysUser> userList = userService.selectUserListByUserIds(trainingEvaluateResult.getTrainerId());
        if (CollectionUtils.isEmpty(userList)) {
            log.error("根据培训教员id，查询用户表为空,评估结果表数据为：" + trainingEvaluateResult.toString());
            throw new ServiceException("查询用户信息数据为空，请联系管理员！");
        }
        traineesAssessorListVo.setNeedId(String.valueOf(user.getUserId()));
        traineesAssessorListVo.setNeedName(user.getNickName());
        traineesAssessorListVo.setSex(user.getSex());
        traineesAssessorListVo.setPhoneNumber(user.getPhonenumber());
        traineesAssessorListVo.setEmail(user.getEmail());
        traineesAssessorListVo.setCourseStartTime(trainingEvaluateResult.getCourseStartTime());
        traineesAssessorListVo.setCourseEndTime(trainingEvaluateResult.getCourseEndTime());
        traineesAssessorListVo.setCourseFormat(trainingEvaluateResult.getCourseFormat());
        traineesAssessorListVo.setNotFilledQuestionnaireTotal(traQuestionnaireFeedBackRecordList.size());
        traineesAssessorListVo.setRecordId(traQuestionnaireFeedBackRecord.getRecordId());
        List<ReviewerInfoVo.HosPlanUserInfoVO> userVoList = userList.stream().map(o -> {
            ReviewerInfoVo.HosPlanUserInfoVO userInfoVO = new ReviewerInfoVo.HosPlanUserInfoVO();
            userInfoVO.setAccountId(String.valueOf(o.getUserId()));
            userInfoVO.setName(o.getNickName());
            return userInfoVO;
        }).collect(Collectors.toList());
        ReviewerInfoVo reviewerInfoVo = new ReviewerInfoVo();
        reviewerInfoVo.setReviewerList(userVoList);
        traineesAssessorListVo.setReviewerInfoVo(reviewerInfoVo);
        traineesAssessorListVo.setVirtualName(StringUtils.isNotBlank(trainingEvaluateResult.getTrainerName()) ? trainingEvaluateResult.getTrainerName() : null);

        return traineesAssessorListVo;
    }

    private HospitalBaseInfo queryHospitalByApplyNo(String hospitalApplyNo) {
        QueryBaseConditionDTO queryBaseConditionDTO = new QueryBaseConditionDTO();
        queryBaseConditionDTO.setCommonId(hospitalApplyNo);
        HospitalBaseInfo hospitalBaseInfo = hospitalBaseInfoMapper.selectHospitalByApplyNo(queryBaseConditionDTO);
        if (hospitalBaseInfo == null) {
            log.error("根据医院id：{}，查询医疗机构详情表为空！", hospitalApplyNo);
            throw new ServiceException(ServiceExceptionEnum.BACK_ERROR_1000001);
        }
        return hospitalBaseInfo;
    }

    private TrainingEvaluateResult queryTrainingEvaluateResultByResultId(Long resultId) {
        TrainingEvaluateResult trainingEvaluateResult = trainingEvaluateResultMapper.selectTrainingEvaluateResultByResultId(resultId);
        if (trainingEvaluateResult == null) {
            log.error("根据评估结果id查询评估结果数据为空,评估结果id为：" + resultId);
            throw new ServiceException("查询评估结果数据为空，请联系管理员！");
        }
        return trainingEvaluateResult;
    }

    private List<TraQuestionnaireFeedBackRecord> queryTraQuestionnaireFeedBackRecordList(String needId, Long questionnaireId, Long feedBackType) {
        //1-根据当前登录用户id做为评审学员id条件且反馈表类型为理论培训和未填写反馈问卷类型，查询反馈问卷填写统计记录表，按创建时间升序排序
        TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord = new TraQuestionnaireFeedBackRecord();
        traQuestionnaireFeedBackRecord.setNeedId(needId);
        traQuestionnaireFeedBackRecord.setFeedBackType(feedBackType);
        traQuestionnaireFeedBackRecord.setFillStatus(Constants.STR_NUM_0);
        traQuestionnaireFeedBackRecord.setStatus((long) Constants.INT_ONE);
        traQuestionnaireFeedBackRecord.setQuestionnaireId(questionnaireId);
        List<TraQuestionnaireFeedBackRecord> traQuestionnaireFeedBackRecords = traQuestionnaireFeedBackRecordMapper.selectTraQuestionnaireFeedBackRecordListByTimeASC(traQuestionnaireFeedBackRecord);
        //2-有未填反馈表记录数据，允许操作，封装返回数据
        if (CollectionUtils.isEmpty(traQuestionnaireFeedBackRecords)) {
            //3-没记录数据，抛异常提示；
            throw new ServiceException(ServiceExceptionEnum.BACK_ERROR_1000001);
        }
        return traQuestionnaireFeedBackRecords;
    }

    public void checkTheoryTraining(SysUser user) {
        //线上校验
        TraAccountExam traAccountExam = new TraAccountExam();
        traAccountExam.setAccount(user.getUserName());
        List<TraAccountExam> traAccountExamList = traAccountExamMapper.selectTraAccountExamList(traAccountExam);
        if (CollUtil.isEmpty(traAccountExamList)) {
            throw new ServiceException("未通过线上线下理论培训,不能填写当前反馈表", 500);
        }
        //所有线上培训匹配通过返回true
        boolean flag = traAccountExamList.stream()
                .allMatch(tra -> Constants.HospitalConstants.NUM_1.equals(tra.getStatus()));
        if (!flag &&
                //线下校验,根据当前用户，查询已发布，已报名，已参与的线下培训数据，如果有证明参与了线下培训
                cstOfflineTrainingManagementMapper.selectCstOfflineTrainingManagementByAccountIdAndStatus(String.valueOf(user.getUserId())) < 1
        ) {
            throw new ServiceException("未通过线上线下理论培训,不能填写当前反馈表", 500);
        }
    }

    private List<HospitalReviewer> packTraQuestionnaireFeedBack(List<TraQuestionnaireFeedBack> traQuestionnaireFeedBackList, SysUser user) {
        //查询医院与评审员的信息，如果有新的医院未有反馈表，可发放,封装医院数据信息返回
        Long userId = user.getUserId();
        //筛选反馈表的医院id
        List<String> backApplyNo = traQuestionnaireFeedBackList.stream().map(TraQuestionnaireFeedBack::getApplyNo).collect(Collectors.toList());

        //根据审批员id，获取hospital_reviewer最新的数据；
        HospitalReviewer hospitalReviewer = new HospitalReviewer();
        hospitalReviewer.setReviewerId(String.valueOf(userId));
        hospitalReviewer.setStatus(Constants.INT_ONE);
        List<HospitalReviewer> hospitalReviewers = hospitalReviewerMapper.selectHospitalReviewerList(hospitalReviewer);
        if (CollectionUtils.isEmpty(hospitalReviewers)) {
            throw new ServiceException("根据当前登录的审批员id，未查询到医院与评审员关联的信息", 500);
        }
        //筛选医院和审批员关联表对比反馈表,多出的医院，证明有医院未有反馈表
        List<HospitalReviewer> reviewerList = hospitalReviewers.stream().filter(o -> !backApplyNo.contains(o.getApplyNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(reviewerList)) {
            //都有反馈表，不需要再填写
            throw new ServiceException("当前登录用户关联的医院都已填写反馈表！", 500);
        }
        return reviewerList;
    }

    /**
     * @param traineesAssessorList
     * @param applyNo
     * @param autCodeList          (入参如果不为空，操作角色为医院，走医院逻辑)
     */
    private void packHospitalInfo(TraineesAssessorListVo traineesAssessorList, String applyNo, List<String> autCodeList) {

        QueryBaseConditionDTO queryBaseConditionDTO = new QueryBaseConditionDTO();
        queryBaseConditionDTO.setCommonId(applyNo);
        HospitalBaseInfo hospitalBaseInfo = hospitalBaseInfoMapper.selectHospitalByApplyNo(queryBaseConditionDTO);
        if (hospitalBaseInfo == null) {
            log.error("根据医院id：{}，查询医疗机构详情表为空！", applyNo);
            throw new ServiceException(ServiceExceptionEnum.BACK_ERROR_1000001);
        }
        if (CollectionUtils.isEmpty(autCodeList)) {
            //根据医院id和status,查询认证自评关联表
            // List<AutSaRelation> autSaRelationListList = selectAutSaRelationList(applyNo,Constants.INT_ONE,evaluateFlag);
            List<AutSaRelation> autSaRelationListList = selectAutSaRelationList(applyNo, Constants.INT_ONE);
            //查询医院周期表
            HospitalReviewCycle hospitalReviewCycle = hospitalReviewCycleMapper.selectByApplyNoAndStageValue(applyNo, AutSaAudCycleEnum.REVIEW_CYCLE.getCycleStageValue());
            if (hospitalReviewCycle == null) {
                log.error("根据医院id：{}，查询医院周期表为空！", applyNo);
                throw new ServiceException(ServiceExceptionEnum.BACK_ERROR_1000001);
            }
            traineesAssessorList.setAutCode(autSaRelationListList.get(0).getAutCode());
            traineesAssessorList.setSceneReviewTime(hospitalReviewCycle.getCycle());
        } else {
            //查询医院周期表，初始化数据时，避免脏数据，这里只会有一条autCode唯一数据
            List<HospitalReviewCycle> hospitalReviewCycleList = hospitalReviewCycleMapper.selectByApplyNoAndStageValue2(applyNo, AutSaAudCycleEnum.REVIEW_CYCLE.getCycleStageValue(), autCodeList.get(0));
            if (CollectionUtils.isEmpty(hospitalReviewCycleList)) {
                log.error("根据医院id：{}，查询医院周期表为空！", applyNo);
                throw new ServiceException(ServiceExceptionEnum.BACK_ERROR_1000001);
            }
            //AutCode数据按最新创建时间排序，取第一个做为填写反馈表
            traineesAssessorList.setAutCode(autCodeList.get(0));
            traineesAssessorList.setSceneReviewTime(hospitalReviewCycleList.get(0).getCycle());
        }
        traineesAssessorList.setApplyNo(applyNo);
        traineesAssessorList.setApplyName(hospitalBaseInfo.getHospitalName());
    }

    private List<AutSaRelation> selectAutSaRelationList(String applyNo, Integer status) {
        AutSaRelation autSaRelation = new AutSaRelation();
        autSaRelation.setHospitalApplyNo(applyNo);
        autSaRelation.setEvaluateFlag(Constants.INT_ONE);
        if (status != null) {
            autSaRelation.setStatus(status);
        }
        List<AutSaRelation> autSaRelationList = autSaRelationMapper.selectAutSaRelationListByCondition(autSaRelation);
        if (CollectionUtils.isEmpty(autSaRelationList)) {
            log.error("查询关联医院与评审员信息为空，不允许操作，查询条件为：{}", autSaRelation.toString());
            throw new ServiceException(ServiceExceptionEnum.BACK_ERROR_1000002);
        }
        return autSaRelationList;
    }

    /**
     * 新增反馈问卷
     *
     * @param req 反馈问卷
     * @return 结果
     */
    @Override
    public int insertTraQuestionnaireFeedBack(TraQuestionnaireFeedBackDTO req) {
        TraQuestionnaireFeedBack traQuestionnaireFeedBack = BeanUtil.toBean(req, TraQuestionnaireFeedBack.class);
        if (CollectionUtils.isNotEmpty(req.getAssessorLeaderIds())) {
            traQuestionnaireFeedBack.setAssessorLeaderIds(String.join(",", req.getAssessorLeaderIds()));
        }
        traQuestionnaireFeedBack.setUserId(SecurityUtils.getUserId());

        //查询是否有相同数据，有就更新为失效，再插入
        traQuestionnaireFeedBack.setFlag((long) Constants.INT_ZERO);
        List<TraQuestionnaireFeedBack> traQuestionnaireFeedBacks = traQuestionnaireFeedBackMapper.selectTraQuestionnaireFeedBackList(traQuestionnaireFeedBack);
        if (CollectionUtils.isNotEmpty(traQuestionnaireFeedBacks)) {
            if (traQuestionnaireFeedBacks.size() > 1) {
                throw new ServiceException("反馈问卷表有效数据大于2条，应为1条，请联系管理员", 500);
            }
            TraQuestionnaireFeedBack updateFeedBack = traQuestionnaireFeedBacks.get(0);
            updateFeedBack.setFlag((long) Constants.INT_ONE);
            traQuestionnaireFeedBackMapper.updateTraQuestionnaireFeedBack(updateFeedBack);
        }
        //提交成功，修改记录表为已填写
        TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord = new TraQuestionnaireFeedBackRecord();
        traQuestionnaireFeedBackRecord.setRecordId(req.getRecordId());
        traQuestionnaireFeedBackRecord.setFillStatus(Constants.STR_NUM_1);
        traQuestionnaireFeedBackRecordMapper.updateTraQuestionnaireFeedBackRecord(traQuestionnaireFeedBackRecord);

        return traQuestionnaireFeedBackMapper.insertTraQuestionnaireFeedBack(traQuestionnaireFeedBack);
    }

    /**
     * 获取当前登录评审员或医院的最新评审流程的评审员与学员列表
     *
     * @param feedBackType
     * @param questionnaireId
     * @return
     */
    @Override
    public TraineesAssessorListVo traineesAssessorList(Long feedBackType, Long questionnaireId) {
        // 获取当前登录用户id
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        String roleKey = user.getRoleKey();
        Long userId = user.getUserId();

        TraineesAssessorListVo traineesAssessorList = new TraineesAssessorListVo();
        String applyNo = null;
        //判断登录用户是医院还是评审员或评审学员角色，获取对应学员数据
        if (AutSaAudRoleEnum.TRAINEES_ASSESSOR.getRoleKey().equals(roleKey) ||
                AutSaAudRoleEnum.ASSESSOR.getRoleKey().equals(roleKey)
        ) {
            List<String> applyNos ;
            //反馈表为学员实践反馈类型且角色为评审员时，根据评审员id，查询对应的医院分配角色为学员的数据，刷选的数据可填写学员实践反馈表
            if (TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_8.getCode().equals(String.valueOf(feedBackType)) &&
                    AutSaAudRoleEnum.ASSESSOR.getRoleKey().equals(roleKey)) {
                applyNos = hospitalReviewerMapper.selectHospitalReviewerApplyNosByReviewerId(String.valueOf(userId), Constants.HospitalConstants.TRAINEES_REVIEW);
            } else {
                //评审学员或评审员
                applyNos = hospitalReviewerMapper.selectHospitalReviewerApplyNosByReviewerId(String.valueOf(userId), null);
            }
            if (CollectionUtils.isEmpty(applyNos)) {
                log.error("根据当前登录用户id为评审员id，查询医院关联数据为空，用户id：" + userId);
                throw new ServiceException(ServiceExceptionEnum.BACK_ERROR_1000001);
            }
            //检查登录用户是否有反馈问卷需要填写
            this.checkUserWriteQuestionnaire(userId, feedBackType, applyNos, null, questionnaireId);
            //封装当前用户未填写反馈问卷的数量
            traineesAssessorList.setNotFilledQuestionnaireTotal(applyNos.size());
            //获取周期阶段为现场评审的医院周期数据
            List<HospitalReviewCycle> hospitalReviewCycleList = hospitalReviewCycleMapper.selectHospitalReviewCycleByApplyNos(applyNos, AutSaAudCycleEnum.REVIEW_CYCLE.getCycleStageValue());
            if (CollectionUtils.isEmpty(hospitalReviewCycleList)) {
                log.error("根据医院ids：" + applyNos.toString() + "，查询医院评审阶段周期为空！");
                throw new ServiceException(ServiceExceptionEnum.BACK_ERROR_1000001);
            }
            hospitalReviewCycleList.forEach(o -> {
                String cycle = o.getCycle();
                String substring = cycle.substring(0, cycle.indexOf(","));
                o.setCycle(substring);
            });
            //取最新的医院ID
            HospitalReviewCycle hospitalReviewCycle = hospitalReviewCycleList.stream().max(Comparator.comparing(HospitalReviewCycle::getCycle)).get();
            // 再根据医院No查询医院与评审员信息，查询学员列表
            applyNo = hospitalReviewCycle.getApplyNo();
            String autCode = hospitalReviewCycle.getAutCode();
            traineesAssessorList.setReviewerInfoVo(packReviewerInfoVo(hospitalReviewCycle.getApplyNo(), autCode));
            //封装医院信息
            this.packHospitalInfo(traineesAssessorList, applyNo, null);
        } else if (AutSaAudRoleEnum.HOSPITAL.getRoleKey().equals(roleKey)) {
            //医院
            SysUserHospital sysUserHospital = sysUserHospitalMapper.selectSysUserHospitalByUserId(userId);
            if (sysUserHospital == null) {
                log.error("根据当前登录医院用户，查询用户医院关联表为空！用户id：" + userId);
                throw new ServiceException(ServiceExceptionEnum.BACK_ERROR_1000001);
            }
            applyNo = sysUserHospital.getHospitalApplyNo();
            //根据医院id和现场评估falg为是，查询自评编码无效与有效的数据
            List<AutSaRelation> autSaRelations = selectAutSaRelationList(applyNo, null);
            List<String> autCodeList = autSaRelations.stream().map(AutSaRelation::getAutCode).collect(Collectors.toList());
            //检查登录用户是否有反馈问卷需要填写
            this.checkUserWriteQuestionnaire(userId, feedBackType, null, autCodeList, questionnaireId);
            //封装当前用户未填写反馈问卷的数量
            traineesAssessorList.setNotFilledQuestionnaireTotal(autCodeList.size());
            traineesAssessorList.setReviewerInfoVo(packReviewerInfoVo(sysUserHospital.getHospitalApplyNo(), autCodeList.get(0)));
            //封装医院信息
            this.packHospitalInfo(traineesAssessorList, applyNo, autCodeList);
        }

        return traineesAssessorList;
    }

    /**
     * 检查登录用户是否有反馈问卷需要填写
     *
     * @param userId          当前登录用户
     * @param feedBackType    反馈问卷类型
     * @param applyNos        医院ids（需要查询医院是否有反馈表进行比较的数据）
     * @param autCodeList     医院autCodes（如果有入参，证明操作角色是医院，需要走医院的逻辑）
     * @param questionnaireId
     */
    private List<TraQuestionnaireFeedBack> checkUserWriteQuestionnaire(Long userId, Long feedBackType, List<String> applyNos, List<String> autCodeList, Long questionnaireId) {
        //区别当前为最新的评审流程医院，查新表-排除填写过问卷的医院，剩余的医院比较评审周期的现场评审节点时间，取最新的医院ID
        TraQuestionnaireFeedBack traQuestionnaireFeedBack = new TraQuestionnaireFeedBack();
        traQuestionnaireFeedBack.setUserId(userId);
        traQuestionnaireFeedBack.setFlag((long) Constants.INT_ZERO);
        traQuestionnaireFeedBack.setFeedBackType(feedBackType);
        traQuestionnaireFeedBack.setQuestionnaireId(questionnaireId);
        if (CollectionUtils.isNotEmpty(autCodeList)) {
            traQuestionnaireFeedBack.setAutCodeList(autCodeList);
        }
        List<TraQuestionnaireFeedBack> traQuestionnaireFeedBackList = traQuestionnaireFeedBackMapper.selectTraQuestionnaireFeedBackList(traQuestionnaireFeedBack);
        if (CollectionUtils.isNotEmpty(traQuestionnaireFeedBackList)) {
            if (CollectionUtils.isNotEmpty(autCodeList)) {
                //医院排除逻辑
                //筛选反馈表的医院autCode
                List<String> backAutCodeList = traQuestionnaireFeedBackList.stream().map(TraQuestionnaireFeedBack::getAutCode).collect(Collectors.toList());
                //查新表-排除填写过问卷的医院；如果为医院，刷选排除条件 aut_code自评编码,刷选处理为未填写的问卷
                List<String> collect = autCodeList.stream().filter(o -> !backAutCodeList.contains(o)).collect(Collectors.toList());
                autCodeList.clear();
                autCodeList.addAll(collect);
                if (CollectionUtils.isEmpty(autCodeList)) {
                    throw new ServiceException(ServiceExceptionEnum.BACK_ERROR_1000001);
                }
            } else if (CollectionUtils.isNotEmpty(applyNos)) {
                //筛选反馈表的医院id
                List<String> backApplyNo = traQuestionnaireFeedBackList.stream().map(TraQuestionnaireFeedBack::getApplyNo).collect(Collectors.toList());
                //查新表-排除填写过问卷的医院；
                List<String> collect = applyNos.stream().filter(o -> !backApplyNo.contains(o)).collect(Collectors.toList());
                applyNos.clear();
                applyNos.addAll(collect);
                if (CollectionUtils.isEmpty(applyNos)) {
                    throw new ServiceException(ServiceExceptionEnum.BACK_ERROR_1000001);
                }
            }
        }
        return traQuestionnaireFeedBackList;
    }

    @Override
    public List<TraQuestionnaireFeedBack> details(Long feedBackType, Long questionnaireId) {
        TraQuestionnaireFeedBack traQuestionnaireFeedBack = new TraQuestionnaireFeedBack();
        traQuestionnaireFeedBack.setFeedBackType(feedBackType);
        //管理员查询所有对应类型问卷
        if (!sysRoleService.isAdmin()) {
            traQuestionnaireFeedBack.setUserId(SecurityUtils.getUserId());
        }
        // traQuestionnaireFeedBack.setFlag((long) Constants.INT_ZERO);
        traQuestionnaireFeedBack.setQuestionnaireId(questionnaireId);
        return traQuestionnaireFeedBackMapper.selectTraQuestionnaireFeedBackList(traQuestionnaireFeedBack);
    }

    @Override
    public ReviewerInfoVo selectReviewerInfo(String autCode) {
        //查询认证自评关联表
        AutSaRelation autSaRelation = new AutSaRelation();
        autSaRelation.setAutCode(autCode);
        List<AutSaRelation> autSaRelationLists = autSaRelationMapper.selectAutSaRelationListByCondition(autSaRelation);
        if (CollectionUtils.isEmpty(autSaRelationLists) || autSaRelationLists.size() > 1) {
            throw new ServiceException("根据自评编码，查询认证自评关联表错误！", 500);
        }
        return packReviewerInfoVo(autSaRelationLists.get(0).getHospitalApplyNo(), autCode);
    }

    public ReviewerInfoVo packReviewerInfoVo(String hospitalApplyNo, String autCode) {
        ReviewerInfoVo reviewerInfoVo = new ReviewerInfoVo();
        // 查询评审员列表
        Map<String, List<HosPlanUserInfoVO>> reviewerListMap =
                iHospitalReviewerService.selectHosPlanUserInfoByApplyNo(hospitalApplyNo, autCode);
        reviewerInfoVo.setReviewerList(BeanUtil.copyToList(reviewerListMap.get("review"), ReviewerInfoVo.HosPlanUserInfoVO.class));
        // 设置评审学员
        reviewerInfoVo.setTraineesList(BeanUtil.copyToList(reviewerListMap.get("traReview"), ReviewerInfoVo.HosPlanUserInfoVO.class));
        return reviewerInfoVo;
    }

}
