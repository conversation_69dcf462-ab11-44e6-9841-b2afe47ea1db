package com.thas.common.core.domain;

import java.io.Serializable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * '认证标准模板 cst_certification_standards
 *
 * <AUTHOR>
 * @date 2022-01-18
 */
@Getter
@Setter
@AllArgsConstructor
@ToString
@NoArgsConstructor
public class CstCertificationStandardsExcel implements Serializable {

    private static final long serialVersionUID = -4167164740907661516L;

    /**
     * 款id
     */
    @NotBlank(message = "条款id不能为空")
    @Size(max = 11, message = "条款id不超过11个字符")
    private String id;

    /**
     * 章id
     */
    private String chapterId;

    /**
     * 章数
     */
    @NotBlank(message = "章数chapterNo不能为空")
    @Size(max = 10, message = "章数不超过10个字符")
    private String chapterNo;

    /**
     * 章
     */
    @NotBlank(message = "章chapter不能为空")
    @Size(max = 50, message = "章不超过50个字符")
    private String chapter;

    /**
     * 节id
     */
    private String sectionId;

    /**
     * 节数
     */
    @NotBlank(message = "节数sectionNo不能为空")
    @Size(max = 10, message = "节数不超过10个字符")
    private String sectionNo;

    /**
     * 节
     */
    @NotBlank(message = "节section不能为空")
    @Size(max = 50, message = "节不超过50个字符")
    private String section;

    /**
     * 条id
     */
    private String articleId;

    /**
     * 条数
     */
    @NotBlank(message = "条数articleNo不能为空")
    @Size(max = 10, message = "条数不超过10个字符")
    private String articleNo;

    /**
     * 条
     */
    @NotBlank(message = "条article不能为空")
    @Size(max = 255, message = "条不超过255个字符")
    private String article;

    /**
     * 款id
     */
    private String clauseId;

    /**
     * 款数
     */
    @NotBlank(message = "款数clauseNo不能为空")
    @Size(max = 10, message = "款数不超过10个字符")
    private String clauseNo;

    /**
     * 款
     */
    @NotBlank(message = "款clause不能为空")
    @Size(max = 300, message = "款不超过300个字符")
    private String clause;

    /**
     * 是否带*(0否 1是）
     */
    @NotBlank(message = "是否带*isStar不能为空")
    @Size(max = 1, message = "是否带*不超过1个字符")
    private String isStar;

    /**
     * 细则标题
     */
    //@NotNull(message = "细则标题detailRulesTitle不能为空")
    @Size(max = 50, message = "细则标题不超过50个字符")
    private String detailRulesTitle;

    /**
     * 细则描述
     */
    @NotNull(message = "细则描述detailRulesDesc不能为空")
    @Size(max = 2500, message = "细则描述不超过2000个字符")
    private String detailRulesDesc;

    /**
     * 达标佐证材料
     */
    @NotNull(message = "细达标佐证材料evidenceMaterial不能为空")
    @Size(max = 2500, message = "达标佐证材料不超过500个字符")
    private String evidenceMaterial;

    /**
     * 相关法律法规、规范性文件
     */
    @NotNull(message = "相关法律法规、规范性文件regulationFile不能为空")
    @Size(max = 2500, message = "相关法律法规、规范性文件不超过2500个字符")
    private String regulationFile;

    /**
     * 国际参考文献
     */
    @NotNull(message = "国际参考文献internationalReference不能为空")
    @Size(max = 65535, message = "国际参考文献不超过65535个字符")
    private String internationalReference;

    /**
     * 状态
     */
    private String status;

    /**
     * 版本号
     */
    private String versionId;

    /**
     * 领域Id
     */
    private String domainId;
}
