<template>
  <div v-loading="loading" class="field-management-list">
    <el-row :gutter="24">
      <el-col :span="24" :xs="24">
        <el-form ref="elForm" :model="queryData" size="small" label-width="80px" inline>
          <el-form-item label="分组名称" prop="groupName">
            <el-input v-model.trim="queryData.groupName" placeholder="请输入分组名称" clearable :style="{ width: '100%' }" :maxlength="20">
            </el-input>
          </el-form-item>

          <el-form-item label="状态" prop="state">
            <el-select v-model.trim="queryData.state" clearable placeholder="请选择状态" filterable>
              <el-option label="开启" :value="1"> </el-option>
              <el-option label="关闭" :value="0"> </el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" size="small" @click="query">
              查询
            </el-button>
            <el-button type="primary" size="small" @click="resetForm"> 重 置 </el-button>
          </el-form-item>
        </el-form>
      </el-col>
      <dict-span style="display: none" dictType="enable_disable" :value="0"></dict-span>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="24">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="toAdd">添加</el-button>
      </el-col>
    </el-row>

    <el-row :gutter="24" style="margin-top: 24px">
      <el-col :span="24">
        <el-table :data="dataSource" border :key="key">
          <el-table-column align="center" label="操作" width="100">
            <template slot-scope="scope">
              <el-button type="text" @click="toEdit(scope.row.id)">编辑</el-button>
              <el-button type="text" @click="deleteDomain(scope.row.groupName, [scope.row.id], scope.row.versionId)">删除</el-button>
            </template>
          </el-table-column>
          <!-- <el-table-column align="center" label="分组名称" prop="groupName"></el-table-column> -->
          <el-table-column align="center" label="分组名称" prop="groupName"></el-table-column>
          <el-table-column align="center" label="关联款项数" prop="cstCount" width="100"></el-table-column>
          <el-table-column align="center" label="创建时间" prop="createTime"></el-table-column>
          <el-table-column align="center" label="更新时间" prop="updateTime"></el-table-column>
          <el-table-column align="center" label="状态" prop="state" width="80">
            <template slot-scope="scope">
              <dict-span dictType="enable_disable" :value="`${scope.row.state}`"></dict-span>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query" />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import request from "@/utils/request";
import DictSpan from "@/components/DetailMessage/dictSpan.vue";
import { param } from "@/utils";
let that = this;
export default {
  name: "FieldManagementList",
  components: {
    DictSpan,
  },
  data() {
    return {
      queryData: {
        groupName: "",
        state: "",
        pageNum: 0,
        pageSize: 10,
        versionId: ""
      },
      dataSource: [],
      versionData: [],
      key: 0,
      total: 0,
      allReady: false,
      loading: false
    };
  },

  watch: {
    $route: (to) => {
      if (!that || !that.allReady) return;
      if (to.path != "/filedManagement/fieldManagementList") return;
      that.query();
      that.standardQuery()
    },
  },

  computed: {},

  mounted() {
    this.query();
    this.standardQuery();
    that = this;
    this.allReady = true;
  },

  methods: {
    toAdd() {
      this.$router.push({
        path: "/filedManagement/fieldManagementEdit"
      });
    },
    toEdit(id) {
      // this.$router.push({
      //   name: "FieldManagementEdit",
      //   params: {
      //     id,
      //   },
      // });
      this.$router.push({
        path: "/filedManagement/fieldManagementEdit/" + id,
      });
    },

    deleteDomain(groupName, id, versionId) {
      const h = this.$createElement;
      this.$msgbox({
        title: "删除确认",
        message: h("p", null, [
          h("span", null, "确定要删除"),
          h(
            "span",
            {
              style: {
                color: "#1890ff",
              },
            },
            groupName
          ),
          h("span", null, "吗?"),
        ]),
        type: "warning",
        distinguishCancelAndClose: true,
        showCancelButton: true,
        confirmButtonText: "删除",
        cancelButtonText: "取消",
      })
        .then(() => {
          request({
            url: "/system/domain/" + id,
            method: "delete",
          }).then((res) => {
            this.query();
            this.getStandard(versionId, true, false);
            this.$message({ type: "success", message: "删除成功" });
          });
        })
        .catch((error) => { });
    },
    resetForm() {
      this.$refs["elForm"].resetFields();
      this.queryData.pageNum = 1
      this.query()
    },
    query() {
      this.loading = true
      request({
        url: "/system/domain/list?" + param(this.queryData),
        method: "get",
      }).then((res) => {
        this.loading = false
        res.code == 200 &&
          (this.dataSource = res.rows) &&
          (this.total = res.total) &&
          (this.key = new Date().getTime());
      }).catch((error) => {
        // console.log(error)
        this.loading = false
      })
    },

    standardQuery() {
      request({
        url: "/system/versioning/list",
        method: "get",
      }).then((res) => {
        this.versionData = res.rows;
        const version = res.rows.find(row => row.status == 1);
        if (version) {
          this.$set(this.queryData, 'versionId', version.versionId);
        }
      });
    },
  },
};
</script>

<style lang='scss' scoped>
.field-management-list {
  padding: 24px;
  min-height: inherit;
  background-color: #fff;
}
</style>
