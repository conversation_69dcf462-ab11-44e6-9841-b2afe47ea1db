<template>
  <div class="application-detail-info">
    <el-form ref="elForm" :model="formData" :rules="rules" size="small" label-width="140px">
      <!-- 一行 -->
      <el-row>
        <el-col :span="12">
          <el-form-item label="开业/院日期" prop="businessTime">
            <el-date-picker v-model.trim="formData.businessTime" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
              :style="{ width: '90%' }" placeholder="请选择开业/院日期" :picker-options="{
                disabledDate: (time) => time.getTime() > Date.now(),
              }" clearable></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮政编码" prop="postalOde">
            <el-input v-model.trim="formData.postalOde" placeholder="请输入邮政编码" clearable :maxlength="6"
              :style="{ width: '90%' }">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 两行 -->
      <el-row>
        <el-col :span="12">
          <el-form-item label="地址" prop="hospitalAddress">
            <el-input v-model.trim="formData.hospitalAddress" placeholder="请输入地址" clearable :style="{ width: '90%' }"
              :maxlength="50">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="座机" prop="hospitalPhone" style="margin-bottom: 0">
            <el-form-item prop="hospitalAreaCode" :rules="rules.hospitalAreaCode"
              style="display: inline-block; width: 90px">
              <el-input v-model.trim="formData.hospitalAreaCode" placeholder="座机区号" :style="{ width: '100%' }"
                :maxlength="4">
              </el-input>
            </el-form-item>
            -
            <el-form-item prop="hospitalPhoneNum" :rules="rules.hospitalPhoneNum"
              style="display: inline-block; width: calc((100% - 160px)/2)">
              <el-input v-model.trim="formData.hospitalPhoneNum" placeholder="座机号码" clearable :style="{ width: '100%' }"
                :maxlength="8"></el-input>
            </el-form-item>
            -
            <el-form-item prop="hospitalExtPhone" :rules="rules.hospitalExtPhone"
              style="display: inline-block; width: calc((100% - 160px)/2)">
              <el-input v-model.trim="formData.hospitalExtPhone" placeholder="分机号码" clearable :style="{ width: '98%' }"
                :maxlength="8"></el-input>
            </el-form-item>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 三行 -->
      <el-row>
        <el-col :span="12">
          <el-form-item label="传真" prop="hospitalFax">
            <el-input v-model.trim="formData.hospitalFax" placeholder="请输入传真" clearable :maxlength="13"
              :style="{ width: '90%' }">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="医院网址" prop="hospitalOfficialWebsite">
            <el-input v-model.trim="formData.hospitalOfficialWebsite" placeholder="请输入医院网址" clearable :maxlength="100"
              :style="{ width: '90%' }"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 四行 -->
      <el-row>
        <el-col :span="12">
          <el-form-item label="联系人姓名" prop="hospitalContacts">
            <el-input v-model.trim="formData.hospitalContacts" placeholder="请输入联系人姓名" clearable :style="{ width: '90%' }"
              :maxlength="50">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系手机号" prop="contactsPhone">
            <el-input v-model.trim="formData.contactsPhone" placeholder="请输入联系手机号" clearable
              :style="{ width: '90%' }"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系邮箱" prop="contactsEmail">
            <el-popover ref="popover" placement="top-start" width="400" trigger="focus">
              <span style="color:red;">该邮箱将用于医院相关业务(如创建账号、找回密码等)的验证</span>
            </el-popover>
            <el-input v-popover:popover v-model.trim="formData.contactsEmail" placeholder="请输入联系邮箱" clearable
              :style="{ width: '90%' }" :maxlength="128">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="通讯地址" prop="postalAddress">
            <el-input v-model.trim="formData.postalAddress" placeholder="请输入通讯地址" clearable :style="{ width: '90%' }"
              :maxlength="50"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 四+行 -->
      <el-divider></el-divider>
      <el-row>
        <el-col :span="12">
          <el-form-item label="" prop="legalPersonType">
            <el-radio-group v-model.trim="formData.legalPersonType" size="medium">
              <el-radio label="1">法定代表人</el-radio>
              <el-radio label="2">院长</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="姓名" prop="legalPersonName">
            <el-input v-model.trim="formData.legalPersonName" placeholder="请输入姓名" clearable :style="{ width: '90%' }"
              :maxlength="50"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别" prop="gender">
            <el-select v-model.trim="formData.gender" placeholder="请输入性别" clearable filterable :style="{ width: '90%' }"
              :disabled="!!(formData.gender && formData.certificateType == 1)">
              <el-option v-for="(item, index) in genderOptions" :key="index" :label="item.dictLabel"
                :value="item.dictValue" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="证件类型" prop="certificateType">
            <el-select v-model.trim="formData.certificateType" placeholder="请选择证件类型" clearable filterable
              :style="{ width: '90%' }" @change="(value) => clearCertificatesNumberByType('certificateNumber')
                ">
              <el-option v-for="(item, index) in certificateTypeOptions" :key="index" :label="item.dictLabel"
                :value="item.dictValue" :disabled="item.disabled"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="证件号" prop="certificateNumber">
            <el-input v-model.trim="formData.certificateNumber" placeholder="请输入证件号" clearable :style="{ width: '90%' }"
              @input="identifyGender"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="职务" prop="legalPersonPost">
            <el-input v-model.trim="formData.legalPersonPost" placeholder="请输入职务" clearable :style="{ width: '90%' }"
              :maxlength="50"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="职称" prop="legalPersonTitle">
            <el-input v-model.trim="formData.legalPersonTitle" placeholder="请输入职称" clearable :style="{ width: '90%' }"
              :maxlength="50"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="座机" style="margin-bottom: 0" prop="legalPersonPhone">
            <el-form-item prop="legalPersonAreaCode" :rules="rules.legalPersonAreaCode"
              style="display: inline-block; width: 90px">
              <el-input v-model.trim="formData.legalPersonAreaCode" placeholder="座机区号" :style="{ width: '100%' }">
              </el-input>
            </el-form-item>
            -
            <el-form-item prop="legalPersonPhoneNum" :rules="rules.legalPersonPhoneNum"
              style="display: inline-block; width: calc((100% - 160px)/2)">
              <el-input v-model.trim="formData.legalPersonPhoneNum" :maxlength="8" placeholder="座机号码" clearable
                :style="{ width: '100%' }"></el-input>
            </el-form-item>
            -
            <el-form-item prop="legalPersonExtPhone" :rules="rules.legalPersonExtPhone"
              style="display: inline-block; width: calc((100% - 160px)/2)">
              <el-input v-model.trim="formData.legalPersonExtPhone" :maxlength="8" placeholder="分机号码" clearable
                :style="{ width: '98%' }"></el-input>
            </el-form-item>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="传真" prop="legalPersonFax">
            <el-input v-model.trim="formData.legalPersonFax" placeholder="请输入传真" clearable
              :style="{ width: '90%' }"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机" prop="legalPersonMobile">
            <el-input v-model.trim="formData.legalPersonMobile" placeholder="请输入手机" clearable
              :style="{ width: '90%' }"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="legalPersonEmail">
            <el-input v-model.trim="formData.legalPersonEmail" placeholder="请输入邮箱" clearable :maxlength="128"
              :style="{ width: '90%' }"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="证件照附件" prop="legalPersonPhoto" required>
            <upload :files="files1" :accept="`.JPG,.JPEG,.PNG`" :data="{ type: '1' }" :fileLimit="2"
              :action="`${uploadUrl}/binTang/common/uploadFile`" @uploadSuccess="fileAdd" @fileRemove="fileRemove">
            </upload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider></el-divider>
      <!-- 五行 -->
      <el-row>
        <!-- <el-col :span="12">
          <el-form-item label="医院服务规划区域" prop="planArea">
            <el-input v-model.trim="formData.planArea" placeholder="请输入医院服务规划区域" clearable :style="{ width: '90%' }"
              :maxlength="50"></el-input>
          </el-form-item>
        </el-col> -->

        <el-col :span="12">
          <el-form-item label="主管单位名称" prop="competentAuthorityName">
            <el-input v-model.trim="formData.competentAuthorityName" placeholder="请输入主管单位名称" clearable
              :style="{ width: '90%' }" :maxlength="50"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="临床科室总数" prop="clinicalDepartmentNum">
            <el-input v-model.trim="formData.clinicalDepartmentNum" placeholder="请输入临床科室总数" clearable type="number"
              :max="Math.pow(2, 31) - 1" :maxlength="10" :style="{ width: '90%' }">
              <template slot="append">个</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="病区总数" prop="inpatientAreaNum">
            <el-input v-model.trim="formData.inpatientAreaNum" placeholder="请输入病区总数" clearable type="number"
              :max="Math.pow(2, 31) - 1" :maxlength="10" :style="{ width: '90%' }">
              <template slot="append">个</template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="医技科室总数" prop="medicalTechnologyNum">
            <el-input v-model.trim="formData.medicalTechnologyNum" placeholder="请输入医技科室总数" type="number"
              :max="Math.pow(2, 31) - 1" :maxlength="10" clearable :style="{ width: '90%' }">
              <template slot="append">个</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="占地面积" prop="areaCovered">
            <el-input v-model.trim="formData.areaCovered" placeholder="请输入占地面积" type="number" :max="Math.pow(2, 31) - 1"
              :maxlength="10" clearable :style="{ width: '90%' }">
              <template slot="append">m²</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="总建筑面积" prop="areaArchitecture">
            <el-input v-model.trim="formData.areaArchitecture" placeholder="请输入总建筑面积" type="number"
              :max="Math.pow(2, 31) - 1" :maxlength="10" clearable :style="{ width: '90%' }">
              <template slot="append">m²</template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="业务用房建筑面积" prop="areaBusinessArchitecture">
            <el-input v-model.trim="formData.areaBusinessArchitecture" placeholder="请输入业务用房建筑面积" type="number"
              :max="Math.pow(2, 31) - 1" :maxlength="10" clearable :style="{ width: '90%' }">
              <template slot="append">m²</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="编制床位" prop="preparationBed">
            <div slot="label" style="display: inline-block;">
              <span>编制床位</span>
              <el-tooltip class="item" effect="dark" content="《医疗机构执业许可证》时核准的床位数" placement="top">
                <i class="el-icon-info" style="color: #909399;"></i>
              </el-tooltip>
            </div>
            <el-input v-model.trim="formData.preparationBed" placeholder="请输入编制床位（《医疗机构执业许可证》时核准的床位数）" type="number"
              :max="Math.pow(2, 31) - 1" :maxlength="10" clearable :style="{ width: '90%' }">
              <template slot="append">个</template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="实际开放总床位数" prop="actualBed">
            <el-input v-model.trim="formData.actualBed" placeholder="请输入实际开放总床位数" type="number" :max="Math.pow(2, 31) - 1"
              :maxlength="10" clearable :style="{ width: '90%' }">
              <template slot="append">个</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="病床使用率" prop="bedUtilization">
            <el-input v-model.trim="formData.bedUtilization" placeholder="请输入病床使用率" type="number"
              :max="Math.pow(2, 31) - 1" :maxlength="10" clearable :style="{ width: '90%' }">
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="全院在岗人员" prop="onDutyNum">
            <el-input v-model.trim="formData.onDutyNum" placeholder="请输入全院在岗人员" type="number" :max="Math.pow(2, 31) - 1"
              :maxlength="10" clearable :style="{ width: '90%' }">
              <template slot="append">人</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="卫生技术人员" prop="healthTechnologyNum">
            <el-input v-model.trim="formData.healthTechnologyNum" placeholder="请输入卫生技术人员" type="number"
              :max="Math.pow(2, 31) - 1" :maxlength="10" clearable :style="{ width: '90%' }">
              <template slot="append">人</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="医疗机构类别" prop="hospitalType">
        <el-radio-group v-model.trim="formData.hospitalType" size="medium">
          <el-radio v-for="(item, index) in hospitalTypeOptions" :key="index" :label="item.dictValue"
            :disabled="item.disabled">{{ item.dictLabel }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="所有制形式" prop="formOwnership">
        <el-radio-group v-model.trim="formData.formOwnership" size="medium">
          <el-radio v-for="(item, index) in formOwnershipOptions" :key="index" :label="item.dictValue"
            :disabled="item.disabled">
            {{ item.dictLabel }}
            <el-input v-if="formOwnershipOther(item.dictValue)" :maxlength="50"
              v-model.trim="formData.formOwnershipOther"></el-input>
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="经营性质" prop="natureOperation">
        <el-radio-group v-model.trim="formData.natureOperation" size="medium">
          <el-radio v-for="(item, index) in natureOperationOptions" :key="index" :label="item.dictValue"
            :disabled="item.disabled">{{ item.dictLabel }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="管理隶属关系" prop="managementAffiliation">
        <el-radio-group v-model.trim="formData.managementAffiliation" size="medium">
          <el-radio v-for="(item, index) in managementAffiliationOptions" :key="index" :label="item.dictValue"
            :disabled="item.disabled">{{ item.dictLabel }}
            <el-input v-if="managementAffiliationOther(item.dictValue)" :maxlength="50"
              v-model.trim="formData.managementAffiliationOther"></el-input>
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="医院目前级别" prop="hospitalLevel">
        <el-radio-group v-model.trim="formData.hospitalLevel" size="medium">
          <el-radio v-for="(item, index) in hospitalLevelOptions" :key="index" :label="item.dictValue"
            :disabled="item.disabled">{{ item.dictLabel }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="教学类别" prop="teachingCategory">
        <el-radio-group v-model.trim="formData.teachingCategory" size="medium">
          <el-radio v-for="(item, index) in teachingCategoryOptions" :key="index" :label="item.dictValue"
            :disabled="item.disabled">{{ item.dictLabel }}
            <el-input :maxlength="50" v-if="teachingCategoryOther(item.dictValue)"
              v-model.trim="formData.teachingCategoryOther"></el-input>
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { checkEmail, checkContactsPhone } from '../../../utils/request';

export default {
  components: {},
  props: {
    initData: {
      type: [Object, null],
      default: () => null
    },
    applyInfo: {
      type: Object,
      default: () => { },
    },
  },
  data() {
    return {
      formData: {
        businessTime: null,
        hospitalAddress: undefined,
        hospitalPhone: "",
        hospitalFax: undefined,
        hospitalOfficialWebsite: undefined,
        postalOde: undefined,
        competentAuthorityName: undefined,
        // planArea: undefined,
        clinicalDepartmentNum: undefined,
        inpatientAreaNum: undefined,
        medicalTechnologyNum: undefined,
        areaCovered: undefined,
        areaArchitecture: undefined,
        areaBusinessArchitecture: undefined,
        preparationBed: undefined,
        actualBed: undefined,
        bedUtilization: undefined,
        onDutyNum: undefined,
        healthTechnologyNum: undefined,
        hospitalType: undefined,
        formOwnership: undefined,
        formOwnershipOther: undefined,
        natureOperation: undefined,
        managementAffiliation: undefined,
        managementAffiliationOther: undefined,
        hospitalLevel: undefined,
        teachingCategory: undefined,
        teachingCategoryOther: undefined,
        hospitalContacts: undefined,
        contactsPhone: undefined,
        postalAddress: undefined,
        contactsEmail: undefined,
        hospitalExtPhone: undefined,

        legalPersonName: undefined,
        gender: undefined,
        certificateType: undefined,
        certificateNumber: undefined,
        legalPersonType: undefined,
        legalPersonPost: undefined,
        legalPersonTitle: undefined,
        legalPersonPhone: undefined,
        legalPersonFax: undefined,
        legalPersonMobile: undefined,
        legalPersonEmail: undefined,
        legalPersonPhoto: undefined,
        legalPersonExtPhone: undefined,
      },

      hospitalTypeOptions: [],
      formOwnershipOptions: [],
      natureOperationOptions: [],
      genderOptions: [],
      certificateTypeOptions: [],
      managementAffiliationOptions: [],
      hospitalLevelOptions: [],
      teachingCategoryOptions: [],
      regMap: {},
      files1: []
    };
  },
  computed: {
    rules() {
      return {
        businessTime: [
          {
            required: true,
            message: "请选择开业/院日期",
            trigger: "change",
          },
        ],
        hospitalAddress: [
          {
            required: true,
            message: "请输入地址",
            trigger: "blur",
          },
        ],
        hospitalPhoneNum: [
          {
            required: true,
            validator: (rule, value, callback) => this.phoneValid(rule, value, callback),
            trigger: ["blur", "change"],
          },
        ],
        hospitalAreaCode: [
          {
            required: true,
            validator: (rule, value, callback) => this.areaCodeValid(rule, value, callback),
            trigger: ["blur", "change"],
          },
        ],
        hospitalPhone: [
          {
            required: true,
          },
        ],
        hospitalExtPhone: [
          {
            required: false,
            validator: (rule, value, callback) => this.regExtPhoneValid(rule, value, callback),
            trigger: ["blur", "change"],
          },
        ],
        hospitalFax: [
          {
            required: false,
            validator: (rule, value, callback) => this.faxValid(rule, value, callback),
            trigger: ["blur", "change"],
          },
        ],
        hospitalContacts: [
          {
            required: true,
            message: "请输入联系人姓名",
            trigger: "blur",
          },
        ],
        contactsEmail: [
          {
            required: true,
            validator: (rule, value, callback) => this.emailValid(rule, value, callback, 'contactsEmail'),
            trigger: ["blur"],
          },

        ],
        contactsPhone: [
          {
            required: true,
            // message: "请输入手机号码",
            validator: (rule, value, callback) => this.mobileValid(rule, value, callback, 'contactsPhone'),
            trigger: ["blur", "change"],
          },
        ],
        postalAddress: [
          {
            required: true,
            message: "请输入通信地址",
            trigger: "blur",
          },
        ],
        hospitalOfficialWebsite: [
          {
            required: true,
            message: "请输入医院网址",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => this.urlValid(rule, value, callback),
            trigger: ["blur", "change"],
          },
        ],
        postalOde: [
          {
            required: true,
            message: "请输入邮政编码",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => this.postalOdeValid(rule, value, callback),
            trigger: ["blur", "change"],
          },
        ],
        competentAuthorityName: [
          {
            required: true,
            message: "请输入主管单位名称",
            trigger: "blur",
          },
        ],
        // planArea: [
        //   {
        //     required: true,
        //     message: "请输入医院服务规划区域",
        //     trigger: "blur",
        //   },
        // ],
        clinicalDepartmentNum: [
          {
            required: true,
            validator: (rule, value, callback) => this.numberValid(rule, value, callback, "请输入临床科室总数"),
            trigger: ["blur", "change"],
          },
        ],
        inpatientAreaNum: [
          {
            required: true,
            validator: (rule, value, callback) => this.numberValid(rule, value, callback, "请输入病区总数"),
            trigger: ["blur", "change"],
          },

        ],
        medicalTechnologyNum: [
          {
            required: true,
            validator: (rule, value, callback) => this.numberValid(rule, value, callback, "请输入医技科室总数"),
            trigger: ["blur", "change"]
          },
        ],
        areaCovered: [
          {
            required: true,
            validator: (rule, value, callback) => this.areaValid(rule, value, callback, "占地面积"),
            trigger: ["blur", "change"]
          },
        ],
        areaArchitecture: [
          {
            required: true,
            validator: (rule, value, callback) => this.areaValid(rule, value, callback, "总建筑面积"),
            trigger: ["blur", "change"],
          },

        ],
        areaBusinessArchitecture: [
          {
            required: true,
            validator: (rule, value, callback) => this.areaValid(rule, value, callback, "业务用房建筑面积"),
            trigger: ["blur", "change"],
          },

        ],
        preparationBed: [
          {
            required: true,
            validator: (rule, value, callback) => this.numberValid(rule, value, callback, "请输入编制床位（《医疗机构执业许可证》时核准的床位数）"),
            trigger: ["blur", "change"],
          },
        ],
        actualBed: [
          {
            required: true,
            validator: (rule, value, callback) => this.numberValid(rule, value, callback, "请输入实际开放总床位数"),
            trigger: ["blur", "change"],
          },
        ],
        bedUtilization: [
          {
            required: true,
            message: "请输入病床使用率",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => this.percentValid(rule, value, callback, "请输入实际开放总床位数"),
            trigger: ["blur", "change"],
          },
        ],
        onDutyNum: [
          {
            required: true,
            message: "请输入全院在岗人员",
            validator: (rule, value, callback) => this.numberValid(rule, value, callback, "请输入全院在岗人员"),
            trigger: ["blur", "change"],
          },
        ],
        healthTechnologyNum: [
          {
            required: true,
            message: "请输入卫生技术人员",
            validator: (rule, value, callback) => this.numberValid(rule, value, callback, "请输入卫生技术人员"),
            trigger: ["blur", "change"],
          },
        ],
        hospitalType: [
          {
            required: true,
            message: "医疗机构类别不能为空",
            trigger: "change",
          },
        ],
        formOwnership: [
          {
            required: true,
            message: "所有制形式不能为空",
            trigger: "change",
          },
        ],
        natureOperation: [
          {
            required: true,
            message: "经营性质不能为空",
            trigger: "change",
          },
        ],
        managementAffiliation: [
          {
            required: true,
            message: "管理隶属关系不能为空",
            trigger: "change",
          },
        ],
        hospitalLevel: [
          {
            required: true,
            message: "医院目前级别不能为空",
            trigger: "change",
          },
        ],
        teachingCategory: [
          {
            required: true,
            message: "教学类别不能为空",
            trigger: "change",
          },
        ],
        legalPersonName: [
          {
            required: true,
            message: "请输入姓名",
            trigger: "blur",
          },
        ],
        gender: [
          {
            required: true,
            message: "请选择性别",
            trigger: "change",
          },
        ],
        certificateType: [
          {
            required: true,
            message: "请选择证件类型",
            trigger: "change",
          },
        ],
        certificateNumber: [
          {
            validator: (rule, value, callback) =>
              this.certificatesValid(value, callback),
          },
        ],
        legalPersonType: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        legalPersonPost: [
          {
            required: true,
            message: "请输入职务",
            trigger: "blur",
          },
        ],
        legalPersonTitle: [
          {
            required: true,
            message: "请输入职称",
            trigger: "blur",
          },
        ],
        legalPersonPhoneNum: [
          {
            required: true,
            // message: "请输入手机号码",
            validator: (rule, value, callback) => this.phoneValid(rule, value, callback),
            trigger: ["blur", "change"],
          },
        ],
        legalPersonAreaCode: [
          {
            required: true,
            // message: "请输入手机号码",
            validator: (rule, value, callback) => this.areaCodeValid(rule, value, callback),
            trigger: ["blur", "change"],
          },
        ],
        legalPersonExtPhone: [
          {
            required: false,
            validator: (rule, value, callback) => this.regExtPhoneValid(rule, value, callback),
            trigger: ["blur", "change"],
          },
        ],
        legalPersonPhone: [
          {
            required: true,
          },
        ],
        legalPersonFax: [
          {
            required: false,
            // message: "请输入手机号码",
            validator: (rule, value, callback) => this.faxValid(rule, value, callback),
            trigger: ["blur", "change"],
          },
        ],
        legalPersonMobile: [
          {
            required: true,
            // message: "请输入手机号码",
            validator: (rule, value, callback) => this.mobileValid(rule, value, callback),
            trigger: ["blur", "change"],
          },
        ],
        legalPersonEmail: [
          {
            required: true,
            // message: "请输入联系邮箱",
            validator: (rule, value, callback) => this.emailValid(rule, value, callback),
            trigger: ["blur", "change"],
          },
        ],
        legalPersonPhoto: [
          {
            required: true,
            message: "请上传证件照附件",
            trigger: "blur",
          },
        ],
      }
    },

    uploadUrl() {
      return localStorage.baseUrl || process.env.VUE_APP_BASE_API;
    },

    formOwnershipOther() {
      return (value) => {
        return this.formData.formOwnership == 6 && value == 6;
      };
    },
    managementAffiliationOther() {
      return (value) => {
        return this.formData.managementAffiliation == 5 && value == 5;
      };
    },
    teachingCategoryOther() {
      return (value) => {
        return this.formData.teachingCategory == 4 && value == 4;
      };
    },
  },

  mounted() {
    this.initDicts();
    if (!this.initData) return;
    let { hospitalBaseInfo = {}, hospitalLegalPerson = {}, fileDetails = {} } = this.initData || {}
    let totalData = {
      ...hospitalBaseInfo,
      ...hospitalLegalPerson
    }
    for (const key in this.formData) {
      if (Object.hasOwnProperty.call(this.formData, key)) {
        if (key == 'hospitalPhone') {
          let phones = totalData[key].split('-')
          this.$set(this.formData, 'hospitalAreaCode', phones[0])
          this.$set(this.formData, 'hospitalPhoneNum', phones[1])
        } else if (key == 'legalPersonPhone') {
          let phones = totalData[key].split('-')
          this.$set(this.formData, 'legalPersonAreaCode', phones[0])
          this.$set(this.formData, 'legalPersonPhoneNum', phones[1])
        } else if (key == 'legalPersonPhoto') {
          this.$set(this.formData, 'legalPersonPhoto', totalData[key]);
          let legalPersonPhoto = totalData[key].split(",") || [];
          this.files1 = legalPersonPhoto.map(photo => {
            return {
              ...fileDetails[photo][0]
            }
          });
        } else if (typeof totalData[key] === 'number' && !isNaN(totalData[key])) {
          this.$set(this.formData, key, `${totalData[key]}`)
        } else {
          this.$set(this.formData, key, totalData[key] || '')
        }
      }
    }
  },
  methods: {
    identifyGender(val) {
      let regString = this.regMap['1'];
      let reg = new RegExp(regString);
      if (this.formData.certificateType == 1) {
        this.formData.gender = '';
        this.$refs["elForm"].clearValidate('gender');
        if (this.checkIDCard(val, reg)) {
          let sexStr = ''
          if (val.length === 15) {
            sexStr = parseInt(val.substring(14, 1), 10) % 2 ? '0' : '1'
          } else if (val.length === 18) {
            sexStr = parseInt(val.substring(17, 1), 10) % 2 ? '0' : '1'
          }
          this.formData.gender = sexStr;
        }
      }
    },
    inputChange(prop, value) {
      // console.log(this.formData[prop]);
    },
    submitForm() {
      this.$refs["elForm"].validate((valid) => {
        if (!valid) return;
        // TODO 提交表单
      });
    },
    resetForm() {
      this.$refs["elForm"].resetFields();
    },
    initDicts() {
      this.getDicts("hospital_type").then((response) => {
        this.hospitalTypeOptions = response.data;
      });
      this.getDicts("ownership_form").then((response) => {
        this.formOwnershipOptions = response.data;
      });
      this.getDicts("business_nature").then((response) => {
        this.natureOperationOptions = response.data;
      });
      this.getDicts("managing_affiliation").then((response) => {
        this.managementAffiliationOptions = response.data;
      });
      this.getDicts("hospital_level").then((response) => {
        this.hospitalLevelOptions = response.data;
      });
      this.getDicts("teaching_category").then((response) => {
        this.teachingCategoryOptions = response.data;
      });

      this.getDicts("sys_user_sex").then((response) => {
        this.genderOptions = response.data;
      });
      this.getDicts("certificate_type").then((response) => {
        this.certificateTypeOptions = response.data;
      });

      this.getBingConfigKey("reg.email").then(response => {
        this.regMap['regEmail'] = response.msg.trim();
      });

      this.getBingConfigKey("reg.mobile").then(response => {
        this.regMap['regMobile'] = response.msg.trim();
      });

      this.getBingConfigKey("reg.areaCode").then(response => {
        this.regMap['regAreaCode'] = response.msg.trim();
      });

      this.getBingConfigKey("reg.phone").then(response => {
        this.regMap['regPhone'] = response.msg.trim();
      });
      this.getBingConfigKey("reg.fax").then(response => {
        this.regMap['regFax'] = response.msg.trim();
      });
      this.getBingConfigKey("reg.extPhone").then(response => {
        this.regMap['regExtPhone'] = response.msg.trim();
      });
      this.getBingConfigKey("reg.area").then(response => {
        this.regMap['regArea'] = response.msg.trim();
      });

      this.getBingConfigKey("reg.postalOde").then(response => {
        this.regMap['regPostalOde'] = response.msg.trim();
      });
      this.getBingConfigKey("reg.number").then(response => {
        this.regMap['regNumber'] = response.msg.trim();
      });
      this.getBingConfigKey("reg.percent").then(response => {
        this.regMap['regPercent'] = response.msg.trim();
      });
      this.getBingConfigKey("reg.url").then(response => {
        this.regMap['regUrl'] = response.msg.trim();
      });
      this.getBingConfigKey("reg.idCard").then(response => {
        this.regMap['1'] = response.msg.trim();
      });
      this.getBingConfigKey("reg.passport").then(response => {
        this.regMap['2'] = response.msg.trim();
      });
      this.getBingConfigKey("reg.reentry").then(response => {
        this.regMap['3'] = response.msg.trim();
      });
      this.getBingConfigKey("reg.hkCard").then(response => {
        this.regMap['4'] = response.msg;
      });
      this.getBingConfigKey("reg.moCard").then(response => {
        this.regMap['5'] = response.msg.trim();
      });
      this.getBingConfigKey("reg.twCard").then(response => {
        this.regMap['6'] = response.msg.trim();
      });

    },

    // emailBlur() {
    //   this.$refs.elForm.validateField('contactsEmail', flag => {
    //     if (!flag) {
    //       checkEmail(this.formData.contactsEmail, 'hospital')
    //     }
    //   })
    // },

    urlValid(rule, value, callback) {
      let regString = this.regMap['regUrl'];
      if (!regString) callback();
      let reg = new RegExp(regString);
      if (!reg.test(value)) callback('请输入正确的医院网址')
      callback()
    },

    percentValid(rule, value, callback) {
      let regString = this.regMap['regPercent'];
      if (!regString) callback();
      let reg = new RegExp(regString);
      if (!reg.test(value)) callback("0≤使用率百分比≤100%，且保留两位小数")
      callback()
    },

    numberValid(rule, value, callback, message) {
      if (!value) callback(message)
      let regString = this.regMap['regNumber'];
      if (!regString) callback();
      let reg = new RegExp(regString);
      if (!reg.test(value)) callback('请输入数字')
      if (value > 99999999) {
        this.$set(this.formData, rule.field, 99999999)
      }
      callback()
    },

    areaValid(rule, value, callback, message) {
      if (!value) callback(`请输入${message}`)
      let regString = this.regMap['regArea'];
      if (!regString) callback();
      let reg = new RegExp(regString);
      if (!reg.test(value)) callback(`请输入正确的${message}`)
      if (value > 99999999) {
        this.$set(this.formData, rule.field, 99999999)
      }
      callback()
    },


    postalOdeValid(rule, value, callback) {
      let regString = this.regMap['regPostalOde'];
      if (!regString) callback();
      let reg = new RegExp(regString);
      if (!reg.test(value)) callback('请输入正确的邮政编码')
      callback()
    },


    areaCodeValid(rule, value, callback) {
      if (!value) callback('请输入')
      let regString = this.regMap['regAreaCode'];
      if (!regString) callback();
      let reg = new RegExp(regString);
      if (!reg.test(value)) callback('区号错误')
      callback()
    },

    phoneValid(rule, value, callback) {
      if (!value) callback('请输入')
      let regString = this.regMap['regPhone'];
      if (!regString) callback();
      let reg = new RegExp(regString);
      if (!reg.test(value)) callback('座机号码错误')
      callback()
    },

    regExtPhoneValid(rule, value, callback) {
      let regString = this.regMap['regExtPhone'];
      if (!regString) callback();
      let reg = new RegExp(regString);
      if (value && !reg.test(value)) callback('分机号错误')
      callback()
    },

    faxValid(rule, value, callback) {
      let regString = this.regMap['regFax'];
      if (!regString) callback();
      let reg = new RegExp(regString);
      if (value && !reg.test(value)) callback('请输入正确的传真号码')
      callback()
    },

    emailValid(rule, value, callback, type) {
      if (!value) callback('请输入邮箱')
      let regString = this.regMap['regEmail'];
      if (!regString) callback();
      let reg = new RegExp(regString);
      if (!reg.test(value)) callback('请输入正确的邮箱')
      if (type === 'contactsEmail') {
        checkEmail(value, 'hospital', this.applyInfo.practiceLicenseNo).then(res => {
          if (res.code != 200) {
            callback(res.message || res.msg)
            return;
          }
          callback()
        }).catch(() => {
          callback(" ")
        })
      } else {
        callback()
      }
    },

    mobileValid(rule, value, callback, type) {
      if (!value) callback('请输入手机号')
      let regString = this.regMap['regMobile'];
      if (!regString) callback();
      let reg = new RegExp(regString);
      if (!reg.test(value)) callback('请输入正确的手机号')
      if (type === 'contactsPhone') {
        checkContactsPhone(value, 'hospital', this.applyInfo.practiceLicenseNo).then(res => {
          if (res.code != 200) {
            callback(res.message || res.msg)
            return;
          }
          callback()
        }).catch(() => {
          callback(" ")
        })
      } else {
        callback()
      }
    },

    clearCertificatesNumberByType(prop) {
      this.$set(this.formData, prop, "");
      this.$refs["elForm"].validateField(prop);
      // 重置性别
      if (this.formData.certificateType == 1) {
        this.formData.gender = '';
        this.$refs["elForm"].clearValidate('gender');
      }
    },


    certificatesValid(value, callback) {
      // debugger;
      let label = "certificateType";
      if (!this.formData[label]) {
        if (value) return callback();
        return callback('请输入证件号码')
      }
      let regString = this.regMap[this.formData[label]]
      if (regString) {
        let cer = this.certificateTypeOptions.find(({ dictValue }) => dictValue == this.formData[label]);
        if (!value) callback('请输入证件号码')
        let reg = new RegExp(regString);
        // 身份证号码特殊处理
        if ((this.formData[label] == 1 && !this.checkIDCard(value, reg)) || !reg.test(value)) callback('请输入正确的证件号码')
      }
      callback();
    },

    checkIDCard(idcode, regString) {
      // 加权因子
      const weightFactor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
      // 校验码
      const checkCode = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']

      const code = String(idcode)
      const last = idcode[17]// 最后一位

      const seventeen = code.substring(0, 17)

      // ISO 7064:1983.MOD 11-2 判断最后一位校验码是否正确
      const arr = seventeen.split('')
      const len = arr.length
      let num = 0
      for (let i = 0; i < len; i++) {
        num = num + arr[i] * weightFactor[i]
      }

      // 获取余数
      const resisue = num % 11
      const lastNo = checkCode[resisue]

      // 身份证号格式的正则思路
      // const idcardPatter = /^[1-9][0-9]{5}([1][9][0-9]{2}|[2][0][0|1][0-9])([0][1-9]|[1][0|1|2])([0][1-9]|[1|2][0-9]|[3][0|1])[0-9]{3}([0-9]|[X])$/
      const idcardPatter = regString;

      // 判断格式是否正确
      const format = idcardPatter.test(idcode)

      // 返回验证结果，校验码和格式同时正确才算是合法的身份证号码
      return last === lastNo && format
    },

    fileAdd(response, file) {
      if (response.code != 200)
        return this.$message.error(response.msg || response.message);
      // this.files1.length = 0;
      file.fileId = response.data.fileId;
      this.files1.push(file);
      this.$set(
        this.formData,
        "legalPersonPhoto",
        this.files1.map((file) => file.fileId).join(",")
      );
      this.$refs["elForm"].validateField("legalPersonPhoto");
      this.bus.$emit('fileIdUpload', 'legalPersonPhoto', this.files1.map((file) => file.fileId))

    },
    fileRemove(file, length, index) {
      this.files1.splice(index, 1);
      this.$set(
        this.formData,
        "legalPersonPhoto",
        this.files1.map((file) => file.fileId).join(",")
      );
      this.$refs["elForm"].validateField("legalPersonPhoto");
    },

    async getValue() {
      try {
        this.formData.hospitalPhone = `${this.formData.hospitalAreaCode}-${this.formData.hospitalPhoneNum}`;
        this.formData.legalPersonPhone = this.formData.legalPersonAreaCode + '-' + this.formData.legalPersonPhoneNum;

        let {
          legalPersonName,
          gender,
          certificateType,
          certificateNumber,
          legalPersonType,
          legalPersonPost,
          legalPersonTitle,
          legalPersonPhone,
          legalPersonPhoneNum,
          legalPersonAreaCode,
          legalPersonFax,
          legalPersonMobile,
          legalPersonEmail,
          legalPersonPhoto,
          legalPersonExtPhone,
          ...data
        } = this.formData;

        if (await this.$refs["elForm"].validate()) {
          return {
            data,
            legal: {
              legalPersonName,
              gender,
              certificateType,
              certificateNumber,
              legalPersonType,
              legalPersonPost,
              legalPersonTitle,
              legalPersonPhone,
              legalPersonPhoneNum,
              legalPersonAreaCode,
              legalPersonFax,
              legalPersonMobile,
              legalPersonEmail,
              legalPersonPhoto,
              legalPersonExtPhone
            }
          };
        }
      } catch (error) {
        return null;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.application-detail-info {
  ::v-deep .el-input {

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      -webkit-appearance: none;
    }

    input[type="number"] {
      -moz-appearance: textfield;
    }
  }

  ::v-deep .el-input {
    input {
      text-align: left;
    }

    .el-input-number__increase,
    .el-input-number__decrease {
      display: none;
    }
  }
}
</style>
