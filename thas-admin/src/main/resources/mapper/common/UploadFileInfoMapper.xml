<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.thas.web.mapper.UploadFileInfoMapper">
    <resultMap id="BaseResultMap" type="com.thas.web.domain.FileInfoDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
    			<result column="origin"  property="origin"/>
    			<result column="down_load_file_name"  property="downLoadFileName"/>
    			<result column="type"  property="type"/>
    			<result column="path"  property="path"/>
    			<result column="platform"  property="platform"/>
    </resultMap>

    <sql id="Base_All_Column">
                id,
                origin,
                type,
                path,
                platform
    </sql>

    <sql id="Base_Where_Condition">
    		<if test="id != null and id != ''" >
               AND id = #{id}
        	</if>
    		<if test="origin != null and origin != ''" >
               AND origin = #{origin}
        	</if>
            <if test="downLoadFileName != null and downLoadFileName != ''" >
            AND down_load_file_name = #{downLoadFileName}
            </if>
    		<if test="type != null and type != ''" >
               AND type = #{type}
        	</if>
    		<if test="path != null and path != ''" >
               AND path = #{path}
        	</if>
    		<if test="platform != null and platform != ''" >
               AND platform = #{platform}
        	</if>
            <if test="createTime != null and createTime != ''" >
                AND create_time = #{createTime}
            </if>
    </sql>

    <sql id ="Base_OrderBy_Condition">
        <if test="orderBy != null and orderBy !=''">
            ORDER BY ${orderBy}
        </if>
    </sql>

    <select id="selectUploadFileInfoList" parameterType="com.thas.web.domain.FileInfoDTO" resultMap="BaseResultMap">
        SELECT
          *
        FROM
        upload_file_info
        WHERE 1 = 1
        <include refid="Base_Where_Condition">
        </include>
    </select>

    <select id="getUploadFileInfoByIds" parameterType="java.lang.String" resultType="com.thas.web.domain.FileInfoDTO">
        SELECT
        id,
        origin,
        down_load_file_name downLoadFileName,
        type,
        path,
        platform
        FROM
        upload_file_info
        WHERE
        id in
        <foreach collection="ids.split(',')" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getUploadFileInfoByOrigin" resultType="com.thas.web.domain.FileInfoDTO">
        select
        id,
        origin,
        down_load_file_name downLoadFileName,
        type,
        path,
        platform
        from
        upload_file_info
        where
        origin = #{origin}
    </select>

    <select id="getUploadFileInfoById" resultType="com.thas.web.domain.FileInfoDTO">
        SELECT
        id,
        origin,
        down_load_file_name downLoadFileName,
        type,
        path,
        platform
        FROM
        upload_file_info
        where
        id = #{fileId}
    </select>

    <insert id="insertUploadFileInfo" parameterType="com.thas.web.domain.FileInfoDTO" useGeneratedKeys="true" keyProperty="id">
        insert into upload_file_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="origin != null">origin,</if>
            <if test="downLoadFileName != null">down_load_file_name,</if>
            <if test="type != null">type,</if>
            <if test="path != null">path,</if>
            <if test="platform != null">platform,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="origin != null">#{origin},</if>
            <if test="downLoadFileName != null">#{downLoadFileName},</if>
            <if test="type != null">#{type},</if>
            <if test="path != null">#{path},</if>
            <if test="platform != null">#{platform},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>
</mapper>
