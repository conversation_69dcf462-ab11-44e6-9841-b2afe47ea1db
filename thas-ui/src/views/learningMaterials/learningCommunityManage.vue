<template>
  <div v-loading="loading">
    <el-card class="learning-community-manage">
      <div class="page-title">学习社区管理</div>
      <div v-for="(item, index) in messageList" :key="index" class="message-item">
        <div class="item-title">
          <div class="left">
            <el-avatar size="medium" icon="el-icon-user-solid"></el-avatar>
            <div>
              <div class="name">{{item.createBy}}</div>
              <div class="create-time" style="color: #999999;">{{item.createTime}}</div>
            </div>
          </div>
          <div class="right" @click="delArticle(item)">
            <i class="el-icon-delete"></i>
            <span>删除</span>
          </div>
        </div>
        <div class="item-content">
          <div class="article">
            <pre class="text">{{ item.content }}</pre>
            <div class="img-list">
              <div v-for="(urlItem, i) in item.coverUrl" :key="i" class="img-item">
                <div class="img-box">
                  <el-image style="width: 100%; height: 100%; position: absolute;" :src="urlItem.url" :preview-src-list="[urlItem.url]" fit="cover"></el-image>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="comment-list">
          <div v-for="(commentItem) in item.list" :key="commentItem.id" class="comment-item">
            <div class="top">
              <el-avatar size="small" src=""></el-avatar>
              <div>
                <span class="user-name">{{ commentItem.createBy }}</span>
                <span class="comment-time">{{ commentItem.createTime }}</span>
              </div>
              <div style="flex: 1; text-align: right;">
                <div class="right" @click="delComment(commentItem)">
                  <i class="el-icon-delete"></i>
                  <span>删除</span>
                </div>
              </div>
            </div>
            <div class="bottom">{{ commentItem.content }}</div>
          </div>
        </div>
      </div>
      <el-empty v-if="!messageList || !messageList.length" :image-size="200"></el-empty>
      <div style="margin-top: 20px; text-align: center;">
        <el-pagination background layout="prev, pager, next" :total="total" @current-change="handleCurrentChange"></el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script>
import request from '@/utils/request'
export default {
  name: 'LearningCommunityManage',
  data() {
    return {
      queryData: {
        pageSize: 10,
        pageNum: 1
      },
      messageList: [],
      total: 0,
      comment: '',
      loading: false
    }
  },
  created() {
    // console.log(this.$route)
    this.query()
  },
  methods: {
    query() {
      this.loading = true
      request({
        url: '/system/community/list',
        method: 'get',
        params: this.queryData
      }).then((res) => {
        this.loading = false
        if (res.code != 200) return
        this.messageList = res.rows
        this.total = res.total
      }).catch((error) => {
        // console.log(error)
        this.loading = false
      })
    },
    handleCurrentChange(val) {
      this.queryData.pageNum = val
      this.query()
    },
    toDetail(item) {
      this.$router.push({
        path: '/learningMaterials/articleDetail',
        query: {
          id: item.id
        }
      })
    },
    delArticle(item) {
      request({
        url: '/system/community/' + item.id,
        method: 'delete'
      }).then((res) => {
        if (res.code != 200) return
        this.query()
      })
    },
    delComment(commentItem) {
      request({
        url: '/system/community/' + commentItem.id,
        method: 'delete'
      }).then((res) => {
        if (res.code != 200) return
        this.query()
      })
    }
  }
}
</script>

<style lang='scss' scoped>
.learning-community-manage {
  margin-bottom: 20px;
  ::v-deep .el-dialog {
    .el-dialog__header {
      background-color: #3c81f5;
      padding-top: 10px;
      .el-dialog__title {
        color: white;
      }
      .el-dialog__headerbtn {
        top: 15px;
        .el-dialog__close {
          color: white;
        }
      }
    }
    .el-dialog__body {
      padding-bottom: 1px;
      & > span {
        color: black;
      }
    }
  }
  .page-title {
    border-bottom: 1px solid #d2d4d6;
    font-size: 18px;
    font-weight: bolder;
    line-height: 50px;
  }
  .message-item {
    margin-top: 20px;
    border-bottom: 1px solid #d2d4d6;
    .item-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      .left {
        display: flex;
        align-items: center;
        font-weight: bold;
        .el-avatar {
          background: #014099;
        }
        .name {
          flex: 1;
          margin-left: 10px;
          margin-bottom: 5px;
          font-size: 16px;
        }
        .create-time {
          margin-left: 10px;
          font-size: 12px;
        }
      }
      .right {
        display: flex;
        align-items: center;
        color: #999999;
        cursor: pointer;
      }
    }
    .item-content {
      display: flex;
      margin-left: 46px;
      margin-bottom: 10px;
      cursor: pointer;
      .article {
        margin-left: 10px;
        font-weight: bolder;
        font-size: 16px;
        .title {
          margin-bottom: 10px;
          font-size: 18px;
          line-height: 30px;
        }
        .text {
          line-height: 30px;
          white-space: pre-wrap;
          word-break: break-word;
        }
        .img-list {
          max-width: 800px;
          display: flex;
          flex-wrap: wrap;
          margin-top: 20px;
          margin-bottom: 20px;
          .img-item {
            width: 250px;
            display: flex;
            border-radius: 5px;
            margin-right: 10px;
            margin-top: 10px;
            overflow: hidden;
            position: relative;
            .img-box {
              width: 100%;
              height: 0px;
              padding-bottom: 100%;
            }
          }
        }
      }
    }
    .options {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-bottom: 20px;
      margin-left: 60px;
      .comment {
        margin-right: 130px;
      }
    }
    .send-comment {
      display: flex;
      margin-bottom: 20px;
      margin-left: 60px;
      .el-avatar {
        margin-right: 15px;
      }
    }
    .send-comment-btn {
      text-align: right;
    }
    .comment-list {
      margin-left: 60px;
      .comment-item {
        .top {
          display: flex;
          align-items: center;
          .user-name {
            margin-left: 10px;
            margin-right: 10px;
            font-size: 16px;
            font-weight: bold;
          }
          .comment-time {
            font-size: 14px;
            color: #999999;
          }
          .right {
            color: #999999;
            cursor: pointer;
          }
        }
        .bottom {
          margin-top: 20px;
          margin-left: 46px;
          margin-bottom: 20px;
          font-size: 14px;
          font-weight: bold;
        }
      }
    }
  }
}
.learning-publish {
  width: 340px;
  height: 280px;
  margin-left: 10px;
  .page-title {
    border-bottom: 1px solid #d2d4d6;
    font-size: 18px;
    font-weight: bolder;
    line-height: 50px;
  }
  .btns {
    display: flex;
    .publish-btn {
      width: 48%;
      height: 200px;
      margin-right: 10px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: #666666;
      cursor: pointer;
      i {
        margin-bottom: 20px;
        font-size: 60px;
      }
      .btn-name {
        font-size: 20px;
        font-weight: bold;
      }
    }
  }
}
</style>