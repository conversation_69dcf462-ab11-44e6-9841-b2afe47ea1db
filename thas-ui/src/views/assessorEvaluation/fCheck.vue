<!--  -->
<template>
  <div class="audit-evaluation" ref="auditEvaluation" v-loading="loading">
    <div v-if="distributed">
      <!-- 医院名称 -->
      <el-card class="box-card" shadow="always" style="margin-bottom: 10px; margin-left: -5px">
        <h1 style="margin: 0">{{ detail.hospitalName }}</h1>
      </el-card>
      <!-- 总结详情 -->
      <summary-show :formData="summaryData"></summary-show>
      <!-- 评价  -->
      <el-card v-loading="replaceLoading" class="box-card autScrollCard" shadow="never" style="margin-top:15px;"
        :style="{ height: autCardHeight }">
        <div slot="header" class="clearfix">
          <h4>现场评审</h4>
          <div class="header-right" v-if="standardLoadedKey" :key="headerSlotKeys">
            <span v-if="srOrIng" @click="submitAllAud">填充全部评审初查(测试) </span>
            <span v-if="showAutFilter.length" :class="{ lightHigh: lightHigh == 10 }"
              @click="handleClauseFilter(10, showAutFilter)">
              待评价 {{ showAutFilter.length }} 款
            </span>
            <!-- 驳回 -->
            <span v-if="showAutRjFilter.length" :class="{ lightHigh: lightHigh == 30 }"
              @click="handleClauseFilter(30, showAutRjFilter)">
              被驳回 {{ showAutRjFilter.length }} 款
            </span>
            <template v-if="showShareBtn">
              <el-button v-if="isSubEvaluate !== 1" type="primary" size="mini" style="margin-right: 10px;"
                @click="submitEvaluate">提交评价结果</el-button>
              <el-button v-if="allReviewerShared" type="primary" size="mini" style="margin-right: 10px;"
                @click="getShareReport">预览临时报告</el-button>
              <!-- <el-button v-if="!shared" type="primary" size="mini" @click="toSubmitShare"
                style="margin-right: 10px;">提交共享</el-button> -->
              <el-select v-if="shared" v-model="shareReviewerId" clearable filterable class="select-reviewer" size="mini"
                placeholder="请选择评审员" @change="selectShareReviewer" style="margin-right: 10px; width: 130px;">
                <el-option v-for="({ reviewerId, reviewerName }) in shareReviewerListWithoutSelf" :key="reviewerId"
                  :label="reviewerName" :value="reviewerId"></el-option>
              </el-select>
              <!-- <el-button v-if="confirmedReviewer(shareReviewerId)" type="primary" size="mini" @click="shareConfirm"
                style="margin-right: 10px;">确认通过</el-button> -->
              <span v-if="rejectClauseIds.length" :class="{ lightHigh: lightHigh == 1 }"
                @click="handleClauseFilter(1, rejectClauseIds)">
                修改意见 {{ rejectClauseIds.length }} 款
              </span>
              <span v-if="showDistributeClauseIdList.length" :class="{ lightHigh: lightHigh == 2 }"
                @click="handleClauseFilter(2, showDistributeClauseIdList)">
                共 {{ showDistributeClauseIdList.length }} 款
              </span>
              <el-button v-if="isLeader && allReviewerShared && isAllSubEvaluate" type="primary" size="mini"
                @click="skipShare">结束共享</el-button>
              <!-- <el-button type="text" size="mini" @click="refreshDetail" style="margin-right: 10px;">刷新(临时)</el-button> -->
            </template>
            <template v-if="hasDomainGroups">
              <el-button type="primary" v-if="isTime2InputGroupText" size="mini" @click="visible = true">待小结</el-button>
              <span v-if="showStBtGroupText && hasDomainGroups && showStBtGroup" @click="visible = true">小结</span>
            </template>
            <el-button v-if="(!showAutFilter || !showAutFilter.length) && detail.evaluateResultStatsVoList && detail.evaluateResultStatsVoList.length" type="primary" size="mini" style="margin-left: 10px;" @click="toGroupEvaluationResults">分组评价结果</el-button>
          </div>
        </div>
        <!-- 款项选择器 -->
        <standard-card ref="standardCard" :key="`base${loadingKeys}`" :clauseIds="clauseIds" :versionId="relation.autCsId"
          @selectChange="selectChange" @loaded="standardLoaded">
        </standard-card>
        <!-- 款项 -->
        <div v-show="clauseList.length" class="evaluation-content" :key="`ach${selectedKeys}`"
          :style="{ height: clauseItemHeight }">
          <el-form :key="loadingKeys" label-width="100px">
            <!-- <clause-item v-for="(clause, index) in clauseList" v-show="isThisPage(index + 1)" :clause="clause" :key="clause.clauseId"> -->
            <clause-item v-for="(clause) in showClauseList" :clause="clause" :key="clause.clauseId">
              <evaluation-self :readonly="true" :type="AutSaAudCurrentStatusEnum.SA_CLAUSE" :clause="clause"
                :aut="saMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-self>
              <evaluation-review :key="headerSlotKeys" :type="AutSaAudCurrentStatusEnum.SR_CLAUSE" :clause="clause"
                :aut="srMap[clause.clauseId]" :status="autSaAudStatus" :startModify="startModify(srMap[clause.clauseId])"
                :params="modifyParams(srMap[clause.clauseId])" :readonly="isSubEvaluate === 1">
              </evaluation-review>
              <evaluation-check :readonly="true" v-if="showReviewCheck" :type="AutSaAudCurrentStatusEnum.SR_V_CLAUSE"
                :clause="clause" :aut="srvMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-check>
              <evaluation-suggest v-if="showSuggest(srMap[clause.clauseId]) && showSuggestTime && !isSureSuggestDone">
                <el-button slot="submit" :disabled="shareSuggestLoading" type="primary" slot-scope="{data, refs}"
                  @click="shareSuggest(clause.clauseId, data, refs)">提交</el-button>
              </evaluation-suggest>
              <evaluation-slot
                v-if="srMap[clause.clauseId] && srMap[clause.clauseId].shareDescList && srMap[clause.clauseId].shareDescList.length">
                <div class="share-item" v-for="item in srMap[clause.clauseId].shareDescList" :key="item.userId">
                  <el-form-item label="修改意见：">
                    <span class="break">{{ item.desc || '-' }}</span>
                  </el-form-item>
                  <el-form-item label="提出人：">
                    <span class="break">{{ item.userName || '-' }}</span>
                  </el-form-item>
                </div>
              </evaluation-slot>
            </clause-item>
          </el-form>
          <pagination v-show="total > limit" :total="total" :page.sync="pageNum" :limit.sync="limit"
            :pageSizes="[limit]" />
        </div>
      </el-card>
      <!-- 总结提交 -->
    </div>
    <el-dialog title="小结(非必填)" :visible.sync="visible" width="90%" :close-on-click-modal="false" destroy-on-close
      custom-class="elDialog" @close="srmClose">
      <srt-summary :key="domainGroupsKeys" ref="srtSummary" :canModify="srmCanModify" :srtMap='srtMap'
        :showBtn="!showStBtGroupText" :domainGroups="detail.domainGroups" :clauseMoveInfo="clauseMoveInfo"
        :distributeClauseIds="distributeClauseIds" :flags="stBtGroup" :fitClauseIds="fitClauseIds"
        @srtSubmit="srtSubmit"></srt-summary>
      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from "@/utils/request";
import evaluationMixin from "@/mixins/evaluation.vue";
// 组件
import SummaryShow from '@/views/hospitalSelfEvaluation/summaryShow/index2.vue'
import SrtSummary from './srtSummary.vue'

export default {
  name: "FAssessorIndex",
  mixins: [evaluationMixin],
  components: {
    SrtSummary,
    SummaryShow
  },
  props: {},
  watch: {
    isTime2InputGroupText: {
      immediate: true,
      handler(value) {
        if (value) {
          this.visible = true
        }
      },
    }
  },
  data() {
    return {
      show: false,
      loading: true,
      relation: {},
      detail: {},
      clauseIds: [],
      rejectClauseIds: [],
      specialInit: true,
      visible: false,
      summaryData: {
        autAdvantage: '',
        autEvaluate: '',
        autProposal: '',
        file: {}
      },

      shareReviewerId: '',
      allReviewerList: [],
      confirmedReviewerList: [],
      shareReviewerClauseMap: {},
      rejectIds: [],

      shareSuggestLoading: false,
      showSuggestTime: false,
      replaceLoading: false,

      domainGroupsKeys: 0,
      groupSubmitted: false,

      clauseMoveInfo: [],

      isEndShare: false,
    };
  },

  computed: {
    // isLeader() {
    //   return this.relation.isLeader == 1
    // },

    isReviewing() {
      return this.detail?.autSaAudStatus == this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_INQUIRE_PROC
    },

    isTime2InputGroupText() {
      return !this.showStBtGroupText && this.hasDomainGroups && this.showStBtGroup
    },
    showShareBtn() {
      let flag = this.showStBtGroupText && this.hasDomainGroups && this.showStBtGroup
      return flag && this.isReviewing && !this.isEndShare;
    },
    srOrIng() {
      let status = [
        this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_SUM_PASS,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_INQUIRE_PROC,
      ];
      let allSubmitted = this.distributeClauseIds.every(id => {
        return this.srMap[id]
      })
      return status.includes(this.autSaAudStatus) && !allSubmitted
    },
    showExamine() {
      return true;
    },
    showDistributeClauseIdList() {
      return this.relation?.distributeClauseIdList || []
    },
    showReviewCheck() {
      // 待审核  审核中  审核驳回
      let status = [
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_REJECT,
      ];
      return status.includes(this.autSaAudStatus)
    },

    hasDomainGroups() {
      return this.detail && this.detail.domainGroups
    },

    stBtGroup() {
      let list = this.domainClauseIds;
      let all_full = this.distributeClauseIds.every(id => this.srMap[id])
      if (!all_full) return [];
      let clauseIds = [...this.distributeClauseIds, ...this.fitClauseIds]
      let ids = list.map(li => li.reduce((flag, id) => {
        return flag && clauseIds.includes(id + '');
      }, true))
      return ids
    },

    showStBtGroup() {
      let status = [
        this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_SUM_PASS,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_INQUIRE_PROC,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_REJECT,
        this.AutSaAudCurrentStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW,
        this.AutSaAudCurrentStatusEnum.AUD_SECOND_TRIAL_REVIEW_PROC
      ];
      return (this.stBtGroup.includes(true) || this.shared) && status.includes(this.autSaAudStatus);
    },

    showStBtGroupText() {
      let ids = [];
      (this.detail.domainGroups || []).forEach(one => {
        ids.push(one.groupId)
        one.children.forEach(two => {
          ids.push(two.groupId)
        })
      })

      let flag = ids.every(id => { return this.btMap[id] || this.groupMap[id] })
      return flag
    },

    srmCanModify() {
      return (this.isTime2InputGroupText || this.shared) && !this.checkOther
    },

    showAutFilter() {
      const list = Object.values(this.srMap);
      let frClauseIds = list.map(({ clauseId }) => Number(clauseId));
      let list2 = this.distributeClauseIds.reduce((list, next) => {
        if (!frClauseIds.includes(Number(next))) list.push(Number(next));
        return list;
      }, [])
      return list2;
    },

    // 被驳回
    showAutRjFilter() {
      const list = Object.values(this.srMap);
      const rjList = list.filter(({ rejected }) => rejected).map(({ clauseId }) => clauseId);
      let dis = this.relation.distributeClauseIdList || [];
      let fList = dis.length ? rjList.filter(id => dis.includes(id)) : rjList;
      return fList;
    },

    shared() {
      return this.isReviewing && this.allReviewerList.some(({ reviewerId, isShare }) => {
        return reviewerId == this.$store.getters.userId && isShare == 1
      })
    },

    allReviewerShared() {
      return this.allReviewerList.every(({ isShare }) => isShare == 1)
    },

    checkOther() {
      return this.shareReviewerId && this.shareReviewerId != this.$store.getters.userId
    },

    // 共享接口的列表入参
    shareRequireData() {
      let {
        applyNo,
        autCode,
      } = this.relation;
      let reviewer = this.allReviewerList.find(({ reviewerId }) => {
        return reviewerId == this.$store.getters.userId
      })
      return {
        applyNo,
        autCode,
        reviewerId: this.$store.getters.userId,
        // shareReviewerId: this.shareReviewerId,
        isShare: 1
      }
    },

    // 已共享的评审员
    shareReviewerList() {
      return this.allReviewerList.filter(({ isShare }) => isShare == 1)
    },
    // 是否提交评价结果,0-否，1-是
    isSubEvaluate() {
      const reviewItem = this.allReviewerList.find((item) => {
        return item.reviewerId == this.$store.getters.userId
      })
      return reviewItem ? reviewItem.isSubEvaluate : ''
    },
    // 是否所有人都已提交评价结果
    isAllSubEvaluate() {
      return this.allReviewerList.every((item) => {
        return item.isSubEvaluate === 1
      })
    },

    shareReviewerListWithoutSelf() {
      return this.shareReviewerList.filter(({ reviewerId }) => {
        return reviewerId != this.$store.getters.userId
      })
    },

    // 该评审员是否已被当前评审员确认通过
    confirmedReviewer() {
      return reviewerId => {
        let curr_user_data = this.shareReviewerListWithoutSelf.find((reviewer) => reviewer.reviewerId == reviewerId)
        if (!curr_user_data) return false
        if (!curr_user_data?.passIds) return true
        return !curr_user_data.passIds.includes(this.$store.getters.userId)
      }
    },

    startModify() {
      return aut => {
        return aut && aut.isShareUpdate == 1 && !this.checkOther;
      }
    },

    modifyParams() {
      return aut => {
        return this.startModify(aut) ? { isShareUpdate: 0 } : {}
      }
    },

    showSuggest() {
      return aut => {
        return this.shared && this.checkOther && aut && !aut.shareDescList?.some(item => item.userId == this.$store.getters.userId)
      }
    },

    isSureSuggestDone() {
      let shareReviewer = this.allReviewerList.find(({ reviewerId }) => reviewerId == this.shareReviewerId)
      if (!shareReviewer) return false;
      if (!shareReviewer.passIds) return false
      return shareReviewer.passIds.includes(this.$store.getters.userId)
    },

    showSuggestValue() {
      return aut => {
        return this.shared && aut && aut.isShareUpdate == 1
      }
    },

    showSuggestModify() {
      return aut => {
        return this.shared && this.checkOther && aut && aut.isShareUpdate == 1 && this.clauseIds.some(id => aut.clauseId == id)
      }
    }
  },

  mounted() {
    this.init2(true, this.$route.query.autCode, this.$route.query.type, '', true);
    this.getClauseMoveInfo();
  },

  methods: {
    async initFinalBack() {
      if (this.detail.autSaAudStatus == this.AutSaAudCurrentStatusEnum.WAIT_AUD_SECOND_TRIAL_REVIEW) {
        this.handleError();
        return;
      }
      if (this.showAutRjFilter.length) {
        this.$set(this, 'clauseIds', this.showAutRjFilter)
        this.$set(this, 'distributeClauseIds', this.showAutRjFilter)
        this.loadingKeys++;
      }

      this.resetDomainGroupFilter()

      if (!this.detail || !this.detail.autSaAudSum) {
        return;
      }
      let { fileIds } = this.detail.autSaAudSum;
      let files = []
      fileIds.split(',').forEach(id => {
        files.push(this.detail.fileDetailMap[id][0])
      })
      this.$set(this.summaryData, 'files', files)

      let reportDesc = this.detail.autSaAudListMap[this.AutSaAudCurrentStatusEnum.SA_REPORT_DESC]
      if (reportDesc && reportDesc.length) {
        let { autDesc } = reportDesc[0];
        try {
          let desc = JSON.parse(autDesc.replace(/\n/g, '\\n').replace(/\r/g, '\\r'))
          this.$set(this.summaryData, 'autAdvantage', desc.autAdvantage)
          this.$set(this.summaryData, 'autEvaluate', desc.autEvaluate)
          this.$set(this.summaryData, 'autProposal', desc.autProposal)
        } catch (error) {
          console.log(error)
        }
      }

      this.getShareList();
      let prev_rejectIds_length = (this.rejectIds || []).length;
      this.rejectIds = this.relation.rejectIds || [];
      if (this.showShareBtn) {
        this.$set(this, 'rejectClauseIds', this.rejectIds);
        // this.$set(this, 'distributeClauseIds', this.rejectIds);
        if (this.detail.autSaAudStatus !== '030202') {
          this.loadingKeys++
        }
      }
      if (prev_rejectIds_length != 0 && this.rejectIds.length == 0) {
        this.loadingKeys++
      }

      this.shareSuggestLoading = false;
    },

    getClauseMoveInfo() {
      request({
        url: '/review/fit/move/clause/move-info',
        method: 'post',
        data: {
          autCode: this.$route.query.autCode
        }
      }).then(res => {
        this.clauseMoveInfo = res.data.moveClauseItemList || []
      })
    },

    resetDomainGroupFilter() {
      let { domainGroups } = this.detail;
      if (!domainGroups || !domainGroups.length) return;
      let domainClauseIds = []
      let clauseMoveInfo = []

      domainGroups.sort((a, b) => {
        return a.groupId - b.groupId
      })

      domainGroups.forEach(one => {
        let list = []
        let { children } = one;
        children.forEach(two => {
          let { children } = two;
          children.forEach(three => {
            let { children } = three;
            list.push(...children.map(child => Number(child.groupDetail)))
          })
        })
        list.sort((a, b) => a - b);

        let groupId = one.groupId;
        let moveInfo = this.clauseMoveInfo.find(item => item.groupId == groupId)
        if (moveInfo) {
          clauseMoveInfo.push(moveInfo)
        }
        domainClauseIds.push(list)
      })

      domainClauseIds = domainClauseIds.map((domain, index) => {
        let moveInfo = clauseMoveInfo[index]
        if (!moveInfo || (moveInfo.inputClauseIds.length == 0 && moveInfo.outputClauseIds.length == 0)) {
          return domain
        }
        let ids = domain.filter(id => !moveInfo.outputClauseIds.includes(id + ''))
        ids.push(...moveInfo.inputClauseIds.map(id => id - 0))
        return [...new Set(ids)]
      })

      this.domainClauseIds = domainClauseIds;
      this.domainGroupsKeys++
    },

    getClause(fieldId) {
      return request({
        url:
          "/hospital/hos/plan/clause/detail?" + `&time=${Date.now()}`,
        method: "post",
        data: {
          personForm: "2",
          applyNo: this.relation.applyNo,
          versionId: this.relation.autCsId,
          // accountId: this.$store.getters.userId,
          fieldId,
        },
      })
    },

    getShareList() {
      request({
        url: '/reviewer/share/list',
        method: 'post',
        data: this.shareRequireData
      }).then(res => {
        this.allReviewerList = res.data;
      }).finally(() => {
        this.replaceLoading = false
      })
    },

    getShareReport() {
      let loading = this.$loading({
        lock: true,
        text: "获取报告中",
        background: "rgba(0, 0, 0, 0.7)",
      })
      request({
        url: '/reviewer/share/report',
        method: "post",
        data: {
          reviewerId: this.$store.getters.userId,
          autCode: this.$route.query.autCode,
        },
      }).then(res => {
        console.log(res);
        let { url } = res.data
        if (url) {
          window.open(url, '_blank')
        }
      }).finally(() => {
        loading.close()
      });
    },

    async selectShareReviewer(reviewerId) {
      this.showSuggestTime = false;
      this.replaceLoading = true;

      await this.init2(true, this.$route.query.autCode, this.$route.query.type, reviewerId);

      this.showSuggestTime = true;
      this.replaceLoading = false;
      this.resetDomainGroupFilter();
    },

    shareSuggest(clauseId, { suggest }, refs) {
      if (!this.shareReviewerId || this.shareReviewerId == this.$store.getters.userId) return;
      refs.validate(valid => {
        if (valid) {
          this.shareSuggestLoading = true;
          request({
            url: '/reviewer/share/update',
            method: 'post',
            data: {
              ...this.shareRequireData,
              shareReviewerId: this.shareReviewerId,
              clauseIds: [clauseId],
              shareDesc: suggest,
            }
          }).then(() => {
            this.getShareList();
            this.init2(false, this.$route.query.autCode, this.$route.query.type, this.shareReviewerId);
            // this.init_detail2(false)
          }).finally(() => {
            this.shareSuggestLoading = false;
          })
        }
      })
    },

    shareConfirm() {
      let reviewer = this.allReviewerList.find(({ reviewerId }) => reviewerId == this.shareReviewerId)
      if (!reviewer) {
        this.$message({
          type: 'warning',
          message: '找不到当前选中的评审员'
        })
        return
      }
      // let ids = this.clauseIds;
      // let undone = ids.some(id => this.srMap[id].isShareUpdate == 1)
      // if (undone) {
      //   this.$message({
      //     type: 'warning',
      //     message: `评审员${reviewer.reviewerName}尚有建议修改项未修改，无法确认通过！`
      //   })
      //   return
      // }

      this.$confirm(`请确认${reviewer.reviewerName || reviewer.reviewerId}共享的评价您已查阅并确认无误`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        request({
          url: '/reviewer/share/pass',
          method: 'post',
          data: {
            ...this.shareRequireData,
            shareReviewerId: this.shareReviewerId
          }
        }).then(res => {
          this.getShareList();
          this.init2(false, this.$route.query.autCode, this.$route.query.type, this.shareReviewerId);
        })
      }).catch(err => {
        console.log(err);
      })
    },


    skipShare() {
      this.loading = true;
      request({
        url: '/reviewer/share/tem-pass',
        method: 'post',
        data: {
          ...this.shareRequireData
        }
      }).then(() => {
        this.isEndShare = true;
        this.init2(true, this.$route.query.autCode, this.$route.query.type);
      }).finally(() => {
        this.loading = false;
      })
    },

    refreshDetail() {
      this.getShareList();
      this.init_detail2(false)
    },

    toSubmitShare() {
      request({
        url: '/reviewer/share/submit',
        method: 'post',
        data: this.shareRequireData
      }).then(res => {
        this.getShareList();
      })
    },

    handleError() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push({ path: "/fAssessor/f/assessorEvaluationList" });
    },

    async srtSubmit(srtObject, submitType) {
      if (Array.isArray(srtObject) && srtObject.length) {
        try {
          for (let [index, item] of srtObject.entries()) {
            await this.submit({
              autCode: this.relation.autCode,
              accountId: this.$store.getters.userId + "",
              submitType: item[0].submitType,
              autSaAudLists: item
            });
          }

          this.$message("批量提交成功-仅测试!");
          if (this.$refs.srtSummary) {
            this.$refs.srtSummary.readonly(true)
            this.visible = false;
          }
        } catch (error) {
          console.log(error);
          if (this.$refs.srtSummary) {
            this.$refs.srtSummary.readonly(false)
          }
        }
        this.groupSubmitted = true

      } else {
        const data = {
          autCode: this.relation.autCode,
          accountId: this.$store.getters.userId + "",
          submitType,
          autSaAudLists: [srtObject]
        }

        try {
          this.groupSubmitted = true
          let res = await this.submit(data);
          // 报错就不提示
          if (!res) return;
          this.$message({
            type: 'success',
            message: "提交成功"
          });
          if (this.$refs.srtSummary) {
            this.$refs.srtSummary.readonly(true)
            this.$refs.srtSummary.reInit();
            // this.init2(false, this.$route.query.autCode, this.$route.query.type);
          }
        } catch (error) {
          if (this.$refs.srtSummary) {
            this.$refs.srtSummary.readonly(false)
          }
        }
      }
    },

    // 评审初查
    submitAllAud() {
      let ids = Object.values(this.srMap).map(({ clauseId }) => clauseId)
      this.makeData(this.AutSaAudCurrentStatusEnum.SR_CLAUSE, undefined, undefined, ids, true)
    },

    srmClose() {
      if (this.groupSubmitted) {
        this.groupSubmitted = false
        this.init_detail2(false);
        // this.init2(false, this.$route.query.autCode, this.$route.query.type, this.shareReviewerId);
        this.replaceLoading = true
      }
    },
    submitEvaluate() {
      this.$confirm('提交后所有评价结果将不能修改，请确认是否提交', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        request({
          url: '/reviewer/share/submit-evaluate',
          method: 'post',
          data: {
            applyNo: this.relation.applyNo,
            autCode: this.$route.query.autCode
          }
        }).then(res => {
          this.$message({
            type: 'success',
            message: '操作成功'
          })
          this.refreshDetail()
        }).catch((error) => {
          console.log(error)
        })
      }).catch(err => {
        console.log(err);
      })
    },
    // 分组评价结果
    toGroupEvaluationResults() {
      this.$router.push({
        path: '/fAssessor/groupEvaluationResults',
        query: { autCode: this.$route.query.autCode || this.relation.autCode }
      })
    }
  }
};
</script>

<style lang="scss" scoped>
.box-card+.box-card {
  margin-top: 15px;
}

h4 {
  margin: 10px 0;
}

::v-deep .elDialog {

  // height: 80%;
  .el-dialog__body {
    padding-bottom: 0;
    margin-top: 0;
    overflow: hidden;
    height: 80%;
    padding-top: 0;
  }

  // .el-dialog__footer {
  //   padding-top: 0;
  //   padding-bottom: 0;
  // }
}

.clearfix {
  color: black;
}

.select-reviewer {
  ::v-deep .el-input__suffix span.el-input__suffix-inner>i {
    visibility: visible !important;
  }
}

.share-item {
  padding-bottom: 20px;

  .break {
    word-break: break-all;
    word-wrap: break-word;
    display: inline-block;
    width: 80%;
    margin: 0;
  }
}
</style>
