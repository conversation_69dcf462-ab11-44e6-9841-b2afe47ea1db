<template>
  <div class='theory-basic-form'>
    <el-descriptions class="margin-top" :column="1">
      <el-descriptions-item label="教员名字">{{formData.needName}}</el-descriptions-item>
      <el-descriptions-item label="联系电话">{{formData.phoneNumber}}</el-descriptions-item>
      <el-descriptions-item label="联系邮箱">{{formData.email}}</el-descriptions-item>
      <el-descriptions-item label="培训日期">{{formData.courseStartTime}} 至 {{formData.courseEndTime}} </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
import mixin from './mixin.vue'
export default {
  name: 'FacultyBasicForm',
  mixins: [mixin],
  data() {
    return {
      applyNo: '',
    }
  },
  computed: {
    validateForm() {
      return this.$refs.form;
    }
  },

  methods: {
    getValue() {
      return this.formData;
    }
  }
}
</script>