<!DOCTYPE html>
<html lang="en" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word"
  xmlns:dt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882" xmlns="http://www.w3.org/TR/REC-html40">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta name="ProgId" content="Word.Document" />
  <meta name="Generator" content="Microsoft Word 14" />
  <meta name="Originator" content="Microsoft Word 14" />
  <title></title>
  <style>
    @font-face {
      font-family: "SimSun";
    }

    @page {
      @top-left {
        content: element(header);
      }

      size:landscape
    }

    body {
      font-family: "SimSun";
    }
  </style>
  <style>
    #content,
    #content1 {
      overflow: hidden;
    }

    #overallEvaluationTable {
      -fs-table-paginate: paginate;
      border-spacing: 0;
      table-layout: fixed;
      word-break: break-strict;
      cellspacing: 0;
      cellpadding: 0;
      border: solid 1px black;
      border-left: 0;
      border-right: 0;
    }

    #scoringMatrixTable {
      page-break-inside: auto;
      -fs-table-paginate: paginate;
      border-spacing: 0;
      table-layout: fixed;
      word-break: break- strict;
      cellspacing: 0;
      cellpadding: 0;
      border: solid 1px black;
      border-left: 0;
      border-right: 0;
    }

    #clauseScoreTable {
      page-break-inside: auto;
      -fs-table-paginate: paginate;
      border-spacing: 0;
      table-layout: fixed;
      word-break: break- strict;
      cellspacing: 0;
      cellpadding: 0;
      border: solid 1px black;
      border-left: 0;
      border-right: 0;
    }

    #clauseScoreTable tr,
    #scoringMatrixTable tr {
      page-break-inside: avoid;
      page-break-after: auto;
    }

    #clauseScoreTable thead th {
      text-align: center;
      background: #EFEFEF;
    }

    #clauseScoreTable thead td {
      text-align: center;
      background: #EFEFEF;
    }

    #clauseScoreTable thead tr:nth-child(2) th,
    #clauseScoreTable thead tr:nth-child(3) th {
      border-bottom: 0;
      padding: 5px;
    }

    #clauseScoreTable thead tr:nth-child(2) td,
    #clauseScoreTable thead tr:nth-child(3) td {
      border-bottom: 0;
      padding: 5px;
    }

    #clauseScoreTable tbody td {
      text-indent: 2px;
      padding: 5px;
    }

    #header>span:first-child {
      margin-left: 20px;
      float: left;
    }

    #header>span:last-child {
      margin-right: 20px;
      float: right;
    }

    #image {
      width: 30%;
      margin: 10px auto 30px auto;
    }

    #image img {
      width: 100%;
    }

    #title {
      width: 80%;
      color: white;
      margin: 0 auto;
      margin-top: 20px;
      background-color: rgb(79, 129, 189);
      overflow: hidden;
    }

    #title p {
      text-align: center;
      margin: 5px;
      color: white;
      padding: 0 10px;
      font-size: 20px;
    }

    #title p:first-child {
      margin-left: 10px;
      margin-right: 10px;
    }

    #sign {
      margin: 0 auto;
      margin-top: 50px;
      width: 80%;
      font-size: 20px;
    }

    #sign div {
      margin: 10px 0;
    }

    #suggest {
      width: 80%;
      margin: 0 auto;
      font-size: 20px;
    }

    #suggest h3 {
      font-size: 20px;
    }

    #suggest div {
      line-height: 1.5;
    }

    #tip {
      width: 80%;
      margin: 0 auto;
      font-size: 19px;
      margin-top: 20px;
    }

    #tip span {
      font-weight: bold;
    }

    #message {
      width: 80%;
      margin: 0 auto;
      margin-top: 20px;
      background-color: rgb(79, 129, 189);
      color: white;
    }

    #message p {
      padding-top: 5px;
      padding-left: 5px;
      padding-bottom: 5px;
      margin: 0;
    }

    #selfReport {
      width: 80%;
      margin: 0 auto;
      margin-top: 82px;
      overflow: hidden;
    }

    #selfReport h2 {
      text-align: center;
    }

    #selfReport #sum .sum {
      min-height: 86px;
      border: 1px solid black;
      padding: 0 10px;
    }

    #selfReport #sum .sum+.sum {
      border-top: 0;
    }

    #selfReport #sum .sum span.sumTip {
      display: inline-block;
      margin-left: 10px;
    }

    #selfResult {
      width: 80%;
      margin: 0 auto;
      margin-top: 80px;
      overflow: hidden;
    }

    #selfResult #resultTable {

      width: 100%;
    }

    #selfResult #resultTable thead th {
      width: 100px;
      text-align: left;
    }

    #clauseDetail {
      width: 80%;
      margin: 0 auto;
      margin-top: 80px;
      overflow: hidden;
    }

    .detailTable {
      width: 100%;
      table-layout: fixed;
    }

    #detailTable thead th {
      text-align: center;
    }

    .detailTable thead tr:first-child th:first-child {
      width: 80px;
    }

    .detailTable thead tr:last-child th {
      width: 40px;
    }

    .detailTable tbody th {
      text-align: left;
      font-weight: lighter;
    }

    #resultTable th,
    #resultTable td {
      text-indent: 10px;
      word-break: break-all;
      word-wrap: break-word;
      padding: 5px;
    }

    td {
      max-width: 80px;
      word-break: break-all;
      word-wrap: break-word;
    }

    .detailTable th {
      text-align: center;
    }

    .detailTable th.yes {
      word-break: break-all;
      word-wrap: break-word;
    }

    .overall-evaluation td {
      height: 100px;
      vertical-align: top;
    }

    .risk-analysis td {
      height: 25px;
      width: 4.1666667%;
      text-align: center;
    }

    .rectified-suggestions td {
      width: 4.1666667%;
    }

    .scoring-matrix td {
      padding-top: 20px;
      padding-bottom: 20px;
      text-align: center;
    }

    .border-table td,
    .border-table th {
      border-top: 1px solid #000;
      border-left: 1px solid #000;
    }

    .border-table td:last-child,
    .border-table th:last-child {
      border-right: 1px solid #000;
    }

    .border-table tr:last-child td,
    .border-table tr:last-child th {
      border-bottom: 1px solid #000;
    }

    .matrix td {
      font-size: 14px;
      text-align: center;
    }

    #request p {
      word-break: break-all;
      word-wrap: break-word;
    }

    .risk-title {
      font-weight: bold;
      margin-bottom: 10px;
    }

    #analysisTable th {
      background: #EFEFEF;
    }

    #analysisTable th,
    #analysisTable td {
      text-indent: 10px;
      word-break: break-all;
      word-wrap: break-word;
      padding: 5px;
    }
  </style>
</head>

<body>
  <div id="content">
    <div id="image">
      <img src="aud_sa_report_image/aud_sa_report_image.png" />
    </div>
    <div id="title">
      <p>《国际医院评审认证标准(中国)》（2021版）</p>
      <p>评审报告${title}</p>
    </div>
    <#compress>
      <div id="sign">
        <div>
          <span>医院名称:</span>
          <span>${hospitalName ! "-"}</span>
        </div>
        <div>
          <span>医院编码:</span>
          <span>${hospitalCode ! "-"}</span>
        </div>
        <div>
          <span>评审认证起止日期:</span>
          <span>${beginDate ! "-"}</span>
          <span>--</span>
          <span>${reviewCycleEnd ! "-"}</span>
        </div>
      </div>
    </#compress>
    <div style="height: 150px;"></div>
    <table id="tip">
      <thead>
        <tr></tr>
      </thead>
      <tbody>
        <td>
          《国际医院评审认证标准(中国)》（2021版）是获得国际医疗质量协会外
          部评审会( <span>International Society for Quality in Health
            Care External Evaluation Association，IEEA</span> )认证的标准。
        </td>
      </tbody>
    </table>
    <div id="message">
      <p>深圳市卫健医院评审评价研究中心</p>
      <p>邮箱：<EMAIL></p>
    </div>
    <div style="page-break-after: always;"></div>
    <#compress>
      <div id="suggest">
        <h3>一、评审结论（建议）：</h3>
        <div>
          <#if reviewResultsZh=='通过认证'>
            √通过认证<br />
            □有条件通过认证<br />
            □不通过认证<br />
          </#if>
          <#if reviewResultsZh=='有条件通过认证'>
            □通过认证<br />
            √有条件通过认证<br />
            □不通过认证<br />
          </#if>
          <#if reviewResultsZh=='不通过认证'>
            □通过认证<br />
            □有条件通过认证<br />
            √不通过认证<br />
          </#if>
        </div>
      </div>
    </#compress>
    <div style="width: 80%; margin: 0 auto;">
      <h3>二、评价概况</h3>
      <h3>（一）概述</h3>
      <table frame="void" style="width: 100%; table-layout: fixed;">
        <tr>
          <td style="word-break: break-word; word-wrap: break-word;">
            深圳市卫健医院评审评价研究中心（下简称为中心）于${submitDate}，收到了${hospitalName}的自评报告。中心依据评审规则，派出评审专家组于${reviewCycleBegin}—${reviewCycleEnd}对该院落实《国际医院评审认证标准（中国）》（2021版）的情况进行了现场评审。
          </td>
        </tr>
      </table>
      <h3>（二）评审方法</h3>
      <table frame="void" style="width: 100%; table-layout: fixed;">
        <tr>
          <td style="word-break: break-word; word-wrap: break-word;">
            此次现场评审由${reviewLeader}担任评审组长，评审专家组成员有${reviewer}。此次现场评审工作，评审团队首先分析了${hospitalName}的自评报告及其相关证明文件，并于约定日期，依据《国际医院评审认证标准（中国）》（2021版），本着公正、公平和透明原则，采取访谈员工、查看资料、数据核实，实地访视及追踪检查等方法，对该院实施现场进行为期三天的查验。
          </td>
        </tr>
      </table>
      <h3>（三）评价意见</h3>
      <table id="overallEvaluationTable" class="overall-evaluation border-table" cellpadding="0" cellspacing="0"
        border="0" frame="void" style="width: 100%; table-layout: fixed;">
        <tbody>
          <tr>
            <td>评审中发现亮点：${autAdvantage}</td>
          </tr>
          <tr>
            <td>评审中发现不足：${autEvaluate}</td>
          </tr>
          <tr>
            <td>对受评医院的整改建议：${autProposal}</td>
          </tr>
          <tr>
            <td>受评医院需考虑的改进机会：${autImprove}</td>
          </tr>
        </tbody>
      </table>
      <p>备注：“整改建议”为必须落实的事项；“改进机会”受评医院可结合自身情况自行决定是否采纳；</p>
      <h3>（四）评价结果汇总</h3>
      <div id="result" style="width: 100%;">
        <table id="resultTable" class="border-table" cellpadding="0" cellspacing="0" border="0" frame="void"
          style="width: 100%; table-layout: fixed; repeat-header: yes;">
          <tbody>
            <tr>
              <th style="border-bottom: none;"></th>
              <th colspan="2" style="text-indent: 0; border-bottom: none;">评价结果</th>
              <th style="text-indent: 0; border-bottom: none;">款数</th>
              <th style="text-indent: 0; border-bottom: none;">达标占比</th>
            </tr>
            <#list autSaAudReportListVo as autSaAudReport>
              <#if autSaAudReport.isBasic==0>
                <#list autSaAudReport.reportList as report>
                  <#if report.autSaAudResult !=6 && report.autSaAudResult !=8>
                    <tr class="report1">
                      <#if report_index==0>
                        <td rowspan="7" style="font-weight:bold;">基本款 <br />(共${autSaAudReport.sumCount}款)</td>
                      </#if>
                      <td colspan="2">
                        <#if report.autSaAudResult==1> 优秀 </#if>
                        <#if report.autSaAudResult==2> 良好 </#if>
                        <#if report.autSaAudResult==3> 达标 </#if>
                        <#if report.autSaAudResult==4> 部分达标 </#if>
                        <#if report.autSaAudResult==5> 不达标 </#if>
                        <#if report.autSaAudResult==7> 达标及以上 </#if>
                      </td>
                      <td>${report.count}款</td>
                      <td>${report.rate}%</td>
                    </tr>
                  </#if>
                </#list>
                <!-- 不适用结果统计封装到末尾 -->
                <#list autSaAudReport.reportList as report>
                  <#if report.autSaAudResult==6>
                    <tr class="report1">
                      <td colspan="2">
                        不适用
                      </td>
                      <td>${report.count}款</td>
                      <td>${report.rate}%</td>
                    </tr>
                  </#if>
                </#list>
              </#if>
              <#if autSaAudReport.isBasic==1>
                <#list autSaAudReport.reportList as report>
                  <#if report.autSaAudResult !=6>
                    <tr class="report1">
                      <#if report_index==0>
                        <td rowspan="32" style="font-weight: bold;">非基本款
                          <br />(共${autSaAudReport.sumCount}款)
                        </td>
                      </#if>
                      <td colspan="2">
                        <#if report.autSaAudResult==1> 优秀 </#if>
                        <#if report.autSaAudResult==2> 良好 </#if>
                        <#if report.autSaAudResult==3> 达标 </#if>
                        <#if report.autSaAudResult==4> 部分达标 </#if>
                        <#if report.autSaAudResult==5> 不达标 </#if>
                        <#if report.autSaAudResult==7> 达标及以上 </#if>
                        <#if report.autSaAudResult==8> 部分达标及以上 </#if>
                      </td>
                      <td>${report.count}款</td>
                      <td>${report.rate}%</td>
                    </tr>
                  </#if>
                </#list>
                <!-- “不适用”排在最后，前端排序 -->
                <#list autSaAudReport.reportList as report>
                  <#if report.autSaAudResult==6>
                    <tr class="report1">
                      <td colspan="2">
                        不适用
                      </td>
                      <td>${report.count}款</td>
                      <td>${report.rate}%</td>
                    </tr>
                  </#if>
                </#list>
              </#if>
              <#if autSaAudReport.isBasic==1>
                <#list autSaAudReport.chapterReportList as chapter>
                  <#list chapter.reportList as report>
                    <#if report.autSaAudResult !=6>
                      <tr class="report1">
                        <#if report_index==0>
                          <td rowspan="8">
                            第
                            <span>
                              <#if chapter.chapterId=='1'> 一 </#if>
                              <#if chapter.chapterId=='2'> 二 </#if>
                              <#if chapter.chapterId=='3'> 三 </#if>
                            </span>
                            章<br />(共${chapter.sumCount}款非基本款)
                          </td>
                        </#if>
                        <td>
                          <#if report.autSaAudResult==1> 优秀 </#if>
                          <#if report.autSaAudResult==2> 良好 </#if>
                          <#if report.autSaAudResult==3> 达标 </#if>
                          <#if report.autSaAudResult==4> 部分达标 </#if>
                          <#if report.autSaAudResult==5> 不达标 </#if>
                          <#if report.autSaAudResult==7> 达标及以上 </#if>
                          <#if report.autSaAudResult==8> 部分达标及以上 </#if>
                        </td>
                        <td>${report.count}款</td>
                        <td>${report.rate}%</td>
                      </tr>
                    </#if>
                  </#list>
                  <!-- “不适用”排在最后，前端排序 -->
                  <#list chapter.reportList as report>
                    <#if report.autSaAudResult==6>
                      <tr class="report1">
                        <#if report_index==0>
                          <td rowspan="8">
                            第
                            <span>
                              <#if chapter.chapterId=='1'> 一 </#if>
                              <#if chapter.chapterId=='2'> 二 </#if>
                              <#if chapter.chapterId=='3'> 三 </#if>
                            </span>
                            章(共${chapter.sumCount}款非基本款)
                          </td>
                        </#if>
                        <td>不适用</td>
                        <td>${report.count}款</td>
                        <td>${report.rate}%</td>
                      </tr>
                    </#if>
                  </#list>
                </#list>
              </#if>
              <#if autSaAudReport.isBasic==2>
                <#list autSaAudReport.reportList as report>
                  <#if report.autSaAudResult !=6>
                    <tr class="report1">
                      <#if report_index==0>
                        <td rowspan="7" style="border-bottom: 1px solid #000; font-weight: bold;">合计占比</td>
                      </#if>
                      <td colspan="2">
                        <#if report.autSaAudResult==1>优秀</#if>
                        <#if report.autSaAudResult==2>良好</#if>
                        <#if report.autSaAudResult==3>达标</#if>
                        <#if report.autSaAudResult==4>部分达标</#if>
                        <#if report.autSaAudResult==5>不达标</#if>
                        <#if report.autSaAudResult==7>达标及以上</#if>
                      </td>
                      <td>${report.count}款</td>
                      <td>${report.rate}%</td>
                    </tr>
                  </#if>
                </#list>
                <!-- “不适用”排在最后，前端排序 -->
                <#list autSaAudReport.reportList as report>
                  <#if report.autSaAudResult==6>
                    <tr class="report1">
                      <#if report_index==0>
                        <td rowspan="7" style="border-bottom: 1px solid #000; font-weight: bold;">合计占比</td>
                      </#if>
                      <td colspan="2">不适用</td>
                      <td>${report.count}款</td>
                      <td>${report.rate}%</td>
                    </tr>
                  </#if>
                </#list>
              </#if>
            </#list>
          </tbody>
        </table>
      </div>
      <h3>（五）风险分析</h3>
      <#if riskInfoList?? && (riskInfoList?size> 0)>
        <div class="risk-title">如有基本款不达标或部分达标时，评审员将对其进行风险评估。</div>
        <table id="analysisTable" class="risk-analysis border-table" cellpadding="0" cellspacing="0" border="0"
          frame="void" style="width: 100%; table-layout: fixed; repeat-header: yes;">
          <tbody>
            <tr>
              <th colspan="3" rowspan="3">基本款款号</th>
              <th colspan="3" rowspan="3">评价结果</th>
              <th colspan="6" rowspan="3">不足</th>
              <th colspan="8">风险等级</th>
            </tr>
            <tr>
              <th colspan="2">低风险</th>
              <th colspan="2">中风险</th>
              <th colspan="2">高风险</th>
              <th colspan="2">严重风险</th>
            </tr>
            <tr>
              <th colspan="1">1分</th>
              <th colspan="1">2分</th>
              <th colspan="1">3分</th>
              <th colspan="1">4分</th>
              <th colspan="2">6分</th>
              <th colspan="2">9分</th>
            </tr>
            <#list riskInfoList as report>
              <tr>
                <td colspan="3">★${report.clauseNo}</td>
                <td colspan="3">
                  <#if report.autResult=='4'>□不达标<br />√部分达标<br /></#if>
                  <#if report.autResult=='5'>√不达标<br />□部分达标<br /></#if>
                </td>
                <td colspan="6">${report.autEvaluate}</td>
                <td colspan="1">
                  <#if report.riskLevel=='1'>√</#if>
                </td>
                <td colspan="1">
                  <#if report.riskLevel=='2'>√</#if>
                </td>
                <td colspan="1">
                  <#if report.riskLevel=='3'>√</#if>
                </td>
                <td colspan="1">
                  <#if report.riskLevel=='4'>√</#if>
                </td>
                <td colspan="2">
                  <#if report.riskLevel=='6'>√</#if>
                </td>
                <td colspan="2">
                  <#if report.riskLevel=='9'>√</#if>
                </td>
              </tr>
            </#list>
          </tbody>
        </table>
        <#else>
          <div class="risk-title">该医院无基本款不达标或部分达标，不涉及风险分析。</div>
      </#if>
      <h3>三、评价结果：</h3>
      <h3>1.填写要求</h3>
      <div id="request">
        <p><span style="font-weight: bold;">①款号栏：</span>应与标准款号一致，并一一对应。</p>
        <p><span style="font-weight: bold;">②评价结果栏：</span>所有条款均应在“优秀”、“良好”、“达标”、“不达标”、“部分达标”、“不适用”六项中选择一项填入此栏，不得多选。</p>
        <p><span style="font-weight: bold;">③评分意见栏：</span></p>
        <p>如评为“优秀”，须填写“亮点”项；</p>
        <p>如评为“良好”或“达标”，选填“亮点”和“改进机会”项；</p>
        <p>如评为“部分达标”或“不达标” ，须填写“不足”与“整改建议”项；</p>
        <p>无论评价结果如何，如有“改进机会”都可填写。</p>
      </div>
      <h3>2.结果明细</h3>
      <#list chapterVosList as chapter>
        <h4>${chapter.chapterNo} ${chapter.chapter}</h4>
        <table id="clauseScoreTable" class="rectified-suggestions border-table" cellpadding="0" cellspacing="0"
          border="0" frame="void" style="width: 100%; table-layout: fixed; repeat-header: yes;">
          <thead>
            <tr>
              <th rowspan="3" style="text-indent: 0;width: 100px;">款号</th>
              <th rowspan="3" style="text-indent: 0;width: 80px;">自评结果</th>
              <th rowspan="3" style="text-indent: 0;width: 80px;">评审结果</th>
              <th colspan="4" style="text-indent: 0;">评分意见</th>
            </tr>
            <tr>
              <th>亮点</th>
              <th>不足</th>
              <th>整改建议</th>
              <th>改进机会</th>
            </tr>
            <tr>
              <td colspan="1">（“优秀”为必填，“良好”和“达标”时为选填）</td>
              <td colspan="2">（“部分达标”或“不达标”时必填）</td>
              <td colspan="1">（选填）</td>
            </tr>
          </thead>
          <tbody>
            <#list chapter.sectionVoList as section>
              <tr>
                <td colspan="7" style="font-weight: bold;">${section.sectionNo}${section.section}</td>
              </tr>
              <#list section.articleVoList as article>
                <tr>
                  <td colspan="7">${article.articleNo}${article.article}</td>
                </tr>
                <#list article.clauseVoList as clause>
                  <tr>
                    <td>
                      <#if clause.isStar=='1'>★</#if>${clause.clauseNo}
                    </td>
                    <td>
                      <#if clause.saClauseResult??>
                        <#if clause.saClauseResult=='5'>不达标</#if>
                        <#if clause.saClauseResult=='4'>部分达标</#if>
                        <#if clause.saClauseResult=='3'>达标</#if>
                        <#if clause.saClauseResult=='2'>良好</#if>
                        <#if clause.saClauseResult=='1'>优秀</#if>
                        <#if clause.saClauseResult=='6'>不适用</#if>
                        <#else>
                          -
                      </#if>
                    </td>
                    <td>
                      <#if clause.srClauseResult??>
                        <#if clause.srClauseResult=='5'>不达标</#if>
                        <#if clause.srClauseResult=='4'>部分达标</#if>
                        <#if clause.srClauseResult=='3'>达标</#if>
                        <#if clause.srClauseResult=='2'>良好</#if>
                        <#if clause.srClauseResult=='1'>优秀</#if>
                        <#if clause.srClauseResult=='6' || clause.srClauseResult=='9'>不适用</#if>
                        <#else>
                          -
                      </#if>
                    </td>
                    <td> ${clause.autAdvantage ! "-"} </td>
                    <td> ${clause.autEvaluate ! "-"} </td>
                    <td> ${clause.autProposal ! "-"} </td>
                    <td> ${clause.autImprove ! "-"} </td>
                  </tr>
                </#list>
              </#list>
            </#list>
          </tbody>
        </table>
      </#list>
    </div>
  </div>
</body>

</html>