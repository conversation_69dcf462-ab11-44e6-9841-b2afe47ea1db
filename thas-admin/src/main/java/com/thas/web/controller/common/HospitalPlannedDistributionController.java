package com.thas.web.controller.common;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.enums.BusinessType;
import com.thas.web.domain.HospitalPlannedDistribution;
import com.thas.web.service.IHospitalPlannedDistributionService;
import com.thas.common.utils.poi.ExcelUtil;
import com.thas.common.core.page.TableDataInfo;

/**
 * 医疗机构分配计划详情Controller
 *
 * <AUTHOR>
 * @date 2022-01-27
 */
@Api(value = "HospitalPlannedDistributionController",tags = "医疗机构分配计划详情Controller")
@RestController
@RequestMapping("/system/distribution")
public class HospitalPlannedDistributionController extends BaseController
{
    @Autowired
    private IHospitalPlannedDistributionService hospitalPlannedDistributionService;

    /**
     * 查询医疗机构分配计划详情列表
     */
    //@PreAuthorize("@ss.hasPermi('system:distribution:list')")
    @GetMapping("/list")
    public TableDataInfo list(HospitalPlannedDistribution hospitalPlannedDistribution)
    {
        startPage();
        List<HospitalPlannedDistribution> list = hospitalPlannedDistributionService.selectHospitalPlannedDistributionList(hospitalPlannedDistribution);
        return getDataTable(list);
    }

    /**
     * 导出医疗机构分配计划详情列表
     */
    //@PreAuthorize("@ss.hasPermi('system:distribution:export')")
    @Log(title = "医疗机构分配计划详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HospitalPlannedDistribution hospitalPlannedDistribution)
    {
        List<HospitalPlannedDistribution> list = hospitalPlannedDistributionService.selectHospitalPlannedDistributionList(hospitalPlannedDistribution);
        ExcelUtil<HospitalPlannedDistribution> util = new ExcelUtil<HospitalPlannedDistribution>(HospitalPlannedDistribution.class);
        util.exportExcel(response, list, "医疗机构分配计划详情数据");
    }

    /**
     * 获取医疗机构分配计划详情详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:distribution:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(hospitalPlannedDistributionService.selectHospitalPlannedDistributionById(id));
    }

    /**
     * 新增医疗机构分配计划详情
     */
    //@PreAuthorize("@ss.hasPermi('system:distribution:add')")
    @Log(title = "医疗机构分配计划详情", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HospitalPlannedDistribution hospitalPlannedDistribution)
    {
        return toAjax(hospitalPlannedDistributionService.insertHospitalPlannedDistribution(hospitalPlannedDistribution));
    }

    /**
     * 修改医疗机构分配计划详情
     */
    //@PreAuthorize("@ss.hasPermi('system:distribution:edit')")
    @ApiOperation("修改医疗机构分配计划详情")
    @Log(title = "医疗机构分配计划详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HospitalPlannedDistribution hospitalPlannedDistribution)
    {
        return toAjax(hospitalPlannedDistributionService.updateHospitalPlannedDistribution(hospitalPlannedDistribution));
    }

    /**
     * 删除医疗机构分配计划详情
     */
    //@PreAuthorize("@ss.hasPermi('system:distribution:remove')")
    @Log(title = "医疗机构分配计划详情", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(hospitalPlannedDistributionService.deleteHospitalPlannedDistributionByIds(ids));
    }

    /**
     * 提交审核结果
     */
    @ApiOperation("提交审核结果")
    @Log(title = "医疗机构分配计划详情", businessType = BusinessType.UPDATE)
    @PutMapping("/submitAuditResult")
    public AjaxResult submitAuditResult(@RequestBody HospitalPlannedDistribution hospitalPlannedDistribution)
    {
        return toAjax(hospitalPlannedDistributionService.submitAuditResult(hospitalPlannedDistribution));
    }


    /**
     * 根据类型查询评审计划信息
     */
    @ApiOperation("根据类型查询评审计划信息")
    @GetMapping(value = "/selectPlannedDistributionInfoByType")
    public AjaxResult selectPlannedDistributionInfoByType(String type) {
        return AjaxResult.success(hospitalPlannedDistributionService.selectPlannedDistributionInfoByType(type));
    }

}
