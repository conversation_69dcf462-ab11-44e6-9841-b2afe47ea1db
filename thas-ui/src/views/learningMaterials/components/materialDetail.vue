<template>
  <div>
    <el-form ref="elForm" :model="formData" size="medium" label-width="100px">
      <el-form-item label="资源标题" prop="title">
        <span>{{ formData.title }}</span>
      </el-form-item>
      <el-form-item label="资源类型" prop="type">
        <span>{{ studyType(formData.type) }}</span>
      </el-form-item>
      <el-form-item label="资源类别" prop="category">
        <span>{{ studyCategory(formData.category) }}</span>
      </el-form-item>
      <el-form-item label="资源状态" prop="status">
        <span>{{ studyStatus(formData.status) }}</span>
      </el-form-item>
      <el-form-item label="资源封面" prop="resourceCoverAddress">
        <!-- <span>{{ formData.resourceCoverAddress }}</span> -->
        <el-image style="width: 100px;" :src="formData.resourceCoverAddress" :preview-src-list="[formData.resourceCoverAddress]" fit="cover"></el-image>
      </el-form-item>
      <el-form-item v-if="formData.type == 1" label="上传视频" prop="resourceAddress">
        <!-- <span>{{ formData.resourceAddress }}</span> -->
        <video :src="formData.resourceAddress" controls></video>
      </el-form-item>
      <el-form-item v-if="formData.type == 2" label="内容描述" prop="contentDescription">
        <editor v-model.trim="formData.contentDescription" :readOnly="true" :minHeight="200"></editor>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
export default {
  components: {},
  props: {
    formData: {
      type: Object,
      default: () => ({
        title: "",
        type: "",
        category: "",
        resourceCoverAddress: "",
        resourceAddress: "",
        contentDescription: "",
        content: "",
        status: 1,
      }),
    },
  },
  data() {
    return {
      studyResourceStatusOptions: [],
      typeOptions: [],
      categoryOptions: [],
    }
  },
  computed: {
    studyStatus() {
      return (value) => {
        const status = this.studyResourceStatusOptions.find(
          (status) => status.dictValue == value
        );
        return status ? status.dictLabel : value;
      };
    },
    studyType() {
      return (value) => {
        const type = this.typeOptions.find(
          (type) => type.dictValue == value
        );
        return type ? type.dictLabel : value;
      };
    },
    studyCategory() {
      return (value) => {
        const category = this.categoryOptions.find(
          (category) => category.dictValue == value
        );
        return category ? category.dictLabel : value;
      };
    },
  },
  watch: {},
  created() {
    this.initDicts()
  },
  methods: {
    initDicts() {
      this.getDicts("study_type").then((response) => {
        this.typeOptions = response.data;
      });
      this.getDicts("study_category").then((response) => {
        this.categoryOptions = response.data;
      });
      this.getDicts("study_resource_status").then((response) => {
        this.studyResourceStatusOptions = response.data;
      });
    },
  },
};
</script>
<style>
</style>
