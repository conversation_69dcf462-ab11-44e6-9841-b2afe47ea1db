<template>
  <div class="detail-message">
    <el-card :shadow='form.shadow || `always`'>
      <div slot="header" class="clearfix" v-if="form.title">
        <span>{{ form.title }}</span>
        <span style="float: right;">
          <el-button v-if="showEdit && !edit" type="text" size="mini" @click="editChange(true)">修改</el-button>
          <el-button v-if="showEdit && edit" type="text" size="mini" @click="editChange(false, true)">保存</el-button>
          <el-button v-if="showEdit && edit" type="text" size="mini" @click="editChange(false, false)">取消</el-button>
        </span>
      </div>
      <el-form v-if="showDefault && useDefault" :model="form" inline :label-width="form.labelWidth || '140px'"
        :class="{ avatar: form.hasAvatar }">
        <el-row :gutter="24">
          <el-col v-for="(field, index) in list" :key="index" :span="field.span || span">
            <el-form-item :label="`${field.label}${field.label ? '：' : ' '}`" :label-width="field.labelWidth || ``"
              :class="[field.clz, form.clz]" :style="field.itemStyle">
              <slot v-if="field.slot" :name="field.slot" :data='field.value'></slot>
              <span v-else-if="field.type == 'text'" :style="field.style">{{ field.value }} {{ field.unit }}</span>
              <dict-span v-else-if="field.type == 'dict'" :dictType="field.dict" :value="field.value"
                :otherValue='field.otherValue'></dict-span>
              <el-image v-else-if="field.type == 'image'" style="width: 100px; height: 100px;border:1px solid #ebeef5"
                :src="field.value" :preview-src-list="[field.value]">
              </el-image>
              <span v-else-if="field.type == 'images'">
                <el-image v-for="(file, index) in field.value" :key="index" :src="file.url" :preview-src-list="[file.url]"
                  style="width: 100px; height: 100px;margin-left:5px;border:1px solid #ebeef5">
                </el-image>
              </span>
              <a v-else-if="field.type == 'file'" @click="previewFile(field.url)">{{ field.value }}</a>
            </el-form-item>
          </el-col>
          <span v-if="list.length == 0"> 暂无数据</span>
        </el-row>
      </el-form>

      <div v-if="showDefault && !useDefault">
        <slot name="useOther"></slot>
      </div>

      <div v-if="form.hasAvatar" class="avatar">
        <el-image style="width: 100px; height: 100px" :src="form.avatar" :preview-src-list="[form.avatar]">
        </el-image>
      </div>

      <div v-if="!showDefault" class="modify">
        <slot name="table"></slot>
      </div>
    </el-card>
  </div>
</template>

<script>
import { download } from "@/utils/request";
import DictSpan from './dictSpan.vue'

const defaultForm = {
  title: "测试标题",
  line: 2,
  hasAvatar: false,
  avatar: "",
  list: [
    {
      label: "士大夫",
      value: "但是",
      type: "text", // text文本 image图片
      slot: "text",
    },
  ],
};

export default {
  name: "DetailMessage",
  props: {
    form: {
      type: Object,
      default: () => defaultForm,
    },
    showEdit: {
      type: Boolean,
      default: () => false
    },
    useDefault: {
      type: Boolean,
      default: () => true
    },
  },
  watch: {
    form: {
      immediate: true,
      deep: true,
      handler() {
        document.getElementsByTagName('body')[0].style.setProperty('fieldLabelWidth', this.form.labelWidth);
      },
    }
  },
  components: {
    DictSpan
  },
  data() {
    return {
      edit: false,
      showDefault: true,
    };
  },
  computed: {
    span() {
      return !this.form.line ? 12 : 24 / parseInt(this.form.line);
    },
    list() {
      return (this.form.list || []).filter(li => !li.hidden)
    },
  },
  methods: {
    download(id, fileName) {
      download("/common/downloadFile", { fileId: id }, fileName);
    },

    previewFile(url) {
      url && window.open(url, '_blank')
    },

    async editChange(status, saved) {
      if (saved) {
        this.$emit('handelSave', this)
      }
      if (!saved) {
        this.hide();
      }
      if (status) {
        this.$set(this, 'edit', status);
        this.$set(this, 'showDefault', !status);
        return
      }

    },

    hide() {
      this.$set(this, 'edit', false);
      this.$set(this, 'showDefault', true);
    },


  },
};
</script>
<style lang='scss' scoped>
.detail-message {
  // padding: 0 10px;
  // margin-top: 10px;
  margin-bottom: 10px;

  ::v-deep .el-card {
    border: 0;

    .el-card__header {
      padding-bottom: 15px;
    }

    .el-card__body {
      padding-bottom: 0;

      .el-form-item {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 10px;
        //
      }

      .modify .el-form-item {
        margin-bottom: 22px;
      }

      .el-form-item.wordBreak {
        word-break: keep-all;
      }

      .el-form-item.long {
        .el-form-item__label {
          line-height: 18px;
        }
      }

      .el-form-item__label {
        align-self: self-start;
        flex-shrink: 0;
      }

      .el-form-item__content {
        span {
          color: black;
          display: inline-block;
          word-break: break-word;
          line-height: 36px;
          vertical-align: initial;
        }

        a {
          color: #409eff;
        }
      }
    }
  }

  .avatar {
    display: inline-block;
    vertical-align: top;
  }
}
</style>
