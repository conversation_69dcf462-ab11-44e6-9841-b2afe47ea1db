<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.TraAnswerSheetMapper">

    <resultMap type="TraAnswerSheet" id="TraAnswerSheetResult">
        <result property="id" column="id"/>
        <result property="examPaperId" column="examPaperId"/>
        <result property="score" column="score"/>
        <result property="status" column="status"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateId" column="update_id"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTraAnswerSheetVo">
        select id,
               exam_paper_id,
               score,
               status,
               create_id,
               create_time,
               update_id,
               update_time
        from tra_answer_sheet
    </sql>

    <select id="selectTraAnswerSheetList" parameterType="TraAnswerSheet" resultMap="TraAnswerDetailResult">
        SELECT
        tas.id id,
        tas.exam_paper_id examPaperId,
        tep.paper_name examPaperName,
        tlr.id learnResourceId,
        tlr.title learnResourceName,
        tas.score score,
        tas.STATUS STATUS,
        tas.create_id createId,
        tas.create_time createTime
        FROM
        tra_answer_sheet tas
        LEFT JOIN tra_exam_paper tep ON tep.id = tas.exam_paper_id
        LEFT JOIN tra_learn_resource tlr ON tlr.id = tep.learn_resource_id
        <where>
            <if test="examPaperId != null ">and tas.exam_paper_id = #{examPaperId}</if>
            <if test="score != null  and score != ''">and tas.score = #{score}</if>
            <if test="status != null ">and tas.status = #{status}</if>
            <if test="createId != null  and createId != ''">and tas.create_id = #{createId}</if>
            <if test="updateId != null  and updateId != ''">and tas.update_id = #{updateId}</if>
            <if test="createTime != null ">
                and date_format(tas.create_time,'%Y-%m-%d %H:%i:%s') &gt;= date_format(#{createTime},'%Y-%m-%d %H:%i:%s')
            </if>
        </where>
        order by tas.create_time desc
    </select>

    <resultMap type="com.thas.web.domain.vo.TraAnswerSheetVO" id="TraAnswerDetailResult">
        <result property="id" column="id"/>
        <result property="examPaperId" column="examPaperId"/>
        <result property="examPaperName" column="examPaperName"/>
        <result property="learnResourceId" column="learnResourceId"/>
        <result property="learnResourceName" column="learnResourceName"/>
        <result property="paperDiscription" column="paperDiscription"/>
        <result property="totalScore" column="totalScore"/>
        <result property="score" column="score"/>
        <result property="status" column="status"/>
        <result property="createId" column="createId"/>
        <result property="createTime" column="createTime"/>
        <collection property="examDetails" ofType="com.thas.web.domain.vo.TraAnswerDetailsVO">
            <result property="examQuestion" column="examQuestion"/>
            <result property="score" column="examScore"/>
            <result property="type" column="examType"/>
            <result property="questionOptions" column="questionOptions"/>
            <result property="correctAnswer" column="correctAnswer"/>
            <result property="answer" column="answer"/>
            <result property="correct" column="correct"/>
        </collection>
    </resultMap>

    <select id="selectTraAnswerSheetById" parameterType="Long" resultMap="TraAnswerDetailResult">
        SELECT tas.id                id,
               tep.id                examPaperId,
               tep.paper_name        examPaperName,
               tlr.id                learnResourceId,
               tlr.title             learnResourceName,
               tep.total_score       totalScore,
               tep.paper_discription paperDiscription,
               tas.score             score,
               tas.status            status,
               tas.create_id         createId,
               tas.create_time       createTime,
               ted.exam_question     examQuestion,
               ted.score             examScore,
               ted.type              examType,
               ted.question_options  questionOptions,
               ted.correct_answer    correctAnswer,
               tad.id                answerId,
               tad.answer_sheet_id   answerSheetId,
               tad.exam_details_id   examDetailsId,
               tad.answer            answer,
               tad.score             answerScore,
               tad.correct           correct
        FROM tra_answer_sheet tas
                 LEFT JOIN tra_exam_paper tep ON tep.id = tas.exam_paper_id
                 LEFT JOIN tra_learn_resource tlr ON tlr.id = tep.learn_resource_id
                 LEFT JOIN tra_exam_details ted ON ted.exam_paper_id = tep.id
                 LEFT JOIN tra_answer_details tad ON tad.answer_sheet_id = tas.id and tad.exam_details_id = ted.id
        where tas.id = #{id}
    </select>

    <select id="selectRespondentsNum" resultType="java.lang.Long">
        SELECT count(*)
        FROM (SELECT count(create_id) count
              FROM tra_answer_sheet tas
              WHERE tas.exam_paper_id IN (SELECT id FROM tra_exam_paper WHERE learn_resource_id = #{id})
              GROUP BY tas.create_id) t
    </select>

    <select id="selectHighestScoreHistory" resultType="java.lang.Long">
        SELECT score
        FROM tra_answer_sheet tas
        WHERE tas.exam_paper_id IN (SELECT id FROM tra_exam_paper WHERE learn_resource_id = #{id})
        AND tas.create_id = #{username}
        ORDER BY score desc LIMIT 1
    </select>
    <select id="selectCountAnswerCurrent" resultType="java.lang.Long">
        SELECT count(1)
        FROM tra_answer_sheet tas
        WHERE tas.exam_paper_id IN (SELECT id FROM tra_exam_paper WHERE learn_resource_id = #{id})
          and tas.create_id = #{username}
    </select>

    <insert id="insertTraAnswerSheet" parameterType="TraAnswerSheet" useGeneratedKeys="true" keyProperty="id">
        insert into tra_answer_sheet
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="examPaperId != null">exam_paper_id,</if>
            <if test="score != null">score,</if>
            <if test="status != null">status,</if>
            <if test="createId != null and createId != ''">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null and updateId != ''">update_id,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="examPaperId != null">#{examPaperId},</if>
            <if test="score != null">#{score},</if>
            <if test="status != null">#{status},</if>
            <if test="createId != null and createId != ''">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null and updateId != ''">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateTraAnswerSheet" parameterType="TraAnswerSheet">
        update tra_answer_sheet
        <trim prefix="SET" suffixOverrides=",">
            <if test="examPaperId != null">exam_paper_id = #{examPaperId},</if>
            <if test="score != null">score = #{score},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createId != null and createId != ''">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null and updateId != ''">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTraAnswerSheetById" parameterType="Long">
        delete
        from tra_answer_sheet
        where id = #{id}
    </delete>

    <delete id="deleteTraAnswerSheetByIds" parameterType="String">
        delete from tra_answer_sheet where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>