<!--  -->
<template>
  <div class="reviewer-certification-application">
    <el-steps :active="active" finish-status="success">
      <el-step title="基本信息"></el-step>
      <el-step title="经历与资质"></el-step>
      <!-- <el-step title="理论培训"></el-step>
      <el-step title="实践培训"></el-step> -->
      <el-step title="提交审核"></el-step>
      <el-step title="审核结果"></el-step>
    </el-steps>

    <keep-alive>
      <component :key="active" :is="components[active]" :ref="components[active]" :refName="components[active]" :info="getInfo" :detail="detail" @reWrite="reWrite"></component>
    </keep-alive>
    <!-- <div v-if="active === 3" style="padding: 24px;">
      <el-form :model="skipForm" :rules="rules" ref="skipForm">
        <el-form-item label="是否申请豁免参与实践培训" prop="appSkip">
          <el-radio-group v-model="skipForm.appSkip">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="skipForm.appSkip === 1" label="豁免申请" prop="appSkipDesc">
          <el-input v-model="skipForm.appSkipDesc" type="textarea" maxlength="500" style="max-width: 600px;"></el-input>
        </el-form-item>
      </el-form>
    </div> -->
    <!-- <div class="btn">
      <el-button v-if="active === 3" type="primary" @click="prev" style="margin: 0 auto">
        上一步
      </el-button>
      <el-button v-if="active < 4" type="primary" @click="next" style="margin: 0 auto" v-loading="loading">
        下一步
      </el-button>
    </div> -->
  </div>
</template>

<script>
import request from "@/utils/request";

// import Theory from "./components/theory.vue";
import Audit from "./components/audit.vue";
import Pass from "./components/pass.vue";
// import Practice from "./components/practice.vue";

export default {
  name: "ReviewerProgress",
  components: {
    // Theory,
    Audit,
    Pass,
    // Practice,
  },
  props: {},
  watch: {},
  data() {
    return {
      active: 2,
      components: ["", "", "Audit", "Pass"],
      loading: false,
      detail: {},
      skipForm: {
        appSkip: null,
        appSkipDesc: "",
      },
      rules: {
        appSkip: [
          {
            required: true,
            message: "请选择是否申请豁免参与实践培训",
            trigger: "change",
          },
        ],
        appSkipDesc: [
          { required: true, message: "请输入豁免申请", trigger: "blur" },
        ],
      },
    };
  },

  computed: {
    getInfo() {
      if (this.detail && this.detail.reviewerBaseInfo) {
        return {
          ...this.detail,
          ...this.detail.reviewerBaseInfo,
        };
      }
      return this.detail && this.detail.reviewerBaseInfo || {};
    },
  },

  created() {
    this.getDetail();
  },


  methods: {
    reWrite() {
      this.active = 2;
    },

    getDetail() {
      request({
        url: "/reviewer/query/reviewer/detail",
        method: "post",
        data: {
          commonId: this.$store.getters.userId,
        },
      }).then((res) => {
        this.detail = res || {};
        let { authStatus } = res.reviewerBaseInfo || {};


        if (!authStatus) {
          this.active = 2;
        } else if (authStatus == 1 || authStatus == 2) {
          this.active = 3;
        }
      });
    },
    async next() {
      // let ref = this.$refs[this.components[this.active]];
      // try {
      //   this.loading = true;
      // let value = await ref.getValue();
      // if (this.active === 3) {
      //   this.$refs.skipForm.validate((valid) => {
      //     if (valid) {
      //       this.active++;
      //     } else {
      //       return false;
      //     }
      //   });
      // } else {
      //   if (value) {
      //     this.active++;
      //   }
      // }
      // if (this.active === 2) {
      //   await this.finalSubmit();
      //   this.getDetail();
      // }
      // } catch (error) {
      //   // console.log(error);
      // } finally {
      //   this.loading = false;
      // }
    },
    prev() {
      this.active--;
    },

    finalSubmit() {
      let _data = {
        reviewerBaseInfo: {
          appSkip: this.skipForm.appSkip,
          accountId: this.detail.reviewerBaseInfo.accountId,
          submitStatus: 1,
        },
        autStatus: this.detail.autStatus == 2 ? 0 : undefined,
      };
      if (this.skipForm.appSkip === 1) {
        _data.reviewerBaseInfo.appSkipDesc = this.skipForm.appSkipDesc;
      }
      return request({
        url: "/reviewer/base/info/submit",
        method: "post",
        data: _data,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.reviewer-certification-application {
  padding-top: 40px;
  padding-bottom: 10px;
  background-color: #fff;
  min-height: inherit;
  ::v-deep .el-steps {
    width: 70%;
    margin: 0 auto 40px auto;
    .el-step__icon {
      width: 40px;
      height: 40px;
    }
    .el-step__head.is-process {
      color: #357cf4;
      border-color: #357cf4;
      & + .el-step__main .el-step__title {
        color: #357cf4;
      }
    }
    .el-step__head.is-success {
      color: #357cf4;
      border-color: #357cf4;
      & + .el-step__main .el-step__title {
        color: #357cf4;
      }
      .el-icon-check {
        font-weight: bold;
        font-size: 20px;
        color: white;
      }
      .el-step__icon.is-text {
        background-color: #357cf4;
      }
    }
    .el-step.is-horizontal .el-step__line {
      top: 19px;
    }
  }
  .btn {
    display: flex;
    justify-content: center;
    ::v-deep .el-button {
      align-self: center;
    }
  }
}
</style>
