<template>
  <el-card v-loading="loading" class="paper-list">
    <el-row :gutter="24">
      <el-col :span="24" :xs="24">
        <el-form ref="queryForm" :model="queryData" size="small" label-width="80px" inline>
          <el-form-item label="考卷名称" prop="paperName">
            <el-input v-model.trim="queryData.paperName" maxlength="128" placeholder="请输入考卷名称" clearable :style="{ width: '100%' }"></el-input>
          </el-form-item>
          <el-form-item label="归属课程" prop="learnResourceTitle">
            <el-input v-model.trim="queryData.learnResourceTitle" maxlength="128" placeholder="请输入课程名称" clearable :style="{ width: '100%' }"></el-input>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model.trim="queryData.status" placeholder="请选择状态" clearable filterable>
              <el-option v-for="statusItem in paperStatusOptions" :key="statusItem.dictValue" :value="statusItem.dictValue" :label="statusItem.dictLabel"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="search">
              查询
            </el-button>
            <el-button type="primary" size="small" @click="resetForm">
              重 置
            </el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="toAddPaper">添加</el-button>
        <el-button icon="el-icon-refresh" size="small" @click="query">刷新</el-button>
      </el-col>
    </el-row>
    <el-row :gutter="24" style="margin-top: 24px;">
      <el-col :span="24">
        <el-table :data="dataSource" border>
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button v-if="scope.row.status === 0" type="text" @click="edit(scope.row)">编辑考卷</el-button>
              <el-button v-if="scope.row.status === 0" type="text" @click="publish(scope.row)">发布</el-button>
              <el-button v-else-if="scope.row.status === 1" type="text" @click="publish(scope.row)">停止</el-button>
              <el-button type="text" @click="del(scope.row)">删除</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" label="考卷名称" prop="paperName"></el-table-column>
          <el-table-column align="center" label="归属课程" prop="learnResourceTitle"></el-table-column>
          <el-table-column align="center" label="答卷" prop="answerSheetNum"></el-table-column>
          <el-table-column align="center" label="状态" prop="status">
            <template slot-scope="scope">
              {{ status(`${scope.row.status}`) }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="创建时间" prop="createdTime"></el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query" />
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
import request from "@/utils/request"
export default {
  name: "PaperList",
  data() {
    return {
      dataSource: [],
      queryData: {
        paperName: "",
        learnResourceTitle: null,
        status: null,
        pageSize: 10,
        pageNum: 0
      },
      total: 0,
      paperStatusOptions: [],
      addForm: {
        name: '',
        content: '',
        type: null,
        sendEmail: null,
        status: null,
        role: null
      },
      props: { multiple: true },
      loading: false
    }
  },
  computed: {
    status() {
      return (value) => {
        const status = this.paperStatusOptions.find(
          (status) => status.dictValue == value
        )
        return status ? status.dictLabel : value
      }
    },
    learnResourceTitle() {
      return (value) => {
        const type = this.noticeTemplateTypeOptions.find(
          (type) => type.dictValue == value
        )
        return type ? type.dictLabel : value
      }
    }
  },
  created() {
    this.initDicts()
    this.query()
  },
  methods: {
    initDicts() {
      this.getDicts("paper_status").then((response) => {
        this.paperStatusOptions = response.data
      })
    },
    search() {
      this.queryData.pageNum = 1
      this.query()
    },
    query() {
      this.loading = true
      request({
        url: '/system/paper/list',
        method: "get",
        params: this.queryData,
      }).then((res) => {
        this.loading = false
        if (res.code != 200) return
        this.dataSource = res.rows
        this.total = res.total
      }).catch((error) => {
        // console.log(error)
        this.loading = false
      })
    },
    resetForm() {
      this.$refs["queryForm"].resetFields()
      this.queryData.pageNum = 1
      this.query()
    },
    edit(data) {
      this.$router.push({
        path: '/learningMaterials/paperManage/editPaper',
        query: {
          id: data.id
        }
      })
    },
    publish(data) {
      const content = data.status === 0 ? '确定发布此考卷？' : data.status === 1 ? '此操作将停止发布该考卷, 是否继续？' : ''
      this.$confirm(content, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let _data = JSON.parse(JSON.stringify(data))
        let message = '发布成功！'
        if (data.status === 0) {
          _data.status = 1
        } else if (data.status === 1) {
          _data.status = 0
          message = '停止成功！'
        }
        request({
          url: '/system/paper/status',
          method: 'get',
          params: _data,
        }).then((res) => {
          if (res.code != 200) {
            this.$message({
              type: 'error',
              message: res.message || '系统错误，请稍后再试。'
            })
          } else {
            this.$message({
              type: 'success',
              message
            })
            this.query()
          }
        })
      }).catch(() => { })
    },
    del(data) {
      this.$confirm('此操作将永久删除该考卷, 是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        request({
          url: '/system/paper/' + data.id,
          method: 'delete'
        }).then((res) => {
          if (res.code !== 200) {
            this.$message({
              type: 'success',
              message: res.message || '系统错误，请稍后再试。'
            })
            return
          }
          this.$message({
            type: 'success',
            message: '删除成功！'
          })
          this.query()
        })
      }).catch(() => { })
    },
    toAddPaper() {
      this.$router.push({ path: '/learningMaterials/paperManage/addPaper' })
    }
  }
}
</script>

<style lang='scss' scoped>
.paper-list {
  padding: 24px;
  ::v-deep .el-dialog {
    .el-dialog__header {
      background-color: #3c81f5;
      padding-top: 10px;
      .el-dialog__title {
        color: white;
      }
      .el-dialog__headerbtn {
        top: 15px;
        .el-dialog__close {
          color: white;
        }
      }
    }
    .el-dialog__body {
      padding-bottom: 0;
      & > span {
        color: black;
      }
    }
  }
}
</style>
