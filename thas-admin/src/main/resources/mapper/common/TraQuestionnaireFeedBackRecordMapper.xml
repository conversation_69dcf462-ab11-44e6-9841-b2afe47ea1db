<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.TraQuestionnaireFeedBackRecordMapper">
    
    <resultMap type="TraQuestionnaireFeedBackRecord" id="TraQuestionnaireFeedBackRecordResult">
        <result property="recordId"    column="record_id"    />
        <result property="resultId"    column="result_id"    />
        <result property="needId"    column="need_id"    />
        <result property="autCode"    column="aut_code"    />
        <result property="feedBackType"    column="feed_back_type"    />
        <result property="questionnaireId"    column="questionnaire_id"    />
        <result property="fillStatus"    column="fill_status"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTraQuestionnaireFeedBackRecordVo">
        select record_id, result_id, need_id, aut_code, feed_back_type, questionnaire_id, fill_status, status, create_by, create_time, update_by, update_time from tra_questionnaire_feed_back_record
    </sql>

    <sql id="SQL_AES_ENCRYPT">'${@com.thas.web.config.SqlAesEncryptConfig@SQL_AES_ENCRYPT}'</sql>

    <select id="selectTraQuestionnaireFeedBackRecordList" parameterType="TraQuestionnaireFeedBackRecord" resultMap="TraQuestionnaireFeedBackRecordResult">
        <include refid="selectTraQuestionnaireFeedBackRecordVo"/>
        <where>  
            <if test="resultId != null "> and result_id = #{resultId}</if>
            <if test="needId != null  and needId != ''"> and need_id = #{needId}</if>
            <if test="autCode != null  and autCode != ''"> and aut_code = #{autCode}</if>
            <if test="feedBackType != null "> and feed_back_type = #{feedBackType}</if>
            <if test="questionnaireId != null "> and questionnaire_id = #{questionnaireId}</if>
            <if test="fillStatus != null  and fillStatus != ''"> and fill_status = #{fillStatus}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
        ORDER BY create_time DESC
    </select>
    
    <select id="selectTraQuestionnaireFeedBackRecordByRecordId" parameterType="Long" resultMap="TraQuestionnaireFeedBackRecordResult">
        <include refid="selectTraQuestionnaireFeedBackRecordVo"/>
        where record_id = #{recordId}
    </select>

    <select id="selectTraQuestionnaireFeedBackRecordListByTimeASC" parameterType="TraQuestionnaireFeedBackRecord" resultMap="TraQuestionnaireFeedBackRecordResult">
        <include refid="selectTraQuestionnaireFeedBackRecordVo"/>
        <where>
            <if test="resultId != null "> and result_id = #{resultId}</if>
            <if test="needId != null  and needId != ''"> and need_id = #{needId}</if>
            <if test="autCode != null  and autCode != ''"> and aut_code = #{autCode}</if>
            <if test="feedBackType != null "> and feed_back_type = #{feedBackType}</if>
            <if test="questionnaireId != null "> and questionnaire_id = #{questionnaireId}</if>
            <if test="fillStatus != null  and fillStatus != ''"> and fill_status = #{fillStatus}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
        ORDER BY create_time ASC
    </select>

    <select id="selectTrainTeacherStatisticsReport" resultType="com.thas.web.domain.StatisticsReportVo">
        SELECT sur.user_id userId,
        AES_DECRYPT(unhex(su.nick_name),<include refid="SQL_AES_ENCRYPT"/>) reviewerName,
        tqfbr.feed_back_type feedBackType
        FROM sys_user_role sur
        LEFT JOIN sys_user su ON sur.user_id = su.user_id
        LEFT JOIN tra_questionnaire_feed_back_record tqfbr ON tqfbr.need_id = sur.user_id
        WHERE
        sur.role_id in('101') AND
        su.status = 0
        ORDER BY su.create_time DESC
    </select>

    <select id="selectTraQuestionnaireFeedBackRecordIdsBySendCondition" resultType="java.lang.String">
       SELECT DISTINCT tqfbr.need_id FROM tra_questionnaire_feed_back_record tqfbr
       LEFT JOIN sys_user_role sur ON tqfbr.need_id = sur.user_id
       WHERE
       tqfbr.need_id IN
        <foreach item="item" collection="needIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
       AND tqfbr.status = 1
       AND tqfbr.feed_back_type = 5
       AND tqfbr.questionnaire_id = #{questionnaireId}
       AND
       (
       sur.role_id IN(101,106) or
       (sur.role_id = 107 AND tqfbr.fill_status = 0)
       )
    </select>

    <select id="selectTraQuestionnaireFeedBackRecordListByTrainerCondition" resultType="java.lang.String">
       SELECT DISTINCT AES_DECRYPT(unhex(su.nick_name),<include refid="SQL_AES_ENCRYPT"/>) nick_name FROM tra_questionnaire_feed_back_record tqfbr
       LEFT JOIN sys_user su ON su.user_id = tqfbr.need_id
       WHERE
       tqfbr.fill_status = 0
       AND tqfbr.`status` = 1
       AND tqfbr.feed_back_type = 7
       AND tqfbr.result_id IN
        <foreach item="item" collection="resultIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
       AND tqfbr.need_id IN
        <foreach item="item" collection="needIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectTraQuestionnaireFeedBackRecordAndUserInfoList" parameterType="TraQuestionnaireFeedBackRecord" resultType="com.thas.web.domain.SendDetailRes">
        SELECT tqfbr.feed_back_type feedBackType , tqfbr.create_time createTime , tqfbr.need_id needId,
        AES_DECRYPT(unhex(su.nick_name),<include refid="SQL_AES_ENCRYPT"/>) needName,
        tqfbr.fill_status fillStatus
        FROM tra_questionnaire_feed_back_record tqfbr
        LEFT JOIN sys_user su ON su.user_id = tqfbr.need_id
        <where>
            <if test="resultId != null "> and tqfbr.result_id = #{resultId}</if>
            <if test="needId != null  and needId != ''"> and tqfbr.need_id = #{needId}</if>
            <if test="autCode != null  and autCode != ''"> and tqfbr.aut_code = #{autCode}</if>
            <if test="feedBackType != null "> and tqfbr.feed_back_type = #{feedBackType}</if>
            <if test="questionnaireId != null "> and tqfbr.questionnaire_id = #{questionnaireId}</if>
            <if test="fillStatus != null  and fillStatus != ''"> and tqfbr.fill_status = #{fillStatus}</if>
            <if test="status != null "> and tqfbr.status = #{status}</if>
        </where>
        ORDER BY tqfbr.create_time DESC
    </select>

    <select id="selectTraQuestionnaireFeedBackRecordAndHospitalInfoList" parameterType="TraQuestionnaireFeedBackRecord" resultType="com.thas.web.domain.SendDetailRes">
        SELECT tqfbr.feed_back_type feedBackType , tqfbr.create_time createTime , tqfbr.need_id needId, hbi.hospital_name needName, tqfbr.fill_status fillStatus
        FROM tra_questionnaire_feed_back_record tqfbr
        LEFT JOIN hospital_base_info hbi ON hbi.apply_no = tqfbr.need_id COLLATE utf8mb4_general_ci
        <where>
            hbi.status = 1
            <if test="resultId != null "> and tqfbr.result_id = #{resultId}</if>
            <if test="needId != null  and needId != ''"> and tqfbr.need_id = #{needId}</if>
            <if test="autCode != null  and autCode != ''"> and tqfbr.aut_code = #{autCode}</if>
            <if test="feedBackType != null "> and tqfbr.feed_back_type = #{feedBackType}</if>
            <if test="questionnaireId != null "> and tqfbr.questionnaire_id = #{questionnaireId}</if>
            <if test="fillStatus != null  and fillStatus != ''"> and tqfbr.fill_status = #{fillStatus}</if>
            <if test="status != null "> and tqfbr.status = #{status}</if>
        </where>
        ORDER BY tqfbr.create_time DESC
    </select>


    <insert id="insertTraQuestionnaireFeedBackRecord" parameterType="TraQuestionnaireFeedBackRecord" useGeneratedKeys="true" keyProperty="recordId">
        insert into tra_questionnaire_feed_back_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="resultId != null">result_id,</if>
            <if test="needId != null and needId != ''">need_id,</if>
            <if test="autCode != null">aut_code,</if>
            <if test="feedBackType != null">feed_back_type,</if>
            <if test="questionnaireId != null">questionnaire_id,</if>
            <if test="fillStatus != null and fillStatus != ''">fill_status,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="resultId != null">#{resultId},</if>
            <if test="needId != null and needId != ''">#{needId},</if>
            <if test="feedBackType != null">#{feedBackType},</if>
            <if test="questionnaireId != null">#{questionnaireId},</if>
            <if test="fillStatus != null and fillStatus != ''">#{fillStatus},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>
    <insert id="insertBatchTraQuestionnaireFeedBackRecord">
        insert into tra_questionnaire_feed_back_record
        <if test="autCodeFlag == 0">
            (result_id,need_id,feed_back_type,questionnaire_id)
            values
            <foreach item="item" collection="list" separator=",">
                (#{item.resultId},#{item.needId},#{item.feedBackType},#{item.questionnaireId})
            </foreach>
        </if>
        <if test="autCodeFlag == 1">
            (result_id,need_id,aut_code,feed_back_type,questionnaire_id)
            values
            <foreach item="item" collection="list" separator=",">
                (#{item.resultId},#{item.needId},#{item.autCode},#{item.feedBackType},#{item.questionnaireId})
            </foreach>
        </if>

    </insert>

    <update id="updateTraQuestionnaireFeedBackRecord" parameterType="TraQuestionnaireFeedBackRecord">
        update tra_questionnaire_feed_back_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="resultId != null">result_id = #{resultId},</if>
            <if test="needId != null and needId != ''">need_id = #{needId},</if>
            <if test="feedBackType != null">feed_back_type = #{feedBackType},</if>
            <if test="questionnaireId != null">questionnaire_id = #{questionnaireId},</if>
            <if test="fillStatus != null and fillStatus != ''">fill_status = #{fillStatus},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <update id="updateTraQuestionnaireFeedBackRecordList">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update tra_questionnaire_feed_back_record
            <set>
                <if test="item.resultId != null">result_id = #{item.resultId},</if>
                <if test="item.needId != null and item.needId != ''">need_id = #{item.needId},</if>
                <if test="item.feedBackType != null">feed_back_type = #{item.feedBackType},</if>
                <if test="item.questionnaireId != null">questionnaire_id = #{item.questionnaireId},</if>
                <if test="item.fillStatus != null and item.fillStatus != ''">fill_status = #{item.fillStatus},</if>
                <if test="item.status != null">status = #{item.status},</if>
            </set>
            where record_id = #{item.recordId}
        </foreach>
    </update>



    <delete id="deleteTraQuestionnaireFeedBackRecordByRecordId" parameterType="Long">
        delete from tra_questionnaire_feed_back_record where record_id = #{recordId}
    </delete>

    <delete id="deleteTraQuestionnaireFeedBackRecordByRecordIds" parameterType="String">
        delete from tra_questionnaire_feed_back_record where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>
</mapper>