<!--  -->
<template>
  <div class="audit-evaluation" ref="auditEvaluation" v-loading="loading">
    <div v-if="distributed">
      <!-- 医院名称 -->
      <el-card class="box-card" shadow="always" style="margin-bottom: 10px; margin-left: -5px">
        <h1 style="margin: 0">{{ detail.hospitalName }}</h1>
      </el-card>

      <!-- 总结详情 -->

      <!-- 评价  -->
      <el-card class="box-card autScrollCard" shadow="never" :style="{height: autCardHeight}">
        <div slot="header" class="clearfix">
          <h4>形式审查</h4>
          <div class="header-right" v-if="standardLoadedKey" :key="headerSlotKeys">
            <span v-if="waitFirstOrProc" @click="submitAllSa">填充全部审查初查(测试)</span>

            <!-- 自评 -->
            <span v-if="showAutFilter.length" :class="{lightHigh: lightHigh == 1}" @click="handleClauseFilter(1, showAutFilter)">
              待审查 {{showAutFilter.length}} 款
            </span>
            <!-- 驳回 -->
            <span v-if="showAutRjFilter.length" :class="{lightHigh: lightHigh == 3}" @click="handleClauseFilter(3, showAutRjFilter)">
              驳回审查 {{showAutRjFilter.length}} 款
            </span>
          </div>
        </div>
        <!-- 款项选择器 -->
        <!-- <evaluation-base :versionId="relation.autCsId" :clauseIds="clauseIds" @selectChange="selectChange" @loaded="standardLoaded"></evaluation-base> -->
        <standard-card ref="standardCard" :key="`base${loadingKeys}`" :clauseIds="clauseIds" :versionId="relation.autCsId" @selectChange="selectChange" @loaded="standardLoaded">
        </standard-card>

        <!-- 条小结 -->

        <!-- 款项 -->
        <div v-show="clauseList.length"  class="evaluation-content" :key="`ach${selectedKeys}`" :style="{height: clauseItemHeight}">
          <el-form :key="loadingKeys" label-width="100px">
            <clause-item v-for="(clause, index) in clauseList" v-show="isThisPage(index + 1)" :clause="clause" :key="clause.clauseId">
              <evaluation-self :readonly="true" :type="AutSaAudCurrentStatusEnum.SA_CLAUSE" :clause="clause" :aut="saMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-self>
              <evaluation-examine :type="AutSaAudCurrentStatusEnum.FR_CLAUSE" :clause="clause" :aut="frMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-examine>
              <!-- <evaluation-check :readonly="true" v-if="showExamineCheck" :type="AutSaAudCurrentStatusEnum.FR_V_CLAUSE" :clause="clause" :aut="frvMap[clause.clauseId]" :status="autSaAudStatus"></evaluation-check> -->
              <!-- <el-button type="danger">有异议</el-button> -->
            </clause-item>
            <pagination v-show="total > limit" :total="total" :page.sync="pageNum" :limit.sync="limit" :pageSizes="[limit]" />
          </el-form>
        </div>
      </el-card>
      <!-- 总结提交 -->

    </div>
  </div>
</template>

<script>
import evaluationMixin from "@/mixins/evaluation.vue";

export default {
  name: "FInspectorIndex",
  mixins: [evaluationMixin],
  components: {
  },
  props: {},
  watch: {
    detail: {
      deep: true,
      immediate: true,
      handler() {
        if (this.detail && this.detail.autSaAudStatus && this.AutSaAudCurrentStatusEnum.WAIT_AUD_FIRST_TRIAL_SUM == this.detail.autSaAudStatus) {
          this.handleError();
        }
      },
    },
  },
  data() {
    return {
      show: false,
      loading: true,
      relation: {},
      detail: {},
      clauseIds: [],
      specialInit: true,
    };
  },

  computed: {
    waitFirstOrProc() {
      return (this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.WAIT_AUD_FIRST_TRIAL ||
        this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_INQUIRE_PROC) && this.showConvenientBtn
    },
    showExamine() {
      return true;
    },
    showExamineCheck() {
      // 审核驳回
      return this.autSaAudStatus === this.AutSaAudCurrentStatusEnum.AUD_FIRST_TRIAL_REVIEW_REJECT
    },

    showAutFilter() {
      const list = Object.values(this.frMap);
      let frClauseIds = list.map(({ clauseId }) => Number(clauseId));
      let list2 = this.distributeClauseIds.reduce((list, next) => {
        if (!frClauseIds.includes(Number(next))) list.push(Number(next));
        return list;
      }, [])
      return list2;
    },

    // 被驳回
    showAutRjFilter() {
      const list = Object.values(this.frMap);
      const rjList = list.filter(({ rejected }) => rejected).map(({ clauseId }) => clauseId);
      return rjList;
    },
  },

  mounted() {
    this.init2(true, this.$route.query.autCode, this.$route.query.type);
  },

  methods: {
    initFinalBack() {
      if (this.showAutRjFilter.length) {
        this.$set(this, 'clauseIds', this.showAutRjFilter)
        this.$set(this, 'distributeClauseIds', this.showAutRjFilter)
        this.loadingKeys++;
      }
    },
    handleError() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push({ path: "/fInspector/f/inspectorEvaluationList" });
    },

    // 初审初查
    submitAllSa() {
      let ids = Object.values(this.frMap).map(({ clauseId }) => clauseId)
      this.makeData(this.AutSaAudCurrentStatusEnum.FR_CLAUSE, undefined, undefined, ids, true)
    },
  },
};
</script>
<style lang="scss" scoped>
.box-card + .box-card {
  margin-top: 15px;
}
h4 {
  margin: 10px 0;
}
</style>
