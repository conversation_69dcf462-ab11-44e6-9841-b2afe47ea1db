<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.system.mapper.SysUserMapper">

	<resultMap type="SysUser" id="SysUserResult">
		<id     property="userId"       column="user_id"      />
		<result property="deptId"       column="dept_id"      />
		<result property="userName"     column="user_name"    />
		<result property="nickName"     column="nick_name"    />
		<result property="email"        column="email"        />
		<result property="phonenumber"  column="phonenumber"  />
		<result property="sex"          column="sex"          />
		<result property="avatar"       column="avatar"       />
		<result property="password"     column="password"     />
		<result property="status"       column="status"       />
		<result property="delFlag"      column="del_flag"     />
		<result property="loginIp"      column="login_ip"     />
		<result property="loginDate"    column="login_date"   />
		<result property="createBy"     column="create_by"    />
		<result property="createTime"   column="create_time"  />
		<result property="updateBy"     column="update_by"    />
		<result property="updateTime"   column="update_time"  />
		<result property="remark"       column="remark"       />
		<result property="isLeave"      column="is_leave"    />
		<result property="roleId"      column="role_id"    />
		<result property="roleKey"      column="role_key"    />
		<association property="dept"    column="dept_id" javaType="SysDept" resultMap="deptResult" />
		<association property="hospitalVo" javaType="HospitalVo" resultMap="HospitalResult" />
		<collection  property="roles"   javaType="java.util.List"        resultMap="RoleResult" />
	</resultMap>

	<resultMap id="deptResult" type="SysDept">
		<id     property="deptId"   column="dept_id"     />
		<result property="parentId" column="parent_id"   />
		<result property="deptName" column="dept_name"   />
		<result property="orderNum" column="order_num"   />
		<result property="leader"   column="leader"      />
		<result property="status"   column="dept_status" />
	</resultMap>

	<resultMap id="RoleResult" type="SysRole">
		<id     property="roleId"       column="role_id"        />
		<result property="roleName"     column="role_name"      />
		<result property="roleKey"      column="role_key"       />
		<result property="roleSort"     column="role_sort"      />
		<result property="dataScope"     column="data_scope"    />
		<result property="status"       column="role_status"    />
	</resultMap>

	<resultMap id="HospitalResult" type="HospitalVo">
		<result property="applyNo" column="apply_no"/>
		<result property="hospitalName" column="hospital_name"/>
	</resultMap>

	<sql id="SQL_AES_ENCRYPT">'${@com.thas.web.config.SqlAesEncryptConfig@SQL_AES_ENCRYPT}'</sql>

	<sql id="SELECT_ENCRYPT_FIELD">
        AES_DECRYPT(unhex(u.nick_name),<include refid="SQL_AES_ENCRYPT"/>) nick_name,
		AES_DECRYPT(unhex(u.email),<include refid="SQL_AES_ENCRYPT"/>) email,
		AES_DECRYPT(unhex(u.phonenumber),<include refid="SQL_AES_ENCRYPT"/>) phonenumber
		</sql>

	<sql id="selectUserVo">
        select u.user_id, u.dept_id, u.user_name, u.avatar, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,u.is_leave,
        d.dept_id, d.parent_id, d.dept_name, d.order_num, d.leader, d.status as dept_status,
        r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status,
		<include refid="SELECT_ENCRYPT_FIELD"/>
        from sys_user u
		    left join sys_dept d on u.dept_id = d.dept_id
		    left join sys_user_role ur on u.user_id = ur.user_id
		    left join sys_role r on r.role_id = ur.role_id
    </sql>

    <select id="selectUserList" parameterType="SysUser" resultMap="SysUserResult">
		select * from (
		select u.user_id, u.dept_id, u.user_name, u.avatar,
		u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time,u.update_time,
		u.remark,
		d.dept_name, d.leader,
		r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as
		role_status,bi.apply_no,bi.hospital_name,
		<include refid="SELECT_ENCRYPT_FIELD"/>from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
		left join sys_user_role ur on u.user_id = ur.user_id
		left join sys_role r on r.role_id = ur.role_id
		left join sys_user_hospital uh on u.user_id = uh.user_id
		left join hospital_base_info bi on uh.hospital_apply_no = bi.apply_no
		where u.del_flag = '0'
		<if test="userId != null and userId != 0">
			AND u.user_id = #{userId}
		</if>
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND u.status = #{status}
		</if>
		<if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
		</if>
		<if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
		</if>
		<if test="deptId != null and deptId != 0">
			AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId},ancestors)))
		</if>
		<if test="createBy != null and createBy != ''">
			AND u.create_by like concat('%', #{createBy}, '%')
		</if>
		<if test="hospitalName != null and hospitalName != ''">
			AND bi.hospital_name like concat('%', #{hospitalName}, '%')
		</if>
		<if test="roleId != null and roleId != 0">
			AND r.role_id = #{roleId}
		</if>
		order by u.update_time DESC
		<!-- 数据范围过滤 -->
		${params.dataScope}
		) aa
		where
		1=1
		<if test="phonenumber != null and phonenumber != ''">AND phonenumber like concat('%', #{phonenumber} ,'%')</if>
		<if test="email != null and email != ''">AND email like concat('%', #{email} ,'%')</if>
		<if test="nickName != null and nickName != ''">AND nick_name like concat('%', #{nickName} ,'%')</if>

	</select>

	<select id="selectAllocatedList" parameterType="SysUser" resultMap="SysUserResult">
		select * from (
		select distinct u.user_id, u.dept_id, u.user_name, u.status, u.create_time,
		<include refid="SELECT_ENCRYPT_FIELD"/>from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
		left join sys_user_role ur on u.user_id = ur.user_id
		left join sys_role r on r.role_id = ur.role_id
		where u.del_flag = '0' and r.role_id = #{roleId}
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
		) aa
		where
		1=1
		<if test="phonenumber != null and phonenumber != ''">AND phonenumber like concat('%', #{phonenumber} , '%')</if>

	</select>

	<select id="selectUnallocatedList" parameterType="SysUser" resultMap="SysUserResult">
		select * from (
	    select distinct u.user_id, u.dept_id, u.user_name, u.status, u.create_time,
		<include refid="SELECT_ENCRYPT_FIELD"/>
	    from sys_user u
			 left join sys_dept d on u.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and (r.role_id != #{roleId} or r.role_id IS NULL)
	    and u.user_id not in (select u.user_id from sys_user u inner join sys_user_role ur on u.user_id = ur.user_id and ur.role_id = #{roleId})
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>

		<!-- 数据范围过滤 -->
		${params.dataScope}
		) aa
		where
		1=1
		<if test="phonenumber != null and phonenumber != ''">AND phonenumber like concat('%', #{phonenumber} , '%')</if>

	</select>

	<select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
	    <include refid="selectUserVo"/>
		where u.user_name = #{userName}
	</select>

	<select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.user_id = #{userId}
	</select>

	<select id="selectUserByAccountId" parameterType="String" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.user_id = #{accountId}
	</select>

	<select id="checkUserNameUnique" parameterType="String" resultType="int">
		select count(1) from sys_user where user_name = #{userName} limit 1
	</select>

	<select id="checkPhoneUnique" parameterType="String" resultMap="SysUserResult">
		select u.user_id, <include refid="SELECT_ENCRYPT_FIELD"/> from sys_user u where
		u.phonenumber = HEX(AES_ENCRYPT(#{phonenumber},<include refid="SQL_AES_ENCRYPT"/>)) limit 1
	</select>

	<select id="checkEmailUnique" parameterType="String" resultMap="SysUserResult">
		select u.user_id, <include refid="SELECT_ENCRYPT_FIELD"/> from sys_user u where
		u.email = HEX(AES_ENCRYPT(#{email},<include refid="SQL_AES_ENCRYPT"/>)) limit 1
	</select>

	<insert id="insertUser" parameterType="SysUser" useGeneratedKeys="true" keyProperty="userId">
 		insert into sys_user(
 			<if test="userId != null and userId != 0">user_id,</if>
 			<if test="deptId != null and deptId != 0">dept_id,</if>
 			<if test="userName != null and userName != ''">user_name,</if>
 			<if test="nickName != null and nickName != ''">nick_name,</if>
 			<if test="email != null and email != ''">email,</if>
 			<if test="avatar != null and avatar != ''">avatar,</if>
 			<if test="phonenumber != null and phonenumber != ''">phonenumber,</if>
 			<if test="sex != null and sex != ''">sex,</if>
 			<if test="password != null and password != ''">password,</if>
 			<if test="status != null and status != ''">status,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			<if test="remark != null and remark != ''">remark,</if>
 			create_time,
		    update_time
 		)values(
 			<if test="userId != null and userId != ''">#{userId},</if>
 			<if test="deptId != null and deptId != ''">#{deptId},</if>
 			<if test="userName != null and userName != ''">#{userName},</if>
 			<if test="nickName != null and nickName != ''">HEX(AES_ENCRYPT(#{nickName},<include refid="SQL_AES_ENCRYPT"/>)),</if>
 			<if test="email != null and email != ''">HEX(AES_ENCRYPT(#{email},<include refid="SQL_AES_ENCRYPT"/>)) ,</if>
		    <if test="avatar != null and avatar != ''">#{avatar},</if>
 			<if test="phonenumber != null and phonenumber != ''">HEX(AES_ENCRYPT( #{phonenumber},<include refid="SQL_AES_ENCRYPT"/>)),</if>
 			<if test="sex != null and sex != ''">#{sex},</if>
 			<if test="password != null and password != ''">#{password},</if>
 			<if test="status != null and status != ''">#{status},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
 			sysdate(),
		    sysdate()
 		)
	</insert>

	<update id="updateUser" parameterType="SysUser">
 		update sys_user
 		<set>
 			<if test="deptId != null and deptId != 0">dept_id = #{deptId},</if>
 			<if test="userName != null and userName != ''">user_name = #{userName},</if>
			<if test="nickName != null and nickName != ''">nick_name = HEX(AES_ENCRYPT(#{nickName},<include refid="SQL_AES_ENCRYPT"/>)),</if>
			<if test="email != null and email != ''">email = HEX(AES_ENCRYPT(#{email},<include refid="SQL_AES_ENCRYPT"/>)) ,</if>
			<if test="phonenumber != null and phonenumber != ''">phonenumber = HEX(AES_ENCRYPT(#{phonenumber},<include refid="SQL_AES_ENCRYPT"/>)),</if>
 			<if test="sex != null and sex != ''">sex = #{sex},</if>
 			<if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
 			<if test="password != null and password != ''">password = #{password},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="isLeave != null and isLeave != ''">is_leave = #{isLeave},</if>
 			<if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
 			<if test="loginDate != null">login_date = #{loginDate},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			<if test="remark != null">remark = #{remark},</if>
 			update_time = sysdate()
 		</set>
 		where user_id = #{userId}
	</update>

	<update id="updateUserStatus" parameterType="SysUser">
 		update sys_user set status = #{status} where user_id = #{userId}
	</update>

	<update id="updateUserAvatar" parameterType="SysUser">
 		update sys_user set avatar = #{avatar} where user_name = #{userName}
	</update>

	<update id="resetUserPwd" parameterType="SysUser">
 		update sys_user set password = #{password} where user_name = #{userName}
	</update>

	<delete id="deleteUserById" parameterType="Long">
		update sys_user set del_flag = '2' where user_id = #{userId}
 	</delete>

 	<delete id="deleteUserByIds" parameterType="Long">
 		update sys_user set del_flag = '2' where user_id in
 		<foreach collection="array" item="userId" open="(" separator="," close=")">
 			#{userId}
        </foreach>
 	</delete>

	<select id="selectUserByEmail" parameterType="String" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.email = HEX(AES_ENCRYPT(#{email},<include refid="SQL_AES_ENCRYPT"/>))
	</select>

	<select id="selectUserByRoleKey" parameterType="map" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		WHERE
		r.role_key = #{roleKey}
		and u.status = 0
		<if test="flag != null and flag != ''">
			and u.is_leave = 0
		</if>
	</select>

	<update id="updatePwdByEmail" parameterType="String">
		update sys_user
		set  password = #{password}
		where email = HEX(AES_ENCRYPT(#{email},<include refid="SQL_AES_ENCRYPT"/>))
	</update>

	<select id="selectHospitalNameByApplyNo" parameterType="string" resultType="string">
		select hospital_name from hospital_base_info where apply_no=#{applyNo}
	</select>

	<resultMap type="RoleAndUserVo" id="RoleAndUserResult">
		<result property="roleId"       column="role_id"       />
		<result property="roleName"      column="role_name"    />
		<collection  property="sysUserList"   javaType="List"   resultMap="SysUserResult2" />
	</resultMap>

	<resultMap type="UserVo" id="SysUserResult2">
		<id     property="userId"       column="user_id"      />
		<result property="userName"     column="user_name"    />
		<result property="nickName"     column="nick_name"    />
		<result property="email"        column="email"        />
		<result property="phonenumber"  column="phonenumber"  />
	</resultMap>

	<select id="selectUserListGroupByRole" resultMap="RoleAndUserResult">
		<include refid="selectUserVo"/>
		where
		1=1
		<if test="roleName != null  and roleName != ''">
		    and r.role_name = #{roleName}
		    and u.`status` = '0'
		</if>

	</select>

	<select id="selectTemplateById" parameterType="long" resultType="MessageTemplateVo">
		select name, content
		from message_template
		where id = #{id}
	</select>

	<select id="isLeaveByAccountId" parameterType="String" resultType="Integer">
		select count(*) from sys_user where user_id = #{accountId} and is_leave = 1 and status = 0
	</select>

	<select id="validIsInTransit" parameterType="string" resultType="string">
		select (
				   case
					   when sur.role_id in ('101', '106','107') and EXISTS(SELECT hr.id
																	 FROM hospital_reviewer hr
																	 WHERE hr.reviewer_id = sur.user_id
																	   and hr.status = '1') then 'Y'
					   when sur.role_id = '103' and EXISTS(SELECT hp.id
														   FROM hospital_pre_exam hp
														   WHERE hp.pre_exam_id = sur.user_id
															 and hp.status = '1')
						   then 'Y'
					   else ''
					   end
				   )
		from sys_user_role sur
		where sur.user_id = #{userId}
	</select>

	<select id="getValidIsInTransitByIds" resultType="java.lang.String">
		SELECT su.user_id userId FROM sys_user su
		LEFT JOIN sys_user_role sur ON sur.user_id = su.user_id
		LEFT JOIN sys_role sr ON  sr.role_id = sur.role_id
		WHERE su.user_id IN
		<foreach collection="userIds" separator="," open="(" close=")" index="index" item="item">
		 #{item}
		</foreach>
		AND su.is_leave = 0 AND su.status = 0
		<if test="roleId != null  and roleId != ''">
			AND	sr.role_id= #{roleId}
		</if>
	</select>

	<select id="selectUserByIds" resultType="com.thas.system.domain.vo.UserVo">
		SELECT user_id userId, user_name userName ,
		AES_DECRYPT(unhex(nick_name),<include refid="SQL_AES_ENCRYPT"/>) nickName,
		AES_DECRYPT(unhex(email),<include refid="SQL_AES_ENCRYPT"/>) email,
		AES_DECRYPT(unhex(phonenumber),<include refid="SQL_AES_ENCRYPT"/>) phonenumber
		FROM sys_user WHERE user_id IN
		<foreach collection="userIds" separator="," open="(" close=")" index="index" item="item">
			#{item}
		</foreach>
	    AND `status` = 0
	</select>

	<select id="selectRoleKeyByUserId" parameterType="Long" resultType="String">
		SELECT
			sr.role_key roleKey
		FROM
			sys_user_role sur
				LEFT JOIN sys_role sr ON sur.role_id = sr.role_id
		WHERE
			sur.user_id = #{userId};
	</select>

	<select id="batchValidIsInTransit" resultType="java.lang.String">
		select (
				   case
					   when sur.role_id in ('101', '106','107') and EXISTS(SELECT hr.id
																	 FROM hospital_reviewer hr
																	 WHERE hr.reviewer_id = sur.user_id
																	   and hr.status = '1') then sur.user_id
					   when sur.role_id = '103' and EXISTS(SELECT hp.id
														   FROM hospital_pre_exam hp
														   WHERE hp.pre_exam_id = sur.user_id
															 and hp.status = '1')
						   then sur.user_id
					   else ""
					   end
				   ) userId
		from sys_user_role sur
		where sur.user_id in
		<foreach collection="userIds" separator="," open="(" close=")" index="index" item="item">
			#{item}
		</foreach>
	</select>

	<select id="selectUserListByUserIds" resultType="com.thas.common.core.domain.entity.SysUser">
		SELECT user_id userId, user_name userName ,
		AES_DECRYPT(unhex(nick_name),<include refid="SQL_AES_ENCRYPT"/>) nickName,
		AES_DECRYPT(unhex(email),<include refid="SQL_AES_ENCRYPT"/>) email,
		AES_DECRYPT(unhex(phonenumber),<include refid="SQL_AES_ENCRYPT"/>) phonenumber
		FROM sys_user
		WHERE user_id IN
		<foreach collection="userIds.split(',')" separator="," open="(" close=")" index="index" item="item">
			#{item}
		</foreach>

	</select>
	<select id="selectUsersByRoleKeys"  resultType="com.thas.system.domain.vo.UserVo">
		SELECT su.user_id userId, su.user_name userName,
		AES_DECRYPT(unhex(su.nick_name),<include refid="SQL_AES_ENCRYPT"/>) nickName,
		AES_DECRYPT(unhex(su.email),<include refid="SQL_AES_ENCRYPT"/>) email,
		AES_DECRYPT(unhex(su.phonenumber),<include refid="SQL_AES_ENCRYPT"/>) phonenumber
		FROM  sys_user su
		LEFT JOIN sys_user_role sur ON su.user_id = sur.user_id
		LEFT JOIN sys_role sr ON sur.role_id = sr.role_id
		WHERE
		su.`status` = 0
		AND sr.role_key in
		<foreach collection="roleKeyList" separator="," open="(" close=")" index="index" item="item">
			#{item}
		</foreach>
	</select>


</mapper>
