<template>
  <div class='conflict-of-interest'>
    <el-descriptions class="conflict-of-interest-form descriptions" :column="2" border :labelStyle="{ width: '100px' }">
      <el-descriptions-item label="评审员姓名">{{ $store.getters.nickName }}</el-descriptions-item>
      <el-descriptions-item label="职务">{{ companyPost }}</el-descriptions-item>
      <el-descriptions-item label="工作单位">{{ company }}</el-descriptions-item>
      <el-descriptions-item label="联系电话">{{ reviewerMobile }}</el-descriptions-item>
      <el-descriptions-item class="hospital" label="拟评审医院">{{ $route.query.hospitalName }}</el-descriptions-item>
    </el-descriptions>
    <el-form class="conflict-of-interest-form" label-width="100px" label-position="left">
      <el-row>
        <el-col :span="24">
          <el-form-item label="保密承诺与利益冲突申报" class="special">
            <b>1. 保密承诺</b> <br>
            <span style="margin-bottom:15px;display:inline-block;">
              本人承诺：在为深圳市卫健医院评审评价中心提供相关服务期间，严格遵守国家保密法和中心相关保密要求，保守在工作过程中接触或掌握的需要保密的信息，保证不因自身利益或任何第三方的利益而泄露任何保密信息或利用保密信息获利。
            </span>

            <span style="margin-bottom:15px;display:inline-block;">
              本人承诺所涉指的保密信息包括但不限于：<br>
              一、中心所有非公开的规章制度、管理流程以及内控文件资料。<br>
              二、中心所有未准公开的相关受评医院患者信息、自评报告、员工资料、内部报告、各种数据和相关资料。<br>
            </span>
            <span style="margin-bottom:15px;display:inline-block;">
              上述保密信息的载体包括但不限于：医院评审评价平台系统、纸质文件、视频、音频、计算机软件以及中心保密资料的任何载体等。
            </span>

            <span style="margin-bottom:15px;display:inline-block;">
              保密义务的期限为无限期保密，直至中心宣布解密或者秘密信息实际上已经公开。
            </span>
            <span style="margin-bottom:15px;display:inline-block;">
              如因本人责任造成涉密信息泄露，将承担相应的责任。
            </span>

            <br>
            <b>2. <a style="color:red;">是否存在</a>任何利益冲突（请勾选√）</b>
            <el-radio-group v-model="radio" style="display:block;margin-left:15px;" @change="radioChange">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
            <br>

            <span style="margin-bottom:15px;display:inline-block;">

              <span v-if="radio == 1" style="color:red;font-size: 16px;">*</span>
              如是，请具体描述利益冲突情况
              <i>（例如：有直接利害关系，直系亲属或有直接经济往来者；评审前三年内以个人身份受邀到该院进行讲学、培训或担任顾问、兼职等）</i>
              :
            </span>
            <div style="margin-top: 10px;padding: 10px 25px;">
              <el-input v-model="interestDesc" type="textarea" :maxlength="100" :rows="4"
                @input="radioChange"></el-input>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-row :gutter="24" style="margin-top:10px;">
      <el-col :span="3" style="height:24px;"></el-col>
      <el-col :span="21">
        <el-button type="primary" size="mini" @click="downloadPdf">下载填写完毕的利益冲突表</el-button>
      </el-col>
    </el-row>
    <el-row :gutter="24" style="margin-top:10px;">
      <el-col :span="3" style="height:24px;"></el-col>
      <el-col :span="21">
        <upload :disabled="!hasDownload" single text="上传盖章完毕的利益冲突表" :tip="true" class="evaluation-upload" :files="files" :data="{type: '1', downLoadFileName: `${$store.getters.nickName}-评审员利益冲突表-${$route.query.hospitalName}.pdf`}" @uploadSuccess="onSuccess" @fileRemove="onError"></upload>
        <span v-if="files.length == 0 && hasValidate" style="color:red;font-size:12px;">请上传盖章完毕的利益冲突表</span>
      </el-col>
    </el-row>
    <el-row :gutter="24" style="margin-top:10px;">
      <el-col :span="3" style="height:24px;"></el-col>
      <el-col :span="21">
        <el-button type="primary" @click="submit" style="margin-bottom:20px;" :disabled="!hasDownload">提交</el-button>
      </el-col>
    </el-row>

  </div>
</template>

<script>
import request, { downloadPDF } from "@/utils/request";


export default {
  name: 'ConflictOfInterest',
  data() {
    return {
      radio: -1,
      files: [],
      interestDesc: '',
      company: '',
      reviewerMobile: "",
      companyPost: '',
      hasDownload: false,
      hasValidate: false
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getDetail();
  },

  methods: {
    radioChange() {
      this.hasDownload = false;
    },
    getDetail() {
      request({
        url: 'reviewer/query/reviewer/detail',
        method: 'post',
        data: {
          commonId: this.$route.query.accountId,
        }
      }).then(res => {
        let { company, companyPost, reviewerMobile } = res.reviewerBaseInfo || {}
        this.company = company;
        this.reviewerMobile = reviewerMobile || "";
        this.companyPost = companyPost;
      })
    },
    async downloadPdf() {
      if (this.radio == -1) {
        return this.$message({
          type: 'warning',
          message: '请勾选是否存在任何利益冲突'
        })
      }

      if (this.radio == 1 && !this.interestDesc.trim()) {
        return this.$message({
          type: 'warning',
          message: '请描述利益冲突情况'
        })
      }

      try {
        let result = await this.update();
        downloadPDF({
          filename: `${this.$store.getters.nickName}-评审员利益冲突表-${this.$route.query.hospitalName}.pdf`,
          ftlTemplateCode: 'review_interest',
          checkStatus: 'N',
          accountId: this.$route.query.accountId,
          applyNo: this.$route.query.applyNo,
          hospitalName: this.$route.query.hospitalName,
        }, () => {
          this.hasDownload = true;
        })
      } catch (error) {
        // console.log(error);
      }
    },

    update() {
      return request({
        url: '/system/reviewer',
        method: 'put',
        data: {
          id: this.$route.query.id,
          // reviewerId: this.$route.query.accountId,
          applyNo: this.$route.query.applyNo,
          hasInterest: Number(this.radio),
          interestDesc: this.interestDesc
        }
      })
    },

    submit() {
      if (!this.$route.query.id || !this.$route.query.applyNo || !this.files[0]) {
        this.hasValidate = true;
        return;
      };
      this.hasDownload = false
      request({
        url: '/system/reviewer/update/interest',
        method: 'post',
        data: {
          id: this.$route.query.id,
          applyNo: this.$route.query.applyNo,
          interestFileId: this.files[0].fileId,
        }
      }).then(() => {
        let ids = this.files.map(({ fileId }) => fileId)
        if (ids.length) {
          this.shareCreate(ids, this.$route.query.accountId, 'reviewer_evaluation_party', this.$route.query.hospitalName)
        }
        this.$store.dispatch("tagsView/delView", this.$route);
        this.$router.push({ path: "/conflictOfInterest/list" });
      }).finally(() => {
        this.hasDownload = true;
      })
    },

    onSuccess(res, file) {
      this.$set(this.files, '0', res.data)
      this.hasValidate = false;
    },
    onError() {
      this.files = []
    },
  }
}

</script>

<style lang='scss' scoped>
.conflict-of-interest {
  .conflict-of-interest-form {
    width: 70%;
    margin: 0 auto;
    border: 1px solid black;
    border-right: 0;
    border-bottom: 0;

    &.descriptions {
      border-right: 1px solid black;

      ::v-deep tbody:first-child {
        .el-descriptions-row {
          .el-descriptions-item__cell {
            border-top: 0;
          }
        }
      }

      ::v-deep .el-descriptions-item__cell {
        border-color: #000;
        border-bottom: 0;
        border-left: 0;

        &:last-child {
          border-right: 0;
        }
      }
    }

    .el-form-item {
      margin-bottom: 0;
      border-bottom: 1px solid black;
      border-right: 1px solid black;

      ::v-deep .el-form-item__label {
        padding: 6px 0;
        text-indent: 5px;
      }

      ::v-deep .el-form-item__content {
        border-left: 1px solid black;
        padding: 6px 5px;

        .el-radio__input.is-checked .el-radio__inner {
          background-color: transparent;
          border-radius: 0;
        }

        .el-radio__input .el-radio__inner {
          border-radius: 0;
        }

        .el-radio__input.is-checked .el-radio__inner::after {
          -webkit-transform: none;
          transform: none;
          content: "✔";
          top: -2px;
          left: 2px;
          transform: rotate(12deg);
          font-weight: bold;
          width: 0;
          height: 0;
          color: #1890ff;
        }

        .el-radio__input .el-radio__inner::after {
          width: 0;
          height: 0;
        }
      }
    }

    ::v-deep .special.el-form-item {
      position: relative;

      .el-form-item__label {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        // padding: 50% 12px;
      }
    }
  }
}</style>
