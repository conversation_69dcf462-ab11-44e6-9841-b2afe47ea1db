<template>
  <div class="review-plan-allot-trail" v-if="showAll" v-loading="loading">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>医院基本详情</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="toHospitalDetail">详细信息
          <i class="el-icon-arrow-right" />
        </el-button>
      </div>
      <detail-message :form="hospitalForm"></detail-message>
    </el-card>
    <el-card class="box-card" shadow="never" v-if="!allAlloted">
      <div slot="header" class="clearfix">
        <span>分配审查员</span>
      </div>
      <div>
        <span style="font-size: 14px; margin-right: 10px">审查员：</span>
        <el-select v-model.trim="userId" @change="userChange" clearable filterable>
          <el-option v-for="(user, index) in userList" :key="index" :label="user.name"
            :value="`${user.accountId}`"></el-option>
        </el-select>
        <div style="display: flex; align-items: center; margin-top: 20px;">
          <div style="width: 56px; text-align: right; margin-right: 10px; font-size: 14px;">分组：</div>
          <el-checkbox-group v-model.trim="selectedGroups" :disabled="!userId" @change="groupChange">
            <el-checkbox v-for="(item) in groupList" :key="item.groupId" :label="item">
              {{ item.groupDetail }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="checkBox">
          <div style="margin-top: 25px">
            <el-button type="primary" @click="submit">保存</el-button>
          </div>
        </div>
      </div>
    </el-card>
    <el-card class="box-card" v-if="allAlloted" shadow="never">
      <div slot="header" class="clearfix">
        <span>选择审查组长</span>
      </div>
      <el-radio-group v-model.trim="leader">
        <el-radio v-for="(item, index) in hosReview" :key="index" :label="item.accountId">{{ item.name }}</el-radio>
      </el-radio-group>
      <div style="margin-top: 20px">
        <el-button @click="setLeader" type="primary">保存</el-button>
      </div>
    </el-card>

    <el-card class="box-card" shadow="never">
      <el-tabs v-model.trim="iAccountId" @tab-click="tabClick">
        <el-tab-pane v-for="(item, index) in hosReviewClauseNumInfos" :key="index"
          :label="`${item.name}(${item.clauseNum})`" :name="`${index}-${item.accountId}`">
          <span v-if="!allottedOrNotAllot.length">此分组暂无款项，可前往分组管理分配款项!</span>
          <div v-else>
            <modify-table :key="index" :data="allottedOrNotAllot.map(clause => clause.clauseId)" :versionId="versionId"
              :showAll="false"></modify-table>
          </div>
        </el-tab-pane>

        <el-tab-pane v-if="selectedGroups.length" :label="`已选择(${selectedGroupClauseList.length})`" name="selected-group">
          <modify-table :key="selectedGroupClauseList.length"
            :data="selectedGroupClauseList.map(clause => clause.clauseId)" :versionId="versionId"
            :showAll="false"></modify-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script>
import { Loading } from "element-ui";
import request from "@/utils/request";
import ModifyTable from "@/views/hospitalSelfEvaluation/components/modifyTable.vue";
export default {
  name: "ReviewPlanAllotExamine",
  components: {
    ModifyTable,
  },
  data() {
    return {
      showAll: false,
      loading: false,
      url: {
        detail: "/hospital/hos/plan/detail",
        submit: "",
      },
      hospital: {},
      allot: {},
      leader: null,
      userId: null,
      user: {},
      userList: [],
      notAllotClauseList: [],
      clauseList: [],
      checkList: [],
      checkAll: false,
      isIndeterminate: true,
      pageNum: 1,
      pageSize: 10,
      total: 0,
      pageNum1: 1,
      pageSize1: 10,
      total1: 0,
      hosReviewClauseNumInfos: [],
      hospitalForm: {},
      versionId: "",
      iAccountId: "",
      loading: null,
      selectedGroups: [],
      selectedClauseMap: {},
      groupList: [],
      fieldIdList: [],
      groupClauseMap: {},
      allottedClauseList: [],
    };
  },
  watch: {
    selectedGroups() {
      if (this.selectedGroups.length) {
        this.iAccountId = 'selected-group'
      } else {
        this.iAccountId = this.hosReviewClauseNumInfos[0] ? `0-${this.hosReviewClauseNumInfos[0].accountId}` : undefined;
      }
    }
  },
  computed: {
    allAlloted() {
      if (this.hosReviewClauseNumInfos.length == 0) return false;
      return !this.hosReviewClauseNumInfos.find((h) => !h.accountId);
    },
    hosReview() {
      return (this.hosReviewClauseNumInfos || []).filter(
        (hos) => hos.accountId != null
      );
    },

    selectedGroupClauseList() {
      let map = {}
      this.selectedGroups.forEach(group => {
        if (!this.selectedClauseMap[group.groupId]) return;
        map[group.groupId] = this.selectedClauseMap[group.groupId]
      })
      let ll = Object.values(map).reduce((list, list2) => {
        list.push(...list2)
        return list
      }, [])
      return ll;
    },

    allottedOrNotAllot() {
      if (this.iAccountId.includes('selected')) return []
      if (this.iAccountId.lastIndexOf('-null') > 0) {
        return this.notAllotClauseList;
      } else {
        return this.allottedClauseList;
      }
    },
    // selectedGroup() {
    //   return id => {
    //     return
    //   }
    // },
  },
  created() {
    // this.allot = this.$route.params.allot;
  },
  async mounted() {
    const versionId = await this.getEnabledVersionId();
    this.versionId = versionId || this.$store.getters.standardBaseVersionId;
    this.applyNo = this.$route.query.applyNo;
    let result = await this.queryAllot();
    this.allot = (result.rows || [])[0] || {}
    // this.allot = this.$route.params.allot;
    this.getDetail();
  },
  methods: {
    queryAllot() {
      return request({
        url: "/hospital/review/plan/list",
        method: "post",
        data: {
          applyNo: this.applyNo
        },
      })
    },
    getDetail() {
      return request({
        url: this.url.detail,
        method: "post",
        data: {
          applyNo: this.allot.applyNo,
          versionId: this.versionId,
          personForm: "1",
        },
      }).then((res) => {
        this.hospital = res.hospitalAuthContact;
        this.userList = res.preExamList || [];
        this.groupList = res.unDomainGroupNodeList || [];
        this.hosReviewClauseNumInfos = res.hosReviewClauseNumInfoVOList || [];
        this.userId = "";
        this.$set(this, "checkList", []);
        this.hospitalForm = {
          shadow: "never",
          list: [
            {
              label: "医院名称",
              value: this.allot.hospitalName,
              type: "text",
            },
            {
              label: "联系人姓名",
              value: res.hospitalAuthContact.authContactName,
              type: "text",
            },
            {
              label: "联系人手机号",
              value: res.hospitalAuthContact.authContactMobile,
              type: "text",
            },
            {
              label: "联系人邮箱",
              value: res.hospitalAuthContact.authContactEmail,
              type: "text",
            },
          ],
        };

        this.getClause4NotAllot()
        let accountId = this.hosReviewClauseNumInfos[0] ? `0-${this.hosReviewClauseNumInfos[0].accountId}` : undefined
        this.iAccountId = accountId;
        this.getClause4Allotted();
        this.showAll = true;
      }).catch((error) => {
        this.$store.dispatch("tagsView/delView", this.$route) &&
          this.$router.push({ name: "ReviewPlanAllotList" });
      });
    },
    toHospitalDetail() {
      this.$router.push({
        path: "/auditManagement/hospitalDetailMessage",
        query: { applyNo: this.$route.query.applyNo },
      });
    },

    userChange() {
      this.$set(this, "checkList", []);
      this.$set(this, "selectedGroups", []);
    },

    getClause4NotAllot() {
      let data = {
        personForm: "1",
        applyNo: this.allot.applyNo,
        versionId: this.versionId
      }

      this.loading = true;
      request({
        url: "/hospital/hos/plan/clause/detail?&time=" + (Date.now()),
        method: "post",
        data,
      }).then(res => {
        this.notAllotClauseList = res.rows
      }).finally(() => {
        this.loading = false;
      })
    },

    getClause4Allotted() {
      if (this.iAccountId.includes('select')) return;
      let iAs = this.iAccountId.split("-");
      let accountId = isNaN(iAs[1]) ? undefined : iAs[1];
      if (!accountId) return;

      let data = {
        personForm: "1",
        applyNo: this.allot.applyNo,
        versionId: this.versionId,
        accountId,
      };

      this.loading = true;
      request({
        url:
          "/hospital/hos/plan/clause/detail?&time=" + (Date.now() + (accountId || 0)),
        method: "post",
        data,
      }).then(res => {
        this.allottedClauseList = res.rows
      }).finally(() => {
        this.loading = false;
      })
    },

    getClause4Selected(fieldId) {
      let data = {
        personForm: "1",
        applyNo: this.allot.applyNo,
        versionId: this.versionId,
        fieldId,
      };

      this.loading = true;
      request({
        url: "/hospital/hos/plan/clause/detail?&time=" + (Date.now() + (fieldId || 0)),
        method: "post",
        data,
      }).then(res => {
        this.$set(this.selectedClauseMap, fieldId, res.rows)
      }).finally(() => {
        this.loading = false;
      })
    },

    tabClick() {
      if (!this.iAccountId.includes('selected-')) {
        if (this.iAccountId == '0-null') return;
        this.getClause4Allotted();
      }
    },
    setLeader() {
      if (!this.leader) {
        return this.$message({
          type: "warning",
          message: "请选择组长",
        });
      }
      this.loading = Loading.service({
        lock: true,
        text: "数据提交中",
        spinner: "el-icon-loading",
      });
      request({
        url: "/hospital/plan/submit",
        method: "post",
        data: {
          accountId: this.leader,
          leaderIs: "1",
          applyNo: this.allot.applyNo,
          completeIs: 1,
          personForm: "1",
          versionId: this.versionId,
        },
      })
        .then((res) => {
          res.code == 200 &&
            this.$store.dispatch("tagsView/delView", this.$route) &&
            this.$router.push({ name: "ReviewPlanAllotList" });
        })
        .finally(() => {
          if (this.loading) {
            this.loading.close();
          }
          this.loading = null;
        });
    },
    submit() {
      if (!this.userId) {
        return this.$message({
          type: "warning",
          message: "请选择审查员",
        });
      }
      let _clauseList = [];
      let _fieldIdList = [];
      if (this.fieldIdList.length) {
        for (let i = 0; i < this.fieldIdList.length; i++) {
          _fieldIdList.push(this.fieldIdList[i].groupId);
        }
      } else {
        return this.$message({
          type: "warning",
          message: "请选择分组",
        });
      }

      // 判断当前选择的分组 是否全部都有数据
      let flag = _fieldIdList.reduce((flag, fieldId) => {
        return this.selectedClauseMap[fieldId] && flag
      }, true)

      if (!flag) {
        return this.$message({
          type: 'warning',
          message: '分组款项不全，请重新获取分组款项信息'
        })
      }

      this.selectedGroupClauseList.forEach(clause => {
        _clauseList.push(clause.id);
      })

      let __clauseList = [...new Set(_clauseList)];
      this.loading = Loading.service({
        lock: true,
        text: "数据提交中",
        spinner: "el-icon-loading",
      });

      let data = {
        accountId: this.userId,
        applyNo: this.allot.applyNo,
        completeIs: 2,
        personForm: "1",
        versionId: this.versionId,
        clauseList: __clauseList.join(","),
        fieldIdList: _fieldIdList.join(","),
      }

      request({
        url: "/hospital/plan/submit",
        method: "post",
        data,
      })
        .then((res) => {
          if (res.code == 200) {
            (this.pageNum = 1) && this.getDetail();
            this.fieldIdList = [];
            this.selectedGroups = [];
          }
        })
        .finally(() => {
          if (this.loading) {
            this.loading.close();
          }
          this.loading = null;
        });
    },
    groupChange(value) {
      this.fieldIdList = value;
      if (value && value.length) {
        value.forEach(item => {
          if (this.selectedClauseMap[item.groupId]) return;
          this.getClause4Selected(item.groupId)
        })
      }
    },
  },
};
</script>

<style lang='scss' scoped>
.review-plan-allot-trail {
  padding: 10px;

  ::v-deep .box-card.el-card {
    border: 0;

    .el-checkbox__input {
      vertical-align: top;
      margin-top: 3px;
    }

    .el-checkbox__label {
      width: 90%;
      display: inline-block;
      word-wrap: break-word;
      white-space: break-spaces;
      font-size: 14px;
      display: inline-flex;

      span:first-child {
        min-width: 60px;
      }
    }

    .el-tab-pane {
      padding-bottom: 20px;
    }
  }

  ::v-deep .el-radio {
    margin-left: 10px;

    .el-radio__label {
      padding-left: 4px;
    }
  }

  .box-card+.box-card {
    margin-top: 10px;
  }

  .checkBox {
    padding-left: 65px;
    margin-top: 20px;

    .el-checkbox {
      padding: 0;
    }

    span+span .el-checkbox {
      padding-top: 0;
    }
  }

  ::v-deep .checkAll {
    .el-checkbox {
      padding: 0;
    }
  }

  p {
    margin: 24px 0;
    padding-left: 65px;
    font-size: 14px;
    display: flex;

    span:first-child {
      min-width: 60px;
    }
  }
}
</style>
