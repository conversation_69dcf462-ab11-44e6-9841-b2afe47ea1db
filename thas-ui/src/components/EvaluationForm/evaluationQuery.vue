<!--  -->
<template>
  <div class='evaluation-audit evaluation' v-loading="submitLoading"  >
    <el-form ref="evaluationForm" :model="formData" :label-width="labelWidth" :rules="rules">
      <el-form-item v-if="!showForm && !readonly" class="form-item" label=" " style="margin:0;">
        <el-button type="danger" size="mini" @click="showFormChange" style="margin:auto;">驳回</el-button>
      </el-form-item>
      <el-form-item v-if="showForm || readonly" class="form-item" :class="{ readonly: !modifying || readonly }"
        label="审查结果：" prop="autResult">
        <span style="color:red;">驳回</span>
      </el-form-item>
      <el-form-item v-if="showForm || readonly" class="form-item" :class="{ readonly: !modifying || readonly }"
        label="驳回原因：" prop="autDesc" :rules="autDescRule">
        <el-input v-if="modifying && !readonly" type="textarea" v-model="formData.autDesc" placeholder="请填写驳回原因" :rows="4"
          :maxlength="descMaxLength"></el-input>
        <span v-else>{{ formData.autDesc || '--' }}</span>
      </el-form-item>
      <el-form-item v-if="(showForm || readonly) && modifying" class="form-item" label=" ">
        <el-button type="primary" @click="submit">提交</el-button>
        <el-button @click="hideFormChange">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import mixin from "./mixin";

const defaultForm = {
  autResult: '',
  autDesc: ''
}
export default {
  name: 'EvaluationQuery',
  components: {},
  mixins: [mixin],
  props: {},
  watch: {
    showForm() {
      if (!this.showForm) {
        this.reply();
      }
    },

    aut: {
      immediate: true,
      deep: true,
      handler() {
        if (!this.aut) {
          this.modifying = true;
        } else {
          this.showForm = true;
        }
      }
    }
  },
  data() {
    return {
      showForm: false
    };
  },

  computed: {
    autDescRule() {
      return [{
        required: true,
        message: '请填写驳回原因',
        trigger: ['blur', 'change']
      }]
    },
  },

  mounted() {
    if (!this.aut) {
      this.modifying = true;
    } else {
      this.showForm = true;
    }

    this.formData.autResult = 2
  },

  methods: {
    submit() {
      const evaluationForm = this.$refs.evaluationForm;
      if (evaluationForm && evaluationForm.validate) {
        evaluationForm.validate(valid => {
          if (!valid) return;
          this.submitEmit()
        })
      }
    },
    showFormChange() {
      this.showForm = true;
      this.formData.autResult = 2
      this.bus.$emit('heightChange')
    },

    hideFormChange() {
      this.showForm = false;
      this.formData.autResult = 0
      this.clearDesc();
    },

    clearDesc() {
      this.formData.autDesc = '';
    }
  }
}

</script>

<style lang='scss'></style>
