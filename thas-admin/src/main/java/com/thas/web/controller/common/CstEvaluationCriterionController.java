package com.thas.web.controller.common;

import com.thas.common.core.domain.CstEvaluationCriterionExcel;
import com.thas.common.validate.ValidGroup;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.enums.BusinessType;
import com.thas.web.domain.CstEvaluationCriterion;
import com.thas.web.service.ICstEvaluationCriterionService;
import com.thas.common.utils.poi.ExcelUtil;
import com.thas.common.core.page.TableDataInfo;

/**
 * 评估标准模板Controller
 *
 * <AUTHOR>
 * @date 2022-01-21
 */
@Api(value="CstEvaluationCriterionController",tags = "评估标准模板")
@RestController
@RequestMapping("/system/criterion")
public class CstEvaluationCriterionController extends BaseController
{
    @Autowired
    private ICstEvaluationCriterionService cstEvaluationCriterionService;

    /**
     * 查询评估标准模板列表
     */
    //@PreAuthorize("@ss.hasPermi('system:criterion:list')")
    @GetMapping("/list")
    public TableDataInfo list(CstEvaluationCriterion cstEvaluationCriterion)
    {
        startPage();
        List<CstEvaluationCriterion> list = cstEvaluationCriterionService.selectCstEvaluationCriterionList(cstEvaluationCriterion);
        return getDataTable(list);
    }

    /**
     * 导出评估标准模板列表
     */
    //@PreAuthorize("@ss.hasPermi('system:criterion:export')")
    @Log(title = "评估标准模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CstEvaluationCriterion cstEvaluationCriterion)
    {
        List<CstEvaluationCriterion> list = cstEvaluationCriterionService.selectCstEvaluationCriterionList(cstEvaluationCriterion);
        ExcelUtil<CstEvaluationCriterion> util = new ExcelUtil<CstEvaluationCriterion>(CstEvaluationCriterion.class);
        util.exportExcel(response, list, "评估标准模板数据");
    }

    /**
     * 获取评估标准模板详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:criterion:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(cstEvaluationCriterionService.selectCstEvaluationCriterionById(id));
    }

    /**
     * 新增评估标准模板
     */
    //@PreAuthorize("@ss.hasPermi('system:criterion:add')")
    @Log(title = "评估标准模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CstEvaluationCriterion cstEvaluationCriterion)
    {
        return toAjax(cstEvaluationCriterionService.insertCstEvaluationCriterion(cstEvaluationCriterion));
    }

    /**
     * 修改评估标准模板
     */
    //@PreAuthorize("@ss.hasPermi('system:criterion:edit')")
    @Log(title = "评估标准模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CstEvaluationCriterion cstEvaluationCriterion)
    {
        return toAjax(cstEvaluationCriterionService.updateCstEvaluationCriterion(cstEvaluationCriterion));
    }

    /**
     * 删除评估标准模板
     */
    //@PreAuthorize("@ss.hasPermi('system:criterion:remove')")
    @Log(title = "评估标准模板", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(cstEvaluationCriterionService.deleteCstEvaluationCriterionByIds(ids));
    }


    /**
     * 通过款id和版本id多条修改评估标准模板
     */
    //@PreAuthorize("@ss.hasPermi('system:criterion:edit')")
    @ApiOperation("通过款id和版本id多条修改评估标准模板")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "certificationStandardsId", value = "认证标准款id", dataType = "Long", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "evaluate", value = "评价", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "standard", value = "标准", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "versionId", value = "版本id", dataType = "Long", dataTypeClass = Long.class)
    })
    @Log(title = "评估标准模板", businessType = BusinessType.UPDATE)
    @PutMapping("/updateListByVersionIdAndClauseId")
    public AjaxResult updateListByVersionIdAndClauseId(@RequestBody List<CstEvaluationCriterion> cstEvaluationCriterions)
    {
        return toAjax(cstEvaluationCriterionService.updateListByVersionIdAndClauseId(cstEvaluationCriterions));
    }


    /**
     * 查询单条认证标准对应的评价列表信息
     */
    @PostMapping(value = "/queryCriterionList")
    public AjaxResult queryCriterionList(@Validated(ValidGroup.Group1.class) @RequestBody CstEvaluationCriterionExcel cstEvaluationCriterionExcel) {
        return AjaxResult.success(cstEvaluationCriterionService.queryCriterionList(cstEvaluationCriterionExcel));
    }

}
