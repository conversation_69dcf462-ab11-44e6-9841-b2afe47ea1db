<template>
  <div>
    <el-card class="article-detail">
      <div class="title">{{ detail.title }}</div>
      <div class="item-title">
        <div class="left">
          <el-avatar size="medium" src=""></el-avatar>
          <div>
            <div class="name">{{ detail.createBy }}</div>
            <div class="create-time" style="color: #999999;">{{ detail.createTime }}</div>
          </div>
        </div>
      </div>
      <div v-html="detail.content" class="content"></div>
      <div class="like">
        <div v-if="detail.likePersons && detail.likePersons.length" class="like-persons">
          <svg fill="#5a7397" style="cursor: pointer;" xmlns="http://www.w3.org/2000/svg" class="svg-icon-path-icon fill" viewBox="0 0 32 32" width="18" height="18">
            <path d="M10.663 28.441v-15.996c0.54-0.195 0.93-0.362 1.17-0.499 0.672-0.385 1.242-0.909 1.71-1.572 0.71-1.006 0.842-1.47 1.613-4.287 0.051-0.185 0.152-0.438 0.306-0.759 0.708-1.187 1.717-1.78 3.025-1.777 1.264 0 2.275 0.592 3.034 1.777 0.189 0.327 0.323 0.692 0.401 1.096s0.079 0.867 0 1.39l-1.399 4.883c-0.025 0.078-0.025 0.134 0 0.17 0.027 0.039 0.066 0.058 0.117 0.058h4.251l-0.005 0.010h0.554c1.65 0 3 1.3 3 2.95 0 0.25-0.050 0.5-0.1 0.75l-1.496 9.555c-0.3 1.3-1.5 2.25-2.9 2.25h-13.282zM8.885 28.441h-3.556c-0.982 0-1.778-0.796-1.778-1.778v-12.444c0-0.982 0.796-1.778 1.778-1.778h3.556v16z"></path>
          </svg>
          <span style="color: #5a7397;">{{ detail.likePersons.join("，") }}</span>
          <span>等{{ detail.likePersons.length }}人觉得很赞</span>
        </div>
        <div class="like-btn">
          <el-button :type="detail.doLike ? 'warning' : ''" plain round style="width: 120px; margin-right: 70px;" @click="like(detail)">
            <svg v-if="detail.doLike" fill="#ff8b2a" style="cursor: pointer;" xmlns="http://www.w3.org/2000/svg" class="svg-icon-path-icon fill" viewBox="0 0 32 32" width="18" height="18">
              <path d="M10.663 28.441v-15.996c0.54-0.195 0.93-0.362 1.17-0.499 0.672-0.385 1.242-0.909 1.71-1.572 0.71-1.006 0.842-1.47 1.613-4.287 0.051-0.185 0.152-0.438 0.306-0.759 0.708-1.187 1.717-1.78 3.025-1.777 1.264 0 2.275 0.592 3.034 1.777 0.189 0.327 0.323 0.692 0.401 1.096s0.079 0.867 0 1.39l-1.399 4.883c-0.025 0.078-0.025 0.134 0 0.17 0.027 0.039 0.066 0.058 0.117 0.058h4.251l-0.005 0.010h0.554c1.65 0 3 1.3 3 2.95 0 0.25-0.050 0.5-0.1 0.75l-1.496 9.555c-0.3 1.3-1.5 2.25-2.9 2.25h-13.282zM8.885 28.441h-3.556c-0.982 0-1.778-0.796-1.778-1.778v-12.444c0-0.982 0.796-1.778 1.778-1.778h3.556v16z"></path>
            </svg>
            <svg v-if="!detail.doLike" style="cursor: pointer;" xmlns="http://www.w3.org/2000/svg" class="svg-icon-path-icon fill" viewBox="0 0 32 32" width="18" height="18">
              <path d="M10.663 12.445c0.54-0.195 0.93-0.362 1.17-0.499 0.672-0.385 1.242-0.909 1.71-1.572 0.71-1.006 0.842-1.47 1.613-4.287 0.051-0.185 0.152-0.438 0.306-0.759 0.708-1.187 1.717-1.78 3.025-1.777 1.264 0 2.275 0.592 3.034 1.777 0.189 0.327 0.323 0.692 0.401 1.096s0.079 0.867 0 1.39l-1.399 4.883c-0.025 0.078-0.025 0.134 0 0.17 0.027 0.039 0.066 0.058 0.117 0.058h4.251l-0.005 0.010h0.554c1.65 0 3 1.3 3 2.95 0 0.25-0.050 0.5-0.1 0.75l-1.496 9.555c-0.3 1.3-1.5 2.25-2.9 2.25h-18.615c-0.982 0-1.778-0.796-1.778-1.778v-12.444c0-0.982 0.796-1.778 1.778-1.778l5.333 0.004zM10.663 14.409v12.254h13.261c0.45 0 0.85-0.3 0.95-0.75 1.193-6.368 1.789-9.694 1.789-9.977 0-0.55-0.45-1-1-1h-1.725l-0.009 0.018h-3.238c-0.714 0-1.28-0.245-1.699-0.736-0.628-0.736-0.59-1.383-0.362-2.204 0.521-1.87 0.962-3.489 1.323-4.857-0.010-0.96-0.451-1.5-1.323-1.622s-1.448 0.358-1.728 1.44l-0.745 2.583c-0.853 2.302-2.52 3.855-5.002 4.66l-0.492 0.19zM5.33 26.663h3.556v-12.444h-3.556v12.444z"></path>
            </svg>
            &nbsp;&nbsp;
            <span style="vertical-align: top; font-size: 18px;">{{ detail.likeNum > 0 ? detail.likeNum : "赞" }}</span>
          </el-button>
        </div>
      </div>
    </el-card>
    <el-card v-if="detail && detail.list && detail.list.length" class="comment">
      <div v-if="showOption" class="send-comment">
        <el-avatar size="medium" src=""></el-avatar>
        <el-input :maxlength="500" v-model.trim="detail.comment" type="textarea" style="flex: 1;" :autosize="{ minRows: 5 }" placeholder="发布你的评论"></el-input>
      </div>
      <div v-if="showOption" class="send-comment-btn">
        <el-button type="primary" @click="sendComment(detail)">评论</el-button>
      </div>
      <div class="comment-list">
        <div v-for="(commentItem) in detail.list" :key="commentItem.id" class="comment-item">
          <div class="top">
            <el-avatar size="small" src=""></el-avatar>
            <div>
              <span class="user-name">{{ commentItem.createBy }}</span>
              <span class="comment-time">{{ commentItem.createTime }}</span>
            </div>
          </div>
          <div class="bottom">{{ commentItem.content }}</div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import request from '@/utils/request'
export default {
  name: 'ArticleDetail',
  data() {
    return {
      detail: {}
    }
  },
  computed: {
    showOption() {
      return this.$store.getters.roles == 'senior-assessor' || this.$store.getters.roles == 'assessor' || this.$store.getters.roles == 'assessor-leader'
    }
  },
  created() {
    this.getDetail()
  },
  methods: {
    getDetail() {
      request({
        url: '/system/community/' + this.$route.query.id,
        method: 'get'
      }).then((res) => {
        if (res.code != 200) return
        res.data.comment = ''
        this.detail = res.data
      })
    },
    // 评论
    sendComment(item) {
      if (!item.comment) {
        return
      }
      request({
        url: '/system/community',
        method: 'post',
        data: {
          pid: item.id,
          content: item.comment
        },
      }).then((res) => {
        if (res.code != 200) return
        item.comment = ''
        this.getDetail()
      })
    },
    like(item) {
      request({
        url: '/system/community',
        method: 'get',
        params: {
          id: item.id,
          type: item.doLike == 0 ? 1 : 0
        },
      }).then((res) => {
        if (res.code != 200) return
        this.getDetail()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.article-detail {
  width: 720px;
  .title {
    margin-bottom: 26px;
    font-size: 24px;
    font-weight: bold;
  }
  .content {
    ::v-deep img {
      width: 100%;
    }
  }
  .item-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    .left {
      display: flex;
      align-items: center;
      font-weight: bold;
      .name {
        flex: 1;
        margin-left: 10px;
        margin-bottom: 5px;
        font-size: 16px;
      }
      .create-time {
        margin-left: 10px;
        font-size: 12px;
      }
    }
    .right {
      display: flex;
      align-items: center;
    }
  }
  .like-persons {
    margin-top: 30px;
    font-size: 14px;
    font-weight: bold;
    span {
      vertical-align: text-bottom;
    }
  }
  .like-btn {
    text-align: right;
  }
}
.comment {
  width: 720px;
  margin-top: 10px;
  margin-bottom: 10px;
  .send-comment {
    display: flex;
    margin-bottom: 20px;
    margin-left: 60px;
    .el-avatar {
      margin-right: 15px;
    }
  }
  .send-comment-btn {
    text-align: right;
  }
  .comment-list {
    margin-left: 60px;
    .comment-item {
      .top {
        display: flex;
        align-items: center;
        .user-name {
          margin-left: 10px;
          margin-right: 10px;
          font-size: 16px;
          font-weight: bold;
        }
        .comment-time {
          font-size: 14px;
          color: #999999;
        }
      }
      .bottom {
        margin-top: 20px;
        margin-left: 46px;
        margin-bottom: 20px;
        font-size: 14px;
        font-weight: bold;
      }
    }
  }
}
</style>
