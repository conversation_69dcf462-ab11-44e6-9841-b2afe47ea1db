<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.CstCertificationStandardsMapper">

    <resultMap type="CstCertificationStandards" id="CstCertificationStandardsResult">
        <result property="id"    column="id"    />
        <result property="chapterId"    column="chapter_id"    />
        <result property="chapterNo"    column="chapter_no"    />
        <result property="chapter"    column="chapter"    />
        <result property="sectionId"    column="section_id"    />
        <result property="sectionNo"    column="section_no"    />
        <result property="section"    column="section"    />
        <result property="articleId"    column="article_id"    />
        <result property="articleNo"    column="article_no"    />
        <result property="article"    column="article"    />
        <result property="clauseId"    column="clause_id"    />
        <result property="clauseNo"    column="clause_no"    />
        <result property="clause"    column="clause"    />
        <result property="isStar"    column="is_star"    />
        <result property="detailRulesTitle"    column="detail_rules_title"    />
        <result property="detailRulesDesc"    column="detail_rules_desc"    />
        <result property="evidenceMaterial"    column="evidence_material"    />
        <result property="regulationFile"    column="regulation_file"    />
        <result property="internationalReference"    column="international_reference"    />
        <result property="status"    column="status"    />
        <result property="createId"    column="create_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateId"    column="update_id"    />
        <result property="updateTime"    column="update_time"    />
        <result property="versionId"    column="version_id"    />
        <result property="domainId"    column="domain_id"    />
    </resultMap>

    <sql id="selectCstCertificationStandardsVo">
        select id, chapter_id, chapter_no, chapter, section_id, section_no, section, article_id, article_no, article, clause_id, clause_no, clause, is_star, detail_rules_title, detail_rules_desc, evidence_material, regulation_file, international_reference, status, create_id, create_time, update_id, update_time, version_id,domain_id from cst_certification_standards
    </sql>

    <select id="selectCstCertificationStandardsList" parameterType="CstCertificationStandards" resultMap="CstCertificationStandardsResult">
        <include refid="selectCstCertificationStandardsVo"/>
        <where>
            <if test="chapterId != null "> and chapter_id = #{chapterId}</if>
            <if test="chapterNo != null  and chapterNo != ''"> and chapter_no = #{chapterNo}</if>
            <if test="chapter != null  and chapter != ''"> and chapter = #{chapter}</if>
            <if test="sectionId != null "> and section_id = #{sectionId}</if>
            <if test="sectionNo != null  and sectionNo != ''"> and section_no = #{sectionNo}</if>
            <if test="section != null  and section != ''"> and section = #{section}</if>
            <if test="articleId != null "> and article_id = #{articleId}</if>
            <if test="articleNo != null  and articleNo != ''"> and article_no = #{articleNo}</if>
            <if test="article != null  and article != ''"> and article = #{article}</if>
            <if test="clauseId != null "> and clause_id = #{clauseId}</if>
            <if test="clauseNo != null  and clauseNo != ''"> and clause_no = #{clauseNo}</if>
            <if test="clause != null  and clause != ''"> and clause = #{clause}</if>
            <if test="isStar != null  and isStar != ''"> and is_star = #{isStar}</if>
            <if test="detailRulesTitle != null  and detailRulesTitle != ''"> and detail_rules_title = #{detailRulesTitle}</if>
            <if test="detailRulesDesc != null  and detailRulesDesc != ''"> and detail_rules_desc = #{detailRulesDesc}</if>
            <if test="evidenceMaterial != null  and evidenceMaterial != ''"> and evidence_material = #{evidenceMaterial}</if>
            <if test="regulationFile != null  and regulationFile != ''"> and regulation_file = #{regulationFile}</if>
            <if test="internationalReference != null  and internationalReference != ''"> and international_reference = #{internationalReference}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createId != null "> and create_id = #{createId}</if>
            <if test="updateId != null "> and update_id = #{updateId}</if>
            <if test="versionId != null "> and version_id = #{versionId}</if>
            <if test="domainId != null "> and domain_id = #{domainId}</if>
        </where>
    </select>

    <select id="selectCstCertificationStandardsById" parameterType="Long" resultMap="CstCertificationStandardsResult">
        <include refid="selectCstCertificationStandardsVo"/>
        where id = #{id}
    </select>

    <insert id="insertCstCertificationStandards" parameterType="CstCertificationStandards" useGeneratedKeys="true" keyProperty="id">
        insert into cst_certification_standards
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="chapterId != null">chapter_id,</if>
            <if test="chapterNo != null and chapterNo != ''">chapter_no,</if>
            <if test="chapter != null and chapter != ''">chapter,</if>
            <if test="sectionId != null">section_id,</if>
            <if test="sectionNo != null and sectionNo != ''">section_no,</if>
            <if test="section != null and section != ''">section,</if>
            <if test="articleId != null">article_id,</if>
            <if test="articleNo != null and articleNo != ''">article_no,</if>
            <if test="article != null and article != ''">article,</if>
            <if test="clauseId != null">clause_id,</if>
            <if test="clauseNo != null and clauseNo != ''">clause_no,</if>
            <if test="clause != null and clause != ''">clause,</if>
            <if test="isStar != null and isStar != ''">is_star,</if>
            <if test="detailRulesTitle != null and detailRulesTitle != ''">detail_rules_title,</if>
            <if test="detailRulesDesc != null and detailRulesDesc != ''">detail_rules_desc,</if>
            <if test="evidenceMaterial != null and evidenceMaterial != ''">evidence_material,</if>
            <if test="regulationFile != null and regulationFile != ''">regulation_file,</if>
            <if test="internationalReference != null and internationalReference != ''">international_reference,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="versionId != null">version_id,</if>
            <if test="domainId  != null">domain_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="chapterId != null">#{chapterId},</if>
            <if test="chapterNo != null and chapterNo != ''">#{chapterNo},</if>
            <if test="chapter != null and chapter != ''">#{chapter},</if>
            <if test="sectionId != null">#{sectionId},</if>
            <if test="sectionNo != null and sectionNo != ''">#{sectionNo},</if>
            <if test="section != null and section != ''">#{section},</if>
            <if test="articleId != null">#{articleId},</if>
            <if test="articleNo != null and articleNo != ''">#{articleNo},</if>
            <if test="article != null and article != ''">#{article},</if>
            <if test="clauseId != null">#{clauseId},</if>
            <if test="clauseNo != null and clauseNo != ''">#{clauseNo},</if>
            <if test="clause != null and clause != ''">#{clause},</if>
            <if test="isStar != null and isStar != ''">#{isStar},</if>
            <if test="detailRulesTitle != null and detailRulesTitle != ''">#{detailRulesTitle},</if>
            <if test="detailRulesDesc != null and detailRulesDesc != ''">#{detailRulesDesc},</if>
            <if test="evidenceMaterial != null and evidenceMaterial != ''">#{evidenceMaterial},</if>
            <if test="regulationFile != null and regulationFile != ''">#{regulationFile},</if>
            <if test="internationalReference != null and internationalReference != ''">#{internationalReference},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="versionId != null">#{versionId},</if>
            <if test="domainId  != null">#{domainId},</if>
        </trim>
    </insert>

    <update id="updateCstCertificationStandards" parameterType="CstCertificationStandards">
        update cst_certification_standards
        <trim prefix="SET" suffixOverrides=",">
            <if test="chapterId != null">chapter_id = #{chapterId},</if>
            <if test="chapterNo != null and chapterNo != ''">chapter_no = #{chapterNo},</if>
            <if test="chapter != null and chapter != ''">chapter = #{chapter},</if>
            <if test="sectionId != null">section_id = #{sectionId},</if>
            <if test="sectionNo != null and sectionNo != ''">section_no = #{sectionNo},</if>
            <if test="section != null and section != ''">section = #{section},</if>
            <if test="articleId != null">article_id = #{articleId},</if>
            <if test="articleNo != null and articleNo != ''">article_no = #{articleNo},</if>
            <if test="article != null and article != ''">article = #{article},</if>
            <if test="clauseId != null">clause_id = #{clauseId},</if>
            <if test="clauseNo != null and clauseNo != ''">clause_no = #{clauseNo},</if>
            <if test="clause != null and clause != ''">clause = #{clause},</if>
            <if test="isStar != null and isStar != ''">is_star = #{isStar},</if>
            <if test="detailRulesTitle != null and detailRulesTitle != ''">detail_rules_title = #{detailRulesTitle},</if>
            <if test="detailRulesDesc != null and detailRulesDesc != ''">detail_rules_desc = #{detailRulesDesc},</if>
            <if test="evidenceMaterial != null and evidenceMaterial != ''">evidence_material = #{evidenceMaterial},</if>
            <if test="regulationFile != null and regulationFile != ''">regulation_file = #{regulationFile},</if>
            <if test="internationalReference != null and internationalReference != ''">international_reference = #{internationalReference},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="versionId != null">version_id = #{versionId},</if>
            <if test="domainId  != null">domain_id  = #{domainId },</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCstCertificationStandardsById" parameterType="Long">
        delete from cst_certification_standards where id = #{id}
    </delete>

    <delete id="deleteCstCertificationStandardsByIds" parameterType="String">
        delete from cst_certification_standards where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <resultMap type="ChapterVo" id="ChapterVoResult">
        <result property="chapterId"    column="chapter_id"    />
        <result property="chapterNo"    column="chapter_no"    />
        <result property="chapter"    column="chapter"    />
        <collection  property="sectionVoList"  javaType="java.util.List"  resultMap="SectionResult" fetchType="lazy"/>
    </resultMap>
    <resultMap id="SectionResult" type="SectionVo">
        <result    property="sectionId"   column="section_id"     />
        <result property="sectionNo" column="section_no"   />
        <result property="section" column="section"   />
        <collection  property="articleVoList"  javaType="java.util.List"  resultMap="ArticleResult"  fetchType="lazy"/>
    </resultMap>
    <resultMap id="ArticleResult" type="ArticleVo">
        <result    property="articleId"   column="article_id"     />
        <result property="articleNo" column="article_no"   />
        <result property="article" column="article"   />
        <collection  property="clauseVoList"  javaType="java.util.List"  resultMap="ClauseResult" fetchType="lazy"/>
    </resultMap>
    <resultMap id="ClauseResult" type="ClauseVo">
        <result     property="clauseId"   column="clause_id"     />
        <result property="clauseNo" column="clause_no"   />
        <result property="clause" column="clause"   />
        <result property="isStar" column="is_star"   />
        <result property="detailRulesTitle" column="detail_rules_title"   />
        <result property="detailRulesDesc" column="detail_rules_desc"   />
        <result property="evidenceMaterial" column="evidence_material"   />
        <result property="regulationFile" column="regulation_file"   />
        <result property="internationalReference" column="international_reference"   />
        <result property="versionId" column="version_id"   />
        <result property="domainId" column="domain_id"   />
        <result property="chapterId" column="chapter_id"/>
        <result property="sectionId" column="section_id"/>
        <result property="articleId" column="article_id"/>
        <collection  property="cstEvaluationCriterionList"  javaType="java.util.List"  resultMap="ACstEvaluationCriterionResult" fetchType="lazy"/>
    </resultMap>


    <resultMap id="ACstEvaluationCriterionResult" type="CstEvaluationCriterion">
        <id property="id" column="id"/>
        <result    property="certificationStandardsId"   column="certification_standards_id"     />
        <result    property="evaluate"   column="evaluate"     />
        <result property="standard" column="standard"   />
        <result property="versionId" column="version_id"   />
    </resultMap>
    <select id="selectByVersionId" resultMap="ChapterVoResult">
        select chapter.chapter_id,chapter.chapter_no,chapter.chapter,
               section.section_id,section.section_no,section.section,
               article.article_id,article.article_no,article.article,
               clause.clause_no ,clause.clause,clause.is_star,clause.detail_rules_title ,clause.detail_rules_desc,clause.evidence_material,clause.regulation_file,clause.international_reference,clause.version_id,clause.domain_id,clause.chapter_id,clause.section_id,clause.article_id,clause.clause_id,
               cec.evaluate,cec.standard,cec.id,cec.certification_standards_id
        from (SELECT chapter_id,chapter_no,chapter
              FROM cst_certification_standards
              where version_id = #{versionId}
              GROUP BY chapter_id,chapter_no,chapter
             ) chapter
        join (SELECT chapter_id,section_id,section_no,section
               FROM cst_certification_standards
               where version_id = #{versionId}
               GROUP BY chapter_id,section_id,section_no,section
        ) section
        on chapter.chapter_id = section.chapter_id
        join (SELECT section_id,article_id,article_no,article
            FROM cst_certification_standards
            where version_id = #{versionId}
            GROUP BY chapter_id,section_id,article_id,article_no,article
        ) article
        on section.section_id = article.section_id
        join (SELECT article_id,clause_id,clause_no,clause,is_star,detail_rules_title,detail_rules_desc,evidence_material,regulation_file,international_reference,version_id,domain_id,chapter_id,section_id
            FROM cst_certification_standards
            where version_id = #{versionId} ) clause
        on article.article_id = clause.article_id
        join cst_evaluation_criterion cec
        on clause.clause_id = cec.certification_standards_id and cec.version_id = #{versionId}
        order by chapter.chapter_id,section.section_id,article.article_id,clause.clause_id
    </select>

    <select id="selectClauseDetailByVersionId" resultMap="ClauseResult">
        SELECT
	ccs.detail_rules_title,
	ccs.detail_rules_desc,
	ccs.evidence_material,
	ccs.international_reference,
	ccs.regulation_file,
	cec.evaluate,
	cec.standard,
	cec.id,
	cec.certification_standards_id,
	ccs.article_id,
	ccs.version_id,
	ccs.clause_id,
	cec.certification_standards_id
FROM
	`cst_certification_standards` ccs
	JOIN cst_evaluation_criterion cec ON ccs.clause_id = cec.certification_standards_id
	AND ccs.version_id = cec.version_id
	AND ccs.article_id = #{articleId}
	AND ccs.version_id = #{versionId}

    </select>

<!--    <select id="selectCstCertificationStandardsByVersionId" resultType="com.thas.common.core.domain.CstCertificationStandardsExcel">-->
<!--        select chapter_id chapterId,-->
<!--               chapter_no chapterNo,-->
<!--               chapter,-->
<!--               section_id sectionId,-->
<!--               section_no sectionNo, section,-->
<!--            article_id articleId, article_no articleNo, article,-->
<!--            clause_id clauseId, clause_no clauseNo, clause,-->
<!--            is_star isStar, detail_rules_title detailRulesTitle, detail_rules_desc detailRulesDesc, evidence_material evidenceMaterial, regulation_file regulationFile, international_reference internationalReference, version_id versionId, domain_id domainId-->
<!--        from cst_certification_standards-->
<!--        where version_id = #{versionId}-->
<!--        order by clause_id-->
<!--    </select>-->

    <select id="selectCstCertificationStandardsByVersionId" resultMap="ChapterVoResult">
        select chapter_id  ,
               chapter_no  ,
               chapter,
               section_id  ,
               section_no  ,
               `section`,
               article_id  , article_no  , article,
               clause_id  , clause_no  , clause,
               is_star  , version_id  , domain_id
        from cst_certification_standards
        where version_id = #{versionId}
        order by clause_id
    </select>

    <resultMap id="ClauseResult2" type="ClauseVo">
        <result     property="clauseId"   column="clause_id"     />
        <result property="clauseNo" column="clause_no"   />
        <result property="clause" column="clause"   />
        <result property="isStar" column="is_star"   />
        <result property="detailRulesTitle" column="detail_rules_title"   />
        <result property="detailRulesDesc" column="detail_rules_desc"   />
        <result property="evidenceMaterial" column="evidence_material"   />
        <result property="regulationFile" column="regulation_file"   />
        <result property="internationalReference" column="international_reference"   />
        <result property="versionId" column="version_id"   />
        <collection property="cstEvaluationCriterionList"
                    column="{clause_id=clause_id, version_id=version_id}"
                    ofType="CstEvaluationCriterion"
                    javaType="ArrayList"
                    select="selectCstEvaluationCriterion" />
    </resultMap>


    <select id="selectDetailByVersionIdAndClauseId" resultMap="CstCertificationStandardsResult">
        <include refid="selectCstCertificationStandardsVo"/>
        where version_id=#{versionId} and clause_id = #{clauseId}
    </select>

    <select id="selectCstCertificationStandardVOByIds" parameterType="String"
            resultType="com.thas.web.dto.CstCertificationStandardVO">
        select
        id,
        clause_id clauseId,
        clause_no clauseNo,
        clause,
        version_id versionId
        from cst_certification_standards
        where id in
        <foreach collection="ids.split(',')" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectCstCertificationStandardVONoInIds" resultType="com.thas.web.dto.CstCertificationStandardVO">
        select
        id,
        clause_id clauseId,
        clause_no clauseNo,
        clause,
        version_id versionId
        from cst_certification_standards
        where version_id = #{versionId}
        <if test="ids != null and ids != ''">
            and
            id not in
            <foreach collection="ids.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectCstCertificationStandardsByDomainId" resultType="com.thas.web.dto.CstCertificationStandardVO">
        select
        id,
        clause_id clauseId,
        clause_no clauseNo,
        clause,
        is_star isStar,
        domain_id domainId,
        version_id versionId
        from cst_certification_standards
        where version_id = #{versionId} and domain_id in
        <foreach collection="domainIds.split(',')" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectCstCertificationStandardsByClauseIds" resultType="com.thas.web.dto.CstCertificationStandardVO">
        select
        id,
        clause_id clauseId,
        clause_no clauseNo,
        clause,
        is_star isStar,
        domain_id domainId,
        version_id versionId
        from cst_certification_standards
        where version_id = #{versionId} and clause_id in
        <foreach collection="clauseIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectCstCertificationStandardsNotInDomainId" resultType="com.thas.web.dto.CstCertificationStandardVO">
        select
        id,
        clause_id clauseId,
        clause_no clauseNo,
        clause,
        is_star isStar,
        version_id versionId
        from cst_certification_standards
        where version_id = #{versionId}
        <if test="domainIds != null and domainIds != ''">
            and domain_id not in
            <foreach collection="domainIds.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectGroupClause" resultType="com.thas.web.dto.GroupClauseInfo">
        select
        domain_id groupId,
        clause_id clauseId
        from cst_certification_standards
        where version_id = #{versionId}
        <if test="domainIds != null and domainIds != ''">
            and domain_id in
            <foreach collection="domainIds.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectCstCertificationStandardsCountByVersionId" parameterType="Long" resultType="Integer">
        select count(*) from cst_certification_standards where version_id = #{versionId}
    </select>

    <select id="selectCstCertificationStandardsCountByDomainIds" resultType="Integer">
        select count(*) from cst_certification_standards where version_id = #{versionId} and
        domain_id in
        <foreach collection="domainIds.split(',')" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectCstEvaluationCriterion" resultMap="ACstEvaluationCriterionResult">
        select  certification_standards_id,evaluate,standard,version_id
        from cst_evaluation_criterion
        where version_id = #{version_id} and certification_standards_id=#{clause_id}
    </select>

    <update id="updateByClauseIdAndVersionId" parameterType="CstCertificationStandards">
        update cst_certification_standards
        <trim prefix="SET" suffixOverrides=",">
            <if test="chapterId != null">chapter_id = #{chapterId},</if>
            <if test="chapterNo != null and chapterNo != ''">chapter_no = #{chapterNo},</if>
            <if test="chapter != null and chapter != ''">chapter = #{chapter},</if>
            <if test="sectionId != null">section_id = #{sectionId},</if>
            <if test="sectionNo != null and sectionNo != ''">section_no = #{sectionNo},</if>
            <if test="section != null and section != ''">section = #{section},</if>
            <if test="articleId != null">article_id = #{articleId},</if>
            <if test="articleNo != null and articleNo != ''">article_no = #{articleNo},</if>
            <if test="article != null and article != ''">article = #{article},</if>
            <if test="clauseId != null">clause_id = #{clauseId},</if>
            <if test="clauseNo != null and clauseNo != ''">clause_no = #{clauseNo},</if>
            <if test="clause != null and clause != ''">clause = #{clause},</if>
            <if test="isStar != null and isStar != ''">is_star = #{isStar},</if>
            <if test="detailRulesTitle != null and detailRulesTitle != ''">detail_rules_title = #{detailRulesTitle},</if>
            <if test="detailRulesDesc != null and detailRulesDesc != ''">detail_rules_desc = #{detailRulesDesc},</if>
            <if test="evidenceMaterial != null and evidenceMaterial != ''">evidence_material = #{evidenceMaterial},</if>
            <if test="regulationFile != null and regulationFile != ''">regulation_file = #{regulationFile},</if>
            <if test="internationalReference != null and internationalReference != ''">international_reference = #{internationalReference},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="versionId != null">version_id = #{versionId},</if>
            <if test="domainId  != null">domain_id = #{domainId },</if>
        </trim>
        where version_id = #{versionId} and clause_id = #{clauseId}
    </update>

    <update id="updateByDomainIdAndVersionId" parameterType="CstCertificationStandards">
        update cst_certification_standards set domain_id = 0
        where version_id = #{versionId} and domain_id = #{domainId}
    </update>

    <update id="updateByClauseIdAndVersionIds" parameterType="java.util.Map">
        update cst_certification_standards set domain_id = #{domainId}
        where version_id = #{versionId}
        and clause_id in
        <foreach collection="clauseIds.split(',')" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>


    <update id="updateStatusDisableByOtherVersionId" parameterType="long">
        update cst_certification_standards set status = '0' where version_id != #{versionId}
    </update>

    <update id="updateStatusStartByVersionId" parameterType="long">
        update cst_certification_standards set status = '1' where version_id = #{versionId}
    </update>

    <select id="selectCountByVersionId" parameterType="String" resultType="map">
        select count(distinct chapter_id) chapterCount,
               count(distinct section_id) sectionCount,
               count(distinct article_id) articleCount,
               count(distinct clause_id) clauseCount
        from cst_certification_standards
        where version_id = #{versionId}
    </select>

    <select id="selectCountDoMianIdByVersionId" parameterType="long" resultType="int">
        select count(1) from cst_certification_standards where domain_id = 0 and version_id =  #{versionId}
    </select>

    <select id="selectCountVersioningByVersionId" parameterType="long" resultType="int">
        select count(1) from cst_versioning where status= 0 and del_flag = 0 and version_id = #{versionId}
    </select>

    <select id="selectByClauseIdsAndVersionId" resultMap="CstCertificationStandardsResult">
        <include refid="selectCstCertificationStandardsVo"/>
        where version_id = #{versionId} and clause_id in (
        <foreach collection="clauseIds" item="clauseId" separator=",">
            #{clauseId}
        </foreach>
        )

    </select>

    <select id="selectAllClauseIdByVersionId" parameterType="String" resultType="com.thas.web.domain.CstCertificationStandards">
        select clause_id clauseId,clause_no clauseNo,chapter_id chapterId,chapter_no chapterNo,article_id articleId ,is_star isStar,domain_id domainId from cst_certification_standards where version_id = #{versionId}
    </select>

    <select id="selectDomainIdListByVersionId" parameterType="Long" resultType="String">
        select distinct(domain_id) from cst_certification_standards where version_id = #{versionId};
    </select>

    <select id="selectDomainIdAndClauseIdByVersionId" parameterType="Long" resultMap="CstCertificationStandardsResult">
        select chapter_id,
               chapter,
               chapter_no,
               domain_id,
               clause_id,
               is_star
        from cst_certification_standards
        where version_id = #{versionId}
    </select>

    <select id="selectClauseNosByClauseIds" resultType="java.lang.String">
        SELECT distinct clause_no clauseNo FROM `cst_certification_standards` WHERE
        version_id = #{autCsId} AND
         clause_id IN
        (
        <foreach collection="clauseIds" item="item" separator=",">
            #{item}
        </foreach>
        )
    </select>

</mapper>
