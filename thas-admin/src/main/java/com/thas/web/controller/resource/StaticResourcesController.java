package com.thas.web.controller.resource;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.thas.common.constant.Constants;
import com.thas.common.crypto.CryptoImpl;
import com.thas.common.crypto.ICrypto;
import com.thas.common.crypto.enums.AlgorithmEnum;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.StringUtils;
import com.thas.common.utils.file.FileTypeUtils;
import com.thas.common.utils.file.FileUtils;
import com.thas.common.utils.file.MimeTypeUtils;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * 外部跳转请求文件资源处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/resource")
@Api(value = "resource", tags = "StaticResourcesController")
public class StaticResourcesController {
    private static final Logger log = LoggerFactory.getLogger(StaticResourcesController.class);

    /**
     * host: *************
     */
    @Value("${sftp.image.host}")
    private String host;

    @Value("${outFileDownload.resourceUrl}")
    private String resourceUrl;

    @Value("${privacy.crypto.sm4Key}")
    private String sm4Key;

    @Autowired
    private ICrypto crypto;

    @ApiOperation("get")
    @GetMapping({"/get/{filePath}"})
    public void fileDownload(HttpServletResponse response, @PathVariable("filePath") String filePath) {
        try {
            //解密
            String decodeFilePath = crypto.decrypt(AlgorithmEnum.SM4, sm4Key, filePath);

            if (!FileUtils.checkAllowOutsideDownload(decodeFilePath)) {
                throw new Exception(StringUtils.format("文件路径({})非法，不允许访问。 ", filePath));
            }
            String fileType = FileTypeUtils.getFileType(decodeFilePath);
            String mimeType = MimeTypeUtils.getMimeTypeByExtension(fileType);
            if(StringUtils.isBlank(mimeType)){
               log.error("文件格式错误,解密的文件名为：{},[fileType:{}],[mimeType:{}]",decodeFilePath,fileType,mimeType);
               throw new ServiceException("文件格式错误");
            }

            // 本地资源路径
            String url = resourceUrl + Constants.SLASH + decodeFilePath;
            HttpRequest httpRequest = HttpUtil.createGet(url);
            httpRequest.contentType(mimeType);
            response.setContentType(mimeType);
            httpRequest.execute().writeBody(response.getOutputStream(), true, null);
        } catch (Exception e) {
            log.error("访问文件失败", e);
        }
    }

}
