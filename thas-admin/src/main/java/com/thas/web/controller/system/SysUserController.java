package com.thas.web.controller.system;

import com.alibaba.fastjson.JSON;
import com.thas.common.annotation.Log;
import com.thas.common.constant.Constants;
import com.thas.common.constant.UserConstants;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.domain.entity.SysRole;
import com.thas.common.core.domain.entity.SysUser;
import com.thas.common.core.domain.model.LoginUser;
import com.thas.common.core.page.TableDataInfo;
import com.thas.common.core.redis.RedisCache;
import com.thas.common.enums.BusinessType;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.SecurityUtils;
import com.thas.common.utils.StringUtils;
import com.thas.common.utils.bean.BeanUtils;
import com.thas.common.utils.poi.ExcelUtil;
import com.thas.common.utils.sms.SMSSendTool;
import com.thas.framework.web.service.TokenService;
import com.thas.system.domain.SysUserOnline;
import com.thas.system.domain.vo.SendEmailCodeVo;
import com.thas.system.domain.vo.UpdatePasswordVo;
import com.thas.system.domain.vo.VerifyEmailCodeVo;
import com.thas.system.service.ISysPostService;
import com.thas.system.service.ISysRoleService;
import com.thas.system.service.ISysUserService;
import com.thas.web.domain.HospitalBaseInfo;
import com.thas.web.dto.*;
import com.thas.web.service.IFileShareService;
import com.thas.web.service.IHospitalBaseInfoService;
import com.thas.web.service.IReviewerBaseInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@Api(value = "SysUserController", tags = "用户信息")
@RestController
@RequestMapping("/system/user")
@Slf4j
public class SysUserController extends BaseController {
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysPostService postService;

    @Autowired
    private IFileShareService fileShareService;

    @Value("${user.resetPassword}")
    private String resetPassword;

    @Value("${user.encryptResetPassword}")
    private String encryptResetPassword;

    @Value("${sms.environment}")
    private String environment;

    @Autowired
    private IHospitalBaseInfoService hospitalBaseInfoService;

    @Autowired
    private IReviewerBaseInfoService reviewerBaseInfoService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 获取用户列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @ApiOperation("获取用户列表")
    @GetMapping("/list")
    public TableDataInfo list(SysUser user) {
        startPage();
        List<SysUser> list = userService.selectUserList(user);
        return getDataTable(list);
    }

    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:user:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysUser user) {
        List<SysUser> list = userService.selectUserList(user);
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.exportExcel(response, list, "用户数据");
    }

    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('system:user:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        List<SysUser> userList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = userService.importUser(userList, updateSupport, operName);
        return AjaxResult.success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.importTemplateExcel(response, "用户数据");
    }

    /**
     * 根据用户编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping(value = {"/", "/{userId}"})
    public AjaxResult getInfo(@PathVariable(value = "userId", required = false) Long userId) {
        userService.checkUserDataScope(userId);
        AjaxResult ajax = AjaxResult.success();
        List<SysRole> roles = roleService.selectRoleAll();
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        ajax.put("posts", postService.selectPostAll());
        if (StringUtils.isNotNull(userId)) {
            SysUser sysUser = userService.selectUserById(userId);
            ajax.put(AjaxResult.DATA_TAG, sysUser);
            ajax.put("postIds", postService.selectPostListByUserId(userId));
            ajax.put("roleIds", sysUser.getRoles().stream().map(SysRole::getRoleId).collect(Collectors.toList()));
        }
        return ajax;
    }

    /**
     * 新增用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @ApiOperation("新增用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userName", value = "用户名", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "nickName", value = "昵称", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "email", value = "邮箱", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "phonenumber", value = "手机号", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "sex", value = "性别（0=男,1=女,2=未知）", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "avatar", value = "头像", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "password", value = "密码", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "salt", value = "盐密码", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "status", value = "0=正常,1=停用", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "isLeave", value = "是否请假（0=正常,1=请假）", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "roleIds", value = "角色（数组角色id）", dataType = "Long", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "postIds", value = "岗位（岗位组id）", dataType = "Long", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "hospitalApplyNo", value = "医院编号", dataType = "String", dataTypeClass = String.class)
    })
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult add(@Validated @RequestBody SysUser user) {
        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user.getUserName()))) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhonenumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setCreateBy(getUsername());
        user.setPassword(SecurityUtils.encryptPassword(encryptResetPassword));

        int res = userService.insertUser(user);

        if (StringUtils.isNotEmpty(user.getHospitalApplyNo())) {
            //同步更新医院信息表联系人电话和邮箱数据
            this.sysUserByUpdateHospitalBaseInfo(user);

            // 如果是生成医院账户，短信/邮件通知用户
            hospitalBaseInfoService.notifyHosAccount(user.getHospitalApplyNo(), user.getUserName(), resetPassword);
            // 将医院的临时文件关联到当前id。
            UpdFileShareTempDataDto updFileShareTempDataDto = new UpdFileShareTempDataDto();
            updFileShareTempDataDto.setTempCode(user.getHospitalApplyNo());
            updFileShareTempDataDto.setUserId(user.getUserId());
            updFileShareTempDataDto.setOwnerId(String.valueOf(user.getUserId()));
            updFileShareTempDataDto.setRoleKey(user.getRoleKey());
            updFileShareTempDataDto.setNickName(user.getNickName());
            fileShareService.updateByOwnerId(updFileShareTempDataDto);
        }
        return toAjax(res);
    }

    private void sysUserByUpdateHospitalBaseInfo(SysUser user) {
        CheckParamOnlyRequest checkParamOnlyRequest = new CheckParamOnlyRequest();
        checkParamOnlyRequest.setHospitalApplyNo(user.getHospitalApplyNo());
        checkParamOnlyRequest.setContactsPhone(user.getPhonenumber());
        checkParamOnlyRequest.setEmail(user.getEmail());
        checkParamOnlyRequest.setRole(Constants.HospitalConstants.ROLE_HOSPITAL);
        //校验医院信息是否唯一
        reviewerBaseInfoService.checkParamOnly(checkParamOnlyRequest);
        HospitalBaseInfo hospitalBaseInfo = new HospitalBaseInfo();
        hospitalBaseInfo.setApplyNo(user.getHospitalApplyNo());
        hospitalBaseInfo.setContactsPhone(user.getPhonenumber());
        hospitalBaseInfo.setContactsEmail(user.getEmail());
        hospitalBaseInfoService.updateHospitalBaseInfo(hospitalBaseInfo);
    }


    /**
     * 修改用户
     */
    //@PreAuthorize("@ss.hasPermi('system:user:edit')")
    @ApiOperation("修改用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "userName", value = "用户名", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "nickName", value = "昵称", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "email", value = "邮箱", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "phonenumber", value = "手机号", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "sex", value = "性别（0=男,1=女,2=未知）", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "avatar", value = "头像", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "password", value = "密码", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "salt", value = "盐密码", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "status", value = "0=正常,1=停用", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "isLeave", value = "是否请假（0=正常,1=请假）", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "roleIds", value = "角色（数组角色id）", dataType = "Long", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "postIds", value = "岗位（岗位组id）", dataType = "Long", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "hospitalApplyNo", value = "医院编号", dataType = "String", dataTypeClass = String.class)
    })
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @Transactional(rollbackFor = Exception.class)
    @PutMapping
    public AjaxResult edit(@RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        if (StringUtils.isNotEmpty(user.getPhonenumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        if (StringUtils.isNotEmpty(user.getHospitalApplyNo())) {
            //同步更新医院信息表联系人电话和邮箱数据
            this.sysUserByUpdateHospitalBaseInfo(user);
        } else {
            // 如果修改成功sys_user表中的数据，需要同步reviewer_base_info表
            reviewerBaseInfoService.updateReviewerBaseInfoByUser(user);
            user.setUpdateBy(getUsername());
        }
        userService.updateUser(user);

        //有登录用户信息时，更新缓存
        Collection<String> keys = redisCache.keys(Constants.LOGIN_TOKEN_KEY + "*");
        for (String key : keys) {
            LoginUser loginUser = redisCache.getCacheObject(key);
            if (StringUtils.isNotEmpty(user.getUserName()) && StringUtils.isNotNull(loginUser.getUser())) {
                if (StringUtils.equals(user.getUserName(), loginUser.getUsername())) {
                    SysUser sysUser = loginUser.getUser();
                    sysUser.setNickName(user.getNickName());
                    sysUser.setPhonenumber(user.getPhonenumber());
                    sysUser.setEmail(user.getEmail());
                    sysUser.setSex(user.getSex());
                    sysUser.setIsLeave(user.getIsLeave());
                    tokenService.setLoginUser(loginUser);
                    log.info("edit-更新redis缓存用户信息成功，缓存SysUser数据为：{}", JSON.toJSONString(sysUser));
                    break;
                }
            }
        }
        return AjaxResult.success();
    }

    /**
     * 删除用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:remove')")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds) {
        if (ArrayUtils.contains(userIds, getUserId())) {
            return error("当前用户不能删除");
        }
        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 重置密码
     */
    @PreAuthorize("@ss.hasPermi('system:user:resetPwd')")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", dataType = "Long", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "managerPwd", value = "管理员密码", dataType = "String", dataTypeClass = String.class)
    })
    @ApiOperation("重置密码")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult resetPwd(@RequestBody SysUserDto userDto) {
        SysUser user = new SysUser();
        BeanUtils.copyProperties(userDto, user);
        logger.info("用户管理-输入的管理员密码为{}", user.getManagerPwd());

        userService.checkUserAllowed(user);
        //验证管理员密码是否正确
        userService.verifyPwd(user.getManagerPwd());
        user.setPassword(SecurityUtils.encryptPassword(encryptResetPassword));
        user.setUpdateBy(getUsername());
        int i = userService.resetPwd(user);

        SysUser sysUser = userService.selectUserById(user.getUserId());
        String message = String.format("您好，您在深圳市卫健医院评审评价研究中心的登录密码已重置为：%s，欢迎使用~", resetPassword);
        //重置密码后，通知用户的方法，邮箱"0",手机短信"1"
        if (i > 0) {
            if ("0".equals(userDto.getReceivingMethod())) {
                //发送邮箱
                String email = sysUser.getEmail();
                userService.sendEmailMessage(email, message, "重置密码");
                logger.info("邮箱发送");
            } else if ("1".equals(userDto.getReceivingMethod())) {
                if (environment.equals("test")) {
                    log.info("测试环境暂时未开通手机短信功能！手机号为：{}，内容为：{}", sysUser.getPhonenumber(), message);
                } else {
                    //发送手机短信
                    String result = SMSSendTool.sendSMS(sysUser.getPhonenumber(), message);
                    logger.info("发送短信");
                    if (!"success".equals(result)) {
                        throw new ServiceException("短信发送失败！");
                    }
                }
            }
        }
        return toAjax(i);
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        user.setUpdateBy(getUsername());
        return toAjax(userService.updateUserStatus(user));
    }

    /**
     * 根据用户编号获取授权角色
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping("/authRole/{userId}")
    public AjaxResult authRole(@PathVariable("userId") Long userId) {
        AjaxResult ajax = AjaxResult.success();
        SysUser user = userService.selectUserById(userId);
        List<SysRole> roles = roleService.selectRolesByUserId(userId);
        ajax.put("user", user);
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        return ajax;
    }

    /**
     * 用户授权角色
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    public AjaxResult insertAuthRole(Long userId, Long[] roleIds) {
        userService.insertUserAuth(userId, roleIds);
        return success();
    }

    /**
     * 发送邮件验证消息
     */
    @ApiOperation("发送邮件验证消息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "imgCode", value = "图片的验证码", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "uuid", value = "图片标识码", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "toEmailNo", value = "邮箱接受方账号", dataType = "String", dataTypeClass = String.class)

    })
    @PostMapping("/sendEmailCode")
    public AjaxResult sendEmailCode(@RequestBody SendEmailCodeVo sendEmailCodeVo) {
        userService.sendEmailCode(sendEmailCodeVo);
        return success();
    }

    /**
     * 【免登】发送邮件验证消息
     */
    @ApiOperation("【免登】发送邮件验证消息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "imgCode", value = "图片的验证码", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "uuid", value = "图片标识码", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "toEmailNo", value = "邮箱接受方账号", dataType = "String", dataTypeClass = String.class)

    })
    @PostMapping("/bing/sendEmailCode")
    public AjaxResult sendEmailCodeBing(@RequestBody SendEmailCodeVo sendEmailCodeVo) {
        userService.sendEmailCode(sendEmailCodeVo);
        return success();
    }

    /**
     * 验证邮箱验证码
     */
    @ApiOperation("验证邮箱验证码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "emailCode", value = "邮件验证码", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "toEmailNo", value = "接收方的邮箱号", dataType = "String", dataTypeClass = String.class)

    })
    @PostMapping("/verifyEmailCode")
    public AjaxResult verifyEmailCode(@RequestBody VerifyEmailCodeVo verifyEmailCodeVo) {
        userService.verifyEmailCode(verifyEmailCodeVo);
        return success();
    }

    /**
     * 【免登】验证邮箱验证码
     */
    @ApiOperation("【免登】验证邮箱验证码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "emailCode", value = "邮件验证码", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "toEmailNo", value = "接收方的邮箱号", dataType = "String", dataTypeClass = String.class)

    })
    @PostMapping("/bing/verifyEmailCode")
    public AjaxResult verifyEmailCodeBing(@RequestBody VerifyEmailCodeVo verifyEmailCodeVo) {
        userService.verifyEmailCode(verifyEmailCodeVo);
        return success();
    }

    /**
     * 通过邮箱修改密码
     */
    @ApiOperation("通过邮箱修改密码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "emailNo", value = "邮箱号", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "password", value = "密码", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "confirmPwd", value = "确认密码", dataType = "String", dataTypeClass = String.class)

    })
    @PostMapping("/updatePwdByEmail")
    public AjaxResult updatePwdByEmail(@RequestBody UpdatePasswordVo updatePasswordVo) {
        userService.updatePasswordByEmail(updatePasswordVo);
        return success();
    }

    /**
     * 【免登】通过邮箱修改密码
     */
    @ApiOperation("【免登】通过邮箱修改密码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "emailNo", value = "邮箱号", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "password", value = "密码", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "confirmPwd", value = "确认密码", dataType = "String", dataTypeClass = String.class)

    })
    @PostMapping("/bing/updatePwdByEmail")
    public AjaxResult updatePwdByEmailBing(@RequestBody UpdatePasswordVo updatePasswordVo) {
        userService.updatePasswordByEmail(updatePasswordVo);
        return success();
    }

    /**
     * 通过角色分组查找用户
     */
    @ApiOperation("通过角色分组查找用户")
    @PostMapping("/selectUserListGroupByRole")
    public AjaxResult selectUserListGroupByRole() {
        return AjaxResult.success(userService.selectUserListGroupByRole(null));
    }

}
