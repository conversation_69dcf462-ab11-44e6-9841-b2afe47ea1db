package com.thas.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.enums.BusinessType;
import com.thas.system.domain.SysUserHospital;
import com.thas.system.service.ISysUserHospitalService;
import com.thas.common.utils.poi.ExcelUtil;
import com.thas.common.core.page.TableDataInfo;

/**
 * 用户医院关系Controller
 *
 * <AUTHOR>
 * @date 2022-01-20
 */
@RestController
@RequestMapping("/system/hospital")
public class SysUserHospitalController extends BaseController
{
    @Autowired
    private ISysUserHospitalService sysUserHospitalService;

    /**
     * 查询用户医院关系列表
     */
    @PreAuthorize("@ss.hasPermi('system:hospital:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysUserHospital sysUserHospital)
    {
        startPage();
        List<SysUserHospital> list = sysUserHospitalService.selectSysUserHospitalList(sysUserHospital);
        return getDataTable(list);
    }

    /**
     * 导出用户医院关系列表
     */
    @PreAuthorize("@ss.hasPermi('system:hospital:export')")
    @Log(title = "用户医院关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysUserHospital sysUserHospital)
    {
        List<SysUserHospital> list = sysUserHospitalService.selectSysUserHospitalList(sysUserHospital);
        ExcelUtil<SysUserHospital> util = new ExcelUtil<SysUserHospital>(SysUserHospital.class);
        util.exportExcel(response, list, "用户医院关系数据");
    }

    /**
     * 获取用户医院关系详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:hospital:query')")
    @GetMapping(value = "/{userId}")
    public AjaxResult getInfo(@PathVariable("userId") Long userId)
    {
        return AjaxResult.success(sysUserHospitalService.selectSysUserHospitalByUserId(userId));
    }

    /**
     * 新增用户医院关系
     */
    @PreAuthorize("@ss.hasPermi('system:hospital:add')")
    @Log(title = "用户医院关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysUserHospital sysUserHospital)
    {
        return toAjax(sysUserHospitalService.insertSysUserHospital(sysUserHospital));
    }

    /**
     * 修改用户医院关系
     */
    @PreAuthorize("@ss.hasPermi('system:hospital:edit')")
    @Log(title = "用户医院关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysUserHospital sysUserHospital)
    {
        return toAjax(sysUserHospitalService.updateSysUserHospital(sysUserHospital));
    }

    /**
     * 删除用户医院关系
     */
    @PreAuthorize("@ss.hasPermi('system:hospital:remove')")
    @Log(title = "用户医院关系", businessType = BusinessType.DELETE)
	@DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds)
    {
        return toAjax(sysUserHospitalService.deleteSysUserHospitalByUserIds(userIds));
    }
}
