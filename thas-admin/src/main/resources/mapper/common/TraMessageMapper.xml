<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thas.web.mapper.TraMessageMapper">

    <resultMap type="TraMessage" id="TraMessageResult">
        <result property="id" column="id"/>
        <result property="learnResourceId" column="learn_resource_id"/>
        <result property="pid" column="pid"/>
        <result property="messageContent" column="message_content"/>
        <result property="replyNum" column="reply_num"/>
        <result property="likeNum" column="like_num"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateId" column="update_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="title" column="title"/>
    </resultMap>

    <sql id="selectTraMessageVo">
        select id,
               learn_resource_id,
               pid,
               message_content,
               reply_num,
               like_num,
               create_id,
               create_time,
               update_id,
               update_time
        from tra_message
    </sql>

    <select id="selectTraMessageList" parameterType="TraMessage" resultMap="TraMessageResult">
        select tm.*, tlr.title title
        from tra_message tm left join tra_learn_resource tlr on tlr.id = tm.learn_resource_id
        <where>
            <if test="learnResourceId != null ">and tm.learn_resource_id = #{learnResourceId}</if>
            <if test="learnResourceName != null ">and tlr.title like concat('%',#{learnResourceName},'%')</if>
            <if test="pid != null ">and tm.pid = #{pid}</if>
            <if test="messageContent != null  and messageContent != ''">and tm.message_content = #{messageContent}</if>
            <if test="replyNum != null ">and tm.reply_num = #{replyNum}</if>
            <if test="likeNum != null ">and tm.like_num = #{likeNum}</if>
            <if test="createId != null  and createId != ''">and tm.create_id = #{createId}</if>
            <if test="updateId != null  and updateId != ''">and tm.update_id = #{updateId}</if>
        </where>
        order by tm.id desc
    </select>

    <select id="selectTraMessageById" parameterType="Long" resultMap="TraMessageResult">
        <include refid="selectTraMessageVo"/>
        where id = #{id}
    </select>
    <select id="selectTraMessageCount" resultType="java.lang.Long">
        select count(1)
        from tra_message
        <where>
            <if test="learnResourceId != null ">and learn_resource_id = #{learnResourceId}</if>
            <if test="pid != null ">and pid = #{pid}</if>
            <if test="messageContent != null  and messageContent != ''">and message_content = #{messageContent}</if>
            <if test="replyNum != null ">and reply_num = #{replyNum}</if>
            <if test="likeNum != null ">and like_num = #{likeNum}</if>
            <if test="createId != null  and createId != ''">and create_id = #{createId}</if>
            <if test="updateId != null  and updateId != ''">and update_id = #{updateId}</if>
        </where>
    </select>

    <insert id="insertTraMessage" parameterType="TraMessage" useGeneratedKeys="true" keyProperty="id">
        insert into tra_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="learnResourceId != null">learn_resource_id,</if>
            <if test="pid != null">pid,</if>
            <if test="messageContent != null">message_content,</if>
            <if test="replyNum != null">reply_num,</if>
            <if test="likeNum != null">like_num,</if>
            <if test="createId != null and createId != ''">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null and updateId != ''">update_id,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="learnResourceId != null">#{learnResourceId},</if>
            <if test="pid != null">#{pid},</if>
            <if test="messageContent != null">#{messageContent},</if>
            <if test="replyNum != null">#{replyNum},</if>
            <if test="likeNum != null">#{likeNum},</if>
            <if test="createId != null and createId != ''">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null and updateId != ''">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateTraMessage" parameterType="TraMessage">
        update tra_message
        <trim prefix="SET" suffixOverrides=",">
            <if test="learnResourceId != null">learn_resource_id = #{learnResourceId},</if>
            <if test="pid != null">pid = #{pid},</if>
            <if test="messageContent != null">message_content = #{messageContent},</if>
            <if test="replyNum != null">reply_num = #{replyNum},</if>
            <if test="likeNum != null">like_num = #{likeNum},</if>
            <if test="createId != null and createId != ''">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null and updateId != ''">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="replyNumPlus">
        update tra_message
        set reply_num = reply_num + 1
        where id = #{pid};
    </update>
    <update id="likeNumPlus">
        update tra_message
        set like_num = like_num + 1
        where id = #{pid};
    </update>
    <update id="likeNumMini">
        update tra_message
        set like_num = like_num - 1
        where id = #{pid};
    </update>

    <delete id="deleteTraMessageById" parameterType="Long">
        delete
        from tra_message
        where id = #{id}
    </delete>

    <delete id="deleteTraMessageByIds" parameterType="String">
        delete from tra_message where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
        or pid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>