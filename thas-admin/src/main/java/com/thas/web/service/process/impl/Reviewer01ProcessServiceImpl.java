package com.thas.web.service.process.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.thas.common.constant.Constants;
import com.thas.common.constant.Constants.HospitalConstants;
import com.thas.common.enums.AutSaAudBusinessCodeEnum;
import com.thas.common.enums.AutSaAudResultEnum;
import com.thas.common.enums.AutSaAudRoleEnum;
import com.thas.common.enums.AutSaAudStatusEnum;
import com.thas.common.enums.AutSaAudSubmitTypeEnum;
import com.thas.common.enums.ServiceExceptionEnum;
import com.thas.common.enums.StatusProcessEnum;
import com.thas.common.exception.ServiceException;
import com.thas.web.domain.AutSaAud;
import com.thas.web.domain.AutSaAudBusinessData;
import com.thas.web.domain.AutSaAudList;
import com.thas.web.domain.AutSaAudQueryDTO;
import com.thas.web.domain.AutSaAudReport;
import com.thas.web.domain.AutSaAudSaveDTO;
import com.thas.web.domain.HospitalReviewer;
import com.thas.web.domain.vo.AutSaAudDetailVO;
import com.thas.web.domain.vo.DomainGroupNode;
import com.thas.web.domain.vo.EvaluateResultStatsVo;
import com.thas.web.dto.CstCertificationStandardVO;
import com.thas.web.dto.HosPlanDetailDTO;
import com.thas.web.dto.ReviewerShareReq;
import com.thas.web.dto.ShareDescDTO;
import com.thas.web.mapper.AutSaAudMapper;
import com.thas.web.mapper.HospitalReviewerMapper;
import com.thas.web.service.*;
import com.thas.web.service.impl.HospitalBaseInfoServiceImpl;
import com.thas.web.service.process.BaseProcessService;
import com.thas.web.service.process.CommonProcessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 评审员评审流程服务 Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-01
 */
@Component("reviewer01ProcessService")
@Slf4j
public class Reviewer01ProcessServiceImpl implements BaseProcessService {

    @Resource
    private CommonProcessService commonProcessService;

    @Resource
    private IAutSaAudBusinessDataService autSaAudBusinessDataService;

    @Resource
    private IHospitalDomainGroupService hospitalDomainGroupService;

    @Resource
    private HospitalReviewerMapper hospitalReviewerMapper;

    @Resource
    private ICstCertificationStandardsService cstCertificationStandardsService;

    @Resource
    private AutSaAudMapper autSaAudMapper;

    @Autowired
    private IReviewFitMoveClauseService reviewFitMoveClauseService;

    @Resource
    private IReviewerShareService reviewerShareService;

    @Resource
    private HospitalBaseInfoServiceImpl hospitalBaseInfoService;

    private Map<String, EvaluateResultStatsVo.EvaluateInfo> getInitializeResultMap() {
        Map<String, EvaluateResultStatsVo.EvaluateInfo> getInitializeResultMap = new HashMap<>();
        getInitializeResultMap.put(AutSaAudResultEnum.EXCELLENT.getCode().toString(), new EvaluateResultStatsVo.EvaluateInfo(AutSaAudResultEnum.EXCELLENT.getCode().toString()));
        getInitializeResultMap.put(AutSaAudResultEnum.GOOD.getCode().toString(), new EvaluateResultStatsVo.EvaluateInfo(AutSaAudResultEnum.GOOD.getCode().toString()));
        getInitializeResultMap.put(AutSaAudResultEnum.REACH_STANDARD.getCode().toString(), new EvaluateResultStatsVo.EvaluateInfo(AutSaAudResultEnum.REACH_STANDARD.getCode().toString()));
        getInitializeResultMap.put(AutSaAudResultEnum.PARTIAL_COMPLIANCE.getCode().toString(), new EvaluateResultStatsVo.EvaluateInfo(AutSaAudResultEnum.PARTIAL_COMPLIANCE.getCode().toString()));
        getInitializeResultMap.put(AutSaAudResultEnum.NOT_STANDARD.getCode().toString(), new EvaluateResultStatsVo.EvaluateInfo(AutSaAudResultEnum.NOT_STANDARD.getCode().toString()));
        getInitializeResultMap.put(AutSaAudResultEnum.NOT_APPLICABLE.getCode().toString(), new EvaluateResultStatsVo.EvaluateInfo(AutSaAudResultEnum.NOT_APPLICABLE.getCode().toString()));
        getInitializeResultMap.put(AutSaAudResultEnum.ABOVE_STANDARD.getCode().toString(), new EvaluateResultStatsVo.EvaluateInfo(AutSaAudResultEnum.ABOVE_STANDARD.getCode().toString()));
        return getInitializeResultMap;

    }


    @Override
    public void process(AutSaAudSaveDTO req) {
        long startTime = System.currentTimeMillis();
        log.info("reviewer01ProcessService.process ------ 开始");
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.SR_CLAUSE_PROCESS, AutSaAudStatusEnum.WAIT_SR_V_CLAUSE,
                AutSaAudStatusEnum.SR_V_CLAUSE_PROCESS)) {
            // 评审中  评审待复查  评审复查中 节点
            if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.SR_V_CLAUSE)) {
                // 校验款信息
                commonProcessService.checkClause(req);
                if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.WAIT_SR_V_CLAUSE)) {
                    // 评审待复查
                    commonProcessService.processInitialScene(req);
                } else {
                    commonProcessService.processClauseAndRJ04Scene(req);
                }
            } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE)) {
                // 共享修改状态改为0，用于证明已共享修改，无需再修改
                if (CollectionUtils.isNotEmpty(req.getAutSaAudLists())) {
                    List<AutSaAud> autSaAudLists = req.getAutSaAudLists();
                    autSaAudLists.forEach(o -> o.setIsShareUpdate(Constants.INT_ZERO));
                }
                //如果是评审组长修改节点，封装修改描述
                this.packAutDesc(req);
                // 直接跟新初查数据    节点不反转
                this.processSrClauseUpdate(req);
            } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE_SHARE)) {
                //当前评审共享提交类型只做转节点用
            } else {
                // 校验评审结   评审中 评审小结
                if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.SR_CLAUSE_PROCESS)) {

                    // 加入条款不适用条款逻辑
                    groupSummary(req);

                    this.processSummary(req, true);
                } else {
                    this.processSummary(req, false);
                }
            }
            if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.SR_CLAUSE_PROCESS)) {
                // 评审中 判断翻转节点  需要 初查+小主题小结+大主题小结+组小结全部完成
                this.flipSrClauseProcessStatus(req);
            }
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.SR_SUMMARY)) {
            // 评审总结
            this.processSrSummary(req);
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.FR_REPORT_F_M_SUMMARY)) {
            // 修改评审报告总结
            commonProcessService.processSummaryScene(req);
            List<AutSaAud> newAutSaAudList = req.getAutSaAudLists().stream().map(a -> {
                a.setSubmitType(AutSaAudSubmitTypeEnum.SR_SUMMARY.getSubmitType());
                return a;
            }).collect(Collectors.toList());
            commonProcessService.batchUpdateAutSaAud(newAutSaAudList);
            String rejectSubmitTypesStr = String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.FR_REPORT_R_CLAUSE.getSubmitType(),
                    AutSaAudSubmitTypeEnum.FR_REPORT_R_CLAUSE_CONFIRM.getSubmitType(), AutSaAudSubmitTypeEnum.FR_REPORT_R_SUMMARY.getSubmitType(),
                    AutSaAudSubmitTypeEnum.SR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE_M_SUMMARY.getSubmitType()));
            commonProcessService.batchInvalidAutSaAudByClauseIds(req.getAutCode(), rejectSubmitTypesStr, "");
        }
        log.info("reviewer01ProcessService.process ------ 结束 耗时：{}", System.currentTimeMillis() - startTime);
    }

    private void groupSummary(AutSaAudSaveDTO req) {
        if (!AutSaAudSubmitTypeEnum.SR_GROUP_SUMMARY.getSubmitType().equals(req.getSubmitType())) {
            return;
        }

        if (CollectionUtils.isEmpty(req.getAutSaAudLists())) {
            return;
        }

        // 分组小结，获取当前分组下全部不适用的条款的大主题id
        fillThemeSummary(req);
    }

    private void fillThemeSummary(AutSaAudSaveDTO req) {
        // 获取到需要填充的主题。
        List<AutSaAud> autSaAudLists = req.getAutSaAudLists();
        List<String> groupIdList = autSaAudLists.stream()
                .map(AutSaAud::getClauseId)
                .collect(Collectors.toList());
        List<String> themeIdList = reviewFitMoveClauseService.groupAllNotApplicableTheme(req.getAutCode(), groupIdList, req.getAutSaRelation().getAutCsId());

        if (CollectionUtils.isEmpty(themeIdList)) {
            return;
        }

        doFillThemeSummary(themeIdList, req);
    }

    private void doFillThemeSummary(List<String> themeIdList, AutSaAudSaveDTO req) {
        List<AutSaAud> batchInsertEntityList = new ArrayList<>();
        for (String themeId : themeIdList) {
            AutSaAud autSaAud = new AutSaAud();
            autSaAud.setAutCode(req.getAutCode());
            autSaAud.setAccountId(req.getAccountId());
            autSaAud.setClauseId(themeId);
            autSaAud.setSubmitType(AutSaAudSubmitTypeEnum.SR_BT_SUMMARY.getSubmitType());
            autSaAud.setAutResult(HospitalConstants.STR_NUM_1);
            autSaAud.setAutDesc("不适用");
            batchInsertEntityList.add(autSaAud);
        }

        // 查询出已经有的主题小结的记录
        List<AutSaAud> existThemeSummaryList = autSaAudMapper.selectAutSaAudListByAutCodeAndTypes(req.getAutCode(), AutSaAudSubmitTypeEnum.SR_BT_SUMMARY.getSubmitType());
        if (CollectionUtils.isNotEmpty(existThemeSummaryList)) {
            Set<String> existThemeIdSet = existThemeSummaryList.stream()
                    .map(AutSaAud::getClauseId)
                    .collect(Collectors.toSet());
            batchInsertEntityList = batchInsertEntityList.stream()
                    .filter(entity -> !existThemeIdSet.contains(entity.getClauseId()))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(batchInsertEntityList)) {
            commonProcessService.batchInsertAutSaAud(batchInsertEntityList);
        }
    }

    private void packAutDesc(AutSaAudSaveDTO req) {
        //评审待复查，评审复查中
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.WAIT_SR_V_CLAUSE,
                AutSaAudStatusEnum.SR_V_CLAUSE_PROCESS)) {
            List<AutSaAud> autSaAudLists = req.getAutSaAudLists();
            if (CollectionUtils.isNotEmpty(autSaAudLists)) {
                List<String> clauseIds = autSaAudLists.stream().filter(o -> StringUtils.isNotBlank(o.getClauseId())).map(AutSaAud::getClauseId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(clauseIds)) {
                    AutSaAud autSaAud = new AutSaAud();
                    autSaAud.setAutCode(req.getAutCode());
                    autSaAud.setSubmitType(req.getSubmitType());
                    autSaAud.setClauseId(StringUtils.join(clauseIds, ","));
                    List<AutSaAud> autSaAudList = autSaAudMapper.selectAutSaAudList(autSaAud);
                    //获取最新的驳回款进行置换
                    autSaAudList = autSaAudList.stream().sorted(Comparator.comparing(AutSaAud::getCreateTime).reversed()).
                            collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(AutSaAud::getClauseId))), ArrayList::new));
                    //查询出来的描述置换到’修改前的描述字段‘
                    if (CollectionUtils.isNotEmpty(autSaAudList)) {
                        List<AutSaAud> finalAutSaAudList = autSaAudList;
                        req.getAutSaAudLists().forEach(reqAutSaAud -> {
                            finalAutSaAudList.forEach(qryAutSaAud -> {
                                if (ObjectUtil.equal(reqAutSaAud.getClauseId(), qryAutSaAud.getClauseId())) {
                                    reqAutSaAud.setBeforeAutDesc(qryAutSaAud.getAutDesc());
                                }
                            });
                        });
                    }

                }
            }
        }
    }

    /**
     * 翻转评审中节点
     *
     * @param req
     */
    private void flipSrClauseProcessStatus(AutSaAudSaveDTO req) {
        // 查询当前评审初查 + 大主题小结 + 组小结 信息，
        List<String> querySubmitTypes = Arrays.asList(AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_BT_SUMMARY.getSubmitType(),
                AutSaAudSubmitTypeEnum.SR_GROUP_SUMMARY.getSubmitType());
        List<AutSaAud> hisClauseOrArticleAutSaAuds = autSaAudMapper.selectAutSaAudListByAutCodeAndTypes(req.getAutCode(), String.join(",", querySubmitTypes));
        // 组小结提交数量
        int groupsSubmitCount = hisClauseOrArticleAutSaAuds.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(),
                AutSaAudSubmitTypeEnum.SR_GROUP_SUMMARY)).collect(Collectors.toList()).size();
        // 大主题小结提交数量
        int btSubmitCount = hisClauseOrArticleAutSaAuds.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(),
                AutSaAudSubmitTypeEnum.SR_BT_SUMMARY)).collect(Collectors.toList()).size();
        // 评审初查提交数量
        int srClauseSubmitNum = hisClauseOrArticleAutSaAuds.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(),
                AutSaAudSubmitTypeEnum.SR_CLAUSE)).collect(Collectors.toList()).size();
        // 判断当前款数量 + 分配的组的小结数量
        // 款上限
        int totalCount = MapUtils.getInteger(cstCertificationStandardsService.selectCountByVersionId(req.getAutSaRelation().getAutCsId()), "clauseCount");
        // 查询分组 + 主题 + 条款信息
        List<DomainGroupNode> domainGroups = hospitalDomainGroupService.selectDomainGroup(null, req.getAutSaRelation().getAutCsId());
        // 组小结数量
        AtomicInteger groupsCount = new AtomicInteger();
        // 大主题小结数量
        AtomicInteger btCount = new AtomicInteger();
        domainGroups.stream().forEach(a -> {
            // 组小结
            groupsCount.getAndIncrement();
            a.getChildren().stream().forEach(aa -> {
                // 大主题 小结
                btCount.getAndIncrement();
            });
        });

        log.info("评审初查已提交数量：{} - 需要提交数量：{}，大主题小结已提交数量：{} - 需要提交数量：{}，组小结已提交数量：{} - 需要提交数量：{}",
                srClauseSubmitNum, totalCount, btSubmitCount, btCount.get(), groupsSubmitCount, groupsCount.get());

        // 满足条件翻转节点
        if (groupsSubmitCount == groupsCount.get() && btSubmitCount == btCount.get() && srClauseSubmitNum == totalCount) {
            //检验：评审中-030202全部共享通过后，转节点
            if (!ObjectUtil.equal(req.getIsSharePass(), Constants.STR_NUM_1)) {
                log.info("评审中-030202转节点时，评审共享未通过，未能转节点！");
                return;
            }

            //评审中翻转节点时，生成预评审报告
            commonProcessService.temReviewReport(req.getAutCode(), req.getAutSaRelation().getAutCsId());

            commonProcessService.updateAutSaRelation(commonProcessService.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), StatusProcessEnum.PS), req.getAutCode());
            req.setTemPassStatus(Constants.STR_NUM_1);
        }
    }

    /**
     * 处理评审小主题小结
     *
     * @param req 流程参数
     */
    private void processSummary(AutSaAudSaveDTO req, boolean flag) {
        // 校验分组信息
        int totalCount = this.checkDomainGroupsAndGetTotalCount(req, flag);
        // 提交评审小主题小结
        List<AutSaAud> hisClauseOrArticleAutSaAuds = autSaAudMapper.selectAutSaAudListByAutCodeAndTypes(req.getAutCode(), req.getSubmitType());
        if (flag && CollectionUtils.isNotEmpty(hisClauseOrArticleAutSaAuds)) {
            hisClauseOrArticleAutSaAuds = hisClauseOrArticleAutSaAuds.stream().filter(a -> StringUtils.equals(a.getAccountId(), req.getAccountId())).collect(Collectors.toList());
        }

        //评审中节点且提交类型为组小结和主题小结时，判断是否评完当前评审员所有小结，如果是转给其他所有评审员，获取提交的接口处理方法
        if (flag && AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.SR_BT_SUMMARY, AutSaAudSubmitTypeEnum.SR_GROUP_SUMMARY)) {
            Integer srBtAndGroupSummaryNum = req.getSrBtAndGroupSummaryNum();
            String submitTypes = AutSaAudSubmitTypeEnum.SR_BT_SUMMARY.getSubmitType() + "," + AutSaAudSubmitTypeEnum.SR_GROUP_SUMMARY.getSubmitType();
            List<AutSaAud> srBtAndGroupSummaryAutSaAudList = autSaAudMapper.selectAutSaAudListByAutCodeAndTypesAndAccountId(req.getAutCode(), submitTypes, req.getAccountId());

            //查询’当前用户提交的小结‘+’入参的条款判断是新增的小结’，判断是否等于负责的所有小结总数，有就评审共享
            List<AutSaAud> autSaAudLists = req.getAutSaAudLists();
            // 已提交数据的款id
            List<String> hisClauseIds = srBtAndGroupSummaryAutSaAudList.stream().map(AutSaAud::getClauseId).collect(Collectors.toList());
            // 新小结条款数据
            List<AutSaAud> insertList = autSaAudLists.stream().filter(k -> !hisClauseIds.contains(k.getClauseId())).collect(Collectors.toList());

            if ((insertList.size() + srBtAndGroupSummaryAutSaAudList.size()) > srBtAndGroupSummaryNum) {
                // 已提交数据 + 当前提交的数据 > 条款上限，直接异常
                log.error("更新操作，节点已提交数据数量：{} + 当前提交的数据数量：{} > 小结条款上限数量：{}", hisClauseIds.size(), insertList.size(), totalCount);
                throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
            } else if ((insertList.size() + srBtAndGroupSummaryAutSaAudList.size()) == srBtAndGroupSummaryNum) {
                //评审共享
                ReviewerShareReq reviewerShareReq = new ReviewerShareReq();
                reviewerShareReq.setAutCode(req.getAutSaRelation().getAutCode());
                reviewerShareReq.setApplyNo(req.getAutSaRelation().getHospitalApplyNo());
                reviewerShareReq.setReviewerId(req.getAccountId());
                reviewerShareReq.setIsShare(Constants.INT_ONE);
                reviewerShareService.submit(reviewerShareReq);

            }
        }

        // 更新条款信息
        commonProcessService.updateClauseAutSaAud(req.getAutSaAudLists(), hisClauseOrArticleAutSaAuds, totalCount);
    }

    /**
     * 处理评审初查修改
     *
     * @param req 流程参数
     */
    private void processSrClauseUpdate(AutSaAudSaveDTO req) {
        // 校验款信息
        commonProcessService.checkClause(req);
        // 款上限
        int totalCount = MapUtils.getInteger(cstCertificationStandardsService.selectCountByVersionId(req.getAutSaRelation().getAutCsId()), "clauseCount");
        // 评审待复查
        commonProcessService.processClauseOrArticle(req, totalCount);
    }

    /**
     * 校验分组信息并获取分组内对应的小结数量
     *
     * @param req  流程参数
     * @param flag 标识
     * @return 数量
     */
    private int checkDomainGroupsAndGetTotalCount(AutSaAudSaveDTO req, boolean flag) {
        if (CollectionUtils.isEmpty(req.getAutSaAudLists()) || req.getAutSaAudLists().stream().anyMatch(a -> StringUtils.isBlank(a.getClauseId()))) {
            log.error("提交评审小结类型：{} ， 数据没有款id ", req.getSubmitType());
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_ERROR_1000002);
        }
        // 评审员当前分配的分组信息
        HospitalReviewer hospitalReviewer = hospitalReviewerMapper.selectHospitalReviewerByApplyNoAndAccountId(req.getAutSaRelation().getHospitalApplyNo(), req.getAccountId());
        if (StringUtils.isBlank(hospitalReviewer.getFieldIdList())) {
            log.error("领域列表为空");
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
        }
        // 查询分组 + 主题 + 条款信息
        List<DomainGroupNode> domainGroups = hospitalDomainGroupService.selectDomainGroup(flag ? hospitalReviewer.getFieldIdList() : null, req.getAutSaRelation().getAutCsId());
        List<String> groupInfos = Lists.newArrayList();
        //获取分组和大主题的总数
        AtomicReference<Integer> srBtAndGroupSummaryNum = new AtomicReference<>(0);
        domainGroups.stream().forEach(a -> {
            if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.SR_GROUP_SUMMARY)) {
                // 组小结
                groupInfos.add(a.getGroupId());
            } else {
                a.getChildren().stream().forEach(aa -> {
                    if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.SR_BT_SUMMARY)) {
                        // 大主题 小结
                        groupInfos.add(aa.getGroupId());
                    }
                });
            }

            if (flag) {
                //组小结
                if (a.getType().equals(Constants.INT_ZERO)) {
                    srBtAndGroupSummaryNum.getAndSet(srBtAndGroupSummaryNum.get() + 1);
                }
                a.getChildren().forEach(aa -> {
                    if (aa.getType().equals(Constants.INT_ONE)) {
                        // 大主题 小结
                        srBtAndGroupSummaryNum.getAndSet(srBtAndGroupSummaryNum.get() + 1);
                    }
                });
            }

        });
        req.setSrBtAndGroupSummaryNum(srBtAndGroupSummaryNum.get());

        if (req.getAutSaAudLists().stream().anyMatch(a -> !groupInfos.contains(a.getClauseId()))) {
            // 入参传入的款id 存在不属于当前版本的id
            log.error("提交评审小结id:, 在当前用户分配的组内不存在");
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
        }
        return groupInfos.size();
    }

    /**
     * 处理评审总结
     *
     * @param req 流程参数
     */
    private void processSrSummary(AutSaAudSaveDTO req) {
        if (req.getAutSaAudLists().stream().anyMatch(a -> StringUtils.isBlank(a.getAutResult()))) {
            log.error("入参审核结果：autResult 为空");
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_ERROR_1000002);
        }
        // 评审复查总结 需要校验前端传入的结果是否满足条件
        List<AutSaAudBusinessData> autSaAudReportBusinessDatas = autSaAudBusinessDataService.selectAutSaAudBusinessData(req.getAutCode(), AutSaAudBusinessCodeEnum.AUT_SA_AUD_REPORT.getCode());
        log.info("查询到评审报告信息：{}", JSON.toJSONString(autSaAudReportBusinessDatas));
        AutSaAudReport autSaAudReport = (CollectionUtils.isNotEmpty(autSaAudReportBusinessDatas) && StringUtils.isNotBlank(autSaAudReportBusinessDatas.get(0).getData())) ? JSON.parseObject(autSaAudReportBusinessDatas.get(0).getData(), AutSaAudReport.class) : null;
        if (autSaAudReport == null || !autSaAudReport.getAutSaAudResult().toString().equals(req.getAutSaAudLists().get(0).getAutResult())) {
            log.info("提交评审复查总结时 数据库表保存的 评审报告信息 为空 或者传入的评审结果和评审报告中的评审结果不一致");
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
        }
        // 处理总结提交
        commonProcessService.processSummary(req);
        String nextStatus;
        AutSaAud autSaAud = req.getAutSaAudLists().get(0);
        if (AutSaAudResultEnum.checkIsAutSaAudResultEnum(autSaAud.getAutResult(), AutSaAudResultEnum.NOT_CERTIFIED)) {
            // 组长 不通过认证 流程单结束
            nextStatus = commonProcessService.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), StatusProcessEnum.RJ);
        } else if (AutSaAudResultEnum.checkIsAutSaAudResultEnum(autSaAud.getAutResult(), AutSaAudResultEnum.CONDITIONAL_CERTIFICATION)) {
            // 组长 有条件通过认证
            nextStatus = commonProcessService.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), StatusProcessEnum.PART_PASS);
        } else {
            // 组长 通过认证
            nextStatus = commonProcessService.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), StatusProcessEnum.PS);
        }
        log.info("节点翻转到：{}", nextStatus);
        // 更新关联表 aut_sa_relation
        commonProcessService.updateAutSaRelation(nextStatus, req.getAutCode());
        commonProcessService.generatePdf(req.getAutCode(), req.getAutSaRelation().getAutCsId());
    }

    @Override
    public List<AutSaAudList> queryList(AutSaAudQueryDTO req) {
        List<AutSaAudList> autSaAudLists = new ArrayList<>();
        AutSaAudList autSaAudList = new AutSaAudList();

        // 公共反参
        commonProcessService.getGroupProgressList(autSaAudList, req);

        // 查询 AutSaAud
        queryAutSaAud(req, autSaAudList);

        autSaAudLists.add(autSaAudList);

        return commonProcessService.listSorted(autSaAudLists);
    }

    private void queryAutSaAud(AutSaAudQueryDTO req, AutSaAudList autSaAudList) {
        AutSaAud autSaAud = new AutSaAud();
        autSaAud.setAutCode(req.getAutSaRelation().getAutCode());
        List<String> querySubmitTypes = Arrays.asList(AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType(),
                AutSaAudSubmitTypeEnum.TR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.TR_SUMMARY.getSubmitType(),
                AutSaAudSubmitTypeEnum.TR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SKIP.getSubmitType());
        autSaAud.setSubmitType(String.join(",", querySubmitTypes));
        autSaAud.setStatus(HospitalConstants.NUM_1);
        List<AutSaAud> autSaAudLists = autSaAudMapper.selectAutSaAudList(autSaAud);

        //封装共享描述信息ShareDescDTOList
        List<AutSaAud> autSaAuds = packShareDescDTOList(autSaAudLists, req);

        autSaAudList.setAutSaAud(autSaAuds);

    }

    private List<AutSaAud> packShareDescDTOList(List<AutSaAud> autSaAudLists, AutSaAudQueryDTO req) {

        if (CollectionUtils.isNotEmpty(autSaAudLists)) {
            if (req.getAutSaRelation() == null || StringUtils.isBlank(req.getAutSaRelation().getAutCode())) {
                throw new ServiceException("封装共享修改描述信息时，入参节点状态不能为空");
            }

            //评审员角色，在030202，0302041节点，返回修改描述信息
            boolean reviewerFlag = AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(),
                    AutSaAudStatusEnum.SR_CLAUSE_PROCESS, AutSaAudStatusEnum.SR_V_CLAUSE_REJECT);
            //评审员组长角色，在030203，030204节点，评审员未修改的款 展示修改描述信息，根据共享状态判断
            boolean reviewerLeaderFlag = AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(),
                    AutSaAudStatusEnum.WAIT_SR_V_CLAUSE, AutSaAudStatusEnum.SR_V_CLAUSE_PROCESS);

            if (reviewerFlag || reviewerLeaderFlag) {
                autSaAudLists.forEach(o -> {
                    //组长加共享状态判断
                    //组长和评审员一样改为有共享修改信息都展示
                    //   boolean ShareStatus = !reviewerLeaderFlag || ObjectUtil.equal(o.getIsShareUpdate(), Constants.INT_ONE);
                    if (StringUtils.isNotBlank(o.getShareDesc())) {
                        List<ShareDescDTO> shareDescDTOList = JSON.parseArray(o.getShareDesc(), ShareDescDTO.class).
                                stream().sorted(Comparator.comparing(ShareDescDTO::getDateTime).reversed()).collect(Collectors.toList());
                        o.setShareDescList(shareDescDTOList);
                    }
                });
            }
        }

        return autSaAudLists;
    }

    @Override
    public AutSaAudDetailVO queryDetail(AutSaAudQueryDTO req) {
        // 组织查询详情反参
        AutSaAudDetailVO autSaAudDetailVO = commonProcessService.tissueDetailResult(req);
        // 评审员增加关联的分组信息
        if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(req.getRoleKey(), AutSaAudRoleEnum.ASSESSOR)) {
            HospitalReviewer hospitalReviewer = hospitalReviewerMapper.selectHospitalReviewerByApplyNoAndAccountId(req.getAutSaRelation().getHospitalApplyNo(), req.getAccountId());
            List<DomainGroupNode> domainGroups = hospitalDomainGroupService.selectDomainGroup(hospitalReviewer.getFieldIdList(), req.getAutSaRelation().getAutCsId());
            autSaAudDetailVO.setDomainGroups(domainGroups);

            //封装分组评价结果统计
            autSaAudDetailVO.setEvaluateResultStatsVoList(this.packEvaluateResultStats(req, autSaAudDetailVO));
        }
        //封装共享修改描述
        packShareDescDTOList(autSaAudDetailVO.getLatestAutSaAudList(), req);
        if (MapUtils.isNotEmpty(autSaAudDetailVO.getRejectAutSaAudListMap())) {
            autSaAudDetailVO.getRejectAutSaAudListMap().forEach((k, v) -> {
                packShareDescDTOList(v, req);
            });
        }
        if (MapUtils.isNotEmpty(autSaAudDetailVO.getAutSaAudListMap())) {
            autSaAudDetailVO.getAutSaAudListMap().forEach((k, v) -> {
                packShareDescDTOList(v, req);
            });
        }
        return autSaAudDetailVO;
    }

    private List<EvaluateResultStatsVo> packEvaluateResultStats(AutSaAudQueryDTO req, AutSaAudDetailVO autSaAudDetailVO) {
        List<EvaluateResultStatsVo> evaluateResultStatsVoList = new ArrayList<>();

        //查询节点范围,030202-现场评审中展示
        if (req.getAutSaRelation() != null && StringUtils.isNotBlank(req.getAutSaRelation().getAutCode()) &&
                StringUtils.isNotBlank(req.getAutSaRelation().getHospitalApplyNo()) &&
                AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.SR_CLAUSE_PROCESS)
        ) {
            //查询用户权限
            //1.如果为组长时，
            //1-1查询所有款是否已完成，完成展示’所有分组‘统计信息，且展示对应的分组信息
            //1-1-1未完成，展示已完成的分组统计信息
            //2.如果为组员时，展示自身完成的分组信息，未完成不展示
            HospitalReviewer hospitalReviewer = hospitalReviewerMapper.selectHospitalReviewerByApplyNoAndAccountId(req.getAutSaRelation().getHospitalApplyNo(), req.getAccountId());
            if (hospitalReviewer == null || hospitalReviewer.getLeaderIs() == null || hospitalReviewer.getFieldIdList() == null) {
                log.error("根据applyNo【{}】和评审员Id【{}】查询评审信息异常，数据为{}", req.getAutSaRelation().getHospitalApplyNo(), req.getAccountId(), JSON.toJSONString(hospitalReviewer));
                throw new ServiceException("查询评审信息为空，请联系管理员");
            }
            List<AutSaAud> srGroupSummaryList = autSaAudDetailVO.getAutSaAudListMap().get(AutSaAudSubmitTypeEnum.SR_GROUP_SUMMARY.getSubmitType());
            if (CollectionUtils.isEmpty(srGroupSummaryList)) {
                //未完成小结不展示
                return evaluateResultStatsVoList;
            }

            //获取分组情况(执行了挪动的逻辑)
            HosPlanDetailDTO hosPlanDetailDTO = new HosPlanDetailDTO();
            hosPlanDetailDTO.setAutCode(req.getAutSaRelation().getAutCode());
            if (ObjectUtil.equal(hospitalReviewer.getLeaderIs(), Constants.INT_ONE)) {
                hosPlanDetailDTO.setIsLeader(hospitalReviewer.getLeaderIs().toString());
            } else if (CollectionUtils.isEmpty(srGroupSummaryList) || srGroupSummaryList.stream().noneMatch(o -> ObjectUtil.equal(o.getAccountId(), req.getAccountId()))) {
                //为组员时，未完成小结不展示
                return evaluateResultStatsVoList;
            } else {
                hosPlanDetailDTO.setFieldId(hospitalReviewer.getFieldIdList());
            }
            //组长查询所有分组，组员查询自身分组
            List<CstCertificationStandardVO> cstCertificationStandardVOS = hospitalBaseInfoService.queryReviewClauseInfo(hosPlanDetailDTO, Long.valueOf(req.getAutSaRelation().getAutCsId()));

            //获取分组数据
            if (CollectionUtils.isNotEmpty(cstCertificationStandardVOS)) {
                Map<String, Set<String>> groupClauseIdSetMap = cstCertificationStandardVOS.stream()
                        .collect(Collectors.groupingBy(CstCertificationStandardVO::getDomainId,
                                Collectors.mapping(cst -> String.valueOf(cst.getClauseId()), Collectors.toSet()))
                        );
                //<id,name>,目前在评审中节点有返回此数据
                Map<String, String> domainGroupNodesMap = autSaAudDetailVO.getDomainGroupNodes().stream().collect(Collectors.toMap(DomainGroupNode::getGroupId, DomainGroupNode::getGroupDetail));
                List<AutSaAud> srClauseList = autSaAudDetailVO.getAutSaAudListMap().get(AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType());

                //初始数据
                Map<String, EvaluateResultStatsVo.EvaluateInfo> allGroupResultMap = getInitializeResultMap();

                for (Map.Entry<String, Set<String>> entry : groupClauseIdSetMap.entrySet()) {
                    Set<String> groupClauseIds = entry.getValue();
                    List<AutSaAud> groupSrClauseList = srClauseList.stream().filter(o -> groupClauseIds.contains(o.getClauseId())).collect(Collectors.toList());
                    //判断分组款数和完成评审的款数是否相等，相等封装分组信息，不相等证明没完成评审不封装
                    if (ObjectUtil.equal(groupClauseIds.size(), groupSrClauseList.size())) {
                        //区分基本款与非基本款,是否为基本款（0:否,1：是）
                        Map<String, String> clauseIdIsStarMap = cstCertificationStandardVOS.stream().collect(Collectors.toMap(o ->
                                o.getClauseId().toString(), CstCertificationStandardVO::getIsStar));

                        //<结果’优秀‘，EvaluateInfo>...
                        Map<String, EvaluateResultStatsVo.EvaluateInfo> resultMap = getInitializeResultMap();

                        groupSrClauseList.forEach(groupSrClause -> {
                            String isStar = clauseIdIsStarMap.get(groupSrClause.getClauseId());
                            EvaluateResultStatsVo.EvaluateInfo evaluateInfo = resultMap.getOrDefault(groupSrClause.getAutResult(), new EvaluateResultStatsVo.EvaluateInfo());

                            //封装为7: 达标之上（1,2,3）的数据
                            this.packAboveStandard(isStar, groupSrClause, resultMap);

                            evaluateInfo.setAutSaAudResult(groupSrClause.getAutResult());
                            //基本款
                            if (ObjectUtil.equal(isStar, Constants.STR_NUM_1)) {
                                evaluateInfo.setBasicClauseCount(evaluateInfo.getBasicClauseCount() + 1);
                            } else {
                                //非基本款
                                evaluateInfo.setNotBasicClauseCount(evaluateInfo.getNotBasicClauseCount() + 1);
                            }
                            resultMap.put(groupSrClause.getAutResult(), evaluateInfo);
                        });
                        //完成所有款且是组长为true,合并所有分组数据
                        Boolean leaderIs = srGroupSummaryList.size() == groupClauseIdSetMap.size() && ObjectUtil.equal(hospitalReviewer.getLeaderIs(), Constants.INT_ONE);
                        EvaluateResultStatsVo evaluateResultStatsVo = calculatingRate(resultMap, entry.getKey(), domainGroupNodesMap.get(entry.getKey()), leaderIs,allGroupResultMap);
                        evaluateResultStatsVoList.add(evaluateResultStatsVo);
                    }
                }

                //判断小结总数是否等于分组数量，等于展示所有分组信息
                if (srGroupSummaryList.size() == groupClauseIdSetMap.size() && ObjectUtil.equal(hospitalReviewer.getLeaderIs(), Constants.INT_ONE)) {
                    //核算占比
                    EvaluateResultStatsVo evaluateResultStatsVo = calculatingRate(allGroupResultMap, "0", "所有分组", false, null);
                    evaluateResultStatsVoList.add(evaluateResultStatsVo);
                }

            }
        }
        evaluateResultStatsVoList = evaluateResultStatsVoList.stream().sorted(Comparator.comparing(EvaluateResultStatsVo::getDomainId)).collect(Collectors.toList());

        return evaluateResultStatsVoList;
    }

    /**
     * 核算分组占比
     *
     * @param groupResultMap 分组数据
     * @param domainId       分组Id
     * @param domainName     分组名称
     * @param allGroupFlag   完成所有款且是组长为true,合并所有分组数据
     * @param allGroupResultMap 当allGroupFlag为true时，封装的所有分组数据对象
     * @return
     */
    private EvaluateResultStatsVo calculatingRate(Map<String, EvaluateResultStatsVo.EvaluateInfo> groupResultMap, String domainId, String domainName, Boolean allGroupFlag, Map<String, EvaluateResultStatsVo.EvaluateInfo> allGroupResultMap) {

        EvaluateResultStatsVo evaluateResultStatsVo = new EvaluateResultStatsVo();
        List<EvaluateResultStatsVo.EvaluateInfo> evaluateInfoList = new ArrayList<>();
        evaluateResultStatsVo.setDomainId(domainId);
        evaluateResultStatsVo.setDomainName(domainName);

        //基本款总数
        int starCount = groupResultMap.values().stream().filter(o-> !AutSaAudResultEnum.checkIsAutSaAudResultEnum(o.getAutSaAudResult(),
                AutSaAudResultEnum.ABOVE_STANDARD)).mapToInt(EvaluateResultStatsVo.EvaluateInfo::getBasicClauseCount).sum();
        //非基本款总数
        int notStarCount = groupResultMap.values().stream().filter(o-> !AutSaAudResultEnum.checkIsAutSaAudResultEnum(o.getAutSaAudResult(),
                AutSaAudResultEnum.ABOVE_STANDARD)).mapToInt(EvaluateResultStatsVo.EvaluateInfo::getNotBasicClauseCount).sum();

        //封装管理员不适用‘9’到‘6，同步算占比，外层不算
        EvaluateResultStatsVo.EvaluateInfo conversionAdminNotApplicableInfo = conversionAdminNotApplicableInfo(groupResultMap, starCount, notStarCount);

        //排除不适用基本款总数
        int excludeStarCount = starCount - conversionAdminNotApplicableInfo.getBasicClauseCount();
        //排除不适用非基本款总数
        int excludeNotStarCount = notStarCount - conversionAdminNotApplicableInfo.getNotBasicClauseCount();

        //核算占比
        groupResultMap.forEach((autResult, evaluateInfo) -> {
            //排除不适用的，分母需减去不是款数
            if (!AutSaAudResultEnum.checkIsAutSaAudResultEnum(autResult, AutSaAudResultEnum.NOT_APPLICABLE, AutSaAudResultEnum.ADMIN_NOT_APPLICABLE)) {
                evaluateInfo.setCount(evaluateInfo.getBasicClauseCount() + evaluateInfo.getNotBasicClauseCount());
                evaluateInfo.setBasicRate(commonProcessService.getRate(String.valueOf(evaluateInfo.getBasicClauseCount()), String.valueOf(excludeStarCount)));
                evaluateInfo.setNotBasicRate(commonProcessService.getRate(String.valueOf(evaluateInfo.getNotBasicClauseCount()), String.valueOf(excludeNotStarCount)));
                //  评分总数 / 分组总数
                evaluateInfo.setRateCount(commonProcessService.getRate(String.valueOf(evaluateInfo.getCount()), String.valueOf(excludeStarCount + excludeNotStarCount)));
            }

            //为组长时，判断小结总数是否等于分组数量，等于展示所有分组信息,证明已经完成所有评审
            if (allGroupFlag) {
                EvaluateResultStatsVo.EvaluateInfo allEvaluateInfo = allGroupResultMap.getOrDefault(autResult, new EvaluateResultStatsVo.EvaluateInfo());
                allEvaluateInfo.setAutSaAudResult(autResult);
                allEvaluateInfo.setBasicClauseCount(allEvaluateInfo.getBasicClauseCount() + evaluateInfo.getBasicClauseCount());
                allEvaluateInfo.setNotBasicClauseCount(allEvaluateInfo.getNotBasicClauseCount() + evaluateInfo.getNotBasicClauseCount());
                allGroupResultMap.put(autResult, allEvaluateInfo);
            }


        });
        if (CollectionUtils.isNotEmpty(groupResultMap.values())) {
            evaluateInfoList.addAll(groupResultMap.values());
        }
        evaluateResultStatsVo.setEvaluateInfoList(evaluateInfoList);
        return evaluateResultStatsVo;

    }

    private EvaluateResultStatsVo.EvaluateInfo conversionAdminNotApplicableInfo(Map<String, EvaluateResultStatsVo.EvaluateInfo> resultMap, Integer starCount, Integer notStarCount) {

        EvaluateResultStatsVo.EvaluateInfo adminNotApplicableEvaluateInfo = resultMap.getOrDefault(AutSaAudResultEnum.ADMIN_NOT_APPLICABLE.getCode().toString(), new EvaluateResultStatsVo.EvaluateInfo());
        EvaluateResultStatsVo.EvaluateInfo notApplicableEvaluateInfo = resultMap.get(AutSaAudResultEnum.NOT_APPLICABLE.getCode().toString());

        notApplicableEvaluateInfo.setAutSaAudResult(AutSaAudResultEnum.NOT_APPLICABLE.getCode().toString());
        notApplicableEvaluateInfo.setBasicClauseCount(notApplicableEvaluateInfo.getBasicClauseCount() + adminNotApplicableEvaluateInfo.getBasicClauseCount());
        notApplicableEvaluateInfo.setNotBasicClauseCount(notApplicableEvaluateInfo.getNotBasicClauseCount() + adminNotApplicableEvaluateInfo.getNotBasicClauseCount());
        notApplicableEvaluateInfo.setCount(notApplicableEvaluateInfo.getBasicClauseCount() + notApplicableEvaluateInfo.getNotBasicClauseCount());
        notApplicableEvaluateInfo.setBasicRate(commonProcessService.getRate(String.valueOf(notApplicableEvaluateInfo.getBasicClauseCount()), String.valueOf(starCount)));
        notApplicableEvaluateInfo.setNotBasicRate(commonProcessService.getRate(String.valueOf(notApplicableEvaluateInfo.getNotBasicClauseCount()), String.valueOf(notStarCount)));
        //  评分总数 / 分组总数
        notApplicableEvaluateInfo.setRateCount(commonProcessService.getRate(String.valueOf(notApplicableEvaluateInfo.getCount()), String.valueOf(starCount + notStarCount)));

        resultMap.put(AutSaAudResultEnum.NOT_APPLICABLE.getCode().toString(), notApplicableEvaluateInfo);
        return notApplicableEvaluateInfo;

    }

    private void packAboveStandard(String isStar, AutSaAud groupSrClause, Map<String, EvaluateResultStatsVo.EvaluateInfo> resultMap) {

        if (AutSaAudResultEnum.checkIsAutSaAudResultEnum(groupSrClause.getAutResult(), AutSaAudResultEnum.EXCELLENT,
                AutSaAudResultEnum.GOOD, AutSaAudResultEnum.REACH_STANDARD)) {
            EvaluateResultStatsVo.EvaluateInfo aboveStandardInfo = resultMap.getOrDefault(AutSaAudResultEnum.ABOVE_STANDARD.getCode().toString(), new EvaluateResultStatsVo.EvaluateInfo());
            aboveStandardInfo.setAutSaAudResult(AutSaAudResultEnum.ABOVE_STANDARD.getCode().toString());
            //基本款
            if (ObjectUtil.equal(isStar, Constants.STR_NUM_1)) {
                aboveStandardInfo.setBasicClauseCount(aboveStandardInfo.getBasicClauseCount() + 1);
            } else {
                //非基本款
                aboveStandardInfo.setNotBasicClauseCount(aboveStandardInfo.getNotBasicClauseCount() + 1);
            }
            resultMap.put(AutSaAudResultEnum.ABOVE_STANDARD.getCode().toString(), aboveStandardInfo);
        }
    }

}
