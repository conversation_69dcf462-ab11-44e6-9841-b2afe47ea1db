<template>
  <el-card v-loading="loading" class="offline-training">
    <el-row :gutter="24">
      <el-col :span="24" :xs="24">
        <el-form ref="queryForm" :model="queryData" size="small" label-width="80px" inline>
          <el-form-item label="培训名称" prop="trainingName">
            <el-input v-model.trim="queryData.trainingName" maxlength="128" placeholder="请输入培训名称" clearable :style="{ width: '100%' }"></el-input>
          </el-form-item>
          <!-- <el-form-item label="培训类型" prop="trainingType">
            <el-select v-model.trim="queryData.trainingType" placeholder="请选择培训类型">
              <el-option value="评审员培训" label="评审员培训"></el-option>
              <el-option value="审查员培训" label="审查员培训"></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="状态" prop="participatesTraining">
            <el-select v-model.trim="queryData.participatesTraining" placeholder="请选择状态" clearable>
              <el-option :value="1" label="未参与"></el-option>
              <el-option :value="0" label="已参与"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="search">
              查询
            </el-button>
            <el-button type="primary" size="small" @click="resetForm">
              重 置
            </el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-row :gutter="24" style="margin-top: 24px">
      <el-col :span="24">
        <el-table :data="dataSource" border>
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button type="text" v-if="apply(scope.row)" @click="toApply(scope.row,'1')">报名</el-button>
              <el-button type="text" v-if="unApply(scope.row)" @click="toApply(scope.row,'0')">取消报名</el-button>
              <el-button type="text" @click="showTrainingDetail(scope.row)">培训详情</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" label="培训名称" prop="trainingName">
            <template slot-scope="scope">
              {{ scope.row.trainingName ? scope.row.trainingName : "--" }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="举办时间" prop="trainingTime">
            <template slot-scope="scope">
              {{ scope.row.trainingTime ? scope.row.trainingTime : "--" }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="举办地点" prop="trainingLocation">
            <template slot-scope="scope">
              {{ scope.row.trainingLocation ? scope.row.trainingLocation : "--" }}
            </template>
          </el-table-column>
          <!-- <el-table-column align="center" label="培训类型" prop="trainingType">
            <template slot-scope="scope">
              {{ scope.row.trainingType ? scope.row.trainingType : "--" }}
            </template>
          </el-table-column> -->
          <el-table-column align="center" label="举办方" prop="organizers">
            <template slot-scope="scope">
              {{ scope.row.organizers ? scope.row.organizers : "--" }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="状态" prop="trainingStatus">
            <template slot-scope="scope">
              {{ trainingStatus(`${scope.row.trainingStatus}`) }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="是否报名" prop="applyStatus">
            <template slot-scope="scope">
              {{ scope.row.applyStatus == 1 ? '已报名' : '未报名' }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="是否参与" prop="participatesTraining">
            <template slot-scope="scope">
              {{ +scope.row.participatesTraining == 0 ? "已参与" : "未参与" }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="创建时间" prop="createTime"></el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryData.pageNum" :limit.sync="queryData.pageSize" @pagination="query" />
      </el-col>
    </el-row>
    <el-dialog title="培训详情" :visible.sync="dialogVisible" width="60%" :close-on-click-modal="false">
      <div class="detail-item">
        <div class="item-name">培训名称：</div>
        <div class="item-value">{{ current.trainingName }}</div>
      </div>
      <!-- <div class="detail-item">
        <div class="item-name">培训类型：</div>
        <div class="item-value">{{ current.trainingType }}</div>
      </div> -->
      <div class="detail-item">
        <div class="item-name">举办时间：</div>
        <div class="item-value">{{ current.trainingTime }}</div>
      </div>
      <div class="detail-item">
        <div class="item-name">举办地点：</div>
        <div class="item-value">{{ current.trainingLocation }}</div>
      </div>
      <div class="detail-item">
        <div class="item-name">举办方：</div>
        <div class="item-value">{{ current.organizers }}</div>
      </div>
      <div class="detail-item">
        <div class="item-name">状态：</div>
        <div class="item-value">{{ trainingStatus(current.trainingStatus) }}</div>
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import request from "@/utils/request";
export default {
  name: "OfflineTraining",
  data() {
    return {
      dataSource: [],
      queryData: {
        trainingName: '',
        trainingType: null,
        participatesTraining: null,
        pageSize: 10,
        pageNum: 1,
      },
      total: 0,
      dialogVisible: false,
      statusOptions: [],
      current: {},
      loading: false,
    }
  },
  computed: {
    trainingStatus() {
      return (value) => {
        const status = this.statusOptions.find((status) => status.dictValue == value)
        return status ? status.dictLabel : value
      }
    },

    apply() {
      return ({ participatesTraining, applyStatus }) => {
        return applyStatus == 0 && participatesTraining != 0
      }
    },
    unApply() {
      return ({ participatesTraining, applyStatus }) => {
        return applyStatus == 1 && participatesTraining != 0
      }
    },
  },
  created() {
    this.initDicts()
    this.query()
  },
  methods: {
    initDicts() {
      this.getDicts("offline_training_status").then((response) => {
        this.statusOptions = response.data
      })
    },
    search() {
      this.queryData.pageNum = 1;
      this.query();
    },
    query() {
      this.loading = true
      request({
        url: '/reviewer/query/cstOffline/training',
        method: "post",
        data: {
          accountId: this.$store.state.user.userId,
          filterIs: '1',
          ...this.queryData
        },
        params: this.queryData
      }).then((res) => {
        this.loading = false
        if (res.code != 200) return;
        this.dataSource = res.rows;
        this.total = res.total;
      }).catch((error) => {
        // console.log(error)
        this.loading = false
      })
    },
    resetForm() {
      this.$refs["queryForm"].resetFields();
      this.queryData.pageNum = 1
      this.query();
    },
    showTrainingDetail(training) {
      this.current = training
      this.dialogVisible = true
    },

    toApply({ id }, applyStatus) {
      request({
        url: '/reviewer/offline/training/apply',
        method: 'post',
        data: [{
          trainingId: `${id}`,
          applyStatus
        }]
      }).then(() => {
        this.query();
      })
    },
  }
}
</script>

<style lang='scss' scoped>
.offline-training {
  padding: 24px;
  ::v-deep .el-dialog {
    .el-dialog__header {
      background-color: #3c81f5;
      padding-top: 10px;
      .el-dialog__title {
        color: white;
      }
      .el-dialog__headerbtn {
        top: 15px;
        .el-dialog__close {
          color: white;
        }
      }
    }
    .el-dialog__body {
      padding-bottom: 1px;
      & > span {
        color: black;
      }
    }
  }
  .detail-item {
    display: flex;
    margin-bottom: 32px;
    font-size: 14px;
    .item-name {
      width: 100px;
      text-align: right;
      color: #666666d8;
    }
    .item-value {
      font-weight: bold;
      color: #000;
    }
  }
}
</style>
