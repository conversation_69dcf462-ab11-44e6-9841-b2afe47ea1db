<template>
  <el-card v-loading="loading" class="online-learning">
    <el-row :gutter="24">
      <el-col :span="24" :xs="24">
        <el-form ref="queryForm" :model="queryData" size="small" label-width="80px" inline>
          <el-form-item label="标题" prop="title">
            <el-input v-model.trim="queryData.title" maxlength="128" placeholder="请输入标题" clearable :style="{ width: '100%' }">
            </el-input>
          </el-form-item>
          <el-form-item label="类型" prop="type">
            <el-select v-model.trim="queryData.type" placeholder="请选择类型" clearable filterable>
              <el-option v-for="cate in typeOptions" :key="cate.dictValue" :value="cate.dictValue" :label="cate.dictLabel">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="类别" prop="category">
            <el-select v-model.trim="queryData.category" placeholder="请选择类别" clearable filterable>
              <el-option v-for="cate in categoryOptions" :key="cate.dictValue" :value="cate.dictValue" :label="cate.dictLabel">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="search">
              查询
            </el-button>
            <el-button type="primary" size="small" @click="resetForm">
              重 置
            </el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-row :gutter="24" style="margin-top: 24px">
      <el-col :span="24">
        <div class="content">
          <div v-for="(source, index) in sourceList" :key="index" class="item" @click="toDetail(source)">
            <div class="cover">
              <img :src="source.resourceCoverAddress" :alt="source.title" width="220px" height="165px">
            </div>
            <div class="resource-title">{{ source.title }}</div>
            <div class="study-count">{{ source.learnNum || 0 }}次学习</div>
          </div>
        </div>
        <div style="text-align: center;">
          <el-pagination background layout="prev, pager, next" :total="total" :current-page="queryData.pageNum" @current-change="handleCurrentChange"></el-pagination>
        </div>
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
import request from "@/utils/request"
import myImg from '@/assets/images/profile.jpg'
export default {
  name: "OnlineLearning",
  data() {
    return {
      sourceList: [],
      queryData: {
        title: "",
        type: null,
        status: 1,
        category: null,
        pageSize: 10,
        pageNum: 1,
      },
      total: 0,
      typeOptions: [],
      categoryOptions: [],
      loading: false
    }
  },
  computed: {
    studyType() {
      return (value) => {
        const type = this.typeOptions.find((type) => type.dictValue == value);
        return type ? type.dictLabel : value;
      };
    },
    studyCategory() {
      return (value) => {
        const category = this.categoryOptions.find(
          (category) => category.dictValue == value
        );
        return category ? category.dictLabel : value;
      };
    }
  },
  created() {
    this.initDicts()
    this.query()
    // for (let index = 0; index < 21; index++) {
    //   this.sourceList.push({
    //     resourceCoverAddress: myImg,
    //     title: '学习资源文字标题' + index,
    //     learnNum: index + '次学习'
    //   })
    // }
  },
  methods: {
    initDicts() {
      this.getDicts("study_type").then((response) => {
        this.typeOptions = response.data
      })
      this.getDicts("study_category").then((response) => {
        this.categoryOptions = response.data
      })
    },
    search() {
      this.queryData.pageNum = 1
      this.query()
    },
    query() {
      this.loading = true
      request({
        url: '/system/resource/list',
        method: "get",
        params: this.queryData,
      }).then((res) => {
        this.loading = false
        if (res.code != 200) return
        this.sourceList = res.rows
        this.total = res.total
      }).catch((error) => {
        // console.log(error)
        this.loading = false
      })
    },
    resetForm() {
      this.$refs["queryForm"].resetFields()
      this.query()
    },
    toDetail(data) {
      request({
        url: '/system/resource/' + data.id,
        method: "get",
      }).then((res) => {
        if (res.code != 200) return
        if (res.data.status == 0) {
          return this.$message({
            type: 'warning',
            message: '抱歉，学习资源已下线'
          })
        } else {
          this.$router.push({
            path: '/learningMaterials/courseDetail',
            query: { id: data.id }
          })
        }
      })
    },
    handleCurrentChange(val) {
      this.queryData.pageNum = val
      this.query()
    }
  }
}
</script>

<style lang="scss" scoped>
.online-learning {
  padding: 24px;
  margin-bottom: 10px;
  .content {
    display: flex;
    flex-wrap: wrap;
    .item {
      width: 220px;
      margin-right: 30px;
      margin-bottom: 30px;
      cursor: pointer;
      .resource-title {
        width: 220px;
        font-size: 16px;
        font-weight: bold;
        line-height: 40px;
        word-break: break-all;
      }
      .study-count {
        font-size: 14px;
        color: #444;
      }
    }
  }
}
</style>
